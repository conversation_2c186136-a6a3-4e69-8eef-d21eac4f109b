#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات حقول التاريخ مع دعم الكتابة بلوحة المفاتيح
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QFormLayout, QLabel, QPushButton
    from PySide6.QtCore import QDate
    from comptabilite_app.ui.style import create_styled_date_edit
    
    class DateTestWindow(QMainWindow):
        """نافذة اختبار حقول التاريخ المحسنة"""
        
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار حقول التاريخ المحسنة")
            self.setGeometry(100, 100, 600, 400)
            
            # الويدجت الرئيسي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout(central_widget)
            
            # عنوان
            title = QLabel("🗓️ اختبار حقول التاريخ المحسنة")
            title.setStyleSheet("""
                font-size: 18px;
                font-weight: bold;
                color: #2563EB;
                margin: 20px 0;
                text-align: center;
            """)
            main_layout.addWidget(title)
            
            # نموذج الاختبار
            form_layout = QFormLayout()
            
            # حقل تاريخ عادي
            self.date_normal = create_styled_date_edit()
            form_layout.addRow("📅 تاريخ عادي:", self.date_normal)
            
            # حقل تاريخ اختياري
            self.date_optional = create_styled_date_edit(
                optional=True, 
                placeholder_text="تاريخ اختياري"
            )
            form_layout.addRow("📅 تاريخ اختياري:", self.date_optional)
            
            # حقل تاريخ مع تاريخ محدد
            self.date_custom = create_styled_date_edit(
                current_date=QDate.currentDate().addDays(30)
            )
            form_layout.addRow("📅 تاريخ مخصص (+30 يوم):", self.date_custom)
            
            # حقل تاريخ دفع (مثل المنتجات)
            self.date_payment = create_styled_date_edit(
                optional=True,
                placeholder_text="غير مدفوع"
            )
            form_layout.addRow("💳 تاريخ الدفع:", self.date_payment)
            
            main_layout.addLayout(form_layout)
            
            # تعليمات الاستخدام
            instructions = QLabel("""
            <h3>تعليمات الاستخدام:</h3>
            <ul>
                <li><b>الكتابة المباشرة:</b> اكتب التاريخ بصيغة DD/MM/YYYY</li>
                <li><b>التنسيق التلقائي:</b> سيتم إضافة الشرطات المائلة تلقائياً</li>
                <li><b>التقويم:</b> انقر على السهم لفتح التقويم</li>
                <li><b>السنة المختصرة:</b> يمكنك كتابة 25 بدلاً من 2025</li>
                <li><b>الحقول الاختيارية:</b> يمكن تركها فارغة</li>
            </ul>
            """)
            instructions.setStyleSheet("""
                background-color: #F0F9FF;
                border: 1px solid #0EA5E9;
                border-radius: 8px;
                padding: 15px;
                margin: 20px 0;
            """)
            main_layout.addWidget(instructions)
            
            # أزرار الاختبار
            buttons_layout = QVBoxLayout()
            
            # زر عرض القيم
            show_values_btn = QPushButton("📋 عرض القيم المدخلة")
            show_values_btn.clicked.connect(self.show_values)
            show_values_btn.setStyleSheet("""
                QPushButton {
                    background-color: #10B981;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    font-weight: bold;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #059669;
                }
            """)
            buttons_layout.addWidget(show_values_btn)
            
            # زر مسح الحقول
            clear_btn = QPushButton("🗑️ مسح جميع الحقول")
            clear_btn.clicked.connect(self.clear_fields)
            clear_btn.setStyleSheet("""
                QPushButton {
                    background-color: #EF4444;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    font-weight: bold;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #DC2626;
                }
            """)
            buttons_layout.addWidget(clear_btn)
            
            # زر تعيين تواريخ تجريبية
            test_dates_btn = QPushButton("🎯 تعيين تواريخ تجريبية")
            test_dates_btn.clicked.connect(self.set_test_dates)
            test_dates_btn.setStyleSheet("""
                QPushButton {
                    background-color: #8B5CF6;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    font-weight: bold;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #7C3AED;
                }
            """)
            buttons_layout.addWidget(test_dates_btn)
            
            main_layout.addLayout(buttons_layout)
            
            # مساحة فارغة
            main_layout.addStretch()
            
        def show_values(self):
            """عرض القيم المدخلة في جميع الحقول"""
            values = []
            
            # التاريخ العادي
            normal_date = self.date_normal.date().toString("dd/MM/yyyy")
            values.append(f"التاريخ العادي: {normal_date}")
            
            # التاريخ الاختياري
            if self.date_optional.date().isValid() and self.date_optional.date() != QDate():
                optional_date = self.date_optional.date().toString("dd/MM/yyyy")
                values.append(f"التاريخ الاختياري: {optional_date}")
            else:
                values.append("التاريخ الاختياري: غير محدد")
            
            # التاريخ المخصص
            custom_date = self.date_custom.date().toString("dd/MM/yyyy")
            values.append(f"التاريخ المخصص: {custom_date}")
            
            # تاريخ الدفع
            if self.date_payment.date().isValid() and self.date_payment.date() != QDate():
                payment_date = self.date_payment.date().toString("dd/MM/yyyy")
                values.append(f"تاريخ الدفع: {payment_date}")
            else:
                values.append("تاريخ الدفع: غير مدفوع")
            
            # عرض النتائج
            result_text = "\n".join(values)
            print("=" * 50)
            print("📋 القيم المدخلة:")
            print("=" * 50)
            print(result_text)
            print("=" * 50)
            
        def clear_fields(self):
            """مسح جميع الحقول"""
            self.date_normal.setDate(QDate.currentDate())
            self.date_optional.setDate(QDate())
            self.date_custom.setDate(QDate.currentDate().addDays(30))
            self.date_payment.setDate(QDate())
            print("🗑️ تم مسح جميع الحقول")
            
        def set_test_dates(self):
            """تعيين تواريخ تجريبية"""
            today = QDate.currentDate()
            
            self.date_normal.setDate(today)
            self.date_optional.setDate(today.addDays(7))
            self.date_custom.setDate(today.addDays(30))
            self.date_payment.setDate(today.addDays(15))
            
            print("🎯 تم تعيين التواريخ التجريبية")

    def main():
        """الدالة الرئيسية"""
        app = QApplication(sys.argv)
        
        # تطبيق ستايل عام
        app.setStyleSheet("""
            QMainWindow {
                background-color: #F8FAFC;
            }
            QLabel {
                color: #1F2937;
            }
        """)
        
        # إنشاء النافذة
        window = DateTestWindow()
        window.show()
        
        print("🚀 تم تشغيل اختبار حقول التاريخ المحسنة")
        print("💡 جرب كتابة التواريخ مباشرة بلوحة المفاتيح!")
        
        sys.exit(app.exec())

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("تأكد من تثبيت PySide6")
    sys.exit(1)
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    sys.exit(1)
