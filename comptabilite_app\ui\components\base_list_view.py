from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QHeaderView, QFrame, QMessageBox)
from PySide6.QtCore import Qt, QSize, Signal
from PySide6.QtGui import QIcon
import sqlite3

# Importer les styles modernes
from ..theme import COLORS, BORDER_RADIUS, SPACING
from ..icons.icons import *

class BaseListView(QWidget):
    """
    Composant de base pour toutes les vues en liste
    Fournit une interface standardisée avec:
    - Un en-tête avec titre et bouton d'ajout
    - Une barre de recherche
    - Un tableau pour afficher les éléments
    """
    
    # Signal émis lorsqu'un élément est ajouté, modifié ou supprimé
    item_changed = Signal()
    
    def __init__(self, db_manager, title="", description="", icon=None, add_button_text="Ajouter"):
        super().__init__()
        self.db_manager = db_manager
        self.title = title
        self.description = description
        self.icon = icon
        self.add_button_text = add_button_text
        self.setup_ui()
    
    def setup_ui(self):
        """Configure l'interface utilisateur de base"""
        layout = QVBoxLayout(self)
        layout.setSpacing(int(SPACING['md'].replace('px', '')))
        layout.setContentsMargins(0, 0, 0, 0)
        
        # En-tête avec titre et bouton d'ajout
        header_container = QWidget()
        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        # Icône et titre
        if self.icon:
            icon_label = QLabel()
            icon_label.setText(svg_to_icon_html(self.icon, "#1A56DB", 24))
            icon_label.setFixedSize(24, 24)
            header_layout.addWidget(icon_label)
        
        title_container = QWidget()
        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(2)
        
        title_label = QLabel(self.title)
        title_label.setObjectName("list_title")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #1A56DB;
        """)
        
        description_label = QLabel(self.description)
        description_label.setObjectName("list_description")
        description_label.setStyleSheet("""
            font-size: 13px;
            color: #4B5563;
        """)
        
        title_layout.addWidget(title_label)
        title_layout.addWidget(description_label)
        
        header_layout.addWidget(title_container)
        header_layout.addStretch()
        
        # Bouton pour ajouter un élément
        self.add_button = QPushButton(self.add_button_text)
        self.add_button.setObjectName("primary_button")
        self.add_button.setIcon(QIcon(":/icons/add.png"))
        self.add_button.setIconSize(QSize(16, 16))
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #1A56DB;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #1E40AF;
            }
        """)
        self.add_button.clicked.connect(self.on_add_clicked)
        header_layout.addWidget(self.add_button)
        
        layout.addWidget(header_container)
        
        # Barre de recherche
        search_container = QFrame()
        search_container.setObjectName("search_container")
        search_container.setStyleSheet("""
            #search_container {
                background-color: white;
                border-radius: 4px;
                border: 1px solid #E5E7EB;
                padding: 8px;
            }
        """)
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(8, 8, 8, 8)
        
        search_icon = QLabel()
        search_icon.setText(svg_to_icon_html(SEARCH_ICON, "#6B7280", 16))
        search_icon.setFixedSize(16, 16)
        search_layout.addWidget(search_icon)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Rechercher...")
        self.search_input.setStyleSheet("""
            border: none;
            font-size: 13px;
            padding: 4px;
        """)
        self.search_input.textChanged.connect(self.filter_items)
        search_layout.addWidget(self.search_input)
        
        layout.addWidget(search_container)
        
        # Tableau des éléments
        table_container = QFrame()
        table_container.setObjectName("table_container")
        table_container.setStyleSheet("""
            #table_container {
                background-color: white;
                border-radius: 4px;
                border: 1px solid #E5E7EB;
            }
        """)
        table_layout = QVBoxLayout(table_container)
        table_layout.setContentsMargins(0, 0, 0, 0)
        
        # Créer le tableau (à configurer dans les classes dérivées)
        self.items_table = QTableWidget()
        
        # Appliquer les styles du tableau
        self.items_table.setStyleSheet("""
            QTableWidget {
                border: none;
                gridline-color: #E5E7EB;
            }
            QHeaderView::section {
                background-color: #F9FAFB;
                color: #374151;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #E5E7EB;
                font-weight: bold;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E5E7EB;
            }
        """)
        self.items_table.setAlternatingRowColors(True)
        self.items_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.items_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.items_table.verticalHeader().setVisible(False)  # Cacher les numéros de ligne
        
        table_layout.addWidget(self.items_table)
        
        # Ajouter le tableau au conteneur
        layout.addWidget(table_container)
    
    def on_add_clicked(self):
        """Méthode appelée lorsque le bouton d'ajout est cliqué"""
        pass
    
    def filter_items(self):
        """Filtre les éléments en fonction du texte de recherche"""
        search_text = self.search_input.text().lower()
        
        for row in range(self.items_table.rowCount()):
            match_found = False
            
            # Parcourir toutes les colonnes sauf la dernière (actions)
            for col in range(self.items_table.columnCount() - 1):
                item = self.items_table.item(row, col)
                if item and search_text in item.text().lower():
                    match_found = True
                    break
            
            self.items_table.setRowHidden(row, not match_found)
    
    def create_action_buttons(self, row_id, item_id, item_name=None):
        """Crée les boutons d'action pour une ligne du tableau"""
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(2, 2, 2, 2)
        actions_layout.setSpacing(6)
        
        # Bouton d'édition
        edit_btn = QPushButton()
        edit_btn.setIcon(QIcon(":/icons/edit.png"))
        edit_btn.setToolTip("Modifier")
        edit_btn.setFixedSize(28, 28)
        edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
        """)
        edit_btn.clicked.connect(lambda: self.on_edit_clicked(item_id))
        
        # Bouton de suppression
        delete_btn = QPushButton()
        delete_btn.setIcon(QIcon(":/icons/delete.png"))
        delete_btn.setToolTip("Supprimer")
        delete_btn.setFixedSize(28, 28)
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #EF4444;
                color: white;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #DC2626;
            }
        """)
        delete_btn.clicked.connect(lambda: self.on_delete_clicked(item_id, item_name))
        
        actions_layout.addWidget(edit_btn)
        actions_layout.addWidget(delete_btn)
        
        return actions_widget
    
    def on_edit_clicked(self, item_id):
        """Méthode appelée lorsque le bouton d'édition est cliqué"""
        pass
    
    def on_delete_clicked(self, item_id, item_name=None):
        """Méthode appelée lorsque le bouton de suppression est cliqué"""
        pass
