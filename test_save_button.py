#!/usr/bin/env python3
"""
Test script to debug the save button issue in products form
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMessageBox
from comptabilite_app.database.db_manager import DatabaseManager
from comptabilite_app.ui.forms.produit_form import ProduitForm

def test_save_button():
    """Test the save button functionality"""
    app = QApplication(sys.argv)
    
    # Initialize database manager
    db_manager = DatabaseManager()
    
    # Create the product form
    form = ProduitForm(db_manager)
    
    # Test the save_produit method directly
    print("🔍 Testing save_produit method...")
    
    # Fill in some test data
    form.designation_input.setText("Test Product")
    form.prix_achat_input.setValue(100.0)
    form.prix_vente_input.setValue(150.0)
    form.stock_input.setValue(10)
    
    # Calculate TTC prices
    form.calculate_ttc_prices()
    
    print(f"📋 Form data:")
    print(f"   Designation: {form.designation_input.text()}")
    print(f"   Prix achat HT: {form.prix_achat_input.value()}")
    print(f"   Prix achat TTC: {form.prix_achat_ttc_input.value()}")
    print(f"   Prix vente HT: {form.prix_vente_input.value()}")
    print(f"   Prix vente TTC: {form.prix_vente_ttc_input.value()}")
    print(f"   Stock: {form.stock_input.value()}")
    
    # Test validation
    print("\n🔍 Testing form validation...")
    validation_result = form.validate_form()
    print(f"   Validation result: {validation_result}")
    
    # Test save method
    print("\n💾 Testing save method...")
    try:
        # Call save_produit method directly
        form.save_produit()
        print("✅ Save method executed successfully!")
    except Exception as e:
        print(f"❌ Error in save method: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # Check if product was saved
    print("\n🔍 Checking if product was saved...")
    try:
        cursor = db_manager.conn.cursor()
        cursor.execute("SELECT * FROM produits WHERE designation = 'Test Product'")
        product = cursor.fetchone()
        if product:
            print("✅ Product found in database!")
            print(f"   Code: {product['code']}")
            print(f"   Designation: {product['designation']}")
            print(f"   Prix achat HT: {product['prix_achat']}")
            print(f"   Prix achat TTC: {product.get('prix_achat_ttc', 'N/A')}")
            print(f"   Prix vente HT: {product['prix_vente']}")
            print(f"   Prix vente TTC: {product.get('prix_vente_ttc', 'N/A')}")
            
            # Clean up test data
            cursor.execute("DELETE FROM produits WHERE designation = 'Test Product'")
            db_manager.conn.commit()
            print("🧹 Test data cleaned up")
        else:
            print("❌ Product not found in database!")
    except Exception as e:
        print(f"❌ Error checking database: {str(e)}")
    
    print("\n🎯 Test completed!")

if __name__ == "__main__":
    test_save_button()
