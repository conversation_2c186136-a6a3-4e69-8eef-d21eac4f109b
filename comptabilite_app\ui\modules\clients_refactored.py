from PySide6.QtWidgets import QTableWidgetItem, QHeaderView, QMessageBox
from PySide6.QtCore import Qt
import sqlite3

# Importer le module de base
from ..components.base_module import BaseModule
from ..icons.icons import CLIENTS_ICON

# Importer le dialogue client
from ..components.client_dialog import ClientDialog

class ClientsModule(BaseModule):
    """Module de gestion des clients avec interface simplifiée"""
    
    def __init__(self, db_manager, signals=None):
        super().__init__(
            db_manager=db_manager,
            signals=signals,
            title="Gestion des Clients",
            description="Ajoutez, modifiez et supprimez des clients",
            icon=CLIENTS_ICON
        )
        
        # Configurer le bouton d'ajout
        self.add_button.setText("Ajouter un client")
        
        # Configurer le tableau
        self.setup_table()
        
        # Charger les données
        self.load_clients()
    
    def setup_table(self):
        """Configure le tableau des clients"""
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "Code", "Nom", "ICE", "IF", "Téléphone", "Adresse", "Actions"
        ])
        
        # Configuration des colonnes
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Code
        self.items_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)  # Nom
        self.items_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)  # ICE
        self.items_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # IF
        self.items_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Téléphone
        self.items_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Stretch)  # Adresse
        self.items_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Actions
        
        # Configurer la recherche
        self.search_input.setPlaceholderText("Rechercher un client...")
    
    def load_clients(self):
        """Charge les clients depuis la base de données"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT * FROM clients ORDER BY code")
        clients = cursor.fetchall()
        
        self.items_table.setRowCount(0)
        
        for row_num, client in enumerate(clients):
            self.items_table.insertRow(row_num)
            
            # Ajouter les données du client
            self.items_table.setItem(row_num, 0, QTableWidgetItem(client['code'] or ""))
            self.items_table.setItem(row_num, 1, QTableWidgetItem(client['nom']))
            self.items_table.setItem(row_num, 2, QTableWidgetItem(client['ice'] or ""))
            self.items_table.setItem(row_num, 3, QTableWidgetItem(client['if_fiscal'] or ""))
            self.items_table.setItem(row_num, 4, QTableWidgetItem(client['telephone'] or ""))
            self.items_table.setItem(row_num, 5, QTableWidgetItem(client['adresse'] or ""))
            
            # Créer les boutons d'action
            actions_widget = self.create_action_buttons(row_num, client['id'], client['nom'])
            self.items_table.setCellWidget(row_num, 6, actions_widget)
            
            # Stocker l'ID du client dans la première colonne (invisible)
            self.items_table.item(row_num, 0).setData(Qt.UserRole, client['id'])
    
    def show_add_dialog(self):
        """Affiche la boîte de dialogue pour ajouter un client"""
        dialog = ClientDialog(self.db_manager, parent=self)
        dialog.client_saved.connect(self.on_client_saved)
        dialog.exec()
    
    def show_edit_dialog(self, client_id):
        """Affiche la boîte de dialogue pour modifier un client"""
        dialog = ClientDialog(self.db_manager, client_id=client_id, parent=self)
        dialog.client_saved.connect(self.on_client_saved)
        dialog.exec()
    
    def on_client_saved(self):
        """Appelé lorsqu'un client est ajouté ou modifié"""
        self.load_clients()
        
        # Émettre le signal pour informer les autres modules
        if self.signals:
            self.signals.clients_changed.emit()
    
    def delete_item(self, client_id, client_name):
        """Supprime un client"""
        confirm = QMessageBox.question(
            self, "Confirmation",
            f"Êtes-vous sûr de vouloir supprimer le client {client_name} ?\nCette action est irréversible.",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if confirm == QMessageBox.Yes:
            try:
                cursor = self.db_manager.conn.cursor()
                cursor.execute("DELETE FROM clients WHERE id = ?", (client_id,))
                self.db_manager.conn.commit()
                
                self.load_clients()
                
                # Émettre le signal pour informer les autres modules
                if self.signals:
                    self.signals.clients_changed.emit()
                
                QMessageBox.information(self, "Succès", f"Client {client_name} supprimé avec succès.")
            except sqlite3.Error as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression du client: {str(e)}")
