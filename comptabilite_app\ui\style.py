"""
Module contenant les styles CSS globaux pour l'application
"""

# Style pour les QComboBox avec hauteur optimisée
COMBOBOX_STYLE = """
    QComboBox {
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 6px;
        background-color: white;
        min-height: 28px;
        font-size: 13px;
    }
    QComboBox::drop-down {
        subcontrol-origin: padding;
        subcontrol-position: top right;
        width: 20px;
        border-left-width: 1px;
        border-left-color: #ced4da;
        border-left-style: solid;
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
    }
    QComboBox QAbstractItemView {
        border: 1px solid #ced4da;
        selection-background-color: #e3f2fd;
        selection-color: black;
        padding: 5px;
        font-size: 13px;
    }
"""

# Style pour les QLineEdit avec hauteur optimisée
LINEEDIT_STYLE = """
    QLineEdit {
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 6px;
        background-color: white;
        min-height: 28px;
        font-size: 13px;
    }
"""

# Style pour les QSpinBox et QDoubleSpinBox avec hauteur optimisée
SPINBOX_STYLE = """
    QSpinBox, QDoubleSpinBox {
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 6px;
        background-color: white;
        min-height: 28px;
        font-size: 13px;
    }
    QSpinBox::up-button, QDoubleSpinBox::up-button {
        subcontrol-origin: border;
        subcontrol-position: top right;
        width: 18px;
        height: 14px;
    }
    QSpinBox::down-button, QDoubleSpinBox::down-button {
        subcontrol-origin: border;
        subcontrol-position: bottom right;
        width: 18px;
        height: 14px;
    }
"""

# Style pour les QPushButton avec hauteur optimisée
BUTTON_STYLE = """
    QPushButton {
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 8px 16px;
        font-weight: bold;
        min-height: 32px;
        font-size: 13px;
    }
    QPushButton:hover {
        background-color: #0069d9;
    }
    QPushButton:pressed {
        background-color: #0062cc;
    }
"""

# Style pour les QTableWidget avec hauteur de ligne optimisée
TABLE_STYLE = """
    QTableWidget {
        border: 1px solid #ddd;
        border-radius: 6px;
        background-color: white;
        gridline-color: #ddd;
    }
    QHeaderView::section {
        background-color: #f8f9fa;
        padding: 8px;
        border: 1px solid #ddd;
        font-weight: bold;
        min-height: 25px;
        font-size: 13px;
    }
    QTableWidget::item {
        padding: 6px;
        min-height: 25px;
        font-size: 13px;
    }
    QTableWidget::item:selected {
        background-color: #e3f2fd;
        color: black;
    }
"""

# Style pour les QLabel avec hauteur optimisée
LABEL_STYLE = """
    QLabel {
        min-height: 22px;
        padding: 4px;
        font-size: 13px;
    }
"""

# Style pour les QDateEdit avec hauteur optimisée et design moderne
DATEEDIT_STYLE = """
    QDateEdit {
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 6px;
        background-color: white;
        min-height: 32px;
        font-size: 14px;
        color: #333;
    }
    QDateEdit:focus {
        border: 2px solid #3B82F6;
        background-color: #F8FAFC;
    }
    QDateEdit:hover {
        border: 1px solid #9CA3AF;
    }
    QDateEdit::drop-down {
        subcontrol-origin: padding;
        subcontrol-position: top right;
        width: 20px;
        border-left-width: 1px;
        border-left-color: #ced4da;
        border-left-style: solid;
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
        background-color: #F3F4F6;
    }
    QDateEdit::drop-down:hover {
        background-color: #E5E7EB;
    }
    QDateEdit::down-arrow {
        image: none;
        border: none;
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-top: 6px solid #6B7280;
        margin: 2px;
    }
    QDateEdit QAbstractItemView {
        background-color: white;
        border: 1px solid #D1D5DB;
        border-radius: 6px;
        selection-background-color: #3B82F6;
    }
"""

# Style amélioré pour les QDateEdit avec option vide
DATEEDIT_OPTIONAL_STYLE = """
    QDateEdit {
        padding: 8px 12px;
        border: 1px solid #D1D5DB;
        border-radius: 6px;
        background-color: white;
        min-height: 32px;
        font-size: 14px;
        color: #6B7280;
    }
    QDateEdit:focus {
        border: 2px solid #10B981;
        background-color: #F0FDF4;
        color: #333;
    }
    QDateEdit:hover {
        border: 1px solid #9CA3AF;
    }
    QDateEdit::drop-down {
        subcontrol-origin: padding;
        subcontrol-position: top right;
        width: 20px;
        border-left-width: 1px;
        border-left-color: #D1D5DB;
        border-left-style: solid;
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
        background-color: #F9FAFB;
    }
    QDateEdit::drop-down:hover {
        background-color: #F3F4F6;
    }
"""

# Style pour les QGroupBox avec espacement optimisé
GROUPBOX_STYLE = """
    QGroupBox {
        font-size: 14px;
        font-weight: bold;
        color: #2c3e50;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        margin-top: 16px;
        padding-top: 16px;
        padding-bottom: 8px;
    }
    QGroupBox::title {
        subcontrol-origin: margin;
        subcontrol-position: top left;
        left: 8px;
        padding: 0 4px;
    }
"""

# Fonction utilitaire pour créer des QDateEdit avec style uniforme
def create_styled_date_edit(current_date=None, optional=False, placeholder_text=None):
    """
    Crée un QDateEdit avec un style uniforme et support de saisie clavier

    Args:
        current_date: Date par défaut (QDate ou None)
        optional: Si True, permet une date vide avec texte de placeholder
        placeholder_text: Texte à afficher quand la date est vide

    Returns:
        QDateEdit configuré avec le style approprié
    """
    from PySide6.QtWidgets import QDateEdit
    from PySide6.QtCore import QDate

    date_edit = QDateEdit()
    date_edit.setCalendarPopup(True)
    date_edit.setDisplayFormat("yyyy/MM/dd")


    # Définir les limites de date raisonnables
    date_edit.setFocusPolicy(Qt.StrongFocus)
    date_edit.setReadOnly(False)
    date_edit.setMinimumDate(QDate(1900, 1, 1))
    date_edit.setMaximumDate(QDate(2100, 12, 31))


    if optional:
        date_edit.setStyleSheet(DATEEDIT_OPTIONAL_STYLE)
        if placeholder_text:
            date_edit.setSpecialValueText(placeholder_text)
        date_edit.setDate(QDate())  # Date vide
        # Permettre les dates nulles pour les champs optionnels
        date_edit.setMinimumDate(QDate())
        date_edit.setClearButtonEnabled(True) # Add clear button for optional dates
    else:
        date_edit.setStyleSheet(DATEEDIT_STYLE)
        if current_date:
            date_edit.setDate(current_date)
        else:
            date_edit.setDate(QDate.currentDate())
        date_edit.setClearButtonEnabled(False) # No clear button for mandatory dates

    return date_edit