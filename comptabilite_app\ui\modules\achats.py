from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QFormLayout, QMessageBox, QHeaderView, QDoubleSpinBox,
                              QSpinBox, QFrame, QComboBox, QDateEdit, QDialog,
                              QGroupBox, QScrollArea, QMenu)
from PySide6.QtCore import Qt, QDate, QPoint
import sqlite3
from datetime import datetime
from ...utils.excel_exporter import ExcelExporter

class AchatsModule(QWidget):
    """Module de gestion des achats de produits"""

    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur du module"""
        # Layout principal (QVBoxLayout)
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # Titre du module
        title = QLabel("Achats de Produits")
        title.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        """)
        main_layout.addWidget(title)

        # Création d'un QScrollArea pour assurer que l'interface est responsive
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)

        # Widget conteneur pour le contenu scrollable
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(20)

        # Section Informations de la facture (QGroupBox avec QFormLayout)
        self.setup_info_facture_section(scroll_layout)

        # Section Articles achetés (QGroupBox avec QTableWidget)
        self.setup_articles_section(scroll_layout)

        # Section Boutons d'action (QHBoxLayout avec deux boutons)
        self.setup_action_buttons(scroll_layout)

        # Ajouter le widget de contenu au scroll area
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)

        # Initialiser les données
        self.initialiser_donnees()

    def setup_info_facture_section(self, parent_layout):
        """Configure la section des informations de la facture"""
        # Création du QGroupBox pour les informations de la facture
        info_facture_group = QGroupBox("Informations de la facture")
        info_facture_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 16px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 10px;
                padding: 0 5px;
            }
        """)

        # Layout pour le contenu du QGroupBox (QFormLayout)
        form_layout = QFormLayout(info_facture_group)
        form_layout.setSpacing(15)
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)

        # Fournisseur (QComboBox)
        self.fournisseur_combo = QComboBox()
        self.fournisseur_combo.setPlaceholderText("Sélectionner un fournisseur")
        self.fournisseur_combo.setStyleSheet("""
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: white;
            min-height: 20px;
        """)
        form_layout.addRow("Fournisseur:", self.fournisseur_combo)

        # Numéro de facture d'achat (QLineEdit)
        self.numero_facture_input = QLineEdit()
        self.numero_facture_input.setPlaceholderText("Ex: FAC-A-2024-001")
        self.numero_facture_input.setStyleSheet("""
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: white;
        """)
        form_layout.addRow("Numéro de facture d'achat:", self.numero_facture_input)

        # Date (QDateEdit au format français)
        from ..style import create_styled_date_edit
        self.date_facture = create_styled_date_edit()
        form_layout.addRow("Date:", self.date_facture)

        # Mode de paiement (QComboBox)
        self.mode_paiement_combo = QComboBox()
        self.mode_paiement_combo.addItems([
            "Espèces", "Virement", "Chèque", "Carte", "Autre"
        ])
        self.mode_paiement_combo.setStyleSheet("""
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: white;
            min-height: 20px;
        """)
        form_layout.addRow("Mode de paiement:", self.mode_paiement_combo)

        # Ajouter le QGroupBox au layout parent
        parent_layout.addWidget(info_facture_group)

    def setup_articles_section(self, parent_layout):
        """Configure la section des articles achetés"""
        # Création du QGroupBox pour les articles achetés
        articles_group = QGroupBox("Articles achetés")
        articles_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 16px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 10px;
                padding: 0 5px;
            }
        """)

        # Layout pour le contenu du QGroupBox
        articles_layout = QVBoxLayout(articles_group)
        articles_layout.setSpacing(15)

        # Tableau des articles (QTableWidget)
        self.articles_table = QTableWidget(0, 5)
        self.articles_table.setHorizontalHeaderLabels([
            "Désignation du produit", "Unité", "Quantité", "Prix unitaire d'achat", "Total ligne"
        ])

        # Style du tableau
        self.articles_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background-color: white;
                gridline-color: #e9ecef;
                selection-background-color: #e3f2fd;
                selection-color: #212529;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #e9ecef;
            }
        """)

        # Configuration des colonnes
        self.articles_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)  # Désignation
        self.articles_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Unité
        self.articles_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Quantité
        self.articles_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Prix unitaire
        self.articles_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Total

        self.articles_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.articles_table.setSelectionMode(QTableWidget.SingleSelection)
        self.articles_table.verticalHeader().setVisible(False)  # Cacher les numéros de ligne

        # Ajouter un menu contextuel pour supprimer des articles
        self.articles_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.articles_table.customContextMenuRequested.connect(self.afficher_menu_contextuel)

        articles_layout.addWidget(self.articles_table)

        # Bouton Ajouter un article
        self.ajouter_article_btn = QPushButton("Ajouter un article")
        self.ajouter_article_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        self.ajouter_article_btn.clicked.connect(self.ajouter_article_dialog)
        articles_layout.addWidget(self.ajouter_article_btn)

        # Ajouter le QGroupBox au layout parent
        parent_layout.addWidget(articles_group)

        # Total de la facture (sera affiché dans la section des boutons d'action)
        self.total_facture_label = QLabel("Total TTC: 0.00 DH")
        self.total_facture_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
        """)

    def setup_action_buttons(self, parent_layout):
        """Configure les boutons d'action en bas de l'interface"""
        # Conteneur pour les boutons (QHBoxLayout)
        buttons_container = QFrame()
        buttons_layout = QHBoxLayout(buttons_container)
        buttons_layout.setSpacing(15)

        # Bouton Enregistrer la facture
        self.enregistrer_facture_btn = QPushButton("Enregistrer la facture")
        self.enregistrer_facture_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #0069d9;
            }
            QPushButton:pressed {
                background-color: #0062cc;
            }
        """)
        self.enregistrer_facture_btn.clicked.connect(self.enregistrer_facture_achat)
        buttons_layout.addWidget(self.enregistrer_facture_btn)

        # Bouton Effacer
        self.effacer_btn = QPushButton("Effacer")
        self.effacer_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:pressed {
                background-color: #545b62;
            }
        """)
        self.effacer_btn.clicked.connect(self.effacer_formulaire)
        buttons_layout.addWidget(self.effacer_btn)

        # Bouton Exporter vers Excel
        self.exporter_btn = QPushButton("📊 Exporter vers Excel")
        self.exporter_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        self.exporter_btn.clicked.connect(self.exporter_vers_excel)
        buttons_layout.addWidget(self.exporter_btn)

        # Spacer pour pousser les boutons à gauche
        buttons_layout.addStretch()

        # Ajouter le conteneur des boutons au layout parent
        parent_layout.addWidget(buttons_container)

        # Conteneur pour le total
        total_container = QFrame()
        total_layout = QHBoxLayout(total_container)
        total_layout.setAlignment(Qt.AlignRight)

        # Total TTC à droite
        total_layout.addWidget(self.total_facture_label)

        # Ajouter le conteneur du total au layout parent
        parent_layout.addWidget(total_container)

    def initialiser_donnees(self):
        """Initialise les données du module"""
        # Charger les fournisseurs
        self.charger_fournisseurs()

        # Générer un numéro de facture
        self.numero_facture_input.setText(self.generer_numero_facture())

    def charger_fournisseurs(self):
        """Charge la liste des fournisseurs dans le QComboBox"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT id, nom FROM fournisseurs ORDER BY nom")
        fournisseurs = cursor.fetchall()

        self.fournisseur_combo.clear()
        self.fournisseur_combo.addItem("-- Sélectionner un fournisseur --", None)

        for fournisseur in fournisseurs:
            self.fournisseur_combo.addItem(fournisseur['nom'], fournisseur['id'])

    def generer_numero_facture(self):
        """Génère un numéro de facture d'achat unique"""
        date_str = datetime.now().strftime("%Y%m")
        cursor = self.db_manager.conn.cursor()

        # Vérifier si la table factures_achat existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='factures_achat'")
        if not cursor.fetchone():
            return f"FAC-A-{date_str}-001"

        cursor.execute("SELECT COUNT(*) FROM factures_achat WHERE numero LIKE ?", (f"FAC-A-{date_str}%",))
        count = cursor.fetchone()[0] + 1
        return f"FAC-A-{date_str}-{count:03d}"

    def ajouter_article_dialog(self):
        """Ouvre une boîte de dialogue pour ajouter un article"""
        dialog = AjouterArticleDialog(self.db_manager)
        if dialog.exec():
            # Récupérer les données du produit
            produit_id = dialog.produit_id
            designation = dialog.designation
            unite = dialog.unite
            quantite = dialog.quantite
            prix_unitaire = dialog.prix_unitaire
            total = quantite * prix_unitaire

            # Ajouter une ligne dans le tableau
            row = self.articles_table.rowCount()
            self.articles_table.insertRow(row)

            # Créer les items du tableau
            article_item = QTableWidgetItem(designation)
            article_item.setData(Qt.UserRole, produit_id)  # Stocker l'ID du produit

            unite_item = QTableWidgetItem(unite)

            # Créer un QSpinBox pour la quantité
            quantite_spin = QSpinBox()
            quantite_spin.setRange(1, 1000000)
            quantite_spin.setValue(quantite)
            quantite_spin.setStyleSheet("border: none; background-color: transparent;")
            quantite_spin.valueChanged.connect(lambda _: self.update_ligne_total(row))

            # Créer un QDoubleSpinBox pour le prix unitaire
            prix_spin = QDoubleSpinBox()
            prix_spin.setRange(0.01, 1000000)
            prix_spin.setDecimals(2)
            prix_spin.setValue(prix_unitaire)
            prix_spin.setSuffix(" DH")
            prix_spin.setStyleSheet("border: none; background-color: transparent;")
            prix_spin.valueChanged.connect(lambda _: self.update_ligne_total(row))

            # Total ligne
            total_item = QTableWidgetItem(f"{total:.2f} DH")

            # Ajouter les items au tableau
            self.articles_table.setItem(row, 0, article_item)
            self.articles_table.setItem(row, 1, unite_item)
            self.articles_table.setCellWidget(row, 2, quantite_spin)
            self.articles_table.setCellWidget(row, 3, prix_spin)
            self.articles_table.setItem(row, 4, total_item)

            # Mettre à jour le total de la facture
            self.calculer_total_facture()

    def update_ligne_total(self, row):
        """Met à jour le total d'une ligne lorsque la quantité ou le prix change"""
        quantite_spin = self.articles_table.cellWidget(row, 2)
        prix_spin = self.articles_table.cellWidget(row, 3)

        if quantite_spin and prix_spin:
            quantite = quantite_spin.value()
            prix = prix_spin.value()
            total = quantite * prix

            self.articles_table.setItem(row, 4, QTableWidgetItem(f"{total:.2f} DH"))

            # Mettre à jour le total de la facture
            self.calculer_total_facture()

    def afficher_menu_contextuel(self, position):
        """Affiche un menu contextuel pour le tableau des articles"""
        menu = QMenu()
        supprimer_action = menu.addAction("Supprimer l'article")

        # Obtenir l'action sélectionnée
        action = menu.exec(self.articles_table.mapToGlobal(position))

        # Traiter l'action sélectionnée
        if action == supprimer_action:
            self.supprimer_article_selectionne()

    def supprimer_article_selectionne(self):
        """Supprime l'article sélectionné du tableau"""
        selected_rows = self.articles_table.selectedIndexes()
        if not selected_rows:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un article à supprimer.")
            return

        row = selected_rows[0].row()
        self.articles_table.removeRow(row)

        # Mettre à jour le total de la facture
        self.calculer_total_facture()

    def calculer_total_facture(self):
        """Calcule le total de la facture d'achat"""
        total = 0
        for row in range(self.articles_table.rowCount()):
            total_text = self.articles_table.item(row, 4).text().replace(" DH", "")
            total += float(total_text)

        self.total_facture_label.setText(f"Total TTC: {total:.2f} DH")

    def effacer_formulaire(self):
        """Réinitialise tous les champs du formulaire"""
        # Réinitialiser les champs de la facture
        self.fournisseur_combo.setCurrentIndex(0)
        self.numero_facture_input.setText(self.generer_numero_facture())
        self.date_facture.setDate(QDate.currentDate())
        self.mode_paiement_combo.setCurrentIndex(0)

        # Vider le tableau des articles
        self.articles_table.setRowCount(0)

        # Réinitialiser le total
        self.calculer_total_facture()

    def exporter_vers_excel(self):
        """Exporter les factures d'achat vers Excel"""
        try:
            exporter = ExcelExporter(self.db_manager)
            success = exporter.export_factures_achat(self)

            if success:
                QMessageBox.information(self, "Succès", "Les données ont été exportées avec succès vers Excel!")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'exportation: {str(e)}")

    def enregistrer_facture_achat(self):
        """Enregistre la facture d'achat et met à jour le stock des produits"""
        # Vérifier qu'il y a au moins une ligne
        if self.articles_table.rowCount() == 0:
            QMessageBox.warning(self, "Erreur", "Ajoutez au moins un article à la facture.")
            return

        # Vérifier qu'un fournisseur est sélectionné
        fournisseur_id = self.fournisseur_combo.currentData()
        if not fournisseur_id:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un fournisseur.")
            return

        # Vérifier que le numéro de facture est renseigné
        numero = self.numero_facture_input.text().strip()
        if not numero:
            QMessageBox.warning(self, "Erreur", "Veuillez renseigner le numéro de facture d'achat.")
            return

        # Récupérer les données de la facture
        date_creation = self.date_facture.date().toString("yyyy-MM-dd")
        mode_paiement = self.mode_paiement_combo.currentText()

        # Calculer le total
        total_ht = 0
        for row in range(self.articles_table.rowCount()):
            total_text = self.articles_table.item(row, 4).text().replace(" DH", "")
            total_ht += float(total_text)

        # Enregistrer la facture d'achat
        cursor = self.db_manager.conn.cursor()
        try:
            # Vérifier si la table factures_achat existe, sinon la créer
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS factures_achat (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    numero TEXT NOT NULL,
                    date_creation TEXT NOT NULL,
                    fournisseur_id INTEGER,
                    mode_paiement TEXT NOT NULL,
                    total_ht REAL NOT NULL,
                    notes TEXT,
                    FOREIGN KEY (fournisseur_id) REFERENCES fournisseurs (id)
                )
            """)

            # Vérifier si la table lignes_facture_achat existe, sinon la créer
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS lignes_facture_achat (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    facture_achat_id INTEGER NOT NULL,
                    produit_id INTEGER,
                    designation TEXT NOT NULL,
                    unite TEXT,
                    quantite INTEGER NOT NULL,
                    prix_unitaire_ht REAL NOT NULL,
                    total_ht REAL NOT NULL,
                    FOREIGN KEY (facture_achat_id) REFERENCES factures_achat (id),
                    FOREIGN KEY (produit_id) REFERENCES produits (id)
                )
            """)

            # Insérer la facture
            cursor.execute(
                """INSERT INTO factures_achat
                   (numero, date_creation, fournisseur_id, mode_paiement, total_ht)
                   VALUES (?, ?, ?, ?, ?)""",
                (numero, date_creation, fournisseur_id, mode_paiement, total_ht)
            )

            facture_id = cursor.lastrowid

            # Insérer les lignes de facture
            for row in range(self.articles_table.rowCount()):
                produit_id = self.articles_table.item(row, 0).data(Qt.UserRole)
                designation = self.articles_table.item(row, 0).text()
                unite = self.articles_table.item(row, 1).text()

                quantite_spin = self.articles_table.cellWidget(row, 2)
                prix_spin = self.articles_table.cellWidget(row, 3)

                quantite = quantite_spin.value()
                prix_unitaire = prix_spin.value()
                total_ligne = quantite * prix_unitaire

                cursor.execute(
                    """INSERT INTO lignes_facture_achat
                       (facture_achat_id, produit_id, designation, unite, quantite, prix_unitaire_ht, total_ht)
                       VALUES (?, ?, ?, ?, ?, ?, ?)""",
                    (facture_id, produit_id, designation, unite, quantite, prix_unitaire, total_ligne)
                )

                # Mettre à jour le stock et le prix d'achat du produit
                cursor.execute(
                    """UPDATE produits SET
                       stock = stock + ?,
                       prix_achat = ?
                       WHERE id = ?""",
                    (quantite, prix_unitaire, produit_id)
                )

            self.db_manager.conn.commit()

            # Intégrer avec la caisse si paiement en espèces
            if mode_paiement == "Espèces":
                self.integrate_with_caisse(facture_id, numero, total_ht)

            QMessageBox.information(self, "Succès", f"Facture d'achat n°{numero} enregistrée avec succès.\nTotal: {total_ht:.2f} DH")

            # Réinitialiser le formulaire
            self.effacer_formulaire()

        except sqlite3.Error as e:
            self.db_manager.conn.rollback()
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement de la facture: {str(e)}")

    def integrate_with_caisse(self, facture_id, numero, montant):
        """Intègre automatiquement avec la caisse pour les paiements en espèces"""
        try:
            # Ajouter une sortie dans la caisse
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                INSERT INTO sorties_caisse (date, nature, objet, reference, montant)
                VALUES (?, ?, ?, ?, ?)
            """, (
                datetime.now().strftime("%Y-%m-%d"),
                "شراء",
                f"شراء بالنقد - فاتورة {numero}",
                numero,
                montant
            ))

            self.db_manager.conn.commit()
            print(f"💸 Sortie de caisse automatique ajoutée: {montant} DH pour la facture d'achat {numero}")

        except Exception as e:
            print(f"❌ Erreur lors de l'intégration avec la caisse: {str(e)}")


class AjouterArticleDialog(QDialog):
    """Boîte de dialogue pour ajouter un article à une facture d'achat"""

    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.produit_id = None
        self.designation = ""
        self.unite = ""
        self.quantite = 1
        self.prix_unitaire = 0.0

        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur de la boîte de dialogue"""
        self.setWindowTitle("Ajouter un article")
        self.setMinimumWidth(500)
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title = QLabel("Ajouter un article")
        title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)

        # Formulaire
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)

        # Produit
        self.produit_combo = QComboBox()
        self.produit_combo.setStyleSheet("""
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: white;
            min-height: 20px;
        """)
        self.charger_produits()
        self.produit_combo.currentIndexChanged.connect(self.produit_selectionne)
        form_layout.addRow("Article:", self.produit_combo)

        # Désignation
        self.designation_input = QLineEdit()
        self.designation_input.setReadOnly(True)
        self.designation_input.setStyleSheet("""
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: #e9ecef;
            color: #495057;
        """)
        form_layout.addRow("Désignation:", self.designation_input)

        # Unité
        self.unite_input = QLineEdit()
        self.unite_input.setReadOnly(True)
        self.unite_input.setStyleSheet("""
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: #e9ecef;
            color: #495057;
        """)
        form_layout.addRow("Unité:", self.unite_input)

        # Quantité
        self.quantite_input = QSpinBox()
        self.quantite_input.setRange(1, 1000000)
        self.quantite_input.setValue(1)
        self.quantite_input.setStyleSheet("""
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: white;
        """)
        self.quantite_input.valueChanged.connect(self.calculer_total)
        form_layout.addRow("Quantité:", self.quantite_input)

        # Prix unitaire
        self.prix_input = QDoubleSpinBox()
        self.prix_input.setRange(0.01, 1000000)
        self.prix_input.setDecimals(2)
        self.prix_input.setSuffix(" DH")
        self.prix_input.setStyleSheet("""
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: white;
        """)
        self.prix_input.valueChanged.connect(self.calculer_total)
        form_layout.addRow("Prix d'achat unitaire:", self.prix_input)

        # Total
        self.total_label = QLabel("0.00 DH")
        self.total_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            padding: 8px;
        """)
        form_layout.addRow("Total:", self.total_label)

        layout.addLayout(form_layout)

        # Boutons
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        self.ajouter_btn = QPushButton("Ajouter")
        self.ajouter_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        self.ajouter_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(self.ajouter_btn)

        self.annuler_btn = QPushButton("Annuler")
        self.annuler_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:pressed {
                background-color: #545b62;
            }
        """)
        self.annuler_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.annuler_btn)

        layout.addLayout(buttons_layout)

    def charger_produits(self):
        """Charge la liste des produits dans le QComboBox"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT id, code, designation, unite, prix_achat FROM produits ORDER BY code")
        produits = cursor.fetchall()

        self.produit_combo.clear()
        self.produit_combo.addItem("-- Sélectionner un article --", None)

        for produit in produits:
            self.produit_combo.addItem(f"{produit['code']} - {produit['designation']}", produit['id'])

    def produit_selectionne(self):
        """Met à jour les champs lorsqu'un produit est sélectionné"""
        produit_id = self.produit_combo.currentData()

        if produit_id:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT * FROM produits WHERE id = ?", (produit_id,))
            produit = cursor.fetchone()

            if produit:
                self.produit_id = produit['id']
                self.designation = produit['designation']
                self.unite = produit['unite'] or ""

                self.designation_input.setText(produit['designation'])
                self.unite_input.setText(produit['unite'] or "")
                self.prix_input.setValue(produit['prix_achat'] or 0)

                self.calculer_total()
        else:
            self.produit_id = None
            self.designation_input.clear()
            self.unite_input.clear()
            self.prix_input.setValue(0)
            self.total_label.setText("0.00 DH")

    def calculer_total(self):
        """Calcule le total de la ligne"""
        self.quantite = self.quantite_input.value()
        self.prix_unitaire = self.prix_input.value()
        total = self.quantite * self.prix_unitaire

        self.total_label.setText(f"{total:.2f} DH")

    def accept(self):
        """Valide la saisie et ferme la boîte de dialogue"""
        if not self.produit_id:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un article.")
            return

        if self.prix_input.value() <= 0:
            QMessageBox.warning(self, "Erreur", "Le prix d'achat doit être supérieur à 0.")
            return

        super().accept()
