{"app_title": "Système de Comptabilité", "dashboard": "Tableau <PERSON>", "clients": "Clients", "suppliers": "Fournisseurs", "products": "Produits", "devis": "<PERSON><PERSON>", "invoices": "Factures", "purchase_orders": "Bons de Commande", "delivery_notes": "<PERSON><PERSON> de Livrai<PERSON>", "marche": "<PERSON><PERSON>", "stock": "Stock", "cash": "Caisse", "tva": "TVA", "reports": "Rapports", "search": "Recherche", "select_date_title": "Sélectionner une date", "weekday_0": "<PERSON>n", "weekday_1": "Mar", "weekday_2": "<PERSON><PERSON>", "weekday_3": "<PERSON><PERSON>", "weekday_4": "Ven", "weekday_5": "Sam", "weekday_6": "<PERSON><PERSON>", "month_1": "<PERSON><PERSON>", "month_2": "<PERSON><PERSON><PERSON><PERSON>", "month_3": "Mars", "month_4": "Avril", "month_5": "<PERSON>", "month_6": "Juin", "month_7": "<PERSON><PERSON><PERSON>", "month_8": "Août", "month_9": "Septembre", "month_10": "Octobre", "month_11": "Novembre", "month_12": "Décembre", "currency": "DH", "not_specified": "Non spécifié", "invalid_date": "Date invalide", "no_family": "Aucune famille", "product_code_change": "Code produit '{old_code}' mis à jour vers '{new_code}'", "renumbering_error": "Erreur lors de la renumérotation des produits: {error}", "client_code_change": "Code client '{old_code}' mis à jour vers '{new_code}'", "clients_renumbered_success": "✅ {count} client(s) renuméroté(s) avec succès", "client_renumbering_error": "Erreur lors de la renumérotation des clients: {error}", "supplier_code_change": "Code fournisseur '{old_code}' mis à jour vers '{new_code}'", "suppliers_renumbered_success": "✅ {count} fournisseur(s) renuméroté(s) avec succès", "supplier_renumbering_error": "Erreur lors de la renumérotation des fournisseurs: {error}", "column_added": "Colonne '{column_name}' ajou<PERSON>e avec succès.", "purchase_date": "Date d'achat", "purchase_invoice_number": "Numéro de facture d'achat", "tax_rate": "Taux de TVA", "payment_method": "Mode de paiement", "payment_date": "Date de paiement", "column_added_with_desc": "Colonne '{column_name}' ({description}) ajoutée avec succès.", "code_column_added_clients": "Colonne 'code' ajoutée à la table clients.", "codes_added_clients": "Codes ajoutés pour {count} client(s) existant(s).", "code_column_added_suppliers": "Colonne 'code' ajoutée à la table fournisseurs.", "codes_added_suppliers": "Codes ajoutés pour {count} fournisseur(s) existant(s).", "code_column_add_error": "Erreur lors de l'ajout de la colonne 'code': {error}", "database_ready": "Base de données prête et tables vérifiées.", "total_invoices": "Total Factures", "total_products": "Total Produits", "total_clients": "Total Clients", "total_suppliers": "Total Fournisseurs", "latest_invoices": "Dernières Factures", "manage_clients": "Gestion des Clients", "search_clients_placeholder": "Rechercher des clients...", "add": "Ajouter", "client_singular": "Client", "manage_suppliers": "Gestion des Fournisseurs", "search_suppliers_placeholder": "Rechercher des fournisseurs...", "add_supplier": "<PERSON><PERSON><PERSON>", "recreating_products_page": "Recréation de la page des produits...", "manage_products": "Gestion des Produits", "search_products_placeholder": "Rechercher des produits...", "add_product": "Ajouter Produit", "products_page_recreated": "Page des produits recréée.", "manage_invoices": "Gestion des Factures", "search_invoices_placeholder": "Rechercher des factures...", "new_invoice": "Nouvelle Facture", "manage_purchase_orders": "Gestion des Bons de Commande", "search_purchase_orders_placeholder": "Rechercher des bons de commande...", "new_purchase_order": "Nouveau Bon de Commande", "delivery_note_title": "<PERSON><PERSON> de Livrai<PERSON>", "search_delivery_notes_placeholder": "Rechercher des bons de livraison...", "new_delivery_note": "Nouveau Bon de Livraison", "manage_devis": "Gestion des Devis", "search_devis_placeholder": "Rechercher des devis...", "new_devis": "Nouveau Devis", "marche_management": "Gestion des Marchés", "search_marches_placeholder": "Rechercher des marchés...", "new_marche": "Nouveau Marché", "marche_general_info": "Informations Générales du Marché", "devis_numero_label": "N° DEVIS", "select_or_type": "<PERSON><PERSON><PERSON><PERSON><PERSON> ou taper", "numero_label": "N°", "manual": "<PERSON>", "nature_prestation": "Nature de prestation", "object_label": "Objet", "automatic": "Automatique", "execution_delay_label": "<PERSON><PERSON><PERSON> d'exé<PERSON>ion", "three_months": "3 mois", "montant_ttc_label": "Montant TTC", "provisional_caution_label": "Caution provisoire", "notification_date_label": "Date de notification d'approbation", "definitive_caution_label": "Caution définitive", "service_order_label": "Ordre de service", "retention_caution_label": "Caution retenue de garantie", "completion_date_label": "Date prévue d'achèvement", "auto": "Auto", "client": "Client", "select_date_placeholder": "Sélectionner une date 📅", "no_client": "Aucun client", "error_fetching_clients": "Erreur lors de la récupération des clients: {error}", "error": "<PERSON><PERSON><PERSON>", "marche_bordereau": "Bordereau de Prix", "marche_frais": "Frais Supplémentaires", "nouveau_marche": "Nouveau Marché", "save": "Enregistrer", "cancel": "Annuler", "invalid_date_error": "Date '{date_str}' invalide. Format attendu: JJ/MM/AAAA.", "customtkinter_available": "✅ CustomTkinter est disponible.", "customtkinter_not_found": "❌ CustomTkinter n'a pas été trouvé.", "install_customtkinter_prompt": "💡 Veuillez installer CustomTkinter en exécutant:"}