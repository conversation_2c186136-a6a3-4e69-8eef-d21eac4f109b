import sqlite3
import os

def create_bons_commande_tables(cursor):
    """Crée les tables pour les bons de commande si elles n'existent pas"""
    # Vérifier si les tables existent déjà
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='bons_commande'")
    bons_commande_exists = cursor.fetchone() is not None

    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='lignes_bon_commande'")
    lignes_bon_commande_exists = cursor.fetchone() is not None

    if not bons_commande_exists:
        print("Création de la table bons_commande...")
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS bons_commande (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            numero TEXT NOT NULL,
            date_creation TEXT NOT NULL,
            date_ordre TEXT,
            client_id INTEGER,
            ref_client TEXT,
            ordre_service TEXT,
            delai_execution TEXT,
            objet TEXT,
            montant_global REAL DEFAULT 0,
            total_ht REAL DEFAULT 0,
            total_tva REAL DEFAULT 0,
            total_ttc REAL DEFAULT 0,
            statut TEXT DEFAULT 'En cours',
            FOREIGN KEY (client_id) REFERENCES clients (id)
        )
        ''')

    if not lignes_bon_commande_exists:
        print("Création de la table lignes_bon_commande...")
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS lignes_bon_commande (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            bon_id INTEGER NOT NULL,
            produit_id INTEGER,
            designation TEXT NOT NULL,
            unite TEXT,
            quantite INTEGER NOT NULL,
            prix_unitaire_ht REAL NOT NULL,
            total_ht REAL NOT NULL,
            FOREIGN KEY (bon_id) REFERENCES bons_commande (id),
            FOREIGN KEY (produit_id) REFERENCES produits (id)
        )
        ''')

    if not bons_commande_exists or not lignes_bon_commande_exists:
        print("Tables pour les bons de commande créées avec succès.")
    else:
        print("Les tables pour les bons de commande existent déjà.")

def migrate_bons_commande(db_path="database/comptabilite.db"):
    """
    Migre la base de données pour ajouter les tables de bons de commande
    """
    if not os.path.exists(db_path):
        print(f"Base de données {db_path} introuvable. Aucune migration nécessaire.")
        return

    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Création des tables pour les bons de commande
    create_bons_commande_tables(cursor)

    conn.commit()
    conn.close()

if __name__ == "__main__":
    migrate_bons_commande()
