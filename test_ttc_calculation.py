#!/usr/bin/env python3
"""
Test script to verify TTC calculation functionality in the products form
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from comptabilite_app.database.db_manager import DatabaseManager
from comptabilite_app.ui.forms.produit_form import ProduitForm

def test_ttc_calculation():
    """Test the TTC calculation functionality"""
    app = QApplication(sys.argv)
    
    # Initialize database manager
    db_manager = DatabaseManager()
    
    # Create and show the product form
    form = ProduitForm(db_manager)
    form.show()
    
    print("✅ Product form with TTC calculation opened successfully!")
    print("📋 Test the following:")
    print("   1. Enter a price in 'Prix d'achat (HT)' field")
    print("   2. Check if 'Prix d'achat (TTC)' is calculated automatically")
    print("   3. Enter a price in 'Prix de vente (HT)' field") 
    print("   4. Check if 'Prix de vente (TTC)' is calculated automatically")
    print("   5. Change the TVA rate and see if prices update")
    print("   6. Try saving a product and check if TTC values are saved")
    
    # Run the application
    sys.exit(app.exec())

if __name__ == "__main__":
    test_ttc_calculation()
