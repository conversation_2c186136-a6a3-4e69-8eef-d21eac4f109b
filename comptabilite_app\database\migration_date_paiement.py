#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Migration pour ajouter le champ 'date_paiement' aux tables factures et produits
"""

import sqlite3
import os

def add_date_paiement_to_tables(db_path):
    """Ajoute le champ date_paiement aux tables nécessaires"""

    # Chemin complet vers la base de données
    if not os.path.isabs(db_path):
        db_path = os.path.join(os.path.dirname(__file__), db_path)

    print(f"Migration: Ajout du champ date_paiement")
    print(f"Base de données: {db_path}")

    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # 1. Ajouter date_paiement à la table factures_vente
        print("Vérification de la table factures_vente...")
        cursor.execute("PRAGMA table_info(factures_vente)")
        columns = cursor.fetchall()
        column_names = [column['name'] for column in columns]

        if 'date_paiement' not in column_names:
            print("Ajout du champ date_paiement à la table factures_vente...")
            cursor.execute("ALTER TABLE factures_vente ADD COLUMN date_paiement TEXT")
            print("✅ Champ date_paiement ajouté à factures_vente")
        else:
            print("✅ Champ date_paiement existe déjà dans factures_vente")

        # 2. Ajouter date_paiement à la table factures (si elle existe)
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='factures'")
        if cursor.fetchone():
            print("Vérification de la table factures...")
            cursor.execute("PRAGMA table_info(factures)")
            columns = cursor.fetchall()
            column_names = [column['name'] for column in columns]

            if 'date_paiement' not in column_names:
                print("Ajout du champ date_paiement à la table factures...")
                cursor.execute("ALTER TABLE factures ADD COLUMN date_paiement TEXT")
                print("✅ Champ date_paiement ajouté à factures")
            else:
                print("✅ Champ date_paiement existe déjà dans factures")

        # 3. Ajouter date_paiement à la table produits
        print("Vérification de la table produits...")
        cursor.execute("PRAGMA table_info(produits)")
        columns = cursor.fetchall()
        column_names = [column['name'] for column in columns]

        if 'date_paiement' not in column_names:
            print("Ajout du champ date_paiement à la table produits...")
            cursor.execute("ALTER TABLE produits ADD COLUMN date_paiement TEXT")
            print("✅ Champ date_paiement ajouté à produits")
        else:
            print("✅ Champ date_paiement existe déjà dans produits")

        # 4. Ajouter date_paiement à la table factures_achat
        print("Vérification de la table factures_achat...")
        cursor.execute("PRAGMA table_info(factures_achat)")
        columns = cursor.fetchall()
        column_names = [column['name'] for column in columns]

        if 'date_paiement' not in column_names:
            print("Ajout du champ date_paiement à la table factures_achat...")
            cursor.execute("ALTER TABLE factures_achat ADD COLUMN date_paiement TEXT")
            print("✅ Champ date_paiement ajouté à factures_achat")
        else:
            print("✅ Champ date_paiement existe déjà dans factures_achat")

        # 5. Ajouter date_paiement à la table bons_commande
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='bons_commande'")
        if cursor.fetchone():
            print("Vérification de la table bons_commande...")
            cursor.execute("PRAGMA table_info(bons_commande)")
            columns = cursor.fetchall()
            column_names = [column['name'] for column in columns]

            if 'date_paiement' not in column_names:
                print("Ajout du champ date_paiement à la table bons_commande...")
                cursor.execute("ALTER TABLE bons_commande ADD COLUMN date_paiement TEXT")
                print("✅ Champ date_paiement ajouté à bons_commande")
            else:
                print("✅ Champ date_paiement existe déjà dans bons_commande")

        conn.commit()
        print("\n🎉 Migration terminée avec succès!")
        print("Le champ 'date_paiement' a été ajouté à toutes les tables nécessaires.")

    except sqlite3.Error as e:
        conn.rollback()
        print(f"❌ Erreur lors de la migration: {str(e)}")
        raise

    finally:
        if conn:
            conn.close()

def run_migration():
    """Exécute la migration sur la base de données principale"""
    # Chemin vers la base de données principale
    db_path = "comptabilite.db"
    add_date_paiement_to_tables(db_path)

if __name__ == "__main__":
    print("🔄 Démarrage de la migration date_paiement")
    print("=" * 50)

    try:
        run_migration()
        print("\n✅ Migration réussie!")
    except Exception as e:
        print(f"\n❌ Échec de la migration: {str(e)}")
        exit(1)
