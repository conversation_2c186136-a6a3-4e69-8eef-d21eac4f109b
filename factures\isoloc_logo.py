from PIL import Image, ImageDraw, ImageFont
import os

def create_isoloc_logo(output_path="isoloc_logo.png", size=(300, 300)):
    """
    Crée un logo ISOLOC similaire à celui de l'image de référence.
    
    Args:
        output_path (str): <PERSON>em<PERSON> où enregistrer le logo
        size (tuple): <PERSON>lle du logo (largeur, hauteur)
    """
    # Créer une image blanche
    img = Image.new('RGB', size, color=(255, 255, 255))
    draw = ImageDraw.Draw(img)
    
    # Dessiner un cercle noir
    margin = 20
    circle_bbox = (margin, margin, size[0] - margin, size[1] - margin)
    draw.ellipse(circle_bbox, outline=(0, 0, 0), width=3)
    
    # Ajouter le texte "ISOLOC"
    try:
        # Essayer de charger une police Arial ou similaire
        try:
            font_large = ImageFont.truetype("arial.ttf", 60)
            font_small = ImageFont.truetype("arial.ttf", 30)
        except:
            # Fallback sur une police par défaut
            font_large = ImageFont.load_default()
            font_small = font_large
        
        # Centrer le texte "ISOLOC"
        text = "ISOLOC"
        text_width = draw.textlength(text, font=font_large)
        text_position = ((size[0] - text_width) // 2, size[1] // 2 - 40)
        draw.text(text_position, text, fill=(0, 0, 0), font=font_large)
        
        # Ajouter "SERVICE" en dessous
        text = "SERVICE"
        text_width = draw.textlength(text, font=font_small)
        text_position = ((size[0] - text_width) // 2, size[1] // 2 + 20)
        draw.text(text_position, text, fill=(0, 0, 0), font=font_small)
        
    except Exception as e:
        print(f"Erreur lors de l'ajout du texte: {str(e)}")
    
    # Enregistrer l'image
    img.save(output_path)
    print(f"Logo ISOLOC créé avec succès: {output_path}")
    
    return output_path

if __name__ == "__main__":
    # Créer le dossier factures s'il n'existe pas
    if not os.path.exists("factures"):
        os.makedirs("factures")
    
    # Créer le logo
    create_isoloc_logo("factures/isoloc_logo.png")
