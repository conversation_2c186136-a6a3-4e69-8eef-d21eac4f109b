import sys
import os

# Ajouter le répertoire courant au path Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import QTranslator, QLocale
    using_pyside = True
except ImportError:
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QTranslator, QLocale
        using_pyside = False
    except ImportError:
        print("Error: Neither PySide6 nor PyQt6 is installed. Please install one of them.")
        sys.exit(1)
from ui.main_window_simple import MainWindowSimple as MainWindow
from ui.login_window import LoginWindow
from database.db_manager import DatabaseManager
from database.migration import migrate_database
from database.migration_bons_commande import migrate_bons_commande
from database.migration_produits import migrate_produits

def main():
    app = QApplication(sys.argv)

    # Configuration de la localisation française
    translator = QTranslator()
    translator.load(QLocale.French, "translations", ".", "")
    app.installTranslator(translator)

    # Migration de la base de données si nécessaire
    migrate_database()
    migrate_bons_commande()
    migrate_produits()

    # Initialisation de la base de données
    db_manager = DatabaseManager()
    db_manager.setup_database()

    # Création de la fenêtre principale (mais ne pas l'afficher tout de suite)
    main_window = MainWindow(db_manager)

    # Création et affichage de la fenêtre de connexion
    login_window = LoginWindow()

    # Connecter le signal de connexion réussie à l'affichage de la fenêtre principale
    def on_login_successful():
        main_window.show()

    login_window.login_successful.connect(on_login_successful)
    login_window.show()

    if using_pyside:
        sys.exit(app.exec())
    else:
        sys.exit(app.exec())

if __name__ == "__main__":
    main()