"""
Module de gestion de la caisse - Style simple comme les autres modules
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QTableWidget, QTableWidgetItem, QPushButton,
                               QHeaderView, QFrame, QLineEdit, QMessageBox,
                               QDialog, QDateEdit)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont
import sqlite3

class CaisseModule(QWidget):
    """Module de caisse simple et fonctionnel"""

    def __init__(self, db_manager, signals=None):
        super().__init__()
        self.db_manager = db_manager
        self.signals = signals
        print("🏦 Initialisation du module Caisse...")
        self.setup_ui()
        print("🎨 Interface utilisateur créée")
        self.load_data()
        print("📊 Données chargées")

    def setup_ui(self):
        """Configuration de l'interface utilisateur"""
        layout = QVBoxLayout()
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title = QLabel("💰 Gestion de la Caisse")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2563EB; margin-bottom: 20px;")
        layout.addWidget(title)

        # Section du solde
        solde_frame = self.create_solde_section()
        layout.addWidget(solde_frame)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        self.add_entree_btn = QPushButton("➕ Ajouter Entrée")
        self.add_entree_btn.setStyleSheet("""
            QPushButton {
                background-color: #059669;
                color: white;
                border: none;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #047857;
            }
        """)
        self.add_entree_btn.clicked.connect(self.add_entree)

        self.add_sortie_btn = QPushButton("➖ Ajouter Sortie")
        self.add_sortie_btn.setStyleSheet("""
            QPushButton {
                background-color: #DC2626;
                color: white;
                border: none;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #B91C1C;
            }
        """)
        self.add_sortie_btn.clicked.connect(self.add_sortie)

        self.refresh_btn = QPushButton("🔄 Actualiser")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
        """)
        self.refresh_btn.clicked.connect(self.load_data)

        buttons_layout.addWidget(self.add_entree_btn)
        buttons_layout.addWidget(self.add_sortie_btn)
        buttons_layout.addWidget(self.refresh_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # Tableau des opérations
        self.create_operations_table()
        layout.addWidget(self.operations_table)

        self.setLayout(layout)

    def create_solde_section(self):
        """Créer la section d'affichage du solde"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #F8FAFC;
                border: 2px solid #E5E7EB;
                border-radius: 10px;
                padding: 20px;
            }
        """)

        layout = QHBoxLayout()

        # Entrées
        entrees_layout = QVBoxLayout()
        entrees_title = QLabel("💰 Total Entrées")
        entrees_title.setFont(QFont("Arial", 12, QFont.Bold))
        entrees_title.setStyleSheet("color: #059669;")
        self.entrees_label = QLabel("0.00 DH")
        self.entrees_label.setFont(QFont("Arial", 16, QFont.Bold))
        self.entrees_label.setStyleSheet("color: #059669;")
        entrees_layout.addWidget(entrees_title)
        entrees_layout.addWidget(self.entrees_label)

        # Sorties
        sorties_layout = QVBoxLayout()
        sorties_title = QLabel("💸 Total Sorties")
        sorties_title.setFont(QFont("Arial", 12, QFont.Bold))
        sorties_title.setStyleSheet("color: #DC2626;")
        self.sorties_label = QLabel("0.00 DH")
        self.sorties_label.setFont(QFont("Arial", 16, QFont.Bold))
        self.sorties_label.setStyleSheet("color: #DC2626;")
        sorties_layout.addWidget(sorties_title)
        sorties_layout.addWidget(self.sorties_label)

        # Solde
        solde_layout = QVBoxLayout()
        solde_title = QLabel("💳 Solde Actuel")
        solde_title.setFont(QFont("Arial", 12, QFont.Bold))
        solde_title.setStyleSheet("color: #374151;")
        self.solde_label = QLabel("0.00 DH")
        self.solde_label.setFont(QFont("Arial", 20, QFont.Bold))
        self.solde_label.setStyleSheet("color: #374151;")
        solde_layout.addWidget(solde_title)
        solde_layout.addWidget(self.solde_label)

        layout.addLayout(entrees_layout)
        layout.addLayout(sorties_layout)
        layout.addLayout(solde_layout)
        layout.addStretch()

        frame.setLayout(layout)
        return frame

    def create_operations_table(self):
        """Créer le tableau des opérations"""
        self.operations_table = QTableWidget()
        self.operations_table.setColumnCount(6)
        self.operations_table.setHorizontalHeaderLabels([
            "Date", "Type", "Description", "Référence", "Montant", "Actions"
        ])

        # Style du tableau
        self.operations_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                background-color: white;
                gridline-color: #E5E7EB;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E5E7EB;
            }
            QTableWidget::item:selected {
                background-color: #EBF4FF;
            }
            QHeaderView::section {
                background-color: #F3F4F6;
                padding: 10px;
                border: none;
                font-weight: bold;
                color: #374151;
            }
        """)

        self.operations_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.operations_table.setAlternatingRowColors(True)
        self.operations_table.setSelectionBehavior(QTableWidget.SelectRows)

    def add_entree(self):
        """Ajouter une entrée"""
        self.show_operation_dialog("Entrée")

    def add_sortie(self):
        """Ajouter une sortie"""
        self.show_operation_dialog("Sortie")

    def show_operation_dialog(self, operation_type):
        """Afficher la boîte de dialogue pour ajouter une opération"""
        dialog = QDialog(self)
        dialog.setWindowTitle(f"Ajouter {operation_type}")
        dialog.setModal(True)
        dialog.resize(400, 300)

        layout = QVBoxLayout()

        # Date
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("Date:"))
        from ..style import create_styled_date_edit
        date_input = create_styled_date_edit()
        date_layout.addWidget(date_input)
        layout.addLayout(date_layout)

        # Description
        desc_layout = QHBoxLayout()
        desc_layout.addWidget(QLabel("Description:"))
        desc_input = QLineEdit()
        desc_input.setPlaceholderText("Description de l'opération")
        desc_layout.addWidget(desc_input)
        layout.addLayout(desc_layout)

        # Référence
        ref_layout = QHBoxLayout()
        ref_layout.addWidget(QLabel("Référence:"))
        ref_input = QLineEdit()
        ref_input.setPlaceholderText("Numéro de facture, bon, etc.")
        ref_layout.addWidget(ref_input)
        layout.addLayout(ref_layout)

        # Montant
        montant_layout = QHBoxLayout()
        montant_layout.addWidget(QLabel("Montant (DH):"))
        montant_input = QLineEdit()
        montant_input.setPlaceholderText("0.00")
        montant_layout.addWidget(montant_input)
        layout.addLayout(montant_layout)

        # Boutons
        buttons_layout = QHBoxLayout()
        save_btn = QPushButton("Enregistrer")
        cancel_btn = QPushButton("Annuler")

        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #059669;
                color: white;
                border: none;
                padding: 10px 20px;
                font-weight: bold;
                border-radius: 5px;
            }
        """)

        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                padding: 10px 20px;
                font-weight: bold;
                border-radius: 5px;
            }
        """)

        def save_operation():
            try:
                date_str = date_input.date().toString("yyyy-MM-dd")
                description = desc_input.text().strip()
                reference = ref_input.text().strip()
                montant_str = montant_input.text().strip().replace(',', '.')

                if not description:
                    QMessageBox.warning(dialog, "Erreur", "Veuillez saisir une description!")
                    return

                if not montant_str:
                    QMessageBox.warning(dialog, "Erreur", "Veuillez saisir un montant!")
                    return

                montant = float(montant_str)
                if montant <= 0:
                    QMessageBox.warning(dialog, "Erreur", "Le montant doit être supérieur à zéro!")
                    return

                # Enregistrer dans la base de données
                cursor = self.db_manager.conn.cursor()

                if operation_type == "Entrée":
                    cursor.execute("""
                        INSERT INTO entrees_caisse (date, nature, objet, reference, montant)
                        VALUES (?, ?, ?, ?, ?)
                    """, (date_str, "Entrée", description, reference, montant))
                else:
                    cursor.execute("""
                        INSERT INTO sorties_caisse (date, nature, objet, reference, montant)
                        VALUES (?, ?, ?, ?, ?)
                    """, (date_str, "Sortie", description, reference, montant))

                self.db_manager.conn.commit()
                self.load_data()

                QMessageBox.information(dialog, "Succès", f"{operation_type} de {montant:.2f} DH enregistrée!")
                dialog.accept()

            except ValueError:
                QMessageBox.warning(dialog, "Erreur", "Montant invalide!")
            except sqlite3.Error as e:
                QMessageBox.critical(dialog, "Erreur", f"Erreur de base de données: {str(e)}")

        save_btn.clicked.connect(save_operation)
        cancel_btn.clicked.connect(dialog.reject)

        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)

        dialog.setLayout(layout)
        dialog.exec()

    def load_data(self):
        """Charger les données"""
        self.load_operations()
        self.calculate_totals()

    def load_operations(self):
        """Charger les opérations dans le tableau"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Vérifier si les tables existent
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='entrees_caisse'")
            if not cursor.fetchone():
                print("Table entrees_caisse n'existe pas")
                return

            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='sorties_caisse'")
            if not cursor.fetchone():
                print("Table sorties_caisse n'existe pas")
                return

            # Récupérer toutes les opérations
            cursor.execute("""
                SELECT date, nature, objet, reference, montant, 'Entrée' as type, id
                FROM entrees_caisse
                UNION ALL
                SELECT date, nature, objet, reference, montant, 'Sortie' as type, id
                FROM sorties_caisse
                ORDER BY date DESC, id DESC
                LIMIT 50
            """)
            operations = cursor.fetchall()

            self.operations_table.setRowCount(len(operations))

            if len(operations) == 0:
                # Afficher un message si aucune opération
                self.operations_table.setRowCount(1)
                self.operations_table.setItem(0, 0, QTableWidgetItem(""))
                self.operations_table.setItem(0, 1, QTableWidgetItem(""))
                self.operations_table.setItem(0, 2, QTableWidgetItem("Aucune opération"))
                self.operations_table.setItem(0, 3, QTableWidgetItem(""))
                self.operations_table.setItem(0, 4, QTableWidgetItem(""))
                self.operations_table.setItem(0, 5, QTableWidgetItem(""))
                return

            for row, operation in enumerate(operations):
                # Date
                self.operations_table.setItem(row, 0, QTableWidgetItem(str(operation[0])))

                # Type
                type_item = QTableWidgetItem(str(operation[5]))
                if operation[5] == "Entrée":
                    type_item.setBackground(Qt.green)
                else:
                    type_item.setBackground(Qt.red)
                self.operations_table.setItem(row, 1, type_item)

                # Description
                self.operations_table.setItem(row, 2, QTableWidgetItem(str(operation[2] or "")))

                # Référence
                self.operations_table.setItem(row, 3, QTableWidgetItem(str(operation[3] or "")))

                # Montant
                montant_item = QTableWidgetItem(f"{float(operation[4]):.2f} DH")
                if operation[5] == "Entrée":
                    montant_item.setForeground(Qt.darkGreen)
                else:
                    montant_item.setForeground(Qt.darkRed)
                self.operations_table.setItem(row, 4, montant_item)

                # Actions
                delete_btn = QPushButton("🗑️")
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #DC2626;
                        color: white;
                        border: none;
                        padding: 5px;
                        border-radius: 3px;
                    }
                """)
                delete_btn.clicked.connect(lambda checked=False, op_id=operation[6], op_type=operation[5]: self.delete_operation(op_id, op_type))
                self.operations_table.setCellWidget(row, 5, delete_btn)

        except sqlite3.Error as e:
            print(f"Erreur lors du chargement des opérations: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement: {str(e)}")

    def calculate_totals(self):
        """Calculer et afficher les totaux"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Vérifier si les tables existent
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='entrees_caisse'")
            if not cursor.fetchone():
                print("⚠️ Table entrees_caisse n'existe pas")
                self.entrees_label.setText("0.00 DH")
                total_entrees = 0
            else:
                # Total des entrées
                cursor.execute("SELECT COALESCE(SUM(montant), 0) FROM entrees_caisse")
                total_entrees = cursor.fetchone()[0]
                self.entrees_label.setText(f"{total_entrees:.2f} DH")

            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='sorties_caisse'")
            if not cursor.fetchone():
                print("⚠️ Table sorties_caisse n'existe pas")
                self.sorties_label.setText("0.00 DH")
                total_sorties = 0
            else:
                # Total des sorties
                cursor.execute("SELECT COALESCE(SUM(montant), 0) FROM sorties_caisse")
                total_sorties = cursor.fetchone()[0]
                self.sorties_label.setText(f"{total_sorties:.2f} DH")

            # Calculer le solde
            solde = total_entrees - total_sorties

            if solde >= 0:
                self.solde_label.setText(f"+{solde:.2f} DH")
                self.solde_label.setStyleSheet("color: #059669; font-size: 20px; font-weight: bold;")
            else:
                self.solde_label.setText(f"{solde:.2f} DH")
                self.solde_label.setStyleSheet("color: #DC2626; font-size: 20px; font-weight: bold;")

            print(f"💰 Totaux calculés: Entrées={total_entrees}, Sorties={total_sorties}, Solde={solde}")

        except sqlite3.Error as e:
            print(f"❌ Erreur lors du calcul des totaux: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors du calcul: {str(e)}")

    def delete_operation(self, operation_id, operation_type):
        """Supprimer une opération"""
        reply = QMessageBox.question(self, "Confirmation",
                                   f"Êtes-vous sûr de vouloir supprimer cette {operation_type.lower()}?",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                cursor = self.db_manager.conn.cursor()

                if operation_type == "Entrée":
                    cursor.execute("DELETE FROM entrees_caisse WHERE id = ?", (operation_id,))
                else:
                    cursor.execute("DELETE FROM sorties_caisse WHERE id = ?", (operation_id,))

                self.db_manager.conn.commit()
                self.load_data()

                QMessageBox.information(self, "Succès", f"{operation_type} supprimée!")

            except sqlite3.Error as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression: {str(e)}")
