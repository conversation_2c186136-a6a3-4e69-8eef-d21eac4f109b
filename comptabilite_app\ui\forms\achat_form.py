from PySide6.QtWidgets import (QVBoxLayout, QHBoxLayout, QLabel,
                               QLineEdit, QComboBox, QDateEdit, QPushButton,
                               QFrame, QSpinBox, QDoubleSpinBox, QMessageBox,
                               QDialog, QFormLayout, QGroupBox)
from PySide6.QtCore import Qt, QDate, Signal

class ArticleWidget(QFrame):
    """Widget pour un article dans le formulaire d'achat"""

    article_deleted = Signal(object)  # Signal émis lorsqu'un article est supprimé
    article_changed = Signal()  # Signal émis lorsqu'un article est modifié

    def __init__(self, article_id=1, parent=None):
        super().__init__(parent)
        self.article_id = article_id
        self.setup_ui()

    def setup_ui(self):
        self.setFrameShape(QFrame.StyledPanel)
        self.setFrameShadow(QFrame.Raised)
        self.setStyleSheet("""
            QFrame {
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 10px;
                margin: 5px 0;
            }
            QLabel {
                font-weight: bold;
            }
            QLineEdit, QSpinBox, QDoubleSpinBox {
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 3px;
            }
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)

        # Layout principal
        layout = QVBoxLayout(self)

        # En-tête avec ID et bouton de suppression
        header_layout = QHBoxLayout()

        article_label = QLabel(f"Article #{self.article_id}")
        header_layout.addWidget(article_label)

        delete_button = QPushButton("Supprimer")
        delete_button.clicked.connect(self.delete_article)
        header_layout.addWidget(delete_button)

        layout.addLayout(header_layout)

        # Formulaire pour les détails de l'article
        form_layout = QFormLayout()

        # Désignation
        self.designation_input = QLineEdit()
        self.designation_input.setMinimumHeight(30)
        form_layout.addRow("Désignation :", self.designation_input)

        # Unité
        self.unite_input = QLineEdit()
        self.unite_input.setMinimumHeight(30)
        form_layout.addRow("Unité :", self.unite_input)

        # Quantité
        self.quantite_input = QSpinBox()
        self.quantite_input.setMinimum(1)
        self.quantite_input.setMaximum(9999)
        self.quantite_input.setValue(1)
        self.quantite_input.setMinimumHeight(30)
        self.quantite_input.valueChanged.connect(self.calculate_totals)
        form_layout.addRow("Quantité :", self.quantite_input)

        # Prix HT
        self.prix_ht_input = QDoubleSpinBox()
        self.prix_ht_input.setMinimum(0)
        self.prix_ht_input.setMaximum(999999.99)
        self.prix_ht_input.setDecimals(2)
        self.prix_ht_input.setSuffix(" DH")
        self.prix_ht_input.setMinimumHeight(30)
        self.prix_ht_input.setStyleSheet("""
            QDoubleSpinBox {
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: white;
            }
        """)
        self.prix_ht_input.valueChanged.connect(self.calculate_totals)
        form_layout.addRow("PU HT :", self.prix_ht_input)

        # Prix Vente
        self.prix_vente_input = QDoubleSpinBox()
        self.prix_vente_input.setMinimum(0)
        self.prix_vente_input.setMaximum(999999.99)
        self.prix_vente_input.setDecimals(2)
        self.prix_vente_input.setSuffix(" DH")
        self.prix_vente_input.setMinimumHeight(30)
        self.prix_vente_input.setStyleSheet("""
            QDoubleSpinBox {
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: white;
            }
        """)
        form_layout.addRow("Prix Vente :", self.prix_vente_input)

        layout.addLayout(form_layout)

        # Connecter les signaux pour détecter les changements
        self.designation_input.textChanged.connect(self.on_article_changed)
        self.unite_input.textChanged.connect(self.on_article_changed)
        self.prix_vente_input.valueChanged.connect(self.on_article_changed)

    def delete_article(self):
        """Supprimer cet article"""
        self.article_deleted.emit(self)

    def calculate_totals(self):
        """Calculer les totaux et émettre le signal de changement"""
        self.on_article_changed()

    def on_article_changed(self):
        """Émettre le signal de changement d'article"""
        self.article_changed.emit()

    def get_data(self):
        """Récupérer les données de l'article"""
        return {
            'id': self.article_id,
            'designation': self.designation_input.text(),
            'unite': self.unite_input.text(),
            'quantite': self.quantite_input.value(),
            'prix_ht': self.prix_ht_input.value(),
            'prix_vente': self.prix_vente_input.value(),
            'total_ht': self.prix_ht_input.value() * self.quantite_input.value()
        }

class AchatForm(QDialog):
    """Formulaire d'achat de produits"""

    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.articles = []  # Liste des widgets d'articles
        self.setup_ui()
        self.load_fournisseurs()

    def setup_ui(self):
        self.setWindowTitle("Formulaire d'achat")
        self.resize(800, 600)
        self.setStyleSheet("""
            QDialog {
                background-color: white;
            }
            QLabel {
                font-size: 12px;
            }
            QLineEdit, QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                min-height: 20px;
                min-width: 200px;
            }
            QPushButton {
                background-color: #1E3A8A;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
            QPushButton#add_article_btn {
                background-color: #10B981;
            }
            QPushButton#add_article_btn:hover {
                background-color: #059669;
            }
            QPushButton#cancel_btn {
                background-color: #6B7280;
            }
            QPushButton#cancel_btn:hover {
                background-color: #4B5563;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #ddd;
                border-radius: 4px;
                margin-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)

        # Layout principal
        main_layout = QVBoxLayout(self)

        # Informations de l'achat
        info_layout = QHBoxLayout()

        # Fournisseur
        fournisseur_layout = QVBoxLayout()
        fournisseur_label = QLabel("Fournisseur :")
        self.fournisseur_combo = QComboBox()
        self.fournisseur_combo.setPlaceholderText("Sélectionner un fournisseur")
        self.fournisseur_combo.setMinimumWidth(250)
        self.fournisseur_combo.setMinimumHeight(35)
        self.fournisseur_combo.setStyleSheet("""
            QComboBox {
                padding: 5px 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #ddd;
            }
        """)
        fournisseur_layout.addWidget(fournisseur_label)
        fournisseur_layout.addWidget(self.fournisseur_combo)
        info_layout.addLayout(fournisseur_layout)

        # Numéro de facture
        facture_layout = QVBoxLayout()
        facture_label = QLabel("Numéro de facture d'achat :")
        self.facture_input = QLineEdit()
        self.facture_input.setMinimumWidth(250)
        self.facture_input.setMinimumHeight(35)
        self.facture_input.setStyleSheet("""
            QLineEdit {
                padding: 5px 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
        """)
        facture_layout.addWidget(facture_label)
        facture_layout.addWidget(self.facture_input)
        info_layout.addLayout(facture_layout)

        main_layout.addLayout(info_layout)

        # Date et mode de paiement
        date_payment_layout = QHBoxLayout()

        # Date
        date_layout = QVBoxLayout()
        date_label = QLabel("Date :")
        from ..style import create_styled_date_edit
        self.date_input = create_styled_date_edit()
        self.date_input.setMinimumWidth(250)
        self.date_input.setMinimumHeight(35)
        date_layout.addWidget(date_label)
        date_layout.addWidget(self.date_input)
        date_payment_layout.addLayout(date_layout)

        # Mode de paiement
        payment_layout = QVBoxLayout()
        payment_label = QLabel("Mode de paiement :")
        self.payment_combo = QComboBox()
        self.payment_combo.addItem("Sélectionner", None)
        self.payment_combo.addItems(["Espèces", "Chèque", "Virement", "Carte bancaire"])
        self.payment_combo.setCurrentIndex(0)
        self.payment_combo.setMinimumWidth(250)
        self.payment_combo.setMinimumHeight(35)
        self.payment_combo.setStyleSheet("""
            QComboBox {
                padding: 5px 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #ddd;
            }
        """)
        payment_layout.addWidget(payment_label)
        payment_layout.addWidget(self.payment_combo)
        date_payment_layout.addLayout(payment_layout)

        main_layout.addLayout(date_payment_layout)

        # Section Articles
        articles_group = QGroupBox("Articles")
        articles_layout = QVBoxLayout(articles_group)

        # Conteneur pour les articles
        self.articles_container = QVBoxLayout()
        articles_layout.addLayout(self.articles_container)

        # Ajouter un premier article par défaut
        self.add_article()

        # Bouton pour ajouter un article
        add_article_btn = QPushButton("+ Ajouter un article")
        add_article_btn.setObjectName("add_article_btn")
        add_article_btn.clicked.connect(self.add_article)
        articles_layout.addWidget(add_article_btn)

        main_layout.addWidget(articles_group)

        # Totaux
        totals_layout = QHBoxLayout()

        # Total HT
        total_ht_layout = QHBoxLayout()
        total_ht_label = QLabel("Total Facture d'achat HT :")
        self.total_ht_value = QLabel("0.00 DH")
        self.total_ht_value.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        total_ht_layout.addWidget(total_ht_label)
        total_ht_layout.addStretch()
        total_ht_layout.addWidget(self.total_ht_value)
        totals_layout.addLayout(total_ht_layout)

        main_layout.addLayout(totals_layout)

        # TVA
        tva_layout = QHBoxLayout()
        tva_label = QLabel("TVA 20% :")
        self.tva_value = QLabel("0.00 DH")
        self.tva_value.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        tva_layout.addWidget(tva_label)
        tva_layout.addStretch()
        tva_layout.addWidget(self.tva_value)
        main_layout.addLayout(tva_layout)

        # TTC
        ttc_layout = QHBoxLayout()
        ttc_label = QLabel("TTC :")
        self.ttc_value = QLabel("0.00 DH")
        self.ttc_value.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        ttc_layout.addWidget(ttc_label)
        ttc_layout.addStretch()
        ttc_layout.addWidget(self.ttc_value)
        main_layout.addLayout(ttc_layout)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("Enregistrer")
        save_btn.setMinimumHeight(40)
        save_btn.setMinimumWidth(150)
        save_btn.clicked.connect(self.save_achat)

        cancel_btn = QPushButton("Annuler")
        cancel_btn.setMinimumHeight(40)
        cancel_btn.setMinimumWidth(150)
        cancel_btn.setObjectName("cancel_btn")
        cancel_btn.clicked.connect(self.reject)

        buttons_layout.addStretch()
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addStretch()

        main_layout.addLayout(buttons_layout)

    def load_fournisseurs(self):
        """Charger la liste des fournisseurs depuis la base de données"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT id, nom FROM fournisseurs ORDER BY nom")
            fournisseurs = cursor.fetchall()

            self.fournisseur_combo.clear()
            self.fournisseur_combo.addItem("Sélectionner un fournisseur", None)
            for fournisseur_id, nom in fournisseurs:
                self.fournisseur_combo.addItem(nom, fournisseur_id)
        except Exception as e:
            print(f"Erreur lors du chargement des fournisseurs: {str(e)}")

    def add_article(self):
        """Ajouter un nouvel article au formulaire"""
        article_id = len(self.articles) + 1
        article_widget = ArticleWidget(article_id)
        article_widget.article_deleted.connect(self.remove_article)
        article_widget.article_changed.connect(self.update_totals)

        self.articles_container.addWidget(article_widget)
        self.articles.append(article_widget)

        self.update_totals()

    def remove_article(self, article_widget):
        """Supprimer un article du formulaire"""
        if len(self.articles) > 1:  # Garder au moins un article
            self.articles_container.removeWidget(article_widget)
            self.articles.remove(article_widget)
            article_widget.deleteLater()

            # Mettre à jour les IDs des articles restants
            for i, article in enumerate(self.articles, 1):
                article.article_id = i
                article.findChild(QLabel).setText(f"Article #{i}")

            self.update_totals()
        else:
            QMessageBox.warning(self, "Impossible de supprimer",
                               "Vous devez avoir au moins un article dans la facture.")

    def update_totals(self):
        """Mettre à jour les totaux de la facture"""
        total_ht = sum(article.get_data()['total_ht'] for article in self.articles)
        tva = total_ht * 0.2  # TVA à 20%
        ttc = total_ht + tva

        self.total_ht_value.setText(f"{total_ht:.2f} DH")
        self.tva_value.setText(f"{tva:.2f} DH")
        self.ttc_value.setText(f"{ttc:.2f} DH")

    def save_achat(self):
        """Enregistrer l'achat dans la base de données"""
        # Vérifier que tous les champs obligatoires sont remplis
        if not self.validate_form():
            return

        try:
            # Récupérer les données du formulaire
            fournisseur_id = self.fournisseur_combo.currentData()
            facture_numero = self.facture_input.text()
            date_achat = self.date_input.date().toString("yyyy-MM-dd")
            mode_paiement = self.payment_combo.currentText()

            # Récupérer les articles
            articles_data = [article.get_data() for article in self.articles]

            # Calculer les totaux
            total_ht = sum(article['total_ht'] for article in articles_data)
            tva = total_ht * 0.2
            ttc = total_ht + tva

            # Enregistrer la facture d'achat
            cursor = self.db_manager.conn.cursor()

            # Insérer la facture
            cursor.execute("""
                INSERT INTO factures_achat (fournisseur_id, numero, date_creation, mode_paiement, total_ht)
                VALUES (?, ?, ?, ?, ?)
            """, (fournisseur_id, facture_numero, date_achat, mode_paiement, total_ht))

            facture_id = cursor.lastrowid

            # Insérer les articles
            for article in articles_data:
                cursor.execute("""
                    INSERT INTO articles_achat (facture_id, designation, unite, quantite,
                                              prix_unitaire, prix_vente, total)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (facture_id, article['designation'], article['unite'],
                     article['quantite'], article['prix_ht'], article['prix_vente'],
                     article['total_ht']))

                # Mettre à jour le stock de produits
                # Vérifier si le produit existe déjà
                cursor.execute("""
                    SELECT id FROM produits WHERE designation = ?
                """, (article['designation'],))

                produit = cursor.fetchone()

                if produit:
                    # Mettre à jour le produit existant
                    produit_id = produit[0]
                    cursor.execute("""
                        UPDATE produits
                        SET prix_achat = ?, prix_vente = ?,
                            stock = stock + ?
                        WHERE id = ?
                    """, (article['prix_ht'], article['prix_vente'],
                         article['quantite'], produit_id))
                else:
                    # Créer un nouveau produit
                    cursor.execute("""
                        INSERT INTO produits (code, designation, unite, prix_achat,
                                           prix_vente, stock)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (self.generate_product_code(), article['designation'],
                         article['unite'], article['prix_ht'], article['prix_vente'],
                         article['quantite']))

            self.db_manager.conn.commit()
            QMessageBox.information(self, "Succès", "La facture d'achat a été enregistrée avec succès.")
            self.accept()

        except Exception as e:
            self.db_manager.conn.rollback()
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue lors de l'enregistrement: {str(e)}")

    def validate_form(self):
        """Valider que tous les champs obligatoires sont remplis"""
        if self.fournisseur_combo.currentIndex() == 0 or self.fournisseur_combo.currentData() is None:
            QMessageBox.warning(self, "Champ obligatoire", "Veuillez sélectionner un fournisseur.")
            self.fournisseur_combo.setFocus()
            return False

        if not self.facture_input.text():
            QMessageBox.warning(self, "Champ obligatoire", "Veuillez saisir un numéro de facture.")
            self.facture_input.setFocus()
            return False

        if self.payment_combo.currentIndex() == 0 or self.payment_combo.currentData() is None:
            QMessageBox.warning(self, "Champ obligatoire", "Veuillez sélectionner un mode de paiement.")
            self.payment_combo.setFocus()
            return False

        # Vérifier que les articles ont des désignations
        for article in self.articles:
            if not article.designation_input.text().strip():
                QMessageBox.warning(self, "Champ obligatoire",
                                   f"Veuillez saisir une désignation pour l'article #{article.article_id}.")
                article.designation_input.setFocus()
                return False

        for article in self.articles:
            data = article.get_data()
            if not data['designation']:
                QMessageBox.warning(self, "Champ obligatoire",
                                   f"Veuillez saisir une désignation pour l'article #{data['id']}.")
                return False

            if not data['unite']:
                QMessageBox.warning(self, "Champ obligatoire",
                                   f"Veuillez saisir une unité pour l'article #{data['id']}.")
                return False

        return True

    def generate_product_code(self):
        """Générer un code produit unique"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT MAX(CAST(SUBSTR(code, 2) AS INTEGER)) FROM produits WHERE code LIKE 'P%'")
            result = cursor.fetchone()[0]

            if result is None:
                next_num = 1
            else:
                next_num = result + 1

            return f"P{next_num:03d}"
        except Exception as e:
            print(f"Erreur lors de la génération du code produit: {str(e)}")
            return f"P{len(self.articles):03d}"
