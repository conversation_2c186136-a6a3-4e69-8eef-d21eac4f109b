from PySide6.QtWidgets import (Q<PERSON><PERSON>t, QVBox<PERSON>ayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QHeaderView, QMessageBox, QComboBox, QFrame,
                              QDialog, QSizePolicy)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QIcon
from PySide6.QtPrintSupport import QPrinter, QPrintDialog
import sqlite3
import datetime
import os
import sys

# Ajouter le répertoire parent pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# Importer la solution d'intégration stock
try:
    from stock_integration_solution import integrate_with_invoice_save, integrate_with_invoice_delete
    STOCK_INTEGRATION_AVAILABLE = True
    print("✅ Module d'intégration stock importé avec succès")
except ImportError as e:
    STOCK_INTEGRATION_AVAILABLE = False
    print(f"⚠️ Module d'intégration stock non disponible: {e}")

# Importer les composants de base
from ..components.base_module import BaseModule
from ..components.base_form_dialog import BaseFormDialog

# Importer les styles et icônes
from ..theme import COLORS, BORDER_RADIUS, SPACING
from ..icons.icons import INVOICE_ICON, PRINT_ICON, PDF_ICON

# Importer la boîte de dialogue de liste des factures
from .factures_list_dialog import FacturesListDialog

# Importer la boîte de dialogue d'importation des bons de commande
from .import_bon_commande_dialog import ImportBonCommandeDialog

# Importer la sélection automatique
from ..components.auto_select_widget import apply_auto_select_to_window

# Importer les générateurs de factures
try:
    from ...utils.excel_invoice_generator import ExcelInvoiceGenerator
    excel_generator_available = True
except ImportError:
    excel_generator_available = False

try:
    from ...utils.isoloc_invoice_generator import IsolocInvoiceGenerator
    isoloc_generator_available = True
except ImportError:
    isoloc_generator_available = False

# Importer l'exportateur Excel
try:
    from ...utils.excel_exporter import ExcelExporter
    excel_exporter_available = True
except ImportError:
    excel_exporter_available = False

class FactureFormDialog(QDialog):
    """Boîte de dialogue simplifiée pour ajouter ou modifier une facture"""

    # Signal émis lorsqu'une facture est ajoutée ou modifiée
    facture_saved = Signal()
    # Signal émis pour actualiser le stock
    stock_updated = Signal()

    def __init__(self, db_manager, facture_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.facture_id = facture_id
        self.is_edit_mode = facture_id is not None

        self.setWindowTitle("Modifier une facture" if self.is_edit_mode else "Ajouter une facture")
        self.setMinimumWidth(800)
        self.setMinimumHeight(600)
        self.setModal(True)

        # Initialiser les variables
        self.numero_facture = None
        self.date_facture = None
        self.client_combo = None
        self.bon_commande_input = None
        self.marche_input = None
        self.objet_input = None
        self.articles_table = None
        self.total_ht_label = None
        self.tva_label = None
        self.ttc_label = None

        # Configurer l'interface utilisateur
        self.setup_ui()

        # Charger les données si on est en mode édition
        if self.is_edit_mode:
            self.charger_facture(facture_id)
        else:
            self.nouvelle_facture()

        # Appliquer la sélection automatique
        apply_auto_select_to_window(self)

    def setup_ui(self):
        """Configure l'interface utilisateur simplifiée de la boîte de dialogue"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # Titre
        title_label = QLabel("Informations de la facture")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #1E3A8A;
            margin-bottom: 10px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # Formulaire principal
        form_layout = QFormLayout()
        form_layout.setSpacing(10)
        form_layout.setLabelAlignment(Qt.AlignRight)

        # Numéro de facture
        self.numero_facture = QLineEdit()
        self.numero_facture.setReadOnly(True)
        form_layout.addRow("N° Facture:", self.numero_facture)

        # Date de facture
        from ..style import create_styled_date_edit
        self.date_facture = create_styled_date_edit()
        form_layout.addRow("Date:", self.date_facture)

        # Date de paiement
        self.date_paiement = create_styled_date_edit(
            optional=True, placeholder_text="Non payé"
        )
        form_layout.addRow("Date de paiement:", self.date_paiement)

        # Client
        self.client_combo = QComboBox()
        self.charger_clients()
        self.client_combo.currentIndexChanged.connect(self.client_selectionne)
        form_layout.addRow("Client:", self.client_combo)

        # Devis (sélectionne ou taper)
        self.devis_combo = QComboBox()
        self.devis_combo.setEditable(True)  # Permet de taper ou sélectionner
        self.devis_combo.setStyleSheet("padding: 8px; border: 1px solid #ddd; border-radius: 4px;")
        self.devis_combo.lineEdit().setPlaceholderText("SÉLECTIONNÉ OU TAPER...")
        self.devis_combo.currentTextChanged.connect(self.on_devis_changed)  # Connecter le signal
        self.charger_devis()
        form_layout.addRow("Devis N°:", self.devis_combo)

        # Bon de commande (liste déroulante)
        self.bon_commande_combo = QComboBox()
        self.charger_bons_commande()
        self.bon_commande_combo.currentIndexChanged.connect(self.bon_commande_selectionne)
        form_layout.addRow("Bon de commande N°:", self.bon_commande_combo)

        # Marché
        self.marche_input = QLineEdit()
        form_layout.addRow("Marché N°:", self.marche_input)

        # إضافة الاقتراحات التلقائية لحقل المشروع
        try:
            from ..components.autocomplete_widget import setup_autocomplete_for_widget
            setup_autocomplete_for_widget(self.marche_input, self.db_manager, "marche_reference")
            print("✅ تم تطبيق الاقتراحات التلقائية على حقل المشروع في الفواتير")
        except Exception as e:
            print(f"❌ خطأ في تطبيق الاقتراحات التلقائية على حقل المشروع في الفواتير: {str(e)}")

        # Objet
        self.objet_input = QLineEdit()
        form_layout.addRow("Objet:", self.objet_input)

        # Bon de livraison N°
        self.bon_livraison_combo = QComboBox()
        self.charger_bons_livraison()
        form_layout.addRow("Bon de livraison N°:", self.bon_livraison_combo)

        # Mode de paiement
        self.mode_paiement_combo = QComboBox()
        self.mode_paiement_combo.addItems([
            "Virement bancaire",
            "Chèque",
            "Espèces",
            "Carte bancaire",
            "Traite",
            "Autre"
        ])
        form_layout.addRow("Mode de paiement :", self.mode_paiement_combo)

        layout.addLayout(form_layout)

        # Tableau des articles
        table_label = QLabel("Articles")
        table_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(table_label)

        self.articles_table = QTableWidget()
        self.articles_table.setColumnCount(7)  # Ajout d'une colonne pour le bouton de suppression
        self.articles_table.setHorizontalHeaderLabels([
            "N°", "Désignation", "Unité", "Quantité", "Prix unitaire HT", "Prix total HT", "Action"
        ])

        # Configurer les colonnes
        self.articles_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.articles_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)

        self.articles_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
            }
            QHeaderView::section {
                background-color: #1E3A8A;
                color: white;
                padding: 8px;
                font-weight: bold;
                border: none;
            }
        """)

        layout.addWidget(self.articles_table)

        # Boutons pour le tableau
        buttons_layout = QHBoxLayout()

        add_line_button = QPushButton("Ajouter une ligne")
        add_line_button.setStyleSheet("""
            QPushButton {
                background-color: #1E3A8A;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1A56DB;
            }
        """)
        add_line_button.clicked.connect(self.ajouter_ligne)

        # Bouton pour importer depuis un bon de commande
        import_bon_button = QPushButton("Importer depuis Bon de commande")
        import_bon_button.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        import_bon_button.clicked.connect(self.importer_depuis_bon_commande)

        delete_all_button = QPushButton("Supprimer tout")
        delete_all_button.setStyleSheet("""
            QPushButton {
                background-color: #EF4444;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #DC2626;
            }
        """)
        delete_all_button.clicked.connect(self.supprimer_toutes_lignes)

        buttons_layout.addWidget(add_line_button)
        buttons_layout.addWidget(import_bon_button)
        buttons_layout.addWidget(delete_all_button)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # Totaux
        totals_layout = QFormLayout()
        totals_layout.setLabelAlignment(Qt.AlignRight)
        totals_layout.setFormAlignment(Qt.AlignRight)

        self.total_ht_label = QLabel("0.00 DH")
        self.tva_label = QLabel("0.00 DH")
        self.ttc_label = QLabel("0.00 DH")

        self.ttc_label.setStyleSheet("font-weight: bold;")

        totals_layout.addRow("Total HT:", self.total_ht_label)
        totals_layout.addRow("TVA 20%:", self.tva_label)
        totals_layout.addRow("Total TTC:", self.ttc_label)

        layout.addLayout(totals_layout)

        # Boutons d'action
        action_buttons_layout = QHBoxLayout()
        action_buttons_layout.setContentsMargins(0, 15, 0, 0)
        action_buttons_layout.setSpacing(10)

        # Bouton Imprimer
        print_button = QPushButton("Imprimer")
        print_button.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
        """)
        print_button.clicked.connect(self.imprimer_facture)

        # Bouton Exporter PDF
        export_pdf_button = QPushButton("Exporter PDF")
        export_pdf_button.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        export_pdf_button.clicked.connect(self.exporter_pdf)

        # Bouton Annuler
        cancel_button = QPushButton("Annuler")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        cancel_button.clicked.connect(self.reject)

        # Bouton Enregistrer
        save_button = QPushButton("Enregistrer")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #1E3A8A;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1A56DB;
            }
        """)
        save_button.clicked.connect(self.save_facture)

        action_buttons_layout.addWidget(print_button)
        action_buttons_layout.addWidget(export_pdf_button)
        action_buttons_layout.addStretch()
        action_buttons_layout.addWidget(cancel_button)
        action_buttons_layout.addWidget(save_button)

        layout.addLayout(action_buttons_layout)

    def charger_clients(self):
        """Charge la liste des clients dans le QComboBox"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT id, nom, ice FROM clients ORDER BY nom")
            clients = cursor.fetchall()

            self.client_combo.clear()
            self.client_combo.addItem("Sélectionner un client", None)

            for client in clients:
                self.client_combo.addItem(client['nom'], {"id": client['id'], "ice": client['ice']})
        except sqlite3.Error as e:
            print(f"Erreur lors du chargement des clients: {str(e)}")
            # Ajouter juste l'option par défaut
            self.client_combo.clear()
            self.client_combo.addItem("Sélectionner un client", None)

    def charger_bons_commande(self, client_id=None):
        """Charge la liste des bons de commande dans le QComboBox"""
        try:
            cursor = self.db_manager.conn.cursor()

            if client_id:
                # Charger seulement les bons de commande du client sélectionné
                cursor.execute("""
                    SELECT b.id, b.numero, b.objet, c.nom as client_nom
                    FROM bons_commande b
                    LEFT JOIN clients c ON b.client_id = c.id
                    WHERE b.client_id = ?
                    ORDER BY b.date_creation DESC
                """, (client_id,))
            else:
                # Charger tous les bons de commande
                cursor.execute("""
                    SELECT b.id, b.numero, b.objet, c.nom as client_nom
                    FROM bons_commande b
                    LEFT JOIN clients c ON b.client_id = c.id
                    ORDER BY b.date_creation DESC
                """)

            bons = cursor.fetchall()

            self.bon_commande_combo.clear()
            self.bon_commande_combo.addItem("Sélectionner un bon de commande", None)

            for bon in bons:
                # Afficher le numéro et le client
                display_text = f"{bon['numero']}"
                if bon['client_nom']:
                    display_text += f" - {bon['client_nom']}"

                self.bon_commande_combo.addItem(display_text, {
                    "id": bon['id'],
                    "numero": bon['numero'],
                    "objet": bon['objet'],
                    "client_nom": bon['client_nom']
                })
        except sqlite3.Error as e:
            print(f"Erreur lors du chargement des bons de commande: {str(e)}")
            # Ajouter juste l'option par défaut
            self.bon_commande_combo.clear()
            self.bon_commande_combo.addItem("Sélectionner un bon de commande", None)

    def charger_bons_livraison(self):
        """Charge la liste des bons de livraison dans le QComboBox"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT b.id, b.numero, c.nom as client_nom
                FROM bons_livraison b
                LEFT JOIN clients c ON b.client_id = c.id
                ORDER BY b.date_creation DESC
            """)
            bons_livraison = cursor.fetchall()

            self.bon_livraison_combo.clear()
            self.bon_livraison_combo.addItem("Sélectionner un bon de livraison", None)

            for bl in bons_livraison:
                # Afficher le numéro et le client
                display_text = f"{bl['numero']}"
                if bl['client_nom']:
                    display_text += f" - {bl['client_nom']}"

                self.bon_livraison_combo.addItem(display_text, {
                    "id": bl['id'],
                    "numero": bl['numero'],
                    "client_nom": bl['client_nom']
                })
        except sqlite3.Error as e:
            print(f"Erreur lors du chargement des bons de livraison: {str(e)}")
            # Ajouter juste l'option par défaut
            self.bon_livraison_combo.clear()
            self.bon_livraison_combo.addItem("Sélectionner un bon de livraison", None)

    def client_selectionne(self):
        """Appelé lorsqu'un client est sélectionné"""
        client_data = self.client_combo.currentData()

        if client_data and client_data.get("id"):
            # Charger les bons de commande du client sélectionné
            self.charger_bons_commande(client_data["id"])
        else:
            # Charger tous les bons de commande si aucun client n'est sélectionné
            self.charger_bons_commande()

        # Vider le tableau des articles car le client a changé
        self.articles_table.setRowCount(0)
        self.calculer_totaux()

    def bon_commande_selectionne(self):
        """Appelé lorsqu'un bon de commande est sélectionné"""
        bon_data = self.bon_commande_combo.currentData()

        if bon_data and bon_data.get("objet"):
            # Remplir automatiquement le champ OBJET
            self.objet_input.setText(bon_data["objet"])

            # Charger automatiquement les articles du bon de commande
            self.charger_articles_bon_commande(bon_data["id"])
        else:
            # Vider le champ OBJET si aucun bon n'est sélectionné
            self.objet_input.clear()
            # Vider le tableau des articles
            self.articles_table.setRowCount(0)
            self.calculer_totaux()

    def nouvelle_facture(self):
        """Initialise une nouvelle facture"""
        # Générer un nouveau numéro de facture
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT COUNT(*) as count FROM factures_vente")
        count = cursor.fetchone()['count']

        # Format: F001 (05/2023)
        current_date = QDate.currentDate()
        numero = f"F{count+1:03d} ({current_date.month():02d}/{current_date.year()})"

        self.numero_facture.setText(numero)
        self.date_facture.setDate(current_date)
        self.client_combo.setCurrentIndex(0)
        self.bon_commande_combo.setCurrentIndex(0)  # Réinitialiser la sélection du bon de commande
        self.marche_input.clear()
        self.objet_input.clear()
        self.bon_livraison_combo.setCurrentIndex(0)  # Réinitialiser la sélection du bon de livraison
        self.mode_paiement_combo.setCurrentIndex(0)  # Réinitialiser à "Virement bancaire"

        # Vider le tableau
        self.articles_table.setRowCount(0)

        # Réinitialiser les totaux
        self.total_ht_label.setText("0.00 DH")
        self.tva_label.setText("0.00 DH")
        self.ttc_label.setText("0.00 DH")

    def charger_facture(self, facture_id):
        """Charge une facture existante"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Récupérer les informations de la facture
            cursor.execute("""
                SELECT f.*, c.nom as client_nom, c.ice as client_ice
                FROM factures_vente f
                LEFT JOIN clients c ON f.client_id = c.id
                WHERE f.id = ?
            """, (facture_id,))
            facture = cursor.fetchone()

            if not facture:
                QMessageBox.warning(self, "Erreur", "Facture introuvable.")
                return

            # Mettre à jour les champs du formulaire
            self.numero_facture.setText(facture['numero'])

            # Convertir la date
            date_obj = datetime.datetime.strptime(facture['date_creation'], '%Y-%m-%d')
            self.date_facture.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))

            # Sélectionner le client
            for i in range(self.client_combo.count()):
                item_data = self.client_combo.itemData(i)
                if item_data and item_data.get("id") == facture['client_id']:
                    self.client_combo.setCurrentIndex(i)
                    break

            # Mettre à jour le bon de commande
            bon_commande_numero = facture['bon_commande'] or ""
            if bon_commande_numero:
                # Chercher le bon de commande dans la liste
                for i in range(self.bon_commande_combo.count()):
                    item_data = self.bon_commande_combo.itemData(i)
                    if item_data and item_data.get("numero") == bon_commande_numero:
                        self.bon_commande_combo.setCurrentIndex(i)
                        break
                else:
                    # Si le bon de commande n'est pas trouvé, rester sur la sélection par défaut
                    self.bon_commande_combo.setCurrentIndex(0)
            else:
                self.bon_commande_combo.setCurrentIndex(0)

            # Mettre à jour les autres champs
            self.marche_input.setText(facture['marche'] or "")
            self.objet_input.setText(facture['objet'] or "")

            # Bon de livraison
            bon_livraison_numero = facture.get('bon_livraison_numero', '')
            if bon_livraison_numero:
                # Chercher le bon de livraison dans la liste
                for i in range(self.bon_livraison_combo.count()):
                    item_data = self.bon_livraison_combo.itemData(i)
                    if item_data and item_data.get("numero") == bon_livraison_numero:
                        self.bon_livraison_combo.setCurrentIndex(i)
                        break
                else:
                    # Si le bon de livraison n'est pas trouvé, rester sur la sélection par défaut
                    self.bon_livraison_combo.setCurrentIndex(0)
            else:
                self.bon_livraison_combo.setCurrentIndex(0)

            # Mode de paiement
            mode_paiement = facture.get('mode_paiement', 'Virement bancaire')
            index = self.mode_paiement_combo.findText(mode_paiement)
            if index >= 0:
                self.mode_paiement_combo.setCurrentIndex(index)
            else:
                self.mode_paiement_combo.setCurrentIndex(0)  # Par défaut

            # Date de paiement
            if facture['date_paiement']:
                try:
                    date_paiement_obj = datetime.datetime.strptime(facture['date_paiement'], '%Y-%m-%d')
                    self.date_paiement.setDate(QDate(date_paiement_obj.year, date_paiement_obj.month, date_paiement_obj.day))
                except:
                    self.date_paiement.setDate(QDate())  # Date vide si erreur
            else:
                self.date_paiement.setDate(QDate())  # Date vide

            # Charger les lignes de la facture
            self.articles_table.setRowCount(0)

            cursor.execute("""
                SELECT * FROM lignes_facture
                WHERE facture_id = ?
                ORDER BY id
            """, (facture_id,))
            lignes = cursor.fetchall()

            for ligne in lignes:
                self.ajouter_ligne_existante(ligne)

            # Mettre à jour les totaux
            self.calculer_totaux()

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement de la facture: {str(e)}")

    def ajouter_ligne(self):
        """Ajoute une nouvelle ligne au tableau des articles"""
        row = self.articles_table.rowCount()
        self.articles_table.insertRow(row)

        # Numéro de ligne
        self.articles_table.setItem(row, 0, QTableWidgetItem(str(row + 1)))

        # Désignation
        self.articles_table.setItem(row, 1, QTableWidgetItem(""))

        # Unité
        self.articles_table.setItem(row, 2, QTableWidgetItem(""))

        # Quantité (QSpinBox)
        quantite_spin = QSpinBox()
        quantite_spin.setRange(1, 10000)
        quantite_spin.setValue(1)
        quantite_spin.setStyleSheet("""
            QSpinBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px;
            }
        """)
        quantite_spin.valueChanged.connect(self.calculer_totaux)
        self.articles_table.setCellWidget(row, 3, quantite_spin)

        # Prix unitaire (QDoubleSpinBox)
        prix_spin = QDoubleSpinBox()
        prix_spin.setRange(0, 1000000)
        prix_spin.setValue(0)
        prix_spin.setDecimals(2)
        prix_spin.setSuffix(" DH")
        prix_spin.setStyleSheet("""
            QDoubleSpinBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px;
            }
        """)
        prix_spin.valueChanged.connect(self.calculer_totaux)
        self.articles_table.setCellWidget(row, 4, prix_spin)

        # Prix total
        self.articles_table.setItem(row, 5, QTableWidgetItem("0.00 DH"))

        # Bouton de suppression
        delete_button = QPushButton("🗑️")
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #EF4444;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #DC2626;
            }
        """)
        delete_button.clicked.connect(lambda: self.supprimer_ligne(row))

        # Créer un widget conteneur pour le bouton
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(2, 2, 2, 2)
        button_layout.addWidget(delete_button)
        button_layout.setAlignment(Qt.AlignCenter)

        self.articles_table.setCellWidget(row, 6, button_container)

        # Mettre à jour les totaux
        self.calculer_totaux()

    def ajouter_ligne_existante(self, ligne):
        """Ajoute une ligne existante au tableau des articles"""
        row = self.articles_table.rowCount()
        self.articles_table.insertRow(row)

        # Numéro de ligne
        self.articles_table.setItem(row, 0, QTableWidgetItem(str(row + 1)))

        # Désignation
        self.articles_table.setItem(row, 1, QTableWidgetItem(ligne['designation']))

        # Unité
        self.articles_table.setItem(row, 2, QTableWidgetItem(ligne['unite'] or ""))

        # Quantité (QSpinBox)
        quantite_spin = QSpinBox()
        quantite_spin.setRange(1, 10000)
        quantite_spin.setValue(ligne['quantite'])
        quantite_spin.setStyleSheet("""
            QSpinBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px;
            }
        """)
        quantite_spin.valueChanged.connect(self.calculer_totaux)
        self.articles_table.setCellWidget(row, 3, quantite_spin)

        # Prix unitaire (QDoubleSpinBox)
        prix_spin = QDoubleSpinBox()
        prix_spin.setRange(0, 1000000)
        prix_spin.setValue(ligne['prix_unitaire_ht'])
        prix_spin.setDecimals(2)
        prix_spin.setSuffix(" DH")
        prix_spin.setStyleSheet("""
            QDoubleSpinBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px;
            }
        """)
        prix_spin.valueChanged.connect(self.calculer_totaux)
        self.articles_table.setCellWidget(row, 4, prix_spin)

        # Prix total
        self.articles_table.setItem(row, 5, QTableWidgetItem(f"{ligne['total_ht']:.2f} DH"))

        # Bouton de suppression
        delete_button = QPushButton("🗑️")
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #EF4444;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #DC2626;
            }
        """)
        delete_button.clicked.connect(lambda: self.supprimer_ligne(row))

        # Créer un widget conteneur pour le bouton
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(2, 2, 2, 2)
        button_layout.addWidget(delete_button)
        button_layout.setAlignment(Qt.AlignCenter)

        self.articles_table.setCellWidget(row, 6, button_container)

    def supprimer_ligne(self, row):
        """Supprime une ligne du tableau"""
        self.articles_table.removeRow(row)

        # Mettre à jour les numéros de ligne
        for r in range(self.articles_table.rowCount()):
            self.articles_table.item(r, 0).setText(str(r + 1))

        self.calculer_totaux()

    def supprimer_toutes_lignes(self):
        """Supprime toutes les lignes du tableau"""
        self.articles_table.setRowCount(0)
        self.calculer_totaux()

    def calculer_totaux(self, *args):
        """Calcule les totaux de la facture"""
        total_ht = 0

        # Parcourir toutes les lignes du tableau
        for row in range(self.articles_table.rowCount()):
            try:
                # Récupérer la quantité depuis le QSpinBox
                quantite_spin = self.articles_table.cellWidget(row, 3)
                if isinstance(quantite_spin, QSpinBox):
                    quantite = quantite_spin.value()
                else:
                    quantite = int(self.articles_table.item(row, 3).text() or "0")

                # Récupérer le prix unitaire depuis le QDoubleSpinBox
                prix_spin = self.articles_table.cellWidget(row, 4)
                if isinstance(prix_spin, QDoubleSpinBox):
                    prix_unitaire = prix_spin.value()
                else:
                    prix_text = self.articles_table.item(row, 4).text().replace("DH", "").strip()
                    prix_unitaire = float(prix_text or "0")

                # Calculer le total de la ligne
                total_ligne = quantite * prix_unitaire
                total_ht += total_ligne

                # Mettre à jour le total de la ligne
                if self.articles_table.item(row, 5):
                    self.articles_table.item(row, 5).setText(f"{total_ligne:.2f} DH")
            except (ValueError, AttributeError) as e:
                print(f"Erreur lors du calcul de la ligne {row}: {str(e)}")

        # Calculer la TVA et le total TTC
        tva = total_ht * 0.2  # TVA 20%
        ttc = total_ht + tva

        # Mettre à jour les labels
        self.total_ht_label.setText(f"{total_ht:.2f} DH")
        self.tva_label.setText(f"{tva:.2f} DH")
        self.ttc_label.setText(f"{ttc:.2f} DH")

    def prepare_facture_data(self):
        """Prépare les données de la facture pour l'enregistrement ou l'exportation"""
        # Vérifier qu'un client est sélectionné
        client_data = self.client_combo.currentData()
        if not client_data:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un client.")
            return None

        # Vérifier qu'il y a au moins une ligne
        if self.articles_table.rowCount() == 0:
            QMessageBox.warning(self, "Erreur", "Veuillez ajouter au moins une ligne à la facture.")
            return None

        client_id = client_data["id"]

        # Valider les données des lignes
        for row in range(self.articles_table.rowCount()):
            designation = self.articles_table.item(row, 1).text().strip()
            if not designation:
                QMessageBox.warning(self, "Erreur", f"Veuillez saisir une désignation pour la ligne {row + 1}.")
                return None

            # Vérifier la quantité (QSpinBox)
            quantite_spin = self.articles_table.cellWidget(row, 3)
            if not isinstance(quantite_spin, QSpinBox):
                QMessageBox.warning(self, "Erreur", f"Erreur dans le champ quantité pour la ligne {row + 1}.")
                return None

            quantite = quantite_spin.value()
            if quantite <= 0:
                QMessageBox.warning(self, "Erreur", f"La quantité doit être supérieure à 0 pour la ligne {row + 1}.")
                return None

            # Vérifier le prix unitaire (QDoubleSpinBox)
            prix_spin = self.articles_table.cellWidget(row, 4)
            if not isinstance(prix_spin, QDoubleSpinBox):
                QMessageBox.warning(self, "Erreur", f"Erreur dans le champ prix unitaire pour la ligne {row + 1}.")
                return None

            prix_unitaire = prix_spin.value()
            if prix_unitaire <= 0:
                QMessageBox.warning(self, "Erreur", f"Le prix unitaire doit être supérieur à 0 pour la ligne {row + 1}.")
                return None

        # Récupérer les données du formulaire
        numero = self.numero_facture.text()
        date_creation = self.date_facture.date().toString("yyyy-MM-dd")
        date_echeance = self.date_facture.date().addDays(30).toString("yyyy-MM-dd")
        date_str = self.date_facture.date().toString("dd/MM/yyyy")

        # Date de paiement (peut être vide)
        date_paiement = None
        if self.date_paiement.date().isValid() and self.date_paiement.date() != QDate():
            date_paiement = self.date_paiement.date().toString("yyyy-MM-dd")
        # Récupérer le numéro du bon de commande sélectionné
        bon_data = self.bon_commande_combo.currentData()
        bon_commande = bon_data["numero"] if bon_data and "numero" in bon_data else ""
        marche = self.marche_input.text()
        objet = self.objet_input.text()

        # Récupérer le numéro du bon de livraison sélectionné
        bl_data = self.bon_livraison_combo.currentData()
        bon_livraison_numero = bl_data["numero"] if bl_data and "numero" in bl_data else ""

        mode_paiement = self.mode_paiement_combo.currentText()

        # Récupérer les informations du client
        client_nom = self.client_combo.currentText()
        client_ice = client_data["ice"] if client_data and "ice" in client_data else ""

        # Récupérer les informations complètes du client depuis la base de données
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT * FROM clients WHERE id = ?", (client_id,))
        client_db = cursor.fetchone()

        client_info = {
            "id": client_id,
            "nom": client_nom,
            "ice": client_ice,
            "adresse": client_db['adresse'] if client_db and 'adresse' in client_db else "",
            "telephone": client_db['telephone'] if client_db and 'telephone' in client_db else "",
            "email": client_db['email'] if client_db and 'email' in client_db else ""
        }

        # Calculer les totaux
        total_ht = float(self.total_ht_label.text().replace("DH", "").strip())
        total_tva = float(self.tva_label.text().replace("DH", "").strip())
        total_ttc = float(self.ttc_label.text().replace("DH", "").strip())

        # Préparer les lignes de la facture
        lignes = []
        for row in range(self.articles_table.rowCount()):
            designation = self.articles_table.item(row, 1).text().strip()
            unite = self.articles_table.item(row, 2).text().strip()

            # Récupérer la quantité depuis le QSpinBox
            quantite_spin = self.articles_table.cellWidget(row, 3)
            quantite = quantite_spin.value()

            # Récupérer le prix unitaire depuis le QDoubleSpinBox
            prix_spin = self.articles_table.cellWidget(row, 4)
            prix_unitaire = prix_spin.value()

            total_ligne = quantite * prix_unitaire

            lignes.append({
                "designation": designation,
                "unite": unite,
                "quantite": quantite,
                "prix_unitaire": prix_unitaire,
                "total_ht": total_ligne,
                "taux_tva": 20.0
            })

        # Préparer les données complètes de la facture
        facture_data = {
            "numero": numero,
            "date": date_str,
            "date_creation": date_creation,
            "date_echeance": date_echeance,
            "client_id": client_id,
            "client": client_info,
            "bon_commande": bon_commande,
            "marche": marche,
            "objet": objet,
            "bon_livraison_numero": bon_livraison_numero,
            "mode_paiement": mode_paiement,
            "lignes": lignes,
            "totaux": {
                "total_ht": total_ht,
                "total_tva": total_tva,
                "total_ttc": total_ttc
            }
        }

        return facture_data

    def save_facture(self):
        """Enregistre la facture dans la base de données"""
        facture_data = self.prepare_facture_data()
        if not facture_data:
            return

        # Récupérer les données nécessaires pour l'enregistrement
        numero = facture_data["numero"]
        date_creation = facture_data["date_creation"]
        date_echeance = facture_data["date_echeance"]
        client_id = facture_data["client_id"]
        bon_commande = facture_data["bon_commande"]
        marche = facture_data["marche"]
        objet = facture_data["objet"]
        bon_livraison_numero = facture_data["bon_livraison_numero"]
        mode_paiement = facture_data["mode_paiement"]
        total_ht = facture_data["totaux"]["total_ht"]
        total_tva = facture_data["totaux"]["total_tva"]
        total_ttc = facture_data["totaux"]["total_ttc"]
        lignes = facture_data["lignes"]

        # Date de paiement
        date_paiement = None
        if self.date_paiement.date().isValid() and self.date_paiement.date() != QDate():
            date_paiement = self.date_paiement.date().toString("yyyy-MM-dd")



        # Enregistrer la facture
        cursor = self.db_manager.conn.cursor()
        try:
            # Insérer ou mettre à jour la facture
            if self.is_edit_mode:
                cursor.execute(
                    """UPDATE factures_vente
                       SET numero = ?, date_creation = ?, date_echeance = ?, client_id = ?,
                           bon_commande = ?, marche = ?, objet = ?, bon_livraison_numero = ?, mode_paiement = ?,
                           total_ht = ?, total_tva = ?, total_ttc = ?, date_paiement = ?
                       WHERE id = ?""",
                    (numero, date_creation, date_echeance, client_id,
                     bon_commande, marche, objet, bon_livraison_numero, mode_paiement,
                     total_ht, total_tva, total_ttc, date_paiement, self.facture_id)
                )

                # Supprimer les anciennes lignes
                cursor.execute("DELETE FROM lignes_facture WHERE facture_id = ?", (self.facture_id,))
                facture_id = self.facture_id
            else:
                cursor.execute(
                    """INSERT INTO factures_vente
                       (numero, date_creation, date_echeance, client_id,
                        bon_commande, marche, objet, bon_livraison_numero, mode_paiement,
                        total_ht, total_tva, total_ttc, statut, date_paiement)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                    (numero, date_creation, date_echeance, client_id,
                     bon_commande, marche, objet, bon_livraison_numero, mode_paiement,
                     total_ht, total_tva, total_ttc, "En attente", date_paiement)
                )

                facture_id = cursor.lastrowid

            # Insérer les lignes de la facture
            for ligne in lignes:
                cursor.execute(
                    """INSERT INTO lignes_facture
                       (facture_id, designation, unite, quantite, prix_unitaire_ht, taux_tva, total_ht)
                       VALUES (?, ?, ?, ?, ?, ?, ?)""",
                    (facture_id, ligne["designation"], ligne["unite"], ligne["quantite"],
                     ligne["prix_unitaire"], ligne["taux_tva"], ligne["total_ht"])
                )

            self.db_manager.conn.commit()

            # Intégrer avec le système de stock (nouvelle méthode)
            if STOCK_INTEGRATION_AVAILABLE:
                print(f"📦 Intégration stock pour facture {facture_id} avec {len(lignes)} lignes")
                success = integrate_with_invoice_save(self.db_manager, facture_id, lignes)
                if success:
                    print("✅ Intégration stock réussie")
                else:
                    print("❌ Échec de l'intégration stock")
            else:
                print("⚠️ Module d'intégration stock non disponible")

            # Intégrer avec la caisse si paiement en espèces
            if mode_paiement == "Espèces":
                self.integrate_with_caisse(facture_id, numero, total_ttc)

            # Émettre les signaux
            self.facture_saved.emit()
            self.stock_updated.emit()
            print("📤 Signal d'actualisation du stock émis depuis la facture")

            QMessageBox.information(self, "Succès", "Facture enregistrée avec succès.")
            self.accept()

        except sqlite3.Error as e:
            self.db_manager.conn.rollback()
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement de la facture: {str(e)}")

    def integrate_with_caisse(self, facture_id, numero, montant):
        """Intègre automatiquement avec la caisse pour les paiements en espèces"""
        try:
            # Ajouter une entrée dans la caisse
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                INSERT INTO entrees_caisse (date, nature, objet, reference, montant)
                VALUES (?, ?, ?, ?, ?)
            """, (
                datetime.datetime.now().strftime("%Y-%m-%d"),
                "بيع",
                f"بيع بالنقد - فاتورة {numero}",
                numero,
                montant
            ))

            self.db_manager.conn.commit()
            print(f"💰 Entrée de caisse automatique ajoutée: {montant} DH pour la facture {numero}")

        except Exception as e:
            print(f"❌ Erreur lors de l'intégration avec la caisse: {str(e)}")

    # Les méthodes de gestion du stock ont été déplacées vers stock_integration_solution.py
    # pour une meilleure organisation et maintenance

    def exporter_pdf(self):
        """Exporte la facture en PDF au format ISOLOC"""
        facture_data = self.prepare_facture_data()
        if not facture_data:
            return

        if not isoloc_generator_available:
            QMessageBox.warning(self, "Erreur", "Le générateur de factures ISOLOC n'est pas disponible.")
            return

        try:
            # Créer le générateur de factures ISOLOC
            generator = IsolocInvoiceGenerator()

            # Générer le PDF
            output_file = generator.generate_pdf(facture_data)

            # Afficher un message de succès
            QMessageBox.information(self, "Succès", f"Facture exportée avec succès dans le fichier {output_file}")

            # Ouvrir le fichier PDF
            os.startfile(output_file) if os.name == 'nt' else os.system(f"xdg-open {output_file}")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'exportation de la facture: {str(e)}")

    def imprimer_facture(self):
        """Imprime la facture en utilisant le modèle ISOLOC"""
        facture_data = self.prepare_facture_data()
        if not facture_data:
            return

        if not isoloc_generator_available:
            QMessageBox.warning(self, "Erreur", "Le générateur de factures ISOLOC n'est pas disponible.")
            return

        try:
            # Créer le générateur de factures ISOLOC
            generator = IsolocInvoiceGenerator()

            # Générer le fichier Excel
            output_file = generator.generate_invoice(facture_data)

            # Ouvrir le fichier Excel pour impression
            QMessageBox.information(self, "Impression",
                                   f"Le fichier Excel a été généré avec succès: {output_file}\n\n"
                                   "Veuillez l'ouvrir et utiliser la fonction d'impression d'Excel.")

            # Ouvrir le fichier Excel
            os.startfile(output_file) if os.name == 'nt' else os.system(f"xdg-open {output_file}")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la préparation de l'impression: {str(e)}")

    def importer_depuis_bon_commande(self):
        """Ouvre une boîte de dialogue pour importer des articles depuis un bon de commande"""
        dialog = ImportBonCommandeDialog(self.db_manager, self)
        if dialog.exec() == QDialog.Accepted:
            # Récupérer les articles sélectionnés
            articles_selectionnes = dialog.get_selected_articles()

            # Ajouter chaque article au tableau
            for article in articles_selectionnes:
                self.ajouter_article_depuis_bon(article)

            # Recalculer les totaux
            self.calculer_totaux()

    def ajouter_article_depuis_bon(self, article):
        """Ajoute un article importé depuis un bon de commande au tableau"""
        row = self.articles_table.rowCount()
        self.articles_table.insertRow(row)

        # Numéro de ligne
        self.articles_table.setItem(row, 0, QTableWidgetItem(str(row + 1)))

        # Désignation
        self.articles_table.setItem(row, 1, QTableWidgetItem(article['designation']))

        # Unité
        self.articles_table.setItem(row, 2, QTableWidgetItem(article['unite'] or ""))

        # Quantité (QSpinBox)
        quantite_spin = QSpinBox()
        quantite_spin.setRange(1, 10000)
        quantite_spin.setValue(article['quantite'])
        quantite_spin.setStyleSheet("""
            QSpinBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px;
            }
        """)
        quantite_spin.valueChanged.connect(self.calculer_totaux)
        self.articles_table.setCellWidget(row, 3, quantite_spin)

        # Prix unitaire (QDoubleSpinBox)
        prix_spin = QDoubleSpinBox()
        prix_spin.setRange(0, 1000000)
        prix_spin.setValue(article['prix_unitaire_ht'])
        prix_spin.setDecimals(2)
        prix_spin.setSuffix(" DH")
        prix_spin.setStyleSheet("""
            QDoubleSpinBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px;
            }
        """)
        prix_spin.valueChanged.connect(self.calculer_totaux)
        self.articles_table.setCellWidget(row, 4, prix_spin)

        # Prix total
        total_ligne = article['quantite'] * article['prix_unitaire_ht']
        self.articles_table.setItem(row, 5, QTableWidgetItem(f"{total_ligne:.2f} DH"))

        # Bouton de suppression
        delete_button = QPushButton("🗑️")
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #EF4444;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #DC2626;
            }
        """)
        delete_button.clicked.connect(lambda: self.supprimer_ligne(row))

        # Créer un widget conteneur pour le bouton
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(2, 2, 2, 2)
        button_layout.addWidget(delete_button)
        button_layout.setAlignment(Qt.AlignCenter)

        self.articles_table.setCellWidget(row, 6, button_container)

    def charger_articles_bon_commande(self, bon_id):
        """Charge automatiquement les articles du bon de commande sélectionné"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT designation, unite, quantite, prix_unitaire_ht, total_ht
                FROM lignes_bon_commande
                WHERE bon_id = ?
                ORDER BY id
            """, (bon_id,))
            articles = cursor.fetchall()

            # Vider le tableau actuel
            self.articles_table.setRowCount(0)

            # Ajouter chaque article du bon de commande
            for article in articles:
                article_data = {
                    'designation': article['designation'],
                    'unite': article['unite'] or "",
                    'quantite': article['quantite'],
                    'prix_unitaire_ht': article['prix_unitaire_ht']
                }
                self.ajouter_article_depuis_bon(article_data)

            # Recalculer les totaux
            self.calculer_totaux()

        except sqlite3.Error as e:
            print(f"Erreur lors du chargement des articles du bon de commande: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des articles: {str(e)}")

class FacturesVenteModule(BaseModule):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("FacturesVenteModule")

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(int(SPACING['lg'].replace('px', '')), int(SPACING['lg'].replace('px', '')), int(SPACING['lg'].replace('px', '')), int(SPACING['lg'].replace('px', '')))
        main_layout.setSpacing(int(SPACING['md'].replace('px', '')))

        # Set up the UI components
        self._setup_header()
        self._setup_table()

        # Connect signals
        self.add_button.clicked.connect(self.show_add_dialog)
        self.search_box.textChanged.connect(self.filter_invoices)

        # Initial load
        self.load_invoices()

    def _setup_header(self):
        # Header container
        header = QFrame(self)
        header.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS['surface']};
                border-radius: {BORDER_RADIUS['md']};
                padding: {SPACING['md']};
            }}
        """)
        header_layout = QHBoxLayout(header)

        # Search box
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("Rechercher une facture...")
        self.search_box.setStyleSheet(f"""
            QLineEdit {{
                padding: {SPACING['sm']};
                background: {COLORS['background']};
                border: 1px solid {COLORS['divider']};
                border-radius: {BORDER_RADIUS['sm']};
                min-width: 300px;
            }}
            QLineEdit:focus {{
                border-color: {COLORS['primary']};
            }}
        """)

        # Add button
        self.add_button = QPushButton("Nouvelle Facture")
        self.add_button.setIcon(QIcon(INVOICE_ICON))
        self.add_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLORS['primary']};
                color: white;
                padding: {SPACING['sm']} {SPACING['md']};
                border-radius: {BORDER_RADIUS['sm']};
                border: none;
            }}
            QPushButton:hover {{
                background-color: {COLORS['primary_light']};
            }}
        """)

        # Layout
        header_layout.addWidget(self.search_box)
        header_layout.addStretch()
        header_layout.addWidget(self.add_button)

        self.layout().addWidget(header)

    def _setup_table(self):
        self.table = QTableWidget(self)
        self.table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {COLORS['surface']};
                border-radius: {BORDER_RADIUS['md']};
                border: none;
            }}
            QTableWidget::item {{
                padding: {SPACING['sm']};
            }}
            QHeaderView::section {{
                background-color: {COLORS['background']};
                padding: {SPACING['sm']};
                border: none;
                font-weight: bold;
                color: {COLORS['text_primary']};
            }}
        """)

        # Configure table
        self.table.setColumnCount(7)
        self.table.setHorizontalHeaderLabels([
            "N° Facture", "Date", "Client", "Montant", "Statut", "Actions", ""
        ])

        # Set column behaviors
        header = self.table.horizontalHeader()
        for i in range(5):
            header.setSectionResizeMode(i, QHeaderView.Stretch)
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # Actions column
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # Padding column
        self.table.setColumnWidth(5, 120)
        self.table.setColumnWidth(6, 20)

        self.layout().addWidget(self.table)

    def create_status_badge(self, status):
        badge = QLabel(status)
        color = {
            'Payée': COLORS['success'],
            'En attente': COLORS['warning'],
            'Annulée': COLORS['error']
        }.get(status, COLORS['text_secondary'])

        badge.setStyleSheet(f"""
            QLabel {{
                background-color: {color}20;
                color: {color};
                padding: 4px 8px;
                border-radius: {BORDER_RADIUS['sm']};
                font-weight: bold;
            }}
        """)
        return badge

    def _get_action_button_style(self):
        return f"""
            QPushButton {{
                background-color: {COLORS['background']};
                border: none;
                border-radius: {BORDER_RADIUS['sm']};
                padding: 4px;
                min-width: 28px;
                min-height: 28px;
            }}
            QPushButton:hover {{
                background-color: {COLORS['divider']};
            }}
        """

    def filter_invoices(self, text):
        for row in range(self.table.rowCount()):
            matches = False
            for col in range(4):
                item = self.table.item(row, col)
                if item and text.lower() in item.text().lower():
                    matches = True
                    break
            self.table.setRowHidden(row, not matches)

    def load_invoices(self):
        self.table.setRowCount(0)
        try:
            conn = sqlite3.connect("comptabilite.db")
            cursor = conn.cursor()
            cursor.execute("""
                SELECT f.num_facture, f.date_facture, c.nom, f.montant_total, f.statut
                FROM factures_vente f
                LEFT JOIN clients c ON f.client_id = c.id
                ORDER BY f.date_facture DESC
            """)

            for row_idx, (num, date, client, montant, statut) in enumerate(cursor.fetchall()):
                self.table.insertRow(row_idx)

                # Add basic data
                self.table.setItem(row_idx, 0, QTableWidgetItem(num))
                self.table.setItem(row_idx, 1, QTableWidgetItem(date))
                self.table.setItem(row_idx, 2, QTableWidgetItem(client))
                self.table.setItem(row_idx, 3, QTableWidgetItem(f"{montant:.2f} €"))

                # Add status badge
                status_cell = QWidget()
                status_layout = QHBoxLayout(status_cell)
                status_layout.addWidget(self.create_status_badge(statut))
                status_layout.setAlignment(Qt.AlignCenter)
                status_layout.setContentsMargins(0, 0, 0, 0)
                self.table.setCellWidget(row_idx, 4, status_cell)

                # Add action buttons
                actions = QWidget()
                actions_layout = QHBoxLayout(actions)
                actions_layout.setSpacing(int(SPACING['xs'].replace('px', '')))
                actions_layout.setContentsMargins(0, 0, 0, 0)

                for action in [
                    ("Modifier", ":/icons/edit.png", lambda n=num: self.edit_invoice(n)),
                    ("Imprimer", PRINT_ICON, lambda n=num: self.print_invoice(n))
                ]:
                    btn = QPushButton()
                    btn.setIcon(QIcon(action[1]))
                    btn.setToolTip(action[0])
                    btn.clicked.connect(action[2])
                    btn.setStyleSheet(self._get_action_button_style())
                    actions_layout.addWidget(btn)

                actions_layout.addStretch()
                self.table.setCellWidget(row_idx, 5, actions)

            conn.close()

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur",
                f"Erreur lors du chargement des factures: {e}")

    def show_add_dialog(self):
        """Show dialog to add a new invoice"""
        dialog = BaseFormDialog(self, "Nouvelle Facture")
        dialog.setStyleSheet(f"""
            QDialog {{
                background-color: {COLORS['surface']};
            }}
            QPushButton[type="primary"] {{
                background-color: {COLORS['primary']};
                color: white;
                border: none;
                padding: {SPACING['sm']} {SPACING['md']};
                border-radius: {BORDER_RADIUS['sm']};
            }}
            QPushButton[type="primary"]:hover {{
                background-color: {COLORS['primary_light']};
            }}
            QLineEdit, QComboBox {{
                padding: {SPACING['sm']};
                border: 1px solid {COLORS['divider']};
                border-radius: {BORDER_RADIUS['sm']};
            }}
            QLineEdit:focus, QComboBox:focus {{
                border-color: {COLORS['primary']};
            }}
        """)

        if dialog.exec_() == QDialog.Accepted:
            self.load_invoices()

    def edit_invoice(self, num_facture):
        """Show dialog to edit an existing invoice"""
        dialog = BaseFormDialog(self, f"Modifier la Facture {num_facture}")
        dialog.setStyleSheet(f"""
            QDialog {{
                background-color: {COLORS['surface']};
            }}
            QPushButton[type="primary"] {{
                background-color: {COLORS['primary']};
                color: white;
                border: none;
                padding: {SPACING['sm']} {SPACING['md']};
                border-radius: {BORDER_RADIUS['sm']};
            }}
            QPushButton[type="primary"]:hover {{
                background-color: {COLORS['primary_light']};
            }}
            QLineEdit, QComboBox {{
                padding: {SPACING['sm']};
                border: 1px solid {COLORS['divider']};
                border-radius: {BORDER_RADIUS['sm']};
            }}
            QLineEdit:focus, QComboBox:focus {{
                border-color: {COLORS['primary']};
            }}
        """)

        if dialog.exec_() == QDialog.Accepted:
            self.load_invoices()

    def print_invoice(self, num_facture):
        """Handle invoice printing"""
        try:
            if excel_generator_available:
                generator = ExcelInvoiceGenerator()
                filepath = generator.generate(num_facture)
                if filepath:
                    os.startfile(filepath)
            else:
                QMessageBox.warning(self, "Attention",
                    "Le générateur Excel n'est pas disponible. Veuillez installer les dépendances nécessaires.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur",
                f"Erreur lors de l'impression de la facture: {e}")

    def create_invoice_dialog(self, title, num_facture=None):
        """Create and return an invoice dialog with modern styling"""
        dialog = BaseFormDialog(self, title)

        # Set modern styling
        dialog.setStyleSheet(f"""
            QDialog {{
                background-color: {COLORS['surface']};
                border-radius: {BORDER_RADIUS['lg']};
            }}
            QLabel {{
                color: {COLORS['text_primary']};
            }}
            QLineEdit, QComboBox, QDateEdit {{
                padding: {SPACING['sm']};
                border: 1px solid {COLORS['divider']};
                border-radius: {BORDER_RADIUS['sm']};
                background-color: {COLORS['background']};
                min-height: 32px;
            }}
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus {{
                border-color: {COLORS['primary']};
            }}
            QPushButton[type="primary"] {{
                background-color: {COLORS['primary']};
                color: white;
                border: none;
                padding: {SPACING['sm']} {SPACING['md']};
                border-radius: {BORDER_RADIUS['sm']};
                min-height: 36px;
            }}
            QPushButton[type="primary"]:hover {{
                background-color: {COLORS['primary_light']};
            }}
            QPushButton[type="secondary"] {{
                background-color: {COLORS['background']};
                color: {COLORS['text_primary']};
                border: 1px solid {COLORS['divider']};
                padding: {SPACING['sm']} {SPACING['md']};
                border-radius: {BORDER_RADIUS['sm']};
                min-height: 36px;
            }}
            QPushButton[type="secondary"]:hover {{
                background-color: {COLORS['divider']};
            }}
        """)

        # Create form layout with proper spacing
        form_layout = QVBoxLayout(dialog)
        form_layout.setSpacing(int(SPACING['md'].replace('px', '')))
        form_layout.setContentsMargins(int(SPACING['lg'].replace('px', '')), int(SPACING['lg'].replace('px', '')),
                                     int(SPACING['lg'].replace('px', '')), int(SPACING['lg'].replace('px', '')))

        # Add form fields
        if num_facture:
            # Load existing invoice data
            conn = sqlite3.connect("comptabilite.db")
            cursor = conn.cursor()
            cursor.execute("""
                SELECT f.client_id, f.date_facture, f.montant_total, f.statut,
                       c.nom as client_nom
                FROM factures_vente f
                LEFT JOIN clients c ON f.client_id = c.id
                WHERE f.num_facture = ?
            """, (num_facture,))
            invoice_data = cursor.fetchone()
            conn.close()

            if invoice_data:
                client_id, date_facture, montant, statut, client_nom = invoice_data

                # Pre-fill the form fields
                dialog.add_combo_field("client", "Client", client_nom)
                dialog.add_date_field("date", "Date", QDate.fromString(date_facture, "yyyy-MM-dd"))
                dialog.add_number_field("montant", "Montant Total", montant)
                dialog.add_combo_field("statut", "Statut", statut,
                    ["En attente", "Payée", "Annulée"])
        else:
            # New invoice form
            dialog.add_combo_field("client", "Client")
            dialog.add_date_field("date", "Date", QDate.currentDate())
            dialog.add_number_field("montant", "Montant Total", 0.0)
            dialog.add_combo_field("statut", "Statut", "En attente",
                ["En attente", "Payée", "Annulée"])

        # Add buttons with modern styling
        button_layout = QHBoxLayout()
        button_layout.setSpacing(int(SPACING['sm'].replace('px', '')))

        save_btn = QPushButton("Enregistrer")
        save_btn.setProperty("type", "primary")
        save_btn.clicked.connect(dialog.accept)

        cancel_btn = QPushButton("Annuler")
        cancel_btn.setProperty("type", "secondary")
        cancel_btn.clicked.connect(dialog.reject)

        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(save_btn)
        form_layout.addLayout(button_layout)

        return dialog
