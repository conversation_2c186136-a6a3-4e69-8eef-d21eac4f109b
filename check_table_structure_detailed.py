#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص تفصيلي لبنية جدول المنتجات
"""

import sqlite3
from pathlib import Path

# إعداد المسار لقاعدة البيانات
DB_PATH = Path(__file__).parent / "database"
DATABASE_FILE = DB_PATH / "accounting.db"

def check_table_structure():
    """فحص بنية جدول المنتجات"""
    try:
        conn = sqlite3.connect(str(DATABASE_FILE))
        cursor = conn.cursor()
        
        print("🔍 فحص بنية جدول المنتجات")
        print("=" * 50)
        
        # 1. عرض بنية الجدول
        cursor.execute("PRAGMA table_info(produits)")
        columns = cursor.fetchall()
        
        print("\n📋 أعمدة جدول المنتجات:")
        for i, col in enumerate(columns):
            print(f"  {i+1}. {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - Default: {col[4]}")
        
        # 2. عرض عينة من البيانات
        print(f"\n📊 عينة من البيانات:")
        cursor.execute("SELECT * FROM produits LIMIT 3")
        products = cursor.fetchall()
        
        column_names = [col[1] for col in columns]
        
        for i, product in enumerate(products, 1):
            print(f"\n--- المنتج {i} ---")
            for j, value in enumerate(product):
                if j < len(column_names):
                    print(f"  {column_names[j]}: {value}")
        
        # 3. فحص خاص لحقل المورد
        print(f"\n🔍 فحص حقل المورد:")
        cursor.execute("SELECT id, code, designation, fournisseur FROM produits")
        products = cursor.fetchall()
        
        for product in products:
            print(f"  ID: {product[0]} | Code: {product[1]} | Fournisseur: '{product[3]}'")
        
        # 4. فحص جدول الموردين
        print(f"\n📋 جدول الموردين:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='fournisseurs'")
        if cursor.fetchone():
            cursor.execute("SELECT id, nom FROM fournisseurs LIMIT 5")
            suppliers = cursor.fetchall()
            for supplier in suppliers:
                print(f"  ID: {supplier[0]} | Nom: {supplier[1]}")
        else:
            print("  ❌ جدول الموردين غير موجود")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الفحص: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_table_structure()