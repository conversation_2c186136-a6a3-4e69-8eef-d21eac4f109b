import sqlite3
import os
import datetime
from .migration_bons_commande import create_bons_commande_tables

class DatabaseManager:
    def __init__(self, db_path="database/comptabilite.db"):
        self.db_path = db_path
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        self.conn = None
        self.connect()

    def connect(self):
        self.conn = sqlite3.connect(self.db_path)
        self.conn.row_factory = sqlite3.Row

    def setup_database(self):
        cursor = self.conn.cursor()

        # Création des tables
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS clients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE,
            nom TEXT NOT NULL,
            ice TEXT,
            if_fiscal TEXT,
            adresse TEXT,
            telephone TEXT,
            email TEXT,
            contact TEXT
        )
        ''')

        cursor.execute('''
        CREATE TABLE IF NOT EXISTS fournisseurs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE,
            nom TEXT NOT NULL,
            ice TEXT,
            if_fiscal TEXT,
            adresse TEXT,
            telephone TEXT,
            email TEXT,
            contact TEXT
        )
        ''')

        # Table des familles de produits
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS familles_produits (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT UNIQUE NOT NULL,
            description TEXT,
            date_creation TEXT DEFAULT CURRENT_DATE
        )
        ''')

        cursor.execute('''
        CREATE TABLE IF NOT EXISTS produits (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE,
            designation TEXT NOT NULL,
            unite TEXT,
            prix_achat REAL,
            prix_vente REAL,
            stock INTEGER DEFAULT 0,
            stock_initial INTEGER DEFAULT 0,
            date_creation TEXT DEFAULT CURRENT_DATE,
            date_paiement TEXT,
            famille_id INTEGER,
            FOREIGN KEY (famille_id) REFERENCES familles_produits (id)
        )
        ''')

        cursor.execute('''
        CREATE TABLE IF NOT EXISTS factures (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            numero TEXT NOT NULL,
            date_creation TEXT NOT NULL,
            date_echeance TEXT,
            client_id INTEGER,
            total_ht REAL NOT NULL,
            total_tva REAL NOT NULL,
            total_ttc REAL NOT NULL,
            statut TEXT DEFAULT 'En attente',
            notes TEXT,
            FOREIGN KEY (client_id) REFERENCES clients (id)
        )
        ''')

        cursor.execute('''
        CREATE TABLE IF NOT EXISTS lignes_facture (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            facture_id INTEGER NOT NULL,
            produit_id INTEGER,
            designation TEXT NOT NULL,
            unite TEXT,
            quantite INTEGER NOT NULL,
            prix_unitaire_ht REAL NOT NULL,
            taux_tva REAL NOT NULL,
            total_ht REAL NOT NULL,
            FOREIGN KEY (facture_id) REFERENCES factures (id),
            FOREIGN KEY (produit_id) REFERENCES produits (id)
        )
        ''')

        cursor.execute('''
        CREATE TABLE IF NOT EXISTS commandes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            numero TEXT NOT NULL,
            date_creation TEXT NOT NULL,
            date_livraison TEXT,
            fournisseur_id INTEGER,
            client_id INTEGER,
            total_ht REAL NOT NULL,
            total_tva REAL NOT NULL,
            total_ttc REAL NOT NULL,
            statut TEXT DEFAULT 'En attente',
            type TEXT DEFAULT 'commande',
            notes TEXT,
            FOREIGN KEY (fournisseur_id) REFERENCES fournisseurs (id),
            FOREIGN KEY (client_id) REFERENCES clients (id)
        )
        ''')

        cursor.execute('''
        CREATE TABLE IF NOT EXISTS lignes_commande (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            commande_id INTEGER NOT NULL,
            produit_id INTEGER,
            designation TEXT NOT NULL,
            quantite INTEGER NOT NULL,
            prix_unitaire_ht REAL NOT NULL,
            taux_tva REAL NOT NULL,
            total_ht REAL NOT NULL,
            FOREIGN KEY (commande_id) REFERENCES commandes (id),
            FOREIGN KEY (produit_id) REFERENCES produits (id)
        )
        ''')

        # Tables pour les factures d'achat
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS factures_achat (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            numero TEXT NOT NULL,
            date_creation TEXT NOT NULL,
            fournisseur_id INTEGER,
            mode_paiement TEXT NOT NULL,
            total_ht REAL NOT NULL,
            notes TEXT,
            FOREIGN KEY (fournisseur_id) REFERENCES fournisseurs (id)
        )
        ''')

        cursor.execute('''
        CREATE TABLE IF NOT EXISTS lignes_facture_achat (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            facture_achat_id INTEGER NOT NULL,
            produit_id INTEGER,
            designation TEXT NOT NULL,
            unite TEXT,
            quantite INTEGER NOT NULL,
            prix_unitaire_ht REAL NOT NULL,
            total_ht REAL NOT NULL,
            FOREIGN KEY (facture_achat_id) REFERENCES factures_achat (id),
            FOREIGN KEY (produit_id) REFERENCES produits (id)
        )
        ''')

        # Table pour les mouvements de stock
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS mouvements_stock (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            produit_id INTEGER NOT NULL,
            type_mouvement TEXT NOT NULL CHECK (type_mouvement IN ('entree', 'sortie')),
            quantite INTEGER NOT NULL,
            prix_unitaire REAL,
            date_mouvement TEXT DEFAULT CURRENT_DATE,
            reference TEXT,
            notes TEXT,
            FOREIGN KEY (produit_id) REFERENCES produits (id)
        )
        ''')

        # Création des tables pour les bons de commande
        create_bons_commande_tables(cursor)

        # Tables pour la gestion de caisse
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS entrees_caisse (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            nature TEXT,
            objet TEXT,
            reference TEXT,
            montant REAL NOT NULL,
            date_creation TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        cursor.execute('''
        CREATE TABLE IF NOT EXISTS sorties_caisse (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            nature TEXT,
            objet TEXT,
            reference TEXT,
            montant REAL NOT NULL,
            date_creation TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Vérifier et ajouter les colonnes manquantes
        self.add_missing_columns()

        self.conn.commit()

    def add_missing_columns(self):
        """Ajoute les colonnes manquantes aux tables existantes"""
        cursor = self.conn.cursor()

        # Vérifier la table lignes_facture
        cursor.execute("PRAGMA table_info(lignes_facture)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        if 'unite' not in column_names:
            try:
                cursor.execute("ALTER TABLE lignes_facture ADD COLUMN unite TEXT")
                print("Colonne 'unite' ajoutée à la table lignes_facture")
            except sqlite3.Error as e:
                print(f"Erreur lors de l'ajout de la colonne 'unite': {str(e)}")

        # Vérifier la table produits
        cursor.execute("PRAGMA table_info(produits)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        if 'stock_initial' not in column_names:
            try:
                cursor.execute("ALTER TABLE produits ADD COLUMN stock_initial INTEGER DEFAULT 0")
                print("Colonne 'stock_initial' ajoutée à la table produits")
            except sqlite3.Error as e:
                print(f"Erreur lors de l'ajout de la colonne 'stock_initial': {str(e)}")

        if 'date_creation' not in column_names:
            try:
                cursor.execute("ALTER TABLE produits ADD COLUMN date_creation TEXT DEFAULT CURRENT_DATE")
                print("Colonne 'date_creation' ajoutée à la table produits")
            except sqlite3.Error as e:
                print(f"Erreur lors de l'ajout de la colonne 'date_creation': {str(e)}")

        if 'date_paiement' not in column_names:
            try:
                cursor.execute("ALTER TABLE produits ADD COLUMN date_paiement TEXT")
                print("Colonne 'date_paiement' ajoutée à la table produits")
            except sqlite3.Error as e:
                print(f"Erreur lors de l'ajout de la colonne 'date_paiement': {str(e)}")

        # Vérifier les tables de factures pour date_paiement
        for table_name in ['factures_achat', 'factures_vente']:
            try:
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                column_names = [column[1] for column in columns]

                if 'date_paiement' not in column_names:
                    try:
                        cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN date_paiement TEXT")
                        print(f"Colonne 'date_paiement' ajoutée à la table {table_name}")
                    except sqlite3.Error as e:
                        print(f"Erreur lors de l'ajout de la colonne 'date_paiement' à {table_name}: {str(e)}")
            except sqlite3.Error:
                # La table n'existe peut-être pas encore
                pass

        # Vérifier la table mouvements_stock pour les colonnes manquantes
        try:
            cursor.execute("PRAGMA table_info(mouvements_stock)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            # Ajouter facture_id pour lier les mouvements aux factures
            if 'facture_id' not in column_names:
                try:
                    cursor.execute("ALTER TABLE mouvements_stock ADD COLUMN facture_id INTEGER")
                    print("Colonne 'facture_id' ajoutée à la table mouvements_stock")
                except sqlite3.Error as e:
                    print(f"Erreur lors de l'ajout de la colonne 'facture_id': {str(e)}")

            # Ajouter designation pour faciliter les recherches
            if 'designation' not in column_names:
                try:
                    cursor.execute("ALTER TABLE mouvements_stock ADD COLUMN designation TEXT")
                    print("Colonne 'designation' ajoutée à la table mouvements_stock")
                except sqlite3.Error as e:
                    print(f"Erreur lors de l'ajout de la colonne 'designation': {str(e)}")

        except sqlite3.Error as e:
            print(f"Erreur lors de la vérification de la table mouvements_stock: {str(e)}")

    def generer_numero_facture(self):
        """Génère un numéro de facture unique basé sur la date et un compteur"""
        date_str = datetime.datetime.now().strftime("%Y%m")
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM factures WHERE numero LIKE ?", (f"F{date_str}%",))
        count = cursor.fetchone()[0] + 1
        return f"F{date_str}-{count:03d}"

    def generer_numero_commande(self):
        """Génère un numéro de commande unique basé sur la date et un compteur"""
        date_str = datetime.datetime.now().strftime("%Y%m")
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM commandes WHERE numero LIKE ?", (f"C{date_str}%",))
        count = cursor.fetchone()[0] + 1
        return f"C{date_str}-{count:03d}"

    def generer_numero_facture_achat(self):
        """Génère un numéro de facture d'achat unique basé sur la date et un compteur"""
        date_str = datetime.datetime.now().strftime("%Y%m")
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM factures_achat WHERE numero LIKE ?", (f"FAC-A-{date_str}%",))
        count = cursor.fetchone()[0] + 1
        return f"FAC-A-{date_str}-{count:03d}"

    def generer_numero_bon_commande(self):
        """Génère un numéro de bon de commande unique au format BC-MMYY-XXX"""
        now = datetime.datetime.now()
        prefix = f"BC-{now.month:02d}{now.year % 100:02d}-"

        cursor = self.conn.cursor()
        cursor.execute("SELECT numero FROM bons_commande WHERE numero LIKE ? ORDER BY numero DESC LIMIT 1",
                      (f"{prefix}%",))
        result = cursor.fetchone()

        if result:
            # Extraire le numéro séquentiel et l'incrémenter
            last_num = result[0]
            seq_num = int(last_num.split('-')[-1]) + 1
        else:
            # Premier bon de commande pour ce mois/année
            seq_num = 1

        # Formater le nouveau numéro
        return f"{prefix}{seq_num:03d}"

    def generer_code_client(self):
        """جينيرير كود عميل بناءً على عدد العملاء"""
        cursor = self.conn.cursor()
        # عد جميع العملاء الموجودين
        cursor.execute("SELECT COUNT(*) FROM clients")
        count = cursor.fetchone()[0]

        # الرقم التالي هو عدد العملاء + 1
        next_number = count + 1
        return f"C{next_number:03d}"

    def generer_code_fournisseur(self):
        """جينيرير كود مورد بناءً على عدد الموردين"""
        cursor = self.conn.cursor()
        # عد جميع الموردين الموجودين
        cursor.execute("SELECT COUNT(*) FROM fournisseurs")
        count = cursor.fetchone()[0]

        # الرقم التالي هو عدد الموردين + 1
        next_number = count + 1
        return f"F{next_number:03d}"

    def generer_code_produit(self):
        """جينيرير كود برودويت بناءً على عدد المنتجات"""
        cursor = self.conn.cursor()
        # عد جميع المنتجات الموجودة
        cursor.execute("SELECT COUNT(*) FROM produits")
        count = cursor.fetchone()[0]

        # الرقم التالي هو عدد المنتجات + 1
        next_number = count + 1
        return f"P{next_number:03d}"

    def renumber_all_products(self):
        """إعادة ترقيم جميع المنتجات تلقائياً بترتيب متسلسل"""
        try:
            cursor = self.conn.cursor()

            # الحصول على جميع المنتجات مرتبة حسب ID
            cursor.execute("SELECT id, code FROM produits ORDER BY id ASC")
            products = cursor.fetchall()

            # إعادة ترقيم كل منتج
            for index, product in enumerate(products, start=1):
                product_id = product['id']
                old_code = product['code']
                new_code = f"P{index:03d}"
                if old_code != new_code:
                    cursor.execute("UPDATE produits SET code = ? WHERE id = ?", (new_code, product_id))
                    print(f"⚙️  تغيير كود المنتج: {old_code} → {new_code}")

            self.conn.commit()
            print(f"✅ تم إعادة ترقيم {len(products)} منتج بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إعادة الترقيم: {e}")

    def renumber_all_clients(self):
        """إعادة ترقيم جميع العملاء تلقائياً بترتيب متسلسل"""
        try:
            cursor = self.conn.cursor()

            # الحصول على جميع العملاء مرتبين حسب ID
            cursor.execute("SELECT id, code FROM clients ORDER BY id ASC")
            clients = cursor.fetchall()

            # إعادة ترقيم كل عميل
            for index, client in enumerate(clients, start=1):
                client_id = client['id']
                old_code = client['code']
                new_code = f"C{index:03d}"
                if old_code != new_code:
                    cursor.execute("UPDATE clients SET code = ? WHERE id = ?", (new_code, client_id))
                    print(f"⚙️  تغيير كود العميل: {old_code} → {new_code}")

            self.conn.commit()
            print(f"✅ تم إعادة ترقيم {len(clients)} عميل بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إعادة ترقيم العملاء: {e}")

    def renumber_all_fournisseurs(self):
        """إعادة ترقيم جميع الموردين تلقائياً بترتيب متسلسل"""
        try:
            cursor = self.conn.cursor()

            # الحصول على جميع الموردين مرتبين حسب ID
            cursor.execute("SELECT id, code FROM fournisseurs ORDER BY id ASC")
            fournisseurs = cursor.fetchall()

            # إعادة ترقيم كل مورد
            for index, fournisseur in enumerate(fournisseurs, start=1):
                fournisseur_id = fournisseur['id']
                old_code = fournisseur['code']
                new_code = f"F{index:03d}"
                if old_code != new_code:
                    cursor.execute("UPDATE fournisseurs SET code = ? WHERE id = ?", (new_code, fournisseur_id))
                    print(f"⚙️  تغيير كود المورد: {old_code} → {new_code}")

            self.conn.commit()
            print(f"✅ تم إعادة ترقيم {len(fournisseurs)} مورد بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إعادة ترقيم الموردين: {e}")

    # Méthodes pour la gestion des familles de produits
    def get_familles_produits(self):
        """Récupère toutes les familles de produits"""
        cursor = self.conn.cursor()
        cursor.execute("SELECT * FROM familles_produits ORDER BY nom")
        return cursor.fetchall()

    def ajouter_famille_produit(self, nom, description=None):
        """Ajoute une nouvelle famille de produit"""
        cursor = self.conn.cursor()
        try:
            cursor.execute(
                "INSERT INTO familles_produits (nom, description) VALUES (?, ?)",
                (nom, description)
            )
            self.conn.commit()
            return cursor.lastrowid
        except sqlite3.IntegrityError:
            raise ValueError(f"Une famille avec le nom '{nom}' existe déjà")

    def supprimer_famille_produit(self, famille_id):
        """Supprime une famille de produit"""
        cursor = self.conn.cursor()
        # Vérifier s'il y a des produits associés
        cursor.execute("SELECT COUNT(*) as count FROM produits WHERE famille_id = ?", (famille_id,))
        count = cursor.fetchone()['count']

        if count > 0:
            raise ValueError(f"Impossible de supprimer cette famille car {count} produit(s) y sont associés")

        cursor.execute("DELETE FROM familles_produits WHERE id = ?", (famille_id,))
        self.conn.commit()
        return cursor.rowcount > 0