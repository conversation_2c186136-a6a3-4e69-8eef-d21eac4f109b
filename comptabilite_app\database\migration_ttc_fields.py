import sqlite3
import os

def migrate_ttc_fields():
    """
    Ajoute les colonnes TTC et taux de TVA à la table produits si elles n'existent pas déjà.
    """
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'comptabilite.db')

    # Vérifier si le répertoire data existe, sinon le créer
    data_dir = os.path.dirname(db_path)
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)

    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    try:
        # Vérifier si la table produits existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='produits'")
        if cursor.fetchone():
            # Vérifier les colonnes existantes
            cursor.execute("PRAGMA table_info(produits)")
            columns = cursor.fetchall()
            column_names = [column['name'] for column in columns]

            # Ajouter les colonnes TTC si elles n'existent pas
            if 'prix_achat_ttc' not in column_names:
                print("Migration: Ajout de la colonne prix_achat_ttc à la table produits...")
                cursor.execute("ALTER TABLE produits ADD COLUMN prix_achat_ttc REAL DEFAULT 0")

            if 'prix_vente_ttc' not in column_names:
                print("Migration: Ajout de la colonne prix_vente_ttc à la table produits...")
                cursor.execute("ALTER TABLE produits ADD COLUMN prix_vente_ttc REAL DEFAULT 0")

            if 'tva_rate' not in column_names:
                print("Migration: Ajout de la colonne tva_rate à la table produits...")
                cursor.execute("ALTER TABLE produits ADD COLUMN tva_rate REAL DEFAULT 20.0")

            # Mettre à jour les prix TTC existants basés sur les prix HT avec TVA 20%
            print("Migration: Calcul des prix TTC pour les produits existants...")
            cursor.execute("""
                UPDATE produits 
                SET prix_achat_ttc = prix_achat * 1.2,
                    prix_vente_ttc = prix_vente * 1.2,
                    tva_rate = 20.0
                WHERE prix_achat_ttc = 0 OR prix_achat_ttc IS NULL
            """)

            conn.commit()
            print("✅ Migration des champs TTC terminée avec succès.")
        else:
            print("❌ La table produits n'existe pas.")

    except Exception as e:
        print(f"❌ Erreur lors de la migration: {str(e)}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    migrate_ttc_fields()
