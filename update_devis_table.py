#!/usr/bin/env python3
"""
تحديث جدول devis لإضافة الحقول الجديدة المطلوبة
"""
import sqlite3
import os

def update_devis_table():
    """تحديث جدول devis بالحقول الجديدة"""

    # مسار قاعدة البيانات
    db_path = os.path.join(os.getcwd(), "database", "comptabilite.db")
    print(f"📁 مسار قاعدة البيانات: {db_path}")

    # إنشاء مجلد database إذا لم يكن موجوداً
    os.makedirs(os.path.dirname(db_path), exist_ok=True)

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        print("🔄 تحديث جدول devis...")

        # حذف الجدول القديم وإنشاء جدول جديد محسن
        cursor.execute("DROP TABLE IF EXISTS devis")

        # إنشاء جدول devis جديد مع جميع الحقول المطلوبة
        cursor.execute("""
            CREATE TABLE devis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                numero TEXT NOT NULL,
                client TEXT,
                date_creation TEXT,
                montant_ht REAL,
                tva REAL,
                montant_ttc REAL,
                statut TEXT DEFAULT 'En attente',
                objet TEXT,
                adresse_livraison TEXT,
                ice_client TEXT,
                type_marche TEXT DEFAULT 'BC',
                numero_bl_auto TEXT,
                numero_manuel TEXT,
                nature_prestation TEXT,
                delai_execution TEXT,
                caution_provisoire REAL,
                date_notification TEXT,
                caution_definitif REAL,
                ordre_service TEXT,
                caution_retenu REAL,
                date_achevement TEXT
            )
        """)
        print("✅ جدول devis الجديد تم إنشاؤه")

        # إدراج بيانات تجريبية محسنة
        sample_devis = [
            ("DEV001", "Client A", "2024-01-15", 10000.00, 2000.00, 12000.00, "En attente",
             "Fourniture matériel informatique", "123 Rue Example", "ICE001", "BC",
             "BL202501-001", "001", "Fourniture", "30 jours", 1200.00, "2024-02-01",
             2400.00, "OS001", 600.00, "2024-03-15"),

            ("DEV002", "Client B", "2024-01-10", 5000.00, 1000.00, 6000.00, "Accepté",
             "Services de maintenance", "456 Avenue Test", "ICE002", "marché",
             "BL202501-002", "002", "Services", "15 jours", 600.00, "2024-01-25",
             1200.00, "OS002", 300.00, "2024-02-10"),

            ("DEV003", "Client C", "2024-01-20", 15000.00, 3000.00, 18000.00, "En cours",
             "Installation réseau", "789 Boulevard Demo", "ICE003", "BC",
             "BL202501-003", "003", "Installation", "45 jours", 1800.00, "2024-02-15",
             3600.00, "OS003", 900.00, "2024-04-01")
        ]

        for devis in sample_devis:
            cursor.execute("""
                INSERT INTO devis (numero, client, date_creation, montant_ht, tva, montant_ttc, statut,
                                 objet, adresse_livraison, ice_client, type_marche, numero_bl_auto,
                                 numero_manuel, nature_prestation, delai_execution, caution_provisoire,
                                 date_notification, caution_definitif, ordre_service, caution_retenu, date_achevement)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, devis)

        print("✅ تم إدراج البيانات التجريبية")

        # إنشاء جدول bons_livraison إذا لم يكن موجوداً
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS bons_livraison (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                numero TEXT NOT NULL,
                date_creation TEXT NOT NULL,
                type_marche TEXT DEFAULT 'BC',
                devis_number TEXT,
                ice_client TEXT,
                adresse_livraison TEXT,
                statut TEXT DEFAULT 'En cours'
            )
        """)
        print("✅ جدول bons_livraison تم إنشاؤه/تحديثه")

        conn.commit()
        conn.close()

        print("🎉 تم تحديث قاعدة البيانات بنجاح!")

        # عرض البيانات المدرجة
        print("\n📊 البيانات التجريبية المدرجة:")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT numero, client, type_marche, numero_bl_auto, nature_prestation FROM devis")
        for row in cursor.fetchall():
            print(f"   {row[0]} - {row[1]} - {row[2]} - {row[3]} - {row[4]}")
        conn.close()

    except Exception as e:
        print(f"❌ خطأ في التحديث: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    update_devis_table()
