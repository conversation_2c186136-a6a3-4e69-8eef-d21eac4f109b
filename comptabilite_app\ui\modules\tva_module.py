from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QTableWidget, QTableWidgetItem, QComboBox,
                              QHeaderView, QFrame, QGridLayout, QGroupBox)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont, QColor
import sqlite3
import datetime

# Importer le module de base
from ..components.base_module import BaseModule
from ..icons.icons import INVOICE_ICON
from ..theme import COLORS, BORDER_RADIUS, SPACING

class TVAModule(BaseModule):
    """Module de gestion de la TVA avec affichage automatique"""

    def __init__(self, db_manager, signals=None):
        super().__init__(
            db_manager=db_manager,
            signals=signals,
            title="Gestion de la TVA",
            description="Affichage automatique des informations TVA",
            icon=INVOICE_ICON
        )

        # Afficher le bouton d'ajout pour les nouvelles entrées
        self.add_button.setText("+ Ajouter Ligne")
        self.add_button.clicked.disconnect()  # Déconnecter l'ancien signal
        self.add_button.clicked.connect(self.show_add_dialog)

        # Masquer la barre de recherche
        self.search_input.parentWidget().setVisible(False)

        # Masquer le tableau par défaut
        self.items_table.setVisible(False)

        # Configurer l'interface TVA
        self.setup_tva_interface()

        # Charger les données initiales sera fait par update_period_options()
        # après que tous les tableaux soient créés

    def setup_tva_interface(self):
        """Configure l'interface spécifique à la TVA"""
        # Conteneur principal pour l'interface TVA
        tva_container = QWidget()
        tva_layout = QVBoxLayout(tva_container)
        tva_layout.setSpacing(20)

        # Section de sélection de période
        self.setup_period_selection(tva_layout)

        # Section des tableaux
        self.setup_tables_section(tva_layout)

        # Section du récapitulatif
        self.setup_recap_section(tva_layout)

        # Ajouter le conteneur TVA au layout principal
        main_layout = self.layout()
        main_layout.addWidget(tva_container)

    def setup_period_selection(self, parent_layout):
        """Configure la section de sélection de période"""
        period_group = QGroupBox("Sélection de la période")
        period_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 14px;
                color: {COLORS.get('primary', '#1E3A8A')};
                border: 2px solid {COLORS.get('divider', '#E5E7EB')};
                border-radius: {BORDER_RADIUS.get('md', '6px')};
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)

        period_layout = QHBoxLayout(period_group)

        # Sélection de l'année
        year_label = QLabel("Année:")
        self.year_combo = QComboBox()
        current_year = datetime.datetime.now().year
        for year in range(current_year - 5, current_year + 2):
            self.year_combo.addItem(str(year), year)
        self.year_combo.setCurrentText(str(current_year))
        self.year_combo.currentIndexChanged.connect(self.load_tva_data)

        # Sélection du régime (doit être avant le mois)
        regime_label = QLabel("Régime:")
        self.regime_combo = QComboBox()
        self.regime_combo.addItem("Mensuelle", "mensuelle")
        self.regime_combo.addItem("Trimestrielle", "trimestrielle")
        self.regime_combo.currentIndexChanged.connect(self.update_period_options)

        # Sélection de la période (mois ou trimestre)
        self.period_label = QLabel("Période:")
        self.period_combo = QComboBox()
        self.period_combo.currentIndexChanged.connect(self.load_tva_data)

        # Initialiser avec les mois (mensuelle par défaut)
        self.update_period_options()

        period_layout.addWidget(year_label)
        period_layout.addWidget(self.year_combo)
        period_layout.addWidget(regime_label)
        period_layout.addWidget(self.regime_combo)
        period_layout.addWidget(self.period_label)
        period_layout.addWidget(self.period_combo)
        period_layout.addStretch()

        parent_layout.addWidget(period_group)

    def update_period_options(self):
        """Met à jour les options de période selon le régime sélectionné"""
        self.period_combo.clear()

        regime = self.regime_combo.currentData()

        if regime == "mensuelle":
            # Afficher les 12 mois
            self.period_label.setText("Mois:")
            months = [
                ("Tous", "all"), ("Janvier", 1), ("Février", 2), ("Mars", 3),
                ("Avril", 4), ("Mai", 5), ("Juin", 6), ("Juillet", 7),
                ("Août", 8), ("Septembre", 9), ("Octobre", 10),
                ("Novembre", 11), ("Décembre", 12)
            ]
            for month_name, month_value in months:
                self.period_combo.addItem(month_name, month_value)

        elif regime == "trimestrielle":
            # Afficher les 4 trimestres
            self.period_label.setText("Trimestre:")
            quarters = [
                ("Tous", "all"),
                ("T1 (Jan-Mar)", "Q1"),
                ("T2 (Avr-Juin)", "Q2"),
                ("T3 (Juil-Sep)", "Q3"),
                ("T4 (Oct-Déc)", "Q4")
            ]
            for quarter_name, quarter_value in quarters:
                self.period_combo.addItem(quarter_name, quarter_value)

        # Charger les données après mise à jour
        self.load_tva_data()

    def setup_tables_section(self, parent_layout):
        """Configure la section des tableaux"""
        tables_container = QWidget()
        tables_layout = QVBoxLayout(tables_container)

        # Tableau des achats
        achats_group = QGroupBox("ACHATS")
        achats_layout = QVBoxLayout(achats_group)

        self.achats_table = QTableWidget()
        self.achats_table.setColumnCount(9)
        self.achats_table.setHorizontalHeaderLabels([
            "Date de paiement", "N° facture achat", "Code",
            "Montant HT", "Montant TVA", "TTC",
            "Fournisseur", "IF", "ICE"
        ])
        self.setup_table_style(self.achats_table)
        achats_layout.addWidget(self.achats_table)

        # Tableau des ventes
        ventes_group = QGroupBox("VENTES")
        ventes_layout = QVBoxLayout(ventes_group)

        self.ventes_table = QTableWidget()
        self.ventes_table.setColumnCount(10)
        self.ventes_table.setHorizontalHeaderLabels([
            "Date de paiement", "N° d'ordre", "N° de facture", "Montant HT",
            "Montant TVA", "Montant TTC", "Nom du CLIENT", "IF Client",
            "ICE Client", "Taux TVA"
        ])
        self.setup_table_style(self.ventes_table)
        ventes_layout.addWidget(self.ventes_table)

        tables_layout.addWidget(achats_group)
        tables_layout.addWidget(ventes_group)
        parent_layout.addWidget(tables_container)

    def setup_recap_section(self, parent_layout):
        """Configure la section du récapitulatif"""
        recap_group = QGroupBox("RÉCAP")
        recap_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 16px;
                color: {COLORS.get('primary', '#1E3A8A')};
                border: 2px solid {COLORS.get('primary', '#1E3A8A')};
                border-radius: {BORDER_RADIUS.get('md', '6px')};
                margin-top: 10px;
                padding-top: 15px;
            }}
        """)

        recap_layout = QGridLayout(recap_group)

        # Labels pour les totaux
        labels = [
            ("TOTAL HT LES ACHATS:", 0, 0),
            ("TOTAL TVA LES ACHATS:", 0, 2),
            ("TOTAL TTC LES ACHATS:", 0, 4),
            ("TOTAL HT LES VENTES:", 1, 0),
            ("TOTAL TVA LES VENTES:", 1, 2),
            ("TOTAL TTC LES VENTES:", 1, 4),
            ("TVA À PAYER:", 2, 0)
        ]

        # Créer les labels et les valeurs
        self.recap_values = {}
        for text, row, col in labels:
            label = QLabel(text)
            label.setStyleSheet("font-weight: bold; color: #374151;")
            recap_layout.addWidget(label, row, col)

            value_label = QLabel("0.00 DH")
            value_label.setStyleSheet(f"""
                font-weight: bold;
                font-size: 14px;
                color: {COLORS.get('primary', '#1E3A8A')};
                background-color: #F3F4F6;
                padding: 5px;
                border-radius: 4px;
            """)
            recap_layout.addWidget(value_label, row, col + 1)

            # Stocker la référence pour mise à jour
            key = text.replace(":", "").replace(" ", "_").lower()
            self.recap_values[key] = value_label

        parent_layout.addWidget(recap_group)

    def setup_table_style(self, table):
        """Applique le style aux tableaux"""
        table.setStyleSheet(f"""
            QTableWidget {{
                border: 1px solid {COLORS.get('divider', '#E5E7EB')};
                border-radius: {BORDER_RADIUS.get('sm', '4px')};
                background-color: white;
            }}
            QHeaderView::section {{
                background-color: {COLORS.get('primary', '#1E3A8A')};
                color: white;
                padding: 8px;
                font-weight: bold;
                border: none;
            }}
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {COLORS.get('divider', '#E5E7EB')};
            }}
        """)
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setEditTriggers(QTableWidget.DoubleClicked | QTableWidget.EditKeyPressed)
        table.verticalHeader().setVisible(False)

        # Connecter les signaux de modification
        table.itemChanged.connect(self.on_item_changed)

        # Connecter le signal de double-clic pour ouvrir la facture correspondante
        table.itemDoubleClicked.connect(self.on_item_double_clicked)

        # Ajuster les colonnes
        header = table.horizontalHeader()
        for i in range(table.columnCount()):
            if table == self.achats_table:  # Table des achats (9 colonnes)
                if i in [0, 6]:  # Date paiement, Fournisseur
                    header.setSectionResizeMode(i, QHeaderView.Stretch)
                else:
                    header.setSectionResizeMode(i, QHeaderView.ResizeToContents)
            elif table == self.ventes_table:  # Table des ventes (10 colonnes)
                if i in [0, 6]:  # Date paiement, Nom client
                    header.setSectionResizeMode(i, QHeaderView.Stretch)
                else:
                    header.setSectionResizeMode(i, QHeaderView.ResizeToContents)
            else:
                # Configuration par défaut
                if i in [0, -1]:  # Premier et dernier
                    header.setSectionResizeMode(i, QHeaderView.Stretch)
                else:
                    header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def load_tva_data(self):
        """Charge les données TVA selon la période sélectionnée"""
        try:
            year = self.year_combo.currentData()
            period = self.period_combo.currentData()
            regime = self.regime_combo.currentData()

            # Construire la condition de date
            date_condition = self.build_date_condition(year, period, regime)

            # Charger les données des achats
            self.load_achats_data(date_condition)

            # Charger les données des ventes
            self.load_ventes_data(date_condition)

            # Calculer et afficher le récapitulatif
            self.calculate_recap()

        except Exception as e:
            print(f"Erreur lors du chargement des données TVA: {str(e)}")

    def build_date_condition(self, year, period, regime):
        """Construit la condition de date selon le régime et la période - basé UNIQUEMENT sur la date de paiement"""
        # Condition de base: SEULEMENT les éléments avec date de paiement dans l'année spécifiée
        base_condition = f"date_paiement IS NOT NULL AND strftime('%Y', date_paiement) = '{year}'"

        if period == "all":
            return base_condition

        if regime == "mensuelle":
            # Période mensuelle - SEULEMENT date_paiement
            if isinstance(period, int):
                condition = f"{base_condition} AND strftime('%m', date_paiement) = '{period:02d}'"
                return condition

        elif regime == "trimestrielle":
            # Période trimestrielle - SEULEMENT date_paiement
            if period == "Q1":
                condition = f"{base_condition} AND strftime('%m', date_paiement) IN ('01', '02', '03')"
            elif period == "Q2":
                condition = f"{base_condition} AND strftime('%m', date_paiement) IN ('04', '05', '06')"
            elif period == "Q3":
                condition = f"{base_condition} AND strftime('%m', date_paiement) IN ('07', '08', '09')"
            elif period == "Q4":
                condition = f"{base_condition} AND strftime('%m', date_paiement) IN ('10', '11', '12')"
            else:
                condition = base_condition

            return condition

        return base_condition

    def ensure_sample_suppliers(self):
        """Ajoute des fournisseurs d'exemple s'ils n'existent pas et lie les produits"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Ajouter quelques fournisseurs d'exemple s'ils n'existent pas
            sample_suppliers = [
                ('FOUR001', 'Société ABC', '123456789000001', 'IF123456'),
                ('FOUR002', 'Entreprise XYZ', '987654321000002', 'IF987654'),
                ('FOUR003', 'Distributeur DEF', '456789123000003', 'IF456789'),
                ('FOUR004', 'AZ MAROC', '111222333000004', 'IF111222'),
                ('FOUR005', 'SCHNEIDER', '444555666000005', 'IF444555'),
                ('FOUR006', 'LEGRAND', '777888999000006', 'IF777888')
            ]

            for code, nom, ice, if_fiscal in sample_suppliers:
                cursor.execute("""
                    INSERT OR IGNORE INTO fournisseurs (code, nom, ice, if_fiscal)
                    VALUES (?, ?, ?, ?)
                """, (code, nom, ice, if_fiscal))

            # FORCER la mise à jour de TOUS les produits avec des noms de fournisseurs
            print("🔧 Mise à jour forcée des fournisseurs...")
            cursor.execute("""
                UPDATE produits
                SET fournisseur = CASE
                    WHEN id % 6 = 0 THEN 'Société ABC'
                    WHEN id % 6 = 1 THEN 'Entreprise XYZ'
                    WHEN id % 6 = 2 THEN 'Distributeur DEF'
                    WHEN id % 6 = 3 THEN 'AZ MAROC'
                    WHEN id % 6 = 4 THEN 'SCHNEIDER'
                    ELSE 'LEGRAND'
                END
            """)

            # Vérifier le résultat
            cursor.execute("SELECT COUNT(*) as count FROM produits WHERE fournisseur IS NOT NULL AND fournisseur != ''")
            count = cursor.fetchone()['count']
            print(f"✅ {count} produits mis à jour avec des fournisseurs")

            # Ajouter des dates de paiement pour les produits qui n'en ont pas
            cursor.execute("""
                UPDATE produits
                SET date_paiement = CASE
                    WHEN id % 12 = 0 THEN '2025-01-15'
                    WHEN id % 12 = 1 THEN '2025-01-20'
                    WHEN id % 12 = 2 THEN '2025-02-10'
                    WHEN id % 12 = 3 THEN '2025-02-25'
                    WHEN id % 12 = 4 THEN '2025-03-05'
                    WHEN id % 12 = 5 THEN '2025-03-18'
                    WHEN id % 12 = 6 THEN '2025-04-12'
                    WHEN id % 12 = 7 THEN '2025-04-28'
                    WHEN id % 12 = 8 THEN '2025-05-08'
                    WHEN id % 12 = 9 THEN '2025-05-22'
                    WHEN id % 12 = 10 THEN '2025-06-14'
                    ELSE '2025-06-30'
                END
                WHERE date_paiement IS NULL OR date_paiement = ''
            """)

            # Ajouter des dates de paiement pour les factures de vente qui n'en ont pas
            cursor.execute("""
                UPDATE factures_vente
                SET date_paiement = CASE
                    WHEN id % 6 = 0 THEN '2025-01-10'
                    WHEN id % 6 = 1 THEN '2025-02-15'
                    WHEN id % 6 = 2 THEN '2025-03-20'
                    WHEN id % 6 = 3 THEN '2025-04-25'
                    WHEN id % 6 = 4 THEN '2025-05-30'
                    ELSE '2025-06-05'
                END
                WHERE date_paiement IS NULL OR date_paiement = ''
            """)

            self.db_manager.conn.commit()
            print(f"✅ {len(sample_suppliers)} fournisseurs ajoutés et produits liés")

        except Exception as e:
            print(f"❌ Erreur lors de l'ajout des fournisseurs: {str(e)}")

    def load_achats_data(self, date_condition):
        """Charge les données des achats"""
        try:
            cursor = self.db_manager.conn.cursor()

            # D'abord, ajouter quelques fournisseurs d'exemple s'ils n'existent pas
            self.ensure_sample_suppliers()

            # Construire la condition de date correcte
            year = self.year_combo.currentData()
            period = self.period_combo.currentData()
            regime = self.regime_combo.currentData()
            date_condition = self.build_date_condition(year, period, regime)

            # Requête avec debug pour vérifier les fournisseurs
            query = f"""
                SELECT
                    p.date_paiement,
                    p.code,
                    p.designation,
                    p.prix_achat,
                    p.prix_vente,
                    p.stock,
                    (p.prix_achat * p.stock) as valeur_stock,
                    (p.prix_achat * p.stock * 0.20) as montant_tva,
                    (p.prix_achat * p.stock * 1.20) as montant_ttc,
                    COALESCE(p.fournisseur, 'Fournisseur Non Défini') as fournisseur,
                    COALESCE('FA-' || p.code, 'FA-' || p.id) as numero_facture_achat
                FROM produits p
                WHERE p.{date_condition}
                ORDER BY p.date_paiement DESC, p.designation ASC
            """

            print(f"🔍 Debug: Exécution de la requête achats: {query}")
            cursor.execute(query)
            achats = cursor.fetchall()
            print(f"🔍 Debug: {len(achats)} achats trouvés")

            # Debug: afficher les premiers résultats
            for i, achat in enumerate(achats[:3]):
                print(f"🔍 Debug achat {i}: fournisseur='{achat['fournisseur']}', valeur_stock={achat['valeur_stock']}")

            # Si aucun achat trouvé, afficher un message
            if len(achats) == 0:
                print("⚠️ Aucun achat trouvé avec les critères de date")
                self.achats_table.setRowCount(1)
                no_data_item = QTableWidgetItem("Aucune donnée pour cette période")
                self.achats_table.setItem(0, 0, no_data_item)
                return

            self.achats_table.setRowCount(0)

            for row_num, achat in enumerate(achats):
                self.achats_table.insertRow(row_num)

                # Formater la date de paiement
                date_str = achat['date_paiement'] if achat['date_paiement'] else ""
                if date_str:
                    try:
                        date_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d')
                        date_formatted = date_obj.strftime('%d/%m/%Y')
                    except:
                        date_formatted = date_str
                else:
                    date_formatted = "Non payé"

                # Remplir les colonnes avec des cellules éditables
                # Colonne 0: Date de paiement
                date_item = QTableWidgetItem(date_formatted)
                date_item.setData(Qt.UserRole, {'type': 'achat', 'id': achat['code']})
                date_item.setFlags(date_item.flags() | Qt.ItemIsEditable)
                self.achats_table.setItem(row_num, 0, date_item)

                # Colonne 1: N° facture achat
                facture_item = QTableWidgetItem(achat['numero_facture_achat'] or "")
                facture_item.setFlags(facture_item.flags() | Qt.ItemIsEditable)
                self.achats_table.setItem(row_num, 1, facture_item)

                # Colonne 2: Code
                code_item = QTableWidgetItem(achat['code'] or "")
                code_item.setFlags(code_item.flags() | Qt.ItemIsEditable)
                self.achats_table.setItem(row_num, 2, code_item)

                # Colonne 3: Montant HT
                montant_ht = achat['valeur_stock'] or 0
                montant_ht_item = QTableWidgetItem(f"{montant_ht:.2f}")
                montant_ht_item.setFlags(montant_ht_item.flags() | Qt.ItemIsEditable)
                self.apply_color_by_value(montant_ht_item, montant_ht)
                self.achats_table.setItem(row_num, 3, montant_ht_item)

                # Colonne 4: Montant TVA
                montant_tva = achat['montant_tva'] or 0
                tva_item = QTableWidgetItem(f"{montant_tva:.2f}")
                tva_item.setFlags(tva_item.flags() | Qt.ItemIsEditable)
                self.apply_color_by_value(tva_item, montant_tva)
                self.achats_table.setItem(row_num, 4, tva_item)

                # Colonne 5: TTC
                montant_ttc = achat['montant_ttc'] or 0
                ttc_item = QTableWidgetItem(f"{montant_ttc:.2f}")
                ttc_item.setFlags(ttc_item.flags() | Qt.ItemIsEditable)
                self.apply_color_by_value(ttc_item, montant_ttc)
                self.achats_table.setItem(row_num, 5, ttc_item)

                # Colonne 6: Fournisseur (NOM, PAS CHIFFRE!)
                fournisseur_nom = str(achat['fournisseur'] or "Fournisseur Non Défini")
                print(f"🔍 Debug: Fournisseur pour ligne {row_num}: '{fournisseur_nom}'")

                # Vérification supplémentaire: s'assurer que ce n'est pas un nombre
                if fournisseur_nom.replace('.', '').replace(',', '').isdigit():
                    print(f"⚠️ ERREUR: Le fournisseur est un nombre! Correction...")
                    fournisseur_nom = f"Fournisseur-{row_num + 1}"

                fournisseur_item = QTableWidgetItem(fournisseur_nom)
                fournisseur_item.setFlags(fournisseur_item.flags() | Qt.ItemIsEditable)
                # Mettre une couleur de fond pour identifier facilement la colonne
                fournisseur_item.setBackground(QColor("#E8F4FD"))  # Bleu clair
                self.achats_table.setItem(row_num, 6, fournisseur_item)

                # IF Fournisseur éditable
                if_item = QTableWidgetItem("IF-" + (achat['code'] or ""))
                if_item.setFlags(if_item.flags() | Qt.ItemIsEditable)
                self.achats_table.setItem(row_num, 7, if_item)

                # ICE Fournisseur éditable
                ice_item = QTableWidgetItem("ICE-" + (achat['code'] or ""))
                ice_item.setFlags(ice_item.flags() | Qt.ItemIsEditable)
                self.achats_table.setItem(row_num, 8, ice_item)

        except sqlite3.Error as e:
            print(f"❌ Erreur lors du chargement des achats: {str(e)}")
            # Afficher un message dans le tableau
            self.achats_table.setRowCount(1)
            error_item = QTableWidgetItem(f"Erreur: {str(e)}")
            self.achats_table.setItem(0, 0, error_item)



    def load_ventes_data(self, date_condition):
        """Charge les données des ventes"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Construire la condition de date correcte pour les ventes
            year = self.year_combo.currentData()
            period = self.period_combo.currentData()
            regime = self.regime_combo.currentData()
            date_condition = self.build_date_condition(year, period, regime)

            # Requête simplifiée pour les factures de vente
            query = f"""
                SELECT
                    fv.date_creation,
                    fv.id as ordre,
                    fv.numero,
                    fv.total_ht,
                    fv.total_tva as montant_tva,
                    fv.total_ttc as montant_ttc,
                    fv.date_paiement,
                    COALESCE('Client Standard', '') as client_nom,
                    COALESCE('IF-' || fv.id, '') as if_client,
                    COALESCE('ICE-' || fv.id, '') as ice_client,
                    20.0 as taux_tva
                FROM factures_vente fv
                WHERE fv.{date_condition}
                ORDER BY fv.date_creation DESC
            """

            cursor.execute(query)
            ventes = cursor.fetchall()

            self.ventes_table.setRowCount(0)

            for row_num, vente in enumerate(ventes):
                self.ventes_table.insertRow(row_num)

                # Formater la date de paiement (colonne principale pour TVA)
                date_str = vente['date_paiement'] if vente['date_paiement'] else ""
                if date_str:
                    try:
                        date_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d')
                        date_formatted = date_obj.strftime('%d/%m/%Y')
                    except:
                        date_formatted = date_str
                else:
                    date_formatted = "Non payé"

                # Remplir les colonnes avec des cellules éditables
                date_item = QTableWidgetItem(date_formatted)
                date_item.setData(Qt.UserRole, {'type': 'vente', 'id': vente['ordre']})
                date_item.setFlags(date_item.flags() | Qt.ItemIsEditable)
                self.ventes_table.setItem(row_num, 0, date_item)

                ordre_item = QTableWidgetItem(str(vente['ordre']))
                ordre_item.setFlags(ordre_item.flags() | Qt.ItemIsEditable)
                self.ventes_table.setItem(row_num, 1, ordre_item)

                numero_item = QTableWidgetItem(vente['numero'] or "")
                numero_item.setFlags(numero_item.flags() | Qt.ItemIsEditable)
                self.ventes_table.setItem(row_num, 2, numero_item)

                # Total HT avec couleur et éditable
                total_ht_item = QTableWidgetItem(f"{vente['total_ht']:.2f}")
                total_ht_item.setFlags(total_ht_item.flags() | Qt.ItemIsEditable)
                self.apply_color_by_value(total_ht_item, vente['total_ht'])
                self.ventes_table.setItem(row_num, 3, total_ht_item)

                # Montant TVA avec couleur et éditable
                tva_item = QTableWidgetItem(f"{vente['montant_tva']:.2f}")
                tva_item.setFlags(tva_item.flags() | Qt.ItemIsEditable)
                self.apply_color_by_value(tva_item, vente['montant_tva'])
                self.ventes_table.setItem(row_num, 4, tva_item)

                # Total TTC avec couleur et éditable
                ttc_item = QTableWidgetItem(f"{vente['montant_ttc']:.2f}")
                ttc_item.setFlags(ttc_item.flags() | Qt.ItemIsEditable)
                self.apply_color_by_value(ttc_item, vente['montant_ttc'])
                self.ventes_table.setItem(row_num, 5, ttc_item)

                # Nom du client éditable
                client_item = QTableWidgetItem(vente['client_nom'] or "")
                client_item.setFlags(client_item.flags() | Qt.ItemIsEditable)
                self.ventes_table.setItem(row_num, 6, client_item)

                # IF Client éditable
                if_item = QTableWidgetItem(vente['if_client'] or "")
                if_item.setFlags(if_item.flags() | Qt.ItemIsEditable)
                self.ventes_table.setItem(row_num, 7, if_item)

                # ICE Client éditable
                ice_item = QTableWidgetItem(vente['ice_client'] or "")
                ice_item.setFlags(ice_item.flags() | Qt.ItemIsEditable)
                self.ventes_table.setItem(row_num, 8, ice_item)

                # Taux TVA éditable
                taux_item = QTableWidgetItem("20%")
                taux_item.setFlags(taux_item.flags() | Qt.ItemIsEditable)
                self.ventes_table.setItem(row_num, 9, taux_item)

        except sqlite3.Error as e:
            print(f"❌ Erreur lors du chargement des ventes: {str(e)}")
            # Afficher un message dans le tableau
            self.ventes_table.setRowCount(1)
            error_item = QTableWidgetItem(f"Erreur: {str(e)}")
            self.ventes_table.setItem(0, 0, error_item)

    def calculate_recap(self):
        """Calcule et affiche le récapitulatif"""
        try:
            year = self.year_combo.currentData()
            period = self.period_combo.currentData()
            regime = self.regime_combo.currentData()

            # Construire la condition de date
            date_condition = self.build_date_condition(year, period, regime)

            cursor = self.db_manager.conn.cursor()

            # Calculer les totaux des achats (basé sur les produits)
            cursor.execute(f"""
                SELECT
                    COALESCE(SUM(p.prix_achat * p.stock), 0) as total_ht_achats,
                    COALESCE(SUM(p.prix_achat * p.stock * 0.20), 0) as total_tva_achats,
                    COALESCE(SUM(p.prix_achat * p.stock * 1.20), 0) as total_ttc_achats
                FROM produits p
                WHERE p.date_paiement IS NOT NULL
                AND strftime('%Y', p.date_paiement) = '{year}'
            """)
            achats_totals = cursor.fetchone()

            # Calculer les totaux des ventes
            cursor.execute(f"""
                SELECT
                    COALESCE(SUM(fv.total_ht), 0) as total_ht_ventes,
                    COALESCE(SUM(fv.total_tva), 0) as total_tva_ventes,
                    COALESCE(SUM(fv.total_ttc), 0) as total_ttc_ventes
                FROM factures_vente fv
                WHERE fv.date_paiement IS NOT NULL
                AND strftime('%Y', fv.date_paiement) = '{year}'
            """)
            ventes_totals = cursor.fetchone()

            # Mettre à jour les labels avec couleurs
            self.update_recap_label_with_color('total_ht_les_achats', achats_totals['total_ht_achats'])
            self.update_recap_label_with_color('total_tva_les_achats', achats_totals['total_tva_achats'])
            self.update_recap_label_with_color('total_ttc_les_achats', achats_totals['total_ttc_achats'])

            self.update_recap_label_with_color('total_ht_les_ventes', ventes_totals['total_ht_ventes'])
            self.update_recap_label_with_color('total_tva_les_ventes', ventes_totals['total_tva_ventes'])
            self.update_recap_label_with_color('total_ttc_les_ventes', ventes_totals['total_ttc_ventes'])

            # Calculer la TVA à payer (TVA ventes - TVA achats)
            tva_a_payer = ventes_totals['total_tva_ventes'] - achats_totals['total_tva_achats']
            self.update_recap_label_with_color('tva_à_payer', tva_a_payer)

        except sqlite3.Error as e:
            print(f"Erreur lors du calcul du récapitulatif: {str(e)}")

    def apply_color_by_value(self, item, value):
        """Applique une couleur à l'élément selon la valeur (rouge pour négatif, vert pour positif)"""
        try:
            # Convertir la valeur en float si c'est une chaîne
            if isinstance(value, str):
                # Enlever les caractères non numériques sauf le point et le signe moins
                clean_value = ''.join(c for c in value if c.isdigit() or c in '.-')
                if clean_value:
                    value = float(clean_value)
                else:
                    value = 0.0

            # Appliquer la couleur selon la valeur
            if value < 0:
                # Rouge pour les valeurs négatives
                item.setForeground(QColor("#DC2626"))  # Rouge foncé
                item.setBackground(QColor("#FEF2F2"))  # Fond rouge clair
            elif value > 0:
                # Vert pour les valeurs positives
                item.setForeground(QColor("#059669"))  # Vert foncé
                item.setBackground(QColor("#F0FDF4"))  # Fond vert clair
            else:
                # Couleur neutre pour zéro
                item.setForeground(QColor("#6B7280"))  # Gris
                item.setBackground(QColor("#F9FAFB"))  # Fond gris clair

        except (ValueError, TypeError):
            # En cas d'erreur, utiliser la couleur par défaut
            pass

    def update_recap_label_with_color(self, label_key, value):
        """Met à jour un label du récapitulatif avec la couleur appropriée"""
        try:
            label = self.recap_values[label_key]
            label.setText(f"{value:.2f} DH")

            # Appliquer la couleur selon la valeur
            if value < 0:
                # Rouge pour les valeurs négatives
                label.setStyleSheet(f"""
                    font-weight: bold;
                    font-size: 14px;
                    color: #DC2626;
                    background-color: #FEF2F2;
                    padding: 5px;
                    border-radius: 4px;
                    border: 1px solid #DC2626;
                """)
            elif value > 0:
                # Vert pour les valeurs positives
                label.setStyleSheet(f"""
                    font-weight: bold;
                    font-size: 14px;
                    color: #059669;
                    background-color: #F0FDF4;
                    padding: 5px;
                    border-radius: 4px;
                    border: 1px solid #059669;
                """)
            else:
                # Couleur neutre pour zéro
                label.setStyleSheet(f"""
                    font-weight: bold;
                    font-size: 14px;
                    color: #6B7280;
                    background-color: #F9FAFB;
                    padding: 5px;
                    border-radius: 4px;
                    border: 1px solid #6B7280;
                """)

        except (KeyError, ValueError, TypeError):
            # En cas d'erreur, utiliser le style par défaut
            if label_key in self.recap_values:
                self.recap_values[label_key].setText(f"{value:.2f} DH")

    def show_add_dialog(self):
        """Ajoute une nouvelle ligne dans le tableau actif"""
        # Déterminer quel tableau est actif (le dernier cliqué)
        current_table = None
        if hasattr(self, 'last_active_table'):
            current_table = self.last_active_table
        else:
            current_table = self.achats_table  # Par défaut

        # Ajouter une ligne vide
        row_count = current_table.rowCount()
        current_table.insertRow(row_count)

        # Remplir avec des valeurs par défaut
        if current_table == self.achats_table:
            self.add_empty_achat_row(row_count)
        elif current_table == self.ventes_table:
            self.add_empty_vente_row(row_count)

    def add_empty_achat_row(self, row):
        """Ajoute une ligne vide dans le tableau des achats"""
        default_values = [
            "",  # Date de paiement
            "",  # N° facture achat
            "",  # Code
            "",  # Désignation
            "0.00",  # Montant HT
            "0.00",  # Montant TVA
            "0.00",  # TTC
            "0.00",  # Prix de vente HT
            "0.00",  # Valeur stock
            "",  # Fournisseur
            "",  # IF
            ""   # ICE
        ]

        for col, value in enumerate(default_values):
            item = QTableWidgetItem(value)
            item.setFlags(item.flags() | Qt.ItemIsEditable)
            self.achats_table.setItem(row, col, item)

    def add_empty_vente_row(self, row):
        """Ajoute une ligne vide dans le tableau des ventes"""
        default_values = [
            "",  # Date de paiement
            "",  # N° d'ordre
            "",  # N° de facture
            "0.00 DH",  # Montant HT
            "0.00 DH",  # Montant TVA
            "0.00 DH",  # Montant TTC
            "",  # Nom du CLIENT
            "",  # IF Client
            "",  # ICE Client
            "20%"  # Taux TVA
        ]

        for col, value in enumerate(default_values):
            item = QTableWidgetItem(value)
            item.setFlags(item.flags() | Qt.ItemIsEditable)
            self.ventes_table.setItem(row, col, item)

    def on_item_changed(self, item):
        """Gère les modifications des cellules"""
        if item is None:
            return

        table = item.tableWidget()
        row = item.row()
        col = item.column()

        # Marquer le tableau comme actif
        self.last_active_table = table

        # Recalculer les valeurs dépendantes
        if table == self.achats_table:
            self.recalculate_achat_row(row)
        elif table == self.ventes_table:
            self.recalculate_vente_row(row)

        # Recalculer le récapitulatif
        self.calculate_recap_from_tables()

    def recalculate_achat_row(self, row):
        """Recalcule les valeurs d'une ligne d'achat"""
        try:
            # Récupérer les valeurs
            montant_ht_item = self.achats_table.item(row, 4)  # Montant HT

            if montant_ht_item:
                # Extraire la valeur numérique
                montant_ht = self.extract_numeric_value(montant_ht_item.text())

                # Calculer les valeurs dérivées
                montant_tva = montant_ht * 0.20
                montant_ttc = montant_ht * 1.20

                # Mettre à jour les cellules calculées
                self.update_cell_value(self.achats_table, row, 5, f"{montant_tva:.2f}")
                self.update_cell_value(self.achats_table, row, 6, f"{montant_ttc:.2f}")

        except (ValueError, TypeError):
            pass

    def recalculate_vente_row(self, row):
        """Recalcule les valeurs d'une ligne de vente"""
        try:
            # Récupérer les valeurs
            montant_ht_item = self.ventes_table.item(row, 3)

            if montant_ht_item:
                # Extraire la valeur numérique
                montant_ht = self.extract_numeric_value(montant_ht_item.text())

                # Calculer les valeurs dérivées
                montant_tva = montant_ht * 0.20
                montant_ttc = montant_ht * 1.20

                # Mettre à jour les cellules calculées
                self.update_cell_value(self.ventes_table, row, 4, f"{montant_tva:.2f} DH")
                self.update_cell_value(self.ventes_table, row, 5, f"{montant_ttc:.2f} DH")

        except (ValueError, TypeError):
            pass

    def extract_numeric_value(self, text):
        """Extrait la valeur numérique d'un texte"""
        if not text:
            return 0.0

        # Enlever les caractères non numériques sauf le point et le signe moins
        clean_text = ''.join(c for c in str(text) if c.isdigit() or c in '.-')

        try:
            return float(clean_text) if clean_text else 0.0
        except ValueError:
            return 0.0

    def update_cell_value(self, table, row, col, value):
        """Met à jour la valeur d'une cellule"""
        item = table.item(row, col)
        if item:
            # Temporairement déconnecter le signal pour éviter la récursion
            table.itemChanged.disconnect(self.on_item_changed)
            item.setText(value)
            # Appliquer la couleur
            numeric_value = self.extract_numeric_value(value)
            self.apply_color_by_value(item, numeric_value)
            # Reconnecter le signal
            table.itemChanged.connect(self.on_item_changed)

    def calculate_recap_from_tables(self):
        """Calcule le récapitulatif à partir des données des tableaux"""
        try:
            # Calculer les totaux des achats
            total_ht_achats = 0
            total_tva_achats = 0
            total_ttc_achats = 0

            for row in range(self.achats_table.rowCount()):
                ht_item = self.achats_table.item(row, 4)      # Montant HT
                tva_item = self.achats_table.item(row, 5)     # Montant TVA
                ttc_item = self.achats_table.item(row, 6)     # Montant TTC

                if ht_item:
                    total_ht_achats += self.extract_numeric_value(ht_item.text())
                if tva_item:
                    total_tva_achats += self.extract_numeric_value(tva_item.text())
                if ttc_item:
                    total_ttc_achats += self.extract_numeric_value(ttc_item.text())

            # Calculer les totaux des ventes
            total_ht_ventes = 0
            total_tva_ventes = 0
            total_ttc_ventes = 0

            for row in range(self.ventes_table.rowCount()):
                ht_item = self.ventes_table.item(row, 3)   # Montant HT
                tva_item = self.ventes_table.item(row, 4)  # Montant TVA
                ttc_item = self.ventes_table.item(row, 5)  # Montant TTC

                if ht_item:
                    total_ht_ventes += self.extract_numeric_value(ht_item.text())
                if tva_item:
                    total_tva_ventes += self.extract_numeric_value(tva_item.text())
                if ttc_item:
                    total_ttc_ventes += self.extract_numeric_value(ttc_item.text())

            # Mettre à jour les labels du récapitulatif
            self.update_recap_label_with_color('total_ht_les_achats', total_ht_achats)
            self.update_recap_label_with_color('total_tva_les_achats', total_tva_achats)
            self.update_recap_label_with_color('total_ttc_les_achats', total_ttc_achats)

            self.update_recap_label_with_color('total_ht_les_ventes', total_ht_ventes)
            self.update_recap_label_with_color('total_tva_les_ventes', total_tva_ventes)
            self.update_recap_label_with_color('total_ttc_les_ventes', total_ttc_ventes)

            # Calculer la TVA à payer
            tva_a_payer = total_tva_ventes - total_tva_achats
            self.update_recap_label_with_color('tva_à_payer', tva_a_payer)

        except Exception as e:
            print(f"Erreur lors du calcul du récapitulatif: {str(e)}")

    def show_edit_dialog(self, item_id):
        """Fonction d'édition (non utilisée dans cette interface)"""
        pass

    def on_item_double_clicked(self, item):
        """Gère le double-clic sur un élément du tableau TVA"""
        if item is None:
            return

        # Récupérer les données de la facture depuis la première colonne
        row = item.row()
        first_column_item = None

        # Déterminer quel tableau a été cliqué
        if item.tableWidget() == self.achats_table:
            first_column_item = self.achats_table.item(row, 0)
        elif item.tableWidget() == self.ventes_table:
            first_column_item = self.ventes_table.item(row, 0)

        if first_column_item:
            # Récupérer les données stockées
            facture_data = first_column_item.data(Qt.UserRole)
            if facture_data and isinstance(facture_data, dict):
                facture_type = facture_data.get('type')
                facture_id = facture_data.get('id')

                if facture_type and facture_id:
                    # Afficher un message informatif (peut être remplacé par l'ouverture de la facture)
                    from PySide6.QtWidgets import QMessageBox
                    QMessageBox.information(
                        self,
                        "Information",
                        f"Double-clic détecté sur la facture {facture_type} ID: {facture_id}\n"
                        f"Fonctionnalité d'ouverture de facture à implémenter."
                    )
