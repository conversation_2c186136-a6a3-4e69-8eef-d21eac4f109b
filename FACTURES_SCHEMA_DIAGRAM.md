# 📊 مخطط قسم الفواتير - Factures Module Schema

## 🏗️ **الهيكل العام للنظام**

```
┌─────────────────────────────────────────────────────────────────┐
│                    🧾 نظام إدارة الفواتير                      │
│                   Système de Gestion des Factures              │
└─────────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │ 📝 إنشاء      │ │ 📋 إدارة     │ │ 🖨️ طباعة   │
        │ الفواتير      │ │ الفواتير     │ │ وتصدير     │
        │ Create        │ │ Management   │ │ Print &    │
        │ Invoices      │ │             │ │ Export     │
        └──────────────┘ └─────────────┘ └────────────┘
```

## 🗄️ **هيكل قاعدة البيانات**

### 📋 **الجداول الرئيسية**

```sql
-- جدول الفواتير الرئيسي
CREATE TABLE factures (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    numero TEXT UNIQUE NOT NULL,           -- رقم الفاتورة (F001)
    client_id INTEGER,                     -- معرف العميل
    date_facture DATE,                     -- تاريخ الفاتورة
    date_echeance DATE,                    -- تاريخ الاستحقاق
    bon_commande TEXT,                     -- رقم بون الكوماند
    marche TEXT,                          -- رقم المشروع/السوق
    objet TEXT,                           -- موضوع الفاتورة
    bon_livraison_numero TEXT,            -- رقم بون التسليم
    mode_paiement TEXT DEFAULT 'Virement', -- طريقة الدفع
    montant_ht REAL DEFAULT 0,            -- المبلغ بدون ضريبة
    montant_tva REAL DEFAULT 0,           -- مبلغ الضريبة
    montant_ttc REAL DEFAULT 0,           -- المبلغ الإجمالي
    statut TEXT DEFAULT 'En attente',     -- حالة الفاتورة
    date_paiement DATE,                   -- تاريخ الدفع
    notes TEXT,                           -- ملاحظات
    FOREIGN KEY (client_id) REFERENCES clients (id)
);

-- جدول تفاصيل الفواتير
CREATE TABLE lignes_facture (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    facture_id INTEGER NOT NULL,          -- معرف الفاتورة
    produit_id INTEGER,                   -- معرف المنتج
    designation TEXT NOT NULL,            -- وصف المنتج/الخدمة
    unite TEXT,                          -- الوحدة (قطعة، متر، كيلو...)
    quantite REAL NOT NULL,              -- الكمية
    prix_unitaire_ht REAL NOT NULL,      -- السعر الوحدوي بدون ضريبة
    taux_tva REAL DEFAULT 20,            -- معدل الضريبة %
    total_ht REAL NOT NULL,              -- المجموع بدون ضريبة
    total_tva REAL NOT NULL,             -- مجموع الضريبة
    total_ttc REAL NOT NULL,             -- المجموع مع الضريبة
    FOREIGN KEY (facture_id) REFERENCES factures (id),
    FOREIGN KEY (produit_id) REFERENCES produits (id)
);
```

## 🔄 **تدفق العمل - Workflow**

```
┌─────────────────┐
│ 🆕 فاتورة جديدة  │
│ New Invoice     │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 📝 ملء البيانات  │
│ Fill Data       │
│ • العميل         │
│ • التاريخ        │
│ • رقم الفاتورة   │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 🛒 إضافة المنتجات│
│ Add Products    │
│ • الوصف         │
│ • الكمية        │
│ • السعر         │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 🧮 حساب المجاميع │
│ Calculate Totals│
│ • HT + TVA = TTC│
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 💾 حفظ الفاتورة  │
│ Save Invoice    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 🖨️ طباعة/تصدير  │
│ Print/Export    │
│ • PDF          │
│ • Excel        │
│ • Print        │
└─────────────────┘
```

## 🎨 **واجهة المستخدم - User Interface**

### 📱 **الشاشة الرئيسية**

```
┌─────────────────────────────────────────────────────────────────┐
│ 🧾 إدارة الفواتير - Gestion des Factures                       │
├─────────────────────────────────────────────────────────────────┤
│ [🆕 فاتورة جديدة] [📋 قائمة الفواتير] [🔍 بحث] [📊 تقارير]      │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ ┌─── معلومات الفاتورة ───┐  ┌─── معلومات العميل ───┐           │
│ │ رقم الفاتورة: F001     │  │ العميل: [اختر العميل ▼] │           │
│ │ التاريخ: [📅]          │  │ العنوان: [تلقائي]      │           │
│ │ الاستحقاق: [📅]        │  │ ICE: [تلقائي]         │           │
│ │ بون الكوماند: [____]   │  │ الهاتف: [تلقائي]       │           │
│ │ المشروع: [____]        │  └─────────────────────────┘           │
│ └─────────────────────────┘                                      │
│                                                                 │
│ ┌─── تفاصيل المنتجات/الخدمات ───────────────────────────────────┐ │
│ │ الوصف        │ الوحدة │ الكمية │ السعر HT │ TVA │ المجموع TTC │ │
│ │─────────────┼───────┼──────┼─────────┼────┼─────────────│ │
│ │ [منتج 1]    │ [قطعة] │ [1]  │ [100.00]│20% │ [120.00]    │ │
│ │ [منتج 2]    │ [متر]  │ [5]  │ [50.00] │20% │ [300.00]    │ │
│ │ [+ إضافة منتج]                                              │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ ┌─── المجاميع ───┐                                              │
│ │ المجموع HT: 350.00 DH │                                      │
│ │ TVA (20%): 70.00 DH   │                                      │
│ │ المجموع TTC: 420.00 DH│                                      │
│ └─────────────────────┘                                        │
│                                                                 │
│ [💾 حفظ] [🖨️ طباعة] [📄 PDF] [📊 Excel] [❌ إلغاء]              │
└─────────────────────────────────────────────────────────────────┘
```

### 📋 **قائمة الفواتير**

```
┌─────────────────────────────────────────────────────────────────┐
│ 📋 قائمة الفواتير - Liste des Factures                         │
├─────────────────────────────────────────────────────────────────┤
│ 🔍 [بحث في الفواتير...]                    [🆕 فاتورة جديدة]    │
├─────────────────────────────────────────────────────────────────┤
│ رقم الفاتورة │ العميل      │ التاريخ    │ المبلغ TTC │ الحالة │ إجراءات │
│─────────────┼────────────┼──────────┼──────────┼──────┼────────│
│ F001        │ شركة ABC    │ 2024-01-15│ 1,200 DH │ مدفوعة│ [✏️][🖨️][🗑️]│
│ F002        │ عميل XYZ    │ 2024-01-16│ 850 DH   │ معلقة │ [✏️][🖨️][🗑️]│
│ F003        │ مؤسسة DEF   │ 2024-01-17│ 2,100 DH │ معلقة │ [✏️][🖨️][🗑️]│
│ F004        │ شركة GHI    │ 2024-01-18│ 750 DH   │ مدفوعة│ [✏️][🖨️][🗑️]│
└─────────────────────────────────────────────────────────────────┘
```

## ⚙️ **الوظائف الرئيسية - Main Functions**

### 🆕 **إنشاء فاتورة جديدة**
```python
def nouvelle_facture():
    """إنشاء فاتورة جديدة"""
    # 1. توليد رقم فاتورة تلقائي
    # 2. تعيين التاريخ الحالي
    # 3. تحميل قائمة العملاء
    # 4. إعداد جدول المنتجات فارغ
    # 5. إعادة تعيين المجاميع
```

### 💾 **حفظ الفاتورة**
```python
def sauvegarder_facture():
    """حفظ الفاتورة في قاعدة البيانات"""
    # 1. التحقق من صحة البيانات
    # 2. حساب المجاميع النهائية
    # 3. حفظ بيانات الفاتورة الرئيسية
    # 4. حفظ تفاصيل المنتجات
    # 5. تحديث حالة المخزون
```

### 🧮 **حساب المجاميع**
```python
def calculer_totaux():
    """حساب مجاميع الفاتورة"""
    # 1. حساب المجموع بدون ضريبة (HT)
    # 2. حساب مجموع الضريبة (TVA)
    # 3. حساب المجموع الإجمالي (TTC)
    # 4. تحديث عرض المجاميع
```

### 🖨️ **طباعة وتصدير**
```python
def imprimer_facture():
    """طباعة الفاتورة"""
    # 1. إنشاء نموذج الفاتورة
    # 2. ملء البيانات
    # 3. تنسيق التخطيط
    # 4. إرسال للطابعة أو حفظ كـ PDF
```

## 🔗 **التكامل مع الوحدات الأخرى**

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 👥 العملاء   │◄──►│ 🧾 الفواتير  │◄──►│ 📦 المنتجات │
│ Clients     │    │ Factures    │    │ Produits    │
└─────────────┘    └─────────────┘    └─────────────┘
       ▲                   ▲                   ▲
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 📊 التقارير  │    │ 💰 المحاسبة  │    │ 📋 المخزون   │
│ Reports     │    │ Accounting  │    │ Stock       │
└─────────────┘    └─────────────┘    └─────────────┘
```

## 📊 **أنواع التقارير**

### 📈 **تقارير الفواتير**
- 📋 قائمة الفواتير حسب الفترة
- 💰 إجمالي المبيعات الشهرية
- 📊 الفواتير حسب العميل
- ⏰ الفواتير المستحقة
- 💳 الفواتير المدفوعة/غير المدفوعة

### 🎯 **مؤشرات الأداء**
- 📈 نمو المبيعات
- 👥 أفضل العملاء
- 📦 أكثر المنتجات مبيعاً
- ⏱️ متوسط وقت التحصيل

## 🔧 **الإعدادات والتخصيص**

### ⚙️ **إعدادات الفواتير**
```
┌─── إعدادات الفواتير ───┐
│ 🏢 معلومات الشركة      │
│ • اسم الشركة           │
│ • العنوان             │
│ • الهاتف/الفاكس        │
│ • البريد الإلكتروني     │
│ • رقم التسجيل التجاري   │
│                       │
│ 🧾 تنسيق الفواتير     │
│ • نموذج الفاتورة       │
│ • الألوان والخطوط      │
│ • الشعار              │
│ • معلومات إضافية       │
│                       │
│ 💰 إعدادات الضريبة     │
│ • معدل TVA الافتراضي   │
│ • أنواع الضرائب        │
│ • إعفاءات ضريبية       │
└─────────────────────────┘
```

## 🚀 **المزايا والخصائص**

### ✨ **المزايا الرئيسية**
- 🔄 **توليد تلقائي** لأرقام الفواتير
- 🧮 **حساب تلقائي** للضرائب والمجاميع
- 👥 **ربط مع قاعدة العملاء** لملء البيانات تلقائياً
- 📦 **ربط مع المخزون** لتتبع المنتجات
- 🖨️ **طباعة احترافية** بتنسيقات متعددة
- 📊 **تقارير شاملة** للمتابعة والتحليل

### 🛡️ **الأمان والموثوقية**
- 🔒 **حماية البيانات** من التعديل غير المصرح
- 📝 **سجل التغييرات** لتتبع التعديلات
- 💾 **نسخ احتياطية** تلقائية
- ✅ **التحقق من صحة البيانات** قبل الحفظ

## 🎯 **خطة التطوير المستقبلية**

### 📅 **المرحلة القادمة**
- 📱 **واجهة محمولة** للوصول من الهاتف
- 🌐 **تكامل مع البنوك** للدفع الإلكتروني
- 📧 **إرسال الفواتير بالبريد الإلكتروني**
- 🔔 **تنبيهات** للفواتير المستحقة
- 📊 **لوحة معلومات تفاعلية**

---

## 🎉 **الخلاصة**

هذا المخطط يوضح النظام الشامل لإدارة الفواتير الذي يتضمن:
- 🗄️ **قاعدة بيانات محكمة** لحفظ جميع البيانات
- 🎨 **واجهة مستخدم سهلة** وبديهية
- 🔄 **تدفق عمل منطقي** من الإنشاء إلى الطباعة
- 🔗 **تكامل كامل** مع باقي وحدات النظام
- 📊 **تقارير شاملة** للمتابعة والتحليل

النظام مصمم ليكون **مرناً وقابلاً للتوسع** لتلبية احتياجات الشركات المختلفة.
