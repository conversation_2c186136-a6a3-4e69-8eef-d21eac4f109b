from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QComboBox, QDateEdit, QSpinBox, QFormLayout,
                              QMessageBox, QHeaderView, QGridLayout, QFrame,
                              QDialog, QSizePolicy)
from PySide6.QtCore import Qt, QDate, QByteArray, QSize
from PySide6.QtPrintSupport import QPrinter, QPrintDialog
from PySide6.QtGui import QTextDocument, QPixmap, QPainter, QIcon
from PySide6.QtSvg import QSvgRenderer
import sqlite3
import datetime
import os

# Importer les icônes
from ..icons.icons import svg_to_icon_html, INVOICE_ICON, EDIT_ICON, DELETE_ICON, PRINT_ICON, LIST_ICON

# Importer la boîte de dialogue de liste des factures
from .factures_list_dialog import FacturesListDialog

# Importer le générateur de factures
try:
    from utils.facture_generator import FactureGenerator
    facture_generator_available = True
except ImportError:
    facture_generator_available = False

class FacturesVenteModule(QWidget):
    """Module de gestion des factures de vente"""

    def __init__(self, db_manager, signals=None):
        super().__init__()
        self.db_manager = db_manager
        self.signals = signals  # Signaux pour la communication entre modules
        self.setup_ui()
        self.nouvelle_facture()

        # Connecter le signal de mise à jour des clients si disponible
        if self.signals:
            self.signals.clients_changed.connect(self.charger_clients)

    def setup_ui(self):
        """Configure l'interface utilisateur du module"""
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # En-tête avec titre et bouton de création
        header_layout = QHBoxLayout()

        # Logo ISOLOC SERVICE
        logo_label = QLabel()
        logo_label.setFixedSize(150, 80)

        # Créer un SVG pour le logo
        svg_content = """
        <svg viewBox="0 0 300 150" xmlns="http://www.w3.org/2000/svg">
            <path d="M150,20 C80,20 20,80 20,150 C20,220 80,280 150,280" stroke="black" stroke-width="10" fill="none"/>
            <text x="150" y="70" font-family="Arial" font-size="40" font-weight="bold" text-anchor="middle">ISOLOC</text>
            <text x="150" y="110" font-family="Arial" font-size="24" text-anchor="middle">SERVICE</text>
        </svg>
        """

        # Créer un QPixmap à partir du SVG
        svg_renderer = QSvgRenderer(QByteArray(svg_content.encode()))
        pixmap = QPixmap(150, 80)
        pixmap.fill(Qt.transparent)
        painter = QPainter(pixmap)
        svg_renderer.render(painter)
        painter.end()

        logo_label.setPixmap(pixmap)
        header_layout.addWidget(logo_label)

        # Titre du module
        title = QLabel("FACTURES VENTE")
        title.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #1e3a8a;
        """)
        header_layout.addWidget(title)

        header_layout.addStretch()

        # Bouton Liste des Factures
        self.liste_factures_btn = QPushButton("LISTE DES FACTURES")
        self.liste_factures_btn.setMinimumHeight(45)
        self.liste_factures_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 16px;
                margin-right: 10px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
        """)
        self.liste_factures_btn.setIcon(QIcon(":/icons/list.png"))
        self.liste_factures_btn.clicked.connect(self.show_factures_list)
        header_layout.addWidget(self.liste_factures_btn)

        # Bouton Créer Facture
        self.creer_facture_btn = QPushButton("CREE FACTURE")
        self.creer_facture_btn.setMinimumHeight(45)
        self.creer_facture_btn.setStyleSheet("""
            QPushButton {
                background-color: #1e3a8a;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 16px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #15296b;
            }
        """)
        self.creer_facture_btn.setIcon(QIcon(":/icons/add.png"))
        self.creer_facture_btn.clicked.connect(self.nouvelle_facture)
        header_layout.addWidget(self.creer_facture_btn)

        main_layout.addLayout(header_layout)

        # Formulaire principal
        form_frame = QFrame()
        form_frame.setFrameShape(QFrame.StyledPanel)
        form_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: white;
            }
        """)
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(15)

        # Informations générales
        info_layout = QGridLayout()
        info_layout.setSpacing(10)

        # Date
        info_layout.addWidget(QLabel("DATE :"), 0, 0)
        from ..style import create_styled_date_edit
        self.date_facture = create_styled_date_edit()
        self.date_facture.setMinimumHeight(35)
        info_layout.addWidget(self.date_facture, 0, 1)

        # Numéro de facture
        info_layout.addWidget(QLabel("N° FACTURE :"), 0, 2)
        self.numero_facture = QLineEdit()
        self.numero_facture.setReadOnly(True)
        self.numero_facture.setMinimumHeight(35)
        self.numero_facture.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #f0f0f0;
                min-height: 35px;
                font-size: 14px;
            }
        """)
        info_layout.addWidget(self.numero_facture, 0, 3)

        # Client
        info_layout.addWidget(QLabel("CLIENT :"), 1, 0)
        self.client_combo = QComboBox()
        self.client_combo.setMinimumHeight(35)
        self.client_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
                min-height: 35px;
                font-size: 14px;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #ddd;
            }
        """)
        info_layout.addWidget(self.client_combo, 1, 1, 1, 3)

        # ICE
        info_layout.addWidget(QLabel("ICE :"), 2, 0)
        self.ice_label = QLabel("")
        self.ice_label.setMinimumHeight(35)
        self.ice_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
                min-height: 35px;
                font-size: 14px;
            }
        """)
        info_layout.addWidget(self.ice_label, 2, 1, 1, 3)

        # Bon de commande
        info_layout.addWidget(QLabel("BON DE COMMANDE N° :"), 3, 0)
        self.bon_commande_input = QLineEdit()
        self.bon_commande_input.setMinimumHeight(35)
        self.bon_commande_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
                min-height: 35px;
                font-size: 14px;
            }
        """)
        info_layout.addWidget(self.bon_commande_input, 3, 1)

        # Marché
        info_layout.addWidget(QLabel("MARCHE N° :"), 3, 2)
        self.marche_input = QLineEdit()
        self.marche_input.setMinimumHeight(35)
        self.marche_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
                min-height: 35px;
                font-size: 14px;
            }
        """)
        info_layout.addWidget(self.marche_input, 3, 3)

        # Objet (description du travail)
        info_layout.addWidget(QLabel("OBJET :"), 4, 0)
        self.objet_input = QLineEdit()
        self.objet_input.setPlaceholderText("Description du travail (ex: ACHAT DE POTS POUR L'AMÉNAGEMENT DES RUES DE LA VILLE)")
        self.objet_input.setMinimumHeight(35)
        self.objet_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
                min-height: 35px;
                font-size: 14px;
            }
        """)
        info_layout.addWidget(self.objet_input, 4, 1, 1, 3)

        form_layout.addLayout(info_layout)

        # Tableau des articles
        self.articles_table = QTableWidget(0, 7)
        self.articles_table.setHorizontalHeaderLabels([
            "", "DESIGNATION", "Unité", "Quantité", "Prix unité HT", "Prix total HT", "Action"
        ])

        # Style du tableau
        self.articles_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
                font-family: Arial;
                font-size: 14px;
            }
            QHeaderView::section {
                background-color: #1e3a8a;
                color: white;
                padding: 12px;
                border: 1px solid #15296b;
                font-weight: bold;
                font-family: Arial;
                font-size: 14px;
                min-height: 40px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
                min-height: 40px;
            }
        """)

        # Définir la hauteur des lignes
        self.articles_table.verticalHeader().setDefaultSectionSize(50)
        self.articles_table.verticalHeader().setVisible(False)

        # Configuration des colonnes
        self.articles_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Fixed)  # Numéro
        self.articles_table.setColumnWidth(0, 40)  # Largeur fixe pour la colonne numéro

        # Définir des largeurs minimales pour chaque colonne
        self.articles_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)  # Désignation
        self.articles_table.setColumnWidth(1, 250)  # Largeur minimale pour la désignation

        self.articles_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Fixed)  # Unité
        self.articles_table.setColumnWidth(2, 100)  # Largeur fixe pour l'unité

        self.articles_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Fixed)  # Quantité
        self.articles_table.setColumnWidth(3, 100)  # Largeur fixe pour la quantité

        self.articles_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Fixed)  # Prix unitaire
        self.articles_table.setColumnWidth(4, 120)  # Largeur fixe pour le prix unitaire

        self.articles_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Fixed)  # Prix total
        self.articles_table.setColumnWidth(5, 120)  # Largeur fixe pour le prix total

        self.articles_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.Fixed)  # Action
        self.articles_table.setColumnWidth(6, 250)  # Largeur fixe pour les actions

        form_layout.addWidget(self.articles_table)

        # Conteneur pour les boutons
        buttons_container = QHBoxLayout()

        # Bouton Ajouter une ligne
        self.ajouter_ligne_btn = QPushButton("Ajouter une ligne")
        self.ajouter_ligne_btn.setMinimumHeight(40)
        self.ajouter_ligne_btn.setStyleSheet("""
            QPushButton {
                background-color: #1e3a8a;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 15px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #15296b;
            }
        """)
        self.ajouter_ligne_btn.clicked.connect(self.ajouter_ligne)
        buttons_container.addWidget(self.ajouter_ligne_btn)

        # Bouton Supprimer toutes les lignes
        self.supprimer_tout_btn = QPushButton("SUPPRIMER TOUS")
        self.supprimer_tout_btn.setMinimumHeight(40)
        self.supprimer_tout_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 15px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.supprimer_tout_btn.clicked.connect(self.supprimer_toutes_lignes)
        buttons_container.addWidget(self.supprimer_tout_btn)

        buttons_container.addStretch()
        form_layout.addLayout(buttons_container)

        # Totaux
        totals_layout = QHBoxLayout()
        totals_layout.addStretch()

        totals_form = QFormLayout()
        totals_form.setSpacing(10)
        totals_form.setLabelAlignment(Qt.AlignRight)

        self.total_ht_label = QLabel("0.00 DH")
        self.total_ht_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        totals_form.addRow("TOTAL HT:", self.total_ht_label)

        self.tva_label = QLabel("0.00 DH")
        self.tva_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        totals_form.addRow("TVA 20%:", self.tva_label)

        self.ttc_label = QLabel("0.00 DH")
        self.ttc_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        totals_form.addRow("TTC:", self.ttc_label)

        totals_layout.addLayout(totals_form)
        form_layout.addLayout(totals_layout)

        main_layout.addWidget(form_frame)

        # Boutons d'action
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        self.valider_btn = QPushButton("Validé la facture")
        self.valider_btn.setMinimumHeight(45)
        self.valider_btn.setStyleSheet("""
            QPushButton {
                background-color: #1e3a8a;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 16px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #15296b;
            }
        """)
        self.valider_btn.clicked.connect(self.enregistrer_facture)
        buttons_layout.addWidget(self.valider_btn)

        self.enregistrer_pdf_btn = QPushButton("Enregistrer PDF")
        self.enregistrer_pdf_btn.setMinimumHeight(45)
        self.enregistrer_pdf_btn.setStyleSheet("""
            QPushButton {
                background-color: #1e3a8a;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 16px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #15296b;
            }
        """)
        self.enregistrer_pdf_btn.clicked.connect(self.exporter_pdf)
        buttons_layout.addWidget(self.enregistrer_pdf_btn)

        self.imprimer_btn = QPushButton("Imprimer")
        self.imprimer_btn.setMinimumHeight(45)
        self.imprimer_btn.setStyleSheet("""
            QPushButton {
                background-color: #1e3a8a;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 16px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #15296b;
            }
        """)
        self.imprimer_btn.clicked.connect(self.imprimer_facture)
        buttons_layout.addWidget(self.imprimer_btn)

        main_layout.addLayout(buttons_layout)

    def charger_clients(self):
        """Charge la liste des clients dans le QComboBox"""
        # Déconnecter le signal pour éviter les appels multiples pendant le chargement
        try:
            self.client_combo.currentIndexChanged.disconnect()
        except:
            pass  # Si le signal n'était pas connecté, ignorer l'erreur

        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT id, nom, ice FROM clients ORDER BY nom")
        clients = cursor.fetchall()

        # Sauvegarder les données actuelles
        current_data = self.client_combo.currentData()

        self.client_combo.clear()
        self.client_combo.addItem("Sélectionner un client", None)

        for client in clients:
            self.client_combo.addItem(client['nom'], {"id": client['id'], "ice": client['ice']})

        # Restaurer l'index précédent si possible
        if current_data:
            # Chercher l'index du client précédemment sélectionné
            for i in range(self.client_combo.count()):
                item_data = self.client_combo.itemData(i)
                if item_data and item_data.get("id") == current_data.get("id"):
                    self.client_combo.setCurrentIndex(i)
                    break

        # Reconnecter le signal après avoir chargé les clients
        self.client_combo.currentIndexChanged.connect(self.client_selectionne)

    def client_selectionne(self):
        """Met à jour les informations du client sélectionné"""
        client_data = self.client_combo.currentData()

        if client_data:
            # Mettre à jour l'ICE
            self.ice_label.setText(client_data["ice"] or "")
        else:
            self.ice_label.setText("")

    def nouvelle_facture(self):
        """Initialise une nouvelle facture"""
        # Générer un numéro de facture
        date_str = datetime.datetime.now().strftime("%m/%Y")
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM factures WHERE numero LIKE ?", (f"F%{date_str}",))
        count = cursor.fetchone()[0] + 1
        self.numero_facture.setText(f"F{count:03d} ({date_str})")

        # Charger les clients
        self.charger_clients()

        # Réinitialiser les champs
        self.bon_commande_input.clear()
        self.marche_input.clear()
        self.objet_input.clear()

        # Réinitialiser le tableau
        self.articles_table.setRowCount(0)

        # Réinitialiser les totaux
        self.calculer_totaux()

    def ajouter_ligne(self):
        """Ajoute une nouvelle ligne au tableau des articles"""
        row = self.articles_table.rowCount()
        self.articles_table.insertRow(row)

        # Définir une hauteur de ligne suffisante
        self.articles_table.setRowHeight(row, 60)

        # Numéro de ligne (QLabel)
        num_label = QLabel(str(row + 1))
        num_label.setAlignment(Qt.AlignCenter)
        num_label.setStyleSheet("""
            background-color: #1e3a8a;
            color: white;
            font-weight: bold;
            padding: 8px;
            font-family: Arial;
            font-size: 14px;
            border-radius: 4px;
        """)
        self.articles_table.setCellWidget(row, 0, num_label)

        # Créer un widget conteneur pour la désignation
        designation_container = QWidget()
        designation_layout = QVBoxLayout(designation_container)
        designation_layout.setContentsMargins(2, 2, 2, 2)
        designation_layout.setSpacing(0)

        # Désignation (QComboBox avec recherche)
        designation_combo = QComboBox()
        designation_combo.setEditable(True)
        designation_combo.setPlaceholderText("Taper pour rechercher un produit")
        designation_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                font-family: Arial;
                font-size: 14px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #ddd;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #ddd;
                selection-background-color: #e3f2fd;
                font-size: 14px;
            }
        """)

        # Charger tous les produits dans le combo
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT id, code, designation, unite, prix_vente FROM produits ORDER BY designation")
        produits = cursor.fetchall()

        # Ajouter une option vide
        designation_combo.addItem("", None)

        # Ajouter les produits
        for produit in produits:
            designation_combo.addItem(f"{produit['code']} - {produit['designation']}", produit)

        # Connecter le signal de changement
        designation_combo.currentIndexChanged.connect(lambda: self.produit_selectionne_combo(row, designation_combo))
        designation_combo.lineEdit().textChanged.connect(lambda: self.rechercher_produit(row))

        designation_layout.addWidget(designation_combo)
        designation_container.setLayout(designation_layout)
        self.articles_table.setCellWidget(row, 1, designation_container)

        # Créer un widget conteneur pour l'unité
        unite_container = QWidget()
        unite_layout = QVBoxLayout(unite_container)
        unite_layout.setContentsMargins(2, 2, 2, 2)
        unite_layout.setSpacing(0)

        # Unité (QLineEdit)
        unite_edit = QLineEdit()
        unite_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                font-family: Arial;
                font-size: 14px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
        """)
        unite_edit.setReadOnly(False)  # Permettre la modification

        unite_layout.addWidget(unite_edit)
        unite_container.setLayout(unite_layout)
        self.articles_table.setCellWidget(row, 2, unite_container)

        # Créer un widget conteneur pour la quantité
        quantite_container = QWidget()
        quantite_layout = QVBoxLayout(quantite_container)
        quantite_layout.setContentsMargins(2, 2, 2, 2)
        quantite_layout.setSpacing(0)

        # Quantité (QSpinBox)
        quantite_spin = QSpinBox()
        quantite_spin.setRange(1, 10000)
        quantite_spin.setValue(1)
        quantite_spin.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                font-family: Arial;
                font-size: 14px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                width: 20px;
                border-left: 1px solid #ddd;
                background-color: #f5f5f5;
            }
        """)
        quantite_spin.valueChanged.connect(lambda: self.calculer_ligne(row))

        quantite_layout.addWidget(quantite_spin)
        quantite_container.setLayout(quantite_layout)
        self.articles_table.setCellWidget(row, 3, quantite_container)

        # Créer un widget conteneur pour le prix unitaire
        prix_container = QWidget()
        prix_layout = QVBoxLayout(prix_container)
        prix_layout.setContentsMargins(2, 2, 2, 2)
        prix_layout.setSpacing(0)

        # Prix unitaire (QLineEdit)
        prix_edit = QLineEdit("0.00")
        prix_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                font-family: Arial;
                font-size: 14px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
        """)
        prix_edit.textChanged.connect(lambda: self.calculer_ligne(row))

        prix_layout.addWidget(prix_edit)
        prix_container.setLayout(prix_layout)
        self.articles_table.setCellWidget(row, 4, prix_container)

        # Créer un widget conteneur pour le prix total
        total_container = QWidget()
        total_layout = QVBoxLayout(total_container)
        total_layout.setContentsMargins(2, 2, 2, 2)
        total_layout.setSpacing(0)

        # Prix total (QLineEdit en lecture seule)
        total_edit = QLineEdit("0.00 DH")
        total_edit.setReadOnly(True)
        total_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                background-color: #f0f0f0;
                font-family: Arial;
                font-size: 14px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-weight: bold;
            }
        """)

        total_layout.addWidget(total_edit)
        total_container.setLayout(total_layout)
        self.articles_table.setCellWidget(row, 5, total_container)

        # Boutons d'action
        action_container = QWidget()
        action_layout = QHBoxLayout(action_container)
        action_layout.setContentsMargins(2, 2, 2, 2)
        action_layout.setSpacing(5)

        # Bouton Calculer
        calculer_btn = QPushButton("Calculer")
        calculer_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
                font-family: Arial;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        calculer_btn.clicked.connect(lambda: self.calculer_ligne(row))
        action_layout.addWidget(calculer_btn)

        # Bouton Valider
        valider_btn = QPushButton("Validé")
        valider_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
                font-family: Arial;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        valider_btn.clicked.connect(lambda: self.valider_ligne(row))
        action_layout.addWidget(valider_btn)

        # Bouton Supprimer
        supprimer_btn = QPushButton("Supp")
        supprimer_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
                font-family: Arial;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        supprimer_btn.clicked.connect(lambda: self.supprimer_ligne(row))
        action_layout.addWidget(supprimer_btn)

        action_container.setLayout(action_layout)
        self.articles_table.setCellWidget(row, 6, action_container)

        # Stocker les références aux widgets pour pouvoir les récupérer plus tard
        self.articles_table.setItem(row, 1, QTableWidgetItem())
        self.articles_table.item(row, 1).setData(Qt.UserRole, designation_combo)

        self.articles_table.setItem(row, 2, QTableWidgetItem())
        self.articles_table.item(row, 2).setData(Qt.UserRole, unite_edit)

        self.articles_table.setItem(row, 3, QTableWidgetItem())
        self.articles_table.item(row, 3).setData(Qt.UserRole, quantite_spin)

        self.articles_table.setItem(row, 4, QTableWidgetItem())
        self.articles_table.item(row, 4).setData(Qt.UserRole, prix_edit)

        self.articles_table.setItem(row, 5, QTableWidgetItem())
        self.articles_table.item(row, 5).setData(Qt.UserRole, total_edit)

    def rechercher_produit(self, row):
        """Recherche un produit par sa désignation"""
        # Récupérer le widget conteneur
        designation_container = self.articles_table.cellWidget(row, 1)
        if not designation_container:
            return

        # Récupérer le QComboBox à partir des données stockées
        designation_combo = None
        if self.articles_table.item(row, 1):
            designation_combo = self.articles_table.item(row, 1).data(Qt.UserRole)

        if not designation_combo or not hasattr(designation_combo, 'lineEdit'):
            return

        search_text = designation_combo.lineEdit().text().strip()
        if len(search_text) < 2:  # Attendre au moins 2 caractères pour rechercher
            return

        # Bloquer temporairement les signaux pour éviter les appels récursifs
        designation_combo.blockSignals(True)

        # Vider le combo et ajouter l'option vide
        designation_combo.clear()
        designation_combo.addItem("", None)

        # Rechercher les produits correspondants
        cursor = self.db_manager.conn.cursor()
        cursor.execute(
            "SELECT id, code, designation, unite, prix_vente FROM produits WHERE designation LIKE ? OR code LIKE ? ORDER BY designation",
            (f"%{search_text}%", f"%{search_text}%")
        )
        produits = cursor.fetchall()

        # Ajouter les produits trouvés au combo
        for produit in produits:
            designation_combo.addItem(f"{produit['code']} - {produit['designation']}", produit)

        # Restaurer le texte de recherche
        designation_combo.setEditText(search_text)

        # Réactiver les signaux
        designation_combo.blockSignals(False)

    def produit_selectionne_combo(self, row, combo):
        """Gère la sélection d'un produit dans le QComboBox"""
        produit = combo.currentData()
        if not produit:
            return

        # Mettre à jour l'unité
        unite_edit = None
        if self.articles_table.item(row, 2):
            unite_edit = self.articles_table.item(row, 2).data(Qt.UserRole)

        if unite_edit:
            unite_edit.setText(produit['unite'] or "")

        # Mettre à jour le prix unitaire
        prix_edit = None
        if self.articles_table.item(row, 4):
            prix_edit = self.articles_table.item(row, 4).data(Qt.UserRole)

        if prix_edit:
            prix_edit.setText(str(produit['prix_vente'] or 0))

        # Calculer le total de la ligne
        self.calculer_ligne(row)

    def calculer_ligne(self, row):
        """Calcule le total d'une ligne"""
        # Récupérer les widgets à partir des données stockées
        quantite_spin = None
        if self.articles_table.item(row, 3):
            quantite_spin = self.articles_table.item(row, 3).data(Qt.UserRole)

        prix_edit = None
        if self.articles_table.item(row, 4):
            prix_edit = self.articles_table.item(row, 4).data(Qt.UserRole)

        total_edit = None
        if self.articles_table.item(row, 5):
            total_edit = self.articles_table.item(row, 5).data(Qt.UserRole)

        if quantite_spin and prix_edit and total_edit:
            try:
                quantite = quantite_spin.value()
                # Récupérer le texte du prix unitaire et nettoyer les caractères non numériques
                prix_text = prix_edit.text()
                # Remplacer tous les caractères non numériques (sauf le point décimal) par une chaîne vide
                prix_text = ''.join(c for c in prix_text if c.isdigit() or c == '.')
                try:
                    prix = float(prix_text or 0)
                except ValueError:
                    # En cas d'erreur, utiliser 0 comme valeur par défaut
                    prix = 0.0
                total = quantite * prix

                total_edit.setText(f"{total:.2f} DH")

                # Recalculer les totaux
                self.calculer_totaux()
            except ValueError:
                pass

    def valider_ligne(self, row):
        """Valide une ligne du tableau"""
        # Mettre en surbrillance la ligne validée
        for col in range(6):
            container = self.articles_table.cellWidget(row, col)
            if container:
                container.setStyleSheet("background-color: #e8f7f0;")

    def supprimer_ligne(self, row):
        """Supprime une ligne du tableau"""
        self.articles_table.removeRow(row)

        # Mettre à jour les numéros de ligne
        for r in range(self.articles_table.rowCount()):
            num_label = self.articles_table.cellWidget(r, 0)
            if num_label:
                num_label.setText(str(r + 1))

        self.calculer_totaux()

    def supprimer_toutes_lignes(self):
        """Supprime toutes les lignes du tableau"""
        self.articles_table.setRowCount(0)
        self.calculer_totaux()

    def calculer_totaux(self):
        """Calcule les totaux de la facture"""
        total_ht = 0

        # Parcourir toutes les lignes du tableau
        for row in range(self.articles_table.rowCount()):
            # Récupérer le widget de total à partir des données stockées
            total_edit = None
            if self.articles_table.item(row, 5):
                total_edit = self.articles_table.item(row, 5).data(Qt.UserRole)

            if total_edit:
                try:
                    # Récupérer le texte du total et nettoyer les caractères non numériques
                    total_text = total_edit.text()
                    # Remplacer tous les caractères non numériques (sauf le point décimal) par une chaîne vide
                    total_text = ''.join(c for c in total_text if c.isdigit() or c == '.')
                    try:
                        total_ht += float(total_text or 0)
                    except ValueError:
                        # En cas d'erreur, ne pas ajouter au total
                        pass
                except ValueError:
                    pass

        # Calculer la TVA et le total TTC
        tva = total_ht * 0.2  # TVA 20%
        ttc = total_ht + tva

        # Mettre à jour les labels
        self.total_ht_label.setText(f"{total_ht:.2f} DH")
        self.tva_label.setText(f"{tva:.2f} DH")
        self.ttc_label.setText(f"{ttc:.2f} DH")

    def enregistrer_facture(self):
        """Enregistre la facture dans la base de données"""
        # Vérifier qu'un client est sélectionné
        client_data = self.client_combo.currentData()
        if not client_data:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un client.")
            return

        client_id = client_data["id"]

        # Vérifier qu'il y a au moins une ligne
        if self.articles_table.rowCount() == 0:
            QMessageBox.warning(self, "Erreur", "Veuillez ajouter au moins une ligne à la facture.")
            return

        # Récupérer les données de la facture
        numero = self.numero_facture.text()
        date_creation = self.date_facture.date().toString("yyyy-MM-dd")
        date_echeance = self.date_facture.date().addDays(30).toString("yyyy-MM-dd")  # Échéance à 30 jours par défaut
        bon_commande = self.bon_commande_input.text()
        marche = self.marche_input.text()
        objet = self.objet_input.text()

        # Calculer les totaux
        # Récupérer et nettoyer les totaux
        def clean_and_convert(text):
            # Remplacer tous les caractères non numériques (sauf le point décimal) par une chaîne vide
            cleaned = ''.join(c for c in text if c.isdigit() or c == '.')
            try:
                return float(cleaned or 0)
            except ValueError:
                return 0.0

        total_ht = clean_and_convert(self.total_ht_label.text())
        total_tva = clean_and_convert(self.tva_label.text())
        total_ttc = clean_and_convert(self.ttc_label.text())

        # Enregistrer la facture
        cursor = self.db_manager.conn.cursor()
        try:
            # Insérer la facture
            cursor.execute(
                """INSERT INTO factures
                   (numero, date_creation, date_echeance, client_id,
                    total_ht, total_tva, total_ttc, statut, notes)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (numero, date_creation, date_echeance, client_id,
                 total_ht, total_tva, total_ttc, "En attente", f"Objet: {objet}, BC: {bon_commande}, Marché: {marche}")
            )

            facture_id = cursor.lastrowid

            # Insérer les lignes de la facture
            for row in range(self.articles_table.rowCount()):
                # Récupérer les widgets à partir des données stockées
                designation_combo = None
                if self.articles_table.item(row, 1):
                    designation_combo = self.articles_table.item(row, 1).data(Qt.UserRole)

                unite_edit = None
                if self.articles_table.item(row, 2):
                    unite_edit = self.articles_table.item(row, 2).data(Qt.UserRole)

                quantite_spin = None
                if self.articles_table.item(row, 3):
                    quantite_spin = self.articles_table.item(row, 3).data(Qt.UserRole)

                prix_edit = None
                if self.articles_table.item(row, 4):
                    prix_edit = self.articles_table.item(row, 4).data(Qt.UserRole)

                if designation_combo and quantite_spin and prix_edit:
                    designation = designation_combo.currentText()
                    unite = unite_edit.text() if unite_edit else ""
                    quantite = quantite_spin.value()
                    # Récupérer le texte du prix unitaire et nettoyer les caractères non numériques
                    prix_text = prix_edit.text()
                    # Remplacer tous les caractères non numériques (sauf le point décimal) par une chaîne vide
                    prix_text = ''.join(c for c in prix_text if c.isdigit() or c == '.')
                    try:
                        prix_unitaire = float(prix_text or 0)
                    except ValueError:
                        # En cas d'erreur, utiliser 0 comme valeur par défaut
                        prix_unitaire = 0.0
                    total_ligne = quantite * prix_unitaire

                    cursor.execute(
                        """INSERT INTO lignes_facture
                           (facture_id, designation, quantite, prix_unitaire_ht, taux_tva, total_ht)
                           VALUES (?, ?, ?, ?, ?, ?)""",
                        (facture_id, f"{designation} ({unite})", quantite, prix_unitaire, 20.0, total_ligne)
                    )

            self.db_manager.conn.commit()
            QMessageBox.information(self, "Succès", "Facture enregistrée avec succès.")
            self.nouvelle_facture()

        except sqlite3.Error as e:
            self.db_manager.conn.rollback()
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement de la facture: {str(e)}")

    def exporter_pdf(self):
        """Exporte la facture en PDF"""
        # Vérifier qu'un client est sélectionné
        client_data = self.client_combo.currentData()
        if not client_data:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un client.")
            return

        # Vérifier qu'il y a au moins une ligne
        if self.articles_table.rowCount() == 0:
            QMessageBox.warning(self, "Erreur", "Veuillez ajouter au moins une ligne à la facture.")
            return

        # Récupérer l'objet (description du travail)
        objet = self.objet_input.text()

        # Utiliser le générateur de factures si disponible
        if facture_generator_available:
            # Préparer les données pour le générateur de factures
            lignes = []
            total_ht = 0

            for row in range(self.articles_table.rowCount()):
                # Récupérer les widgets à partir des données stockées
                designation_combo = None
                if self.articles_table.item(row, 1):
                    designation_combo = self.articles_table.item(row, 1).data(Qt.UserRole)

                unite_edit = None
                if self.articles_table.item(row, 2):
                    unite_edit = self.articles_table.item(row, 2).data(Qt.UserRole)

                quantite_spin = None
                if self.articles_table.item(row, 3):
                    quantite_spin = self.articles_table.item(row, 3).data(Qt.UserRole)

                prix_edit = None
                if self.articles_table.item(row, 4):
                    prix_edit = self.articles_table.item(row, 4).data(Qt.UserRole)

                if designation_combo and unite_edit and quantite_spin and prix_edit:
                    designation = designation_combo.currentText()
                    unite = unite_edit.text()
                    quantite = quantite_spin.value()
                    # Récupérer le texte du prix unitaire et nettoyer les caractères non numériques
                    prix_text = prix_edit.text()
                    # Remplacer tous les caractères non numériques (sauf le point décimal) par une chaîne vide
                    prix_text = ''.join(c for c in prix_text if c.isdigit() or c == '.')
                    try:
                        prix_unitaire = float(prix_text or 0)
                    except ValueError:
                        # En cas d'erreur, utiliser 0 comme valeur par défaut
                        prix_unitaire = 0.0
                    prix_total = quantite * prix_unitaire
                    total_ht += prix_total

                    lignes.append({
                        "designation": designation,
                        "unite": unite,
                        "quantite": quantite,
                        "prix_unitaire": prix_unitaire,
                        "taux_tva": 20.0
                    })

            # Calculer TVA et TTC
            tva = total_ht * 0.2
            ttc = total_ht + tva

            # Récupérer les informations supplémentaires
            bon_commande = self.bon_commande_input.text()
            marche = self.marche_input.text()
            objet = self.objet_input.text()

            # Calculer la date d'échéance (30 jours après la date de facture)
            date_echeance = self.date_facture.date().addDays(30).toString("dd/MM/yyyy")

            # Créer les données de la facture
            facture_data = {
                "numero": self.numero_facture.text(),
                "date": self.date_facture.date().toString("dd/MM/yyyy"),
                "echeance": date_echeance,
                "bon_commande": bon_commande,
                "marche": marche,
                "objet": objet,
                "mode_paiement": "Virement bancaire",  # Par défaut
                "client": {
                    "nom": self.client_combo.currentText(),
                    "adresse": "Adresse du client",  # À compléter avec les données réelles
                    "ice": self.ice_label.text() or "N/A",
                    "telephone": "",  # À compléter avec les données réelles
                    "email": ""  # À compléter avec les données réelles
                },
                "lignes": lignes,
                "totaux": {
                    "total_ht": total_ht,
                    "total_tva": tva,
                    "total_ttc": ttc
                }
            }

            # Générer la facture
            generator = FactureGenerator()
            output_file = generator.generate_facture(facture_data)

            QMessageBox.information(self, "Succès", f"Facture exportée avec succès dans le fichier {output_file}")
            return

        # Méthode alternative si le générateur n'est pas disponible
        # Créer le contenu HTML de la facture avec le style ISOLOC
        html = f"""
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                @page {{ size: A4; margin: 2cm; }}
                body {{
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                    color: #000;
                }}
                .container {{
                    width: 100%;
                    max-width: 800px;
                    margin: 0 auto;
                }}
                .header {{
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 60px;
                }}
                .logo {{
                    width: 120px;
                }}
                .facture-info {{
                    text-align: right;
                }}
                .facture-info h1 {{
                    color: #1e3a8a;
                    margin-bottom: 10px;
                    font-family: Arial, sans-serif;
                    font-size: 24px;
                    font-weight: bold;
                }}
                .facture-info p {{
                    margin: 3px 0;
                    font-size: 12px;
                }}
                .client-info {{
                    margin-bottom: 20px;
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                }}
                .client-info p {{
                    margin: 3px 0;
                }}
                .objet {{
                    margin-bottom: 30px;
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                }}
                .objet h3 {{
                    text-transform: uppercase;
                    margin-bottom: 5px;
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                    font-weight: bold;
                }}
                .objet p {{
                    margin: 3px 0;
                    text-transform: uppercase;
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 30px;
                }}
                th {{
                    background-color: #1e3a8a;
                    color: white;
                    padding: 8px;
                    text-align: left;
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                    font-weight: bold;
                }}
                td {{
                    padding: 8px;
                    border-bottom: 1px solid #ddd;
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                    vertical-align: top;
                }}
                .item-description {{
                    font-size: 10px;
                    color: #666;
                    margin-top: 3px;
                }}
                .totals {{
                    width: 250px;
                    margin-left: auto;
                }}
                .totals table {{
                    width: 100%;
                    margin-bottom: 5px;
                }}
                .totals td {{
                    padding: 3px;
                    border: none;
                    text-align: right;
                    font-size: 12px;
                }}
                .totals .total-row {{
                    font-weight: bold;
                }}
                .footer {{
                    margin-top: 80px;
                    padding-top: 10px;
                    border-top: 1px solid #ddd;
                    font-size: 8px;
                    color: #333;
                    text-align: center;
                }}
                .footer p {{
                    margin: 2px 0;
                }}
                .montant-lettres {{
                    font-style: italic;
                    margin: 20px 0;
                    font-size: 11px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">
                        <!-- Logo ISOLOC SERVICE -->
                        <svg viewBox="0 0 300 150" xmlns="http://www.w3.org/2000/svg">
                            <path d="M150,20 C80,20 20,80 20,150 C20,220 80,280 150,280" stroke="black" stroke-width="10" fill="none"/>
                            <text x="150" y="70" font-family="Arial" font-size="40" font-weight="bold" text-anchor="middle">ISOLOC</text>
                            <text x="150" y="110" font-family="Arial" font-size="24" text-anchor="middle">SERVICE</text>
                        </svg>
                    </div>
                    <div class="facture-info">
                        <h1>FACTURE</h1>
                        <p><strong>Référence :</strong> {self.numero_facture.text()}</p>
                        <p><strong>Date :</strong> {self.date_facture.date().toString("dd/MM/yyyy")}</p>
                    </div>
                </div>

                <div class="client-info">
                    <p><strong>Client :</strong><br>{self.client_combo.currentText()}</p>
                </div>

                <div class="objet">
                    <p><strong>Objet :</strong><br>{objet}</p>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th style="width: 5%;">N°</th>
                            <th style="width: 45%;">Description</th>
                            <th style="width: 10%;">Unité</th>
                            <th style="width: 10%;">Quantité</th>
                            <th style="width: 15%;">Prix unitaire HT</th>
                            <th style="width: 15%;">Prix Total (HT)</th>
                        </tr>
                    </thead>
                    <tbody>
        """

        # Ajouter les lignes
        total_ht = 0
        for row in range(self.articles_table.rowCount()):
            # Récupérer les widgets à partir des données stockées
            designation_combo = None
            if self.articles_table.item(row, 1):
                designation_combo = self.articles_table.item(row, 1).data(Qt.UserRole)

            unite_edit = None
            if self.articles_table.item(row, 2):
                unite_edit = self.articles_table.item(row, 2).data(Qt.UserRole)

            quantite_spin = None
            if self.articles_table.item(row, 3):
                quantite_spin = self.articles_table.item(row, 3).data(Qt.UserRole)

            prix_edit = None
            if self.articles_table.item(row, 4):
                prix_edit = self.articles_table.item(row, 4).data(Qt.UserRole)

            if designation_combo and unite_edit and quantite_spin and prix_edit:
                designation = designation_combo.currentText()
                unite = unite_edit.text()
                quantite = quantite_spin.value()
                # Récupérer le texte du prix unitaire et nettoyer les caractères non numériques
                prix_text = prix_edit.text()
                # Remplacer tous les caractères non numériques (sauf le point décimal) par une chaîne vide
                prix_text = ''.join(c for c in prix_text if c.isdigit() or c == '.')
                try:
                    prix_unitaire = float(prix_text or 0)
                except ValueError:
                    # En cas d'erreur, utiliser 0 comme valeur par défaut
                    prix_unitaire = 0.0
                prix_total = quantite * prix_unitaire
                total_ht += prix_total

                # Ajouter des caractéristiques et spécifications
                specs = "Caractéristiques et spécifications..."

                html += f"""
                    <tr>
                        <td>{row + 1}</td>
                        <td>
                            {designation}
                            <div class="item-description">{specs}</div>
                        </td>
                        <td style="text-align: center;">{unite}</td>
                        <td style="text-align: center;">{quantite}</td>
                        <td style="text-align: right;">{prix_unitaire:.2f}</td>
                        <td style="text-align: right;">{prix_total:.2f}</td>
                    </tr>
                """

        # Calculer TVA et TTC
        tva = total_ht * 0.2
        ttc = total_ht + tva

        # Convertir le montant en lettres (exemple simplifié)
        montant_lettres = "Vingt-cinq mille deux cents dirhams TTC"

        # Ajouter les totaux
        html += f"""
                    </tbody>
                </table>

                <div class="totals">
                    <table>
                        <tr>
                            <td>Total HT</td>
                            <td>{total_ht:.2f}</td>
                        </tr>
                        <tr>
                            <td>TVA 20%</td>
                            <td>{tva:.2f}</td>
                        </tr>
                        <tr class="total-row">
                            <td>Total TTC</td>
                            <td>{ttc:.2f}</td>
                        </tr>
                    </table>
                </div>

                <div class="montant-lettres">
                    <p>Arrêté la présente facture à la somme de :</p>
                    <p>{montant_lettres}</p>
                </div>

                <div class="footer">
                    <p>ISOLOC SERVICE SARL - capital 100 000 dhs - adresse 7 RUE MOHAMED BAQUA 12 ETG APT N°57, Casablanca</p>
                    <p>TP 32136116 - CNSS 5302205 - IF 40253477 - RC 414653 CASABLANCA - ICE 001654789000098</p>
                    <p>RIB 007780002835000000654552 - TEL 0522829122 - fax 0521344545 - RC 52108787/8</p>
                </div>
            </div>
        </body>
        </html>
        """

        # Créer le document PDF
        document = QTextDocument()
        document.setHtml(html)

        # Configurer l'imprimante
        printer = QPrinter(QPrinter.PrinterResolution)
        printer.setOutputFormat(QPrinter.PdfFormat)

        # Créer le dossier de sortie s'il n'existe pas
        os.makedirs("factures", exist_ok=True)

        # Définir le nom du fichier
        filename = f"factures/facture_{self.numero_facture.text().replace('/', '_').replace(' ', '_').replace('(', '').replace(')', '')}.pdf"
        printer.setOutputFileName(filename)

        # Imprimer le document
        document.print_(printer)

        QMessageBox.information(self, "Succès", f"Facture exportée avec succès dans le fichier {filename}")

    def imprimer_facture(self):
        """Imprime la facture"""
        # Vérifier qu'un client est sélectionné
        if not self.client_combo.currentData():
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un client.")
            return

        # Vérifier qu'il y a au moins une ligne
        if self.articles_table.rowCount() == 0:
            QMessageBox.warning(self, "Erreur", "Veuillez ajouter au moins une ligne à la facture.")
            return

        # Récupérer l'objet (description du travail)
        objet = self.objet_input.text()

        # Créer le contenu HTML de la facture avec le style ISOLOC (même que pour l'export PDF)
        html = f"""
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                @page {{ size: A4; margin: 2cm; }}
                body {{
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                    color: #000;
                }}
                .container {{
                    width: 100%;
                    max-width: 800px;
                    margin: 0 auto;
                }}
                .header {{
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 60px;
                }}
                .logo {{
                    width: 120px;
                }}
                .facture-info {{
                    text-align: right;
                }}
                .facture-info h1 {{
                    color: #1e3a8a;
                    margin-bottom: 10px;
                    font-family: Arial, sans-serif;
                    font-size: 24px;
                    font-weight: bold;
                }}
                .facture-info p {{
                    margin: 3px 0;
                    font-size: 12px;
                }}
                .client-info {{
                    margin-bottom: 20px;
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                }}
                .client-info p {{
                    margin: 3px 0;
                }}
                .objet {{
                    margin-bottom: 30px;
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                }}
                .objet h3 {{
                    text-transform: uppercase;
                    margin-bottom: 5px;
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                    font-weight: bold;
                }}
                .objet p {{
                    margin: 3px 0;
                    text-transform: uppercase;
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 30px;
                }}
                th {{
                    background-color: #1e3a8a;
                    color: white;
                    padding: 8px;
                    text-align: left;
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                    font-weight: bold;
                }}
                td {{
                    padding: 8px;
                    border-bottom: 1px solid #ddd;
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                    vertical-align: top;
                }}
                .item-description {{
                    font-size: 10px;
                    color: #666;
                    margin-top: 3px;
                }}
                .totals {{
                    width: 250px;
                    margin-left: auto;
                }}
                .totals table {{
                    width: 100%;
                    margin-bottom: 5px;
                }}
                .totals td {{
                    padding: 3px;
                    border: none;
                    text-align: right;
                    font-size: 12px;
                }}
                .totals .total-row {{
                    font-weight: bold;
                }}
                .footer {{
                    margin-top: 80px;
                    padding-top: 10px;
                    border-top: 1px solid #ddd;
                    font-size: 8px;
                    color: #333;
                    text-align: center;
                }}
                .footer p {{
                    margin: 2px 0;
                }}
                .montant-lettres {{
                    font-style: italic;
                    margin: 20px 0;
                    font-size: 11px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">
                        <!-- Logo ISOLOC SERVICE -->
                        <svg viewBox="0 0 300 150" xmlns="http://www.w3.org/2000/svg">
                            <path d="M150,20 C80,20 20,80 20,150 C20,220 80,280 150,280" stroke="black" stroke-width="10" fill="none"/>
                            <text x="150" y="70" font-family="Arial" font-size="40" font-weight="bold" text-anchor="middle">ISOLOC</text>
                            <text x="150" y="110" font-family="Arial" font-size="24" text-anchor="middle">SERVICE</text>
                        </svg>
                    </div>
                    <div class="facture-info">
                        <h1>FACTURE</h1>
                        <p><strong>Référence :</strong> {self.numero_facture.text()}</p>
                        <p><strong>Date :</strong> {self.date_facture.date().toString("dd/MM/yyyy")}</p>
                    </div>
                </div>

                <div class="client-info">
                    <p><strong>Client :</strong><br>{self.client_combo.currentText()}</p>
                </div>

                <div class="objet">
                    <p><strong>Objet :</strong><br>{objet}</p>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th style="width: 5%;">N°</th>
                            <th style="width: 45%;">Description</th>
                            <th style="width: 10%;">Unité</th>
                            <th style="width: 10%;">Quantité</th>
                            <th style="width: 15%;">Prix unitaire HT</th>
                            <th style="width: 15%;">Prix Total (HT)</th>
                        </tr>
                    </thead>
                    <tbody>
        """

        # Ajouter les lignes
        total_ht = 0
        for row in range(self.articles_table.rowCount()):
            # Récupérer les widgets à partir des données stockées
            designation_combo = None
            if self.articles_table.item(row, 1):
                designation_combo = self.articles_table.item(row, 1).data(Qt.UserRole)

            unite_edit = None
            if self.articles_table.item(row, 2):
                unite_edit = self.articles_table.item(row, 2).data(Qt.UserRole)

            quantite_spin = None
            if self.articles_table.item(row, 3):
                quantite_spin = self.articles_table.item(row, 3).data(Qt.UserRole)

            prix_edit = None
            if self.articles_table.item(row, 4):
                prix_edit = self.articles_table.item(row, 4).data(Qt.UserRole)

            if designation_combo and unite_edit and quantite_spin and prix_edit:
                designation = designation_combo.currentText()
                unite = unite_edit.text()
                quantite = quantite_spin.value()
                # Récupérer le texte du prix unitaire et nettoyer les caractères non numériques
                prix_text = prix_edit.text()
                # Remplacer tous les caractères non numériques (sauf le point décimal) par une chaîne vide
                prix_text = ''.join(c for c in prix_text if c.isdigit() or c == '.')
                try:
                    prix_unitaire = float(prix_text or 0)
                except ValueError:
                    # En cas d'erreur, utiliser 0 comme valeur par défaut
                    prix_unitaire = 0.0
                prix_total = quantite * prix_unitaire
                total_ht += prix_total

                # Ajouter des caractéristiques et spécifications
                specs = "Caractéristiques et spécifications..."

                html += f"""
                    <tr>
                        <td>{row + 1}</td>
                        <td>
                            {designation}
                            <div class="item-description">{specs}</div>
                        </td>
                        <td style="text-align: center;">{unite}</td>
                        <td style="text-align: center;">{quantite}</td>
                        <td style="text-align: right;">{prix_unitaire:.2f}</td>
                        <td style="text-align: right;">{prix_total:.2f}</td>
                    </tr>
                """

        # Calculer TVA et TTC
        tva = total_ht * 0.2
        ttc = total_ht + tva

        # Convertir le montant en lettres (exemple simplifié)
        montant_lettres = "Vingt-cinq mille deux cents dirhams TTC"

        # Ajouter les totaux
        html += f"""
                    </tbody>
                </table>

                <div class="totals">
                    <table>
                        <tr>
                            <td>Total HT</td>
                            <td>{total_ht:.2f}</td>
                        </tr>
                        <tr>
                            <td>TVA 20%</td>
                            <td>{tva:.2f}</td>
                        </tr>
                        <tr class="total-row">
                            <td>Total TTC</td>
                            <td>{ttc:.2f}</td>
                        </tr>
                    </table>
                </div>

                <div class="montant-lettres">
                    <p>Arrêté la présente facture à la somme de :</p>
                    <p>{montant_lettres}</p>
                </div>

                <div class="footer">
                    <p>ISOLOC SERVICE SARL - capital 100 000 dhs - adresse 7 RUE MOHAMED BAQUA 12 ETG APT N°57, Casablanca</p>
                    <p>TP 32136116 - CNSS 5302205 - IF 40253477 - RC 414653 CASABLANCA - ICE 001654789000098</p>
                    <p>RIB 007780002835000000654552 - TEL 0522829122 - fax 0521344545 - RC 52108787/8</p>
                </div>
            </div>
        </body>
        </html>
        """

        # Créer le document
        document = QTextDocument()
        document.setHtml(html)

        # Configurer l'imprimante
        printer = QPrinter(QPrinter.PrinterResolution)

        # Afficher la boîte de dialogue d'impression
        dialog = QPrintDialog(printer, self)
        if dialog.exec() == QPrintDialog.Accepted:
            document.print_(printer)
            QMessageBox.information(self, "Succès", "Facture envoyée à l'imprimante.")

    def show_factures_list(self):
        """Affiche la liste des factures dans une boîte de dialogue"""
        dialog = FacturesListDialog(self.db_manager, self)

        # Connecter les signaux
        dialog.item_selected.connect(self.charger_facture)
        dialog.item_added.connect(self.nouvelle_facture)
        dialog.item_edited.connect(self.charger_facture)

        dialog.exec()

    def charger_facture(self, facture_id):
        """Charge une facture existante"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Récupérer les informations de la facture
            cursor.execute("""
                SELECT f.*, c.nom as client_nom, c.ice as client_ice
                FROM factures_vente f
                LEFT JOIN clients c ON f.client_id = c.id
                WHERE f.id = ?
            """, (facture_id,))
            facture = cursor.fetchone()

            if not facture:
                QMessageBox.warning(self, "Erreur", "Facture introuvable.")
                return

            # Mettre à jour les champs du formulaire
            self.numero_facture.setText(facture['numero'])

            # Convertir la date
            date_obj = datetime.datetime.strptime(facture['date_creation'], '%Y-%m-%d')
            self.date_facture.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))

            # Sélectionner le client
            for i in range(self.client_combo.count()):
                item_data = self.client_combo.itemData(i)
                if item_data and item_data.get("id") == facture['client_id']:
                    self.client_combo.setCurrentIndex(i)
                    break

            # Mettre à jour les autres champs
            self.bon_commande_input.setText(facture['bon_commande'] or "")
            self.marche_input.setText(facture['marche'] or "")
            self.objet_input.setText(facture['objet'] or "")

            # Charger les lignes de la facture
            self.articles_table.setRowCount(0)

            cursor.execute("""
                SELECT * FROM lignes_facture
                WHERE facture_id = ?
                ORDER BY id
            """, (facture_id,))
            lignes = cursor.fetchall()

            for ligne in lignes:
                self.ajouter_ligne_existante(ligne)

            # Mettre à jour les totaux
            self.calculer_totaux()

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement de la facture: {str(e)}")

    def ajouter_ligne_existante(self, ligne):
        """Ajoute une ligne existante au tableau des articles"""
        row = self.articles_table.rowCount()
        self.articles_table.insertRow(row)

        # Définir une hauteur de ligne suffisante
        self.articles_table.setRowHeight(row, 60)

        # Numéro de ligne (QLabel)
        num_label = QLabel(str(row + 1))
        num_label.setAlignment(Qt.AlignCenter)
        num_label.setStyleSheet("""
            background-color: #1e3a8a;
            color: white;
            font-weight: bold;
            padding: 8px;
            font-family: Arial;
            font-size: 14px;
            border-radius: 4px;
        """)
        self.articles_table.setCellWidget(row, 0, num_label)

        # Créer un widget conteneur pour la désignation
        designation_container = QWidget()
        designation_layout = QVBoxLayout(designation_container)
        designation_layout.setContentsMargins(2, 2, 2, 2)
        designation_layout.setSpacing(0)

        # Désignation (QComboBox avec recherche)
        designation_combo = QComboBox()
        designation_combo.setEditable(True)
        designation_combo.setPlaceholderText("Taper pour rechercher un produit")
        designation_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                font-family: Arial;
                font-size: 14px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #ddd;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #ddd;
                selection-background-color: #e3f2fd;
                font-size: 14px;
            }
        """)

        # Charger tous les produits dans le combo
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT id, code, designation, unite, prix_vente FROM produits ORDER BY designation")
        produits = cursor.fetchall()

        # Ajouter une option vide
        designation_combo.addItem("", None)

        # Ajouter les produits
        for produit in produits:
            designation_combo.addItem(f"{produit['code']} - {produit['designation']}", produit)

        # Sélectionner le produit si disponible
        if ligne['produit_id']:
            for i in range(designation_combo.count()):
                item_data = designation_combo.itemData(i)
                if item_data and item_data.get("id") == ligne['produit_id']:
                    designation_combo.setCurrentIndex(i)
                    break
        else:
            # Si pas de produit_id, définir le texte directement
            designation_combo.setCurrentText(ligne['designation'])

        # Connecter le signal de changement
        designation_combo.currentIndexChanged.connect(lambda: self.produit_selectionne_combo(row, designation_combo))
        designation_combo.lineEdit().textChanged.connect(lambda: self.rechercher_produit(row))

        designation_layout.addWidget(designation_combo)
        designation_container.setLayout(designation_layout)
        self.articles_table.setCellWidget(row, 1, designation_container)

        # Créer un widget conteneur pour l'unité
        unite_container = QWidget()
        unite_layout = QVBoxLayout(unite_container)
        unite_layout.setContentsMargins(2, 2, 2, 2)
        unite_layout.setSpacing(0)

        # Unité (QLineEdit)
        unite_edit = QLineEdit(ligne['unite'] or "")
        unite_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                font-family: Arial;
                font-size: 14px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
        """)
        unite_edit.setReadOnly(False)  # Permettre la modification

        unite_layout.addWidget(unite_edit)
        unite_container.setLayout(unite_layout)
        self.articles_table.setCellWidget(row, 2, unite_container)

        # Créer un widget conteneur pour la quantité
        quantite_container = QWidget()
        quantite_layout = QVBoxLayout(quantite_container)
        quantite_layout.setContentsMargins(2, 2, 2, 2)
        quantite_layout.setSpacing(0)

        # Quantité (QSpinBox)
        quantite_spin = QSpinBox()
        quantite_spin.setRange(1, 10000)
        quantite_spin.setValue(ligne['quantite'])
        quantite_spin.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                font-family: Arial;
                font-size: 14px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                width: 20px;
                border-left: 1px solid #ddd;
                background-color: #f5f5f5;
            }
        """)
        quantite_spin.valueChanged.connect(lambda: self.calculer_ligne(row))

        quantite_layout.addWidget(quantite_spin)
        quantite_container.setLayout(quantite_layout)
        self.articles_table.setCellWidget(row, 3, quantite_container)

        # Créer un widget conteneur pour le prix unitaire
        prix_container = QWidget()
        prix_layout = QVBoxLayout(prix_container)
        prix_layout.setContentsMargins(2, 2, 2, 2)
        prix_layout.setSpacing(0)

        # Prix unitaire (QLineEdit)
        prix_edit = QLineEdit(str(ligne['prix_unitaire_ht']))
        prix_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                font-family: Arial;
                font-size: 14px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
        """)
        prix_edit.textChanged.connect(lambda: self.calculer_ligne(row))

        prix_layout.addWidget(prix_edit)
        prix_container.setLayout(prix_layout)
        self.articles_table.setCellWidget(row, 4, prix_container)

        # Créer un widget conteneur pour le prix total
        total_container = QWidget()
        total_layout = QVBoxLayout(total_container)
        total_layout.setContentsMargins(2, 2, 2, 2)
        total_layout.setSpacing(0)

        # Prix total (QLineEdit en lecture seule)
        total_edit = QLineEdit(f"{ligne['total_ht']:.2f} DH")
        total_edit.setReadOnly(True)
        total_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                background-color: #f0f0f0;
                font-family: Arial;
                font-size: 14px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-weight: bold;
            }
        """)

        total_layout.addWidget(total_edit)
        total_container.setLayout(total_layout)
        self.articles_table.setCellWidget(row, 5, total_container)

        # Boutons d'action
        action_container = QWidget()
        action_layout = QHBoxLayout(action_container)
        action_layout.setContentsMargins(2, 2, 2, 2)
        action_layout.setSpacing(5)

        # Bouton de suppression
        delete_btn = QPushButton("🗑️")
        delete_btn.setToolTip("Supprimer cette ligne")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
                min-width: 30px;
                max-width: 30px;
                min-height: 30px;
                max-height: 30px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_btn.clicked.connect(lambda: self.supprimer_ligne(row))
        action_layout.addWidget(delete_btn)

        action_container.setLayout(action_layout)
        self.articles_table.setCellWidget(row, 6, action_container)

        # Stocker les références aux widgets pour pouvoir les récupérer plus tard
        self.articles_table.setItem(row, 1, QTableWidgetItem())
        self.articles_table.item(row, 1).setData(Qt.UserRole, designation_combo)

        self.articles_table.setItem(row, 2, QTableWidgetItem())
        self.articles_table.item(row, 2).setData(Qt.UserRole, unite_edit)

        self.articles_table.setItem(row, 3, QTableWidgetItem())
        self.articles_table.item(row, 3).setData(Qt.UserRole, quantite_spin)

        self.articles_table.setItem(row, 4, QTableWidgetItem())
        self.articles_table.item(row, 4).setData(Qt.UserRole, prix_edit)

        self.articles_table.setItem(row, 5, QTableWidgetItem())
        self.articles_table.item(row, 5).setData(Qt.UserRole, total_edit)

        # Calculer le total de la ligne
        self.calculer_ligne(row)
