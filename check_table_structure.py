#!/usr/bin/env python3
"""
Check the structure of the produits table
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from comptabilite_app.database.db_manager import DatabaseManager

def check_table_structure():
    """Check the structure of the produits table"""
    print("🔍 Checking produits table structure...")
    
    # Initialize database manager
    db_manager = DatabaseManager()
    
    try:
        cursor = db_manager.conn.cursor()
        
        # Check table structure
        cursor.execute("PRAGMA table_info(produits)")
        columns = cursor.fetchall()
        
        print("📋 Current columns in produits table:")
        for col in columns:
            print(f"   - {col['name']}: {col['type']}")
        
        # Check if famille_id column exists
        column_names = [col['name'] for col in columns]
        
        if 'famille_id' not in column_names:
            print("❌ famille_id column is missing!")
            print("🔧 Adding famille_id column...")
            
            try:
                cursor.execute("ALTER TABLE produits ADD COLUMN famille_id INTEGER")
                db_manager.conn.commit()
                print("✅ famille_id column added successfully!")
            except Exception as e:
                print(f"❌ Error adding famille_id column: {str(e)}")
        else:
            print("✅ famille_id column exists!")
            
        # Check if familles_produits table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='familles_produits'")
        if not cursor.fetchone():
            print("❌ familles_produits table is missing!")
            print("🔧 Creating familles_produits table...")
            
            try:
                cursor.execute("""
                    CREATE TABLE familles_produits (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        nom TEXT UNIQUE NOT NULL,
                        description TEXT,
                        date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                db_manager.conn.commit()
                print("✅ familles_produits table created successfully!")
            except Exception as e:
                print(f"❌ Error creating familles_produits table: {str(e)}")
        else:
            print("✅ familles_produits table exists!")
        
    except Exception as e:
        print(f"❌ Error checking table structure: {str(e)}")

if __name__ == "__main__":
    check_table_structure()
