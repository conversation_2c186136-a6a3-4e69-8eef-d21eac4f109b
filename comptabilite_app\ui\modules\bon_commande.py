from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QComboBox, QDateEdit, QSpinBox, QFormLayout,
                              QMessageBox, QHeaderView, QGridLayout)
from PySide6.QtCore import Qt, QDate
import sqlite3
import datetime
import os

class BonCommandeModule(QWidget):
    """Module de gestion des bons de commande"""

    def __init__(self, db_manager, signals=None):
        super().__init__()
        self.db_manager = db_manager
        self.signals = signals  # Signaux pour la communication entre modules
        self.setup_ui()
        self.nouveau_bon()

        # Connecter le signal de mise à jour des clients si disponible
        if self.signals:
            self.signals.clients_changed.connect(self.charger_clients)

    def setup_ui(self):
        """Configure l'interface utilisateur du module"""
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # Titre du module
        title = QLabel("BON DE COMMANDE")
        title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)

        # Section des informations générales
        info_layout = QGridLayout()
        info_layout.setSpacing(10)

        # Numéro de bon de commande
        info_layout.addWidget(QLabel("N° DE BON DE COMMANDE"), 0, 0)
        self.numero_bon = QLineEdit()
        self.numero_bon.setReadOnly(True)
        self.numero_bon.setStyleSheet("border: 1px solid #4CAF50; border-radius: 3px; padding: 5px;")
        info_layout.addWidget(self.numero_bon, 1, 0)

        # Client
        info_layout.addWidget(QLabel("CLIENT"), 0, 1)
        self.client_combo = QComboBox()
        self.client_combo.setStyleSheet("border: 1px solid #4CAF50; border-radius: 3px; padding: 5px;")
        self.client_combo.setPlaceholderText("Sélectionne client")
        info_layout.addWidget(self.client_combo, 1, 1)

        # Objet
        info_layout.addWidget(QLabel("OBJET"), 0, 2)
        self.objet_input = QLineEdit()
        self.objet_input.setStyleSheet("border: 1px solid #4CAF50; border-radius: 3px; padding: 5px;")
        info_layout.addWidget(self.objet_input, 1, 2)

        # Montant global
        info_layout.addWidget(QLabel("MONTANT GLOBAL"), 0, 3)
        self.montant_global_input = QLineEdit()
        self.montant_global_input.setStyleSheet("border: 1px solid #4CAF50; border-radius: 3px; padding: 5px;")
        self.montant_global_input.setReadOnly(False)  # Rendre saisissable
        self.montant_global_input.textChanged.connect(self.verifier_montant_global)
        info_layout.addWidget(self.montant_global_input, 1, 3)

        # Délai
        info_layout.addWidget(QLabel("DELAI"), 0, 0, 1, 1, Qt.AlignLeft)
        self.delai_input = QLineEdit("Manuel")
        self.delai_input.setStyleSheet("border: 1px solid #4CAF50; border-radius: 3px; padding: 5px;")
        info_layout.addWidget(self.delai_input, 3, 0)

        # Date ordre de service
        info_layout.addWidget(QLabel("DATE ORDRE DE SERVICE"), 2, 1)
        from ..style import create_styled_date_edit
        self.date_ordre_input = create_styled_date_edit()
        info_layout.addWidget(self.date_ordre_input, 3, 1)

        # Délai d'exécution
        info_layout.addWidget(QLabel("DÉLAI D'EXÉCUTION"), 2, 2)
        self.delai_execution_input = QLineEdit("")
        self.delai_execution_input.setStyleSheet("border: 1px solid #4CAF50; border-radius: 3px; padding: 5px;")
        info_layout.addWidget(self.delai_execution_input, 3, 2)

        main_layout.addLayout(info_layout)

        # Section détails
        details_label = QLabel("DÉTAILS")
        details_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        main_layout.addWidget(details_label)

        # En-têtes du tableau
        headers_layout = QHBoxLayout()
        headers_layout.addWidget(QLabel("DÉSIGNATION"), 3)
        headers_layout.addWidget(QLabel("UNITÉ"), 1)
        headers_layout.addWidget(QLabel("QTÉ"), 1)
        headers_layout.addWidget(QLabel("PRIX UNITAIRE HT"), 1)
        headers_layout.addWidget(QLabel("PRIX TOTAL HT"), 1)
        headers_layout.addWidget(QLabel("ACTION"), 1)
        main_layout.addLayout(headers_layout)

        # Tableau des articles
        self.articles_table = QTableWidget(5, 6)
        self.articles_table.setHorizontalHeaderLabels([
            "DÉSIGNATION", "UNITÉ", "QTÉ", "PRIX UNITAIRE HT", "PRIX TOTAL HT", "ACTION"
        ])
        self.articles_table.horizontalHeader().setVisible(False)
        self.articles_table.verticalHeader().setVisible(False)
        self.articles_table.setShowGrid(False)
        self.articles_table.setStyleSheet("""
            QTableWidget {
                border: none;
                background-color: transparent;
            }
            QTableWidget::item {
                border: 1px solid #4CAF50;
                border-radius: 3px;
                padding: 5px;
                margin: 2px;
            }
        """)

        # Configuration des colonnes
        self.articles_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)  # Désignation
        self.articles_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Unité
        self.articles_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Quantité
        self.articles_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Prix unitaire
        self.articles_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Prix total
        self.articles_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Action

        # Connecter le signal cellChanged pour calculer automatiquement les totaux
        self.articles_table.cellChanged.connect(self.calculer_ligne)

        # Ajouter des lignes au tableau
        for row in range(5):
            self.ajouter_ligne_tableau(row)

        main_layout.addWidget(self.articles_table)

        # Section totaux
        totals_layout = QHBoxLayout()
        totals_layout.addStretch()

        totals_form = QFormLayout()
        totals_form.setSpacing(10)
        totals_form.setLabelAlignment(Qt.AlignRight)

        self.total_ht_label = QLineEdit("automatique")
        self.total_ht_label.setReadOnly(True)
        self.total_ht_label.setStyleSheet("border: 1px solid #4CAF50; border-radius: 3px; padding: 5px;")
        totals_form.addRow("TOTAL HT", self.total_ht_label)

        self.tva_label = QLineEdit("automatique")
        self.tva_label.setReadOnly(True)
        self.tva_label.setStyleSheet("border: 1px solid #4CAF50; border-radius: 3px; padding: 5px;")
        totals_form.addRow("TVA 20%", self.tva_label)

        self.ttc_label = QLineEdit("automatique")
        self.ttc_label.setReadOnly(True)
        self.ttc_label.setStyleSheet("border: 1px solid #4CAF50; border-radius: 3px; padding: 5px;")
        totals_form.addRow("TTC", self.ttc_label)

        totals_layout.addLayout(totals_form)
        main_layout.addLayout(totals_layout)

        # Message de notification
        self.notification_label = QLabel("")
        self.notification_label.setObjectName("notification_label")
        self.notification_label.setStyleSheet("color: #2196F3; font-style: italic; margin: 10px 0;")
        self.notification_label.setAlignment(Qt.AlignRight)
        main_layout.addWidget(self.notification_label)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        self.valider_btn = QPushButton("Valider")
        self.valider_btn.setStyleSheet("""
            QPushButton {
                background-color: white;
                color: black;
                border: 1px solid #4CAF50;
                border-radius: 3px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #e8f5e9;
            }
        """)
        self.valider_btn.clicked.connect(self.enregistrer_bon)
        buttons_layout.addWidget(self.valider_btn)

        self.enregistrer_pdf_btn = QPushButton("Enregistrer PDF")
        self.enregistrer_pdf_btn.setStyleSheet("""
            QPushButton {
                background-color: white;
                color: black;
                border: 1px solid #4CAF50;
                border-radius: 3px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #e8f5e9;
            }
        """)
        self.enregistrer_pdf_btn.clicked.connect(self.exporter_pdf)
        buttons_layout.addWidget(self.enregistrer_pdf_btn)

        self.imprimer_btn = QPushButton("Imprimer")
        self.imprimer_btn.setStyleSheet("""
            QPushButton {
                background-color: white;
                color: black;
                border: 1px solid #4CAF50;
                border-radius: 3px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #e8f5e9;
            }
        """)
        self.imprimer_btn.clicked.connect(self.imprimer_bon)
        buttons_layout.addWidget(self.imprimer_btn)

        buttons_layout.addStretch()
        main_layout.addLayout(buttons_layout)

    def charger_clients(self):
        """Charge la liste des clients dans le QComboBox"""
        # Déconnecter le signal pour éviter les appels multiples pendant le chargement
        try:
            self.client_combo.currentIndexChanged.disconnect()
        except:
            pass  # Si le signal n'était pas connecté, ignorer l'erreur

        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT id, nom FROM clients ORDER BY nom")
        clients = cursor.fetchall()

        # Sauvegarder les données actuelles
        current_data = self.client_combo.currentData()

        self.client_combo.clear()
        self.client_combo.addItem("Sélectionne client", None)

        for client in clients:
            self.client_combo.addItem(client['nom'], client['id'])

        # Restaurer l'index précédent si possible
        if current_data:
            # Chercher l'index du client précédemment sélectionné
            for i in range(self.client_combo.count()):
                if self.client_combo.itemData(i) == current_data:
                    self.client_combo.setCurrentIndex(i)
                    break

        # Reconnecter le signal après avoir chargé les clients
        try:
            self.client_combo.currentIndexChanged.connect(self.client_selectionne)
        except:
            pass  # Si la méthode n'existe pas, ignorer l'erreur

    def client_selectionne(self):
        """Méthode appelée lorsqu'un client est sélectionné"""
        # Cette méthode peut être étendue pour mettre à jour d'autres informations
        # basées sur le client sélectionné si nécessaire
        pass

    def nouveau_bon(self):
        """Initialise un nouveau bon de commande"""
        # Générer un numéro de bon de commande
        date_str = datetime.datetime.now().strftime("%Y%m")
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM commandes WHERE numero LIKE ?", (f"BC{date_str}%",))
        count = cursor.fetchone()[0] + 1
        self.numero_bon.setText(f"BC{date_str}-{count:03d}")

        # Charger les clients
        self.charger_clients()

        # Réinitialiser les champs
        self.objet_input.clear()
        self.montant_global_input.setText("0.00 DH")
        self.delai_input.setText("")
        self.date_ordre_input.setDate(QDate.currentDate())
        self.delai_execution_input.setText("")

        # Réinitialiser le tableau
        self.articles_table.setRowCount(0)
        for row in range(5):
            self.articles_table.insertRow(row)
            self.ajouter_ligne_tableau(row)

        # Réinitialiser les totaux
        self.total_ht_label.setText("0.00 DH")
        self.tva_label.setText("0.00 DH")
        self.ttc_label.setText("0.00 DH")

        # Réinitialiser la notification
        self.notification_label.setText("")

    def calculer_ligne(self, row, column):
        """Calcule automatiquement le total d'une ligne lorsque la quantité ou le prix unitaire change"""
        # Ne pas recalculer si on modifie la colonne du total
        if column == 4:
            return

        # Récupérer la quantité et le prix unitaire
        qte_item = self.articles_table.item(row, 2)
        prix_item = self.articles_table.item(row, 3)

        if qte_item and prix_item and qte_item.text() and prix_item.text():
            try:
                qte = int(qte_item.text())
                prix = float(prix_item.text().replace("DH", "").strip())
                total = qte * prix

                # Mettre à jour le total de la ligne
                self.articles_table.setItem(row, 4, QTableWidgetItem(f"{total:.2f} DH"))

                # Recalculer les totaux
                self.calculer_totaux()
            except ValueError:
                pass

    def ajouter_ligne_tableau(self, row):
        """Ajoute une ligne au tableau avec les widgets appropriés"""
        # Créer un QComboBox pour la désignation du produit
        designation_combo = QComboBox()
        designation_combo.setStyleSheet("""
            border: none;
            background-color: transparent;
            padding: 2px;
        """)
        designation_combo.addItem("-- Sélectionner un produit --", None)
        self.charger_produits_combo(designation_combo)
        designation_combo.currentIndexChanged.connect(lambda: self.produit_selectionne(row))
        self.articles_table.setCellWidget(row, 0, designation_combo)

        # Créer un QTableWidgetItem pour l'unité (sera rempli automatiquement)
        unite_item = QTableWidgetItem("")
        self.articles_table.setItem(row, 1, unite_item)

        # Créer un QSpinBox pour la quantité
        quantite_spin = QSpinBox()
        quantite_spin.setRange(1, 1000000)
        quantite_spin.setValue(1)
        quantite_spin.setStyleSheet("border: none; background-color: transparent;")
        quantite_spin.valueChanged.connect(lambda: self.calculer_ligne_spin(row))
        self.articles_table.setCellWidget(row, 2, quantite_spin)

        # Créer un QLineEdit pour le prix unitaire
        prix_edit = QLineEdit("0.00")
        prix_edit.setStyleSheet("border: none; background-color: transparent;")
        prix_edit.textChanged.connect(lambda: self.calculer_ligne_spin(row))
        self.articles_table.setCellWidget(row, 3, prix_edit)

        # Créer un QTableWidgetItem pour le total de la ligne
        total_item = QTableWidgetItem("0.00 DH")
        self.articles_table.setItem(row, 4, total_item)

        # Ajouter les boutons d'action
        action_cell = QWidget()
        action_layout = QHBoxLayout(action_cell)
        action_layout.setContentsMargins(2, 2, 2, 2)

        edit_btn = QPushButton("✏️")
        edit_btn.setToolTip("Modifier")
        edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 3px;
                min-width: 24px;
                min-height: 24px;
                max-width: 24px;
                max-height: 24px;
            }
        """)
        edit_btn.clicked.connect(lambda _, r=row: self.editer_ligne(r))
        action_layout.addWidget(edit_btn)

        delete_btn = QPushButton("🗑️")
        delete_btn.setToolTip("Supprimer")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 3px;
                min-width: 24px;
                min-height: 24px;
                max-width: 24px;
                max-height: 24px;
            }
        """)
        delete_btn.clicked.connect(lambda _, r=row: self.supprimer_ligne(r))
        action_layout.addWidget(delete_btn)

        self.articles_table.setCellWidget(row, 5, action_cell)

    def charger_produits_combo(self, combo):
        """Charge la liste des produits dans un QComboBox"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT id, code, designation, unite, prix_vente FROM produits ORDER BY designation")
        produits = cursor.fetchall()

        for produit in produits:
            combo.addItem(f"{produit['code']} - {produit['designation']}",
                         {"id": produit['id'], "unite": produit['unite'], "prix": produit['prix_vente']})

    def produit_selectionne(self, row):
        """Met à jour les champs lorsqu'un produit est sélectionné"""
        designation_combo = self.articles_table.cellWidget(row, 0)
        produit_data = designation_combo.currentData()

        if produit_data:
            # Mettre à jour l'unité
            unite = produit_data["unite"] or ""
            self.articles_table.item(row, 1).setText(unite)

            # Mettre à jour le prix unitaire
            prix_edit = self.articles_table.cellWidget(row, 3)
            prix_edit.setText(str(produit_data["prix"]))

            # Calculer le total de la ligne
            self.calculer_ligne_spin(row)

    def calculer_ligne_spin(self, row):
        """Calcule le total d'une ligne avec les widgets QSpinBox et QLineEdit"""
        quantite_spin = self.articles_table.cellWidget(row, 2)
        prix_edit = self.articles_table.cellWidget(row, 3)

        if quantite_spin and prix_edit:
            try:
                quantite = quantite_spin.value()
                prix = float(prix_edit.text().replace("DH", "").strip() or 0)
                total = quantite * prix

                # Mettre à jour le total de la ligne
                if not self.articles_table.item(row, 4):
                    self.articles_table.setItem(row, 4, QTableWidgetItem())
                self.articles_table.item(row, 4).setText(f"{total:.2f} DH")

                # Recalculer les totaux
                self.calculer_totaux()
            except ValueError:
                pass

    def calculer_totaux(self):
        """Calcule les totaux du bon de commande"""
        total_ht = 0

        # Parcourir toutes les lignes du tableau
        for row in range(self.articles_table.rowCount()):
            total_item = self.articles_table.item(row, 4)
            if total_item and total_item.text():
                try:
                    total_ht += float(total_item.text().replace("DH", "").strip())
                except ValueError:
                    pass

        # Calculer la TVA et le total TTC
        tva = total_ht * 0.2  # TVA 20%
        ttc = total_ht + tva

        # Mettre à jour les labels
        self.total_ht_label.setText(f"{total_ht:.2f} DH")
        self.tva_label.setText(f"{tva:.2f} DH")
        self.ttc_label.setText(f"{ttc:.2f} DH")

        # Vérifier si le montant global est différent du TTC
        self.verifier_montant_global()

    def verifier_montant_global(self):
        """Vérifie si le montant global est différent du TTC et affiche une notification"""
        try:
            montant_global = float(self.montant_global_input.text().replace("DH", "").strip() or 0)
            ttc = float(self.ttc_label.text().replace("DH", "").strip() or 0)

            # Notification en bas du formulaire
            notification = self.findChild(QLabel, "notification_label")

            if abs(montant_global - ttc) > 0.01:  # Tolérance pour les erreurs d'arrondi
                if notification:
                    notification.setText("Le montant est différent du montant global, merci de corriger.")
                    notification.setStyleSheet("color: red; font-style: italic; margin: 10px 0;")
            else:
                if notification:
                    notification.setText("")
        except ValueError:
            pass

    def editer_ligne(self, row):
        """Ouvre une boîte de dialogue pour éditer une ligne"""
        QMessageBox.information(self, "Édition", f"Édition de la ligne {row+1}")

    def supprimer_ligne(self, row):
        """Supprime une ligne du tableau"""
        # Vider les cellules de la ligne
        for col in range(5):
            self.articles_table.setItem(row, col, QTableWidgetItem(""))

        # Recalculer les totaux
        self.calculer_totaux()

    def enregistrer_bon(self):
        """Enregistre le bon de commande dans la base de données"""
        # Vérifier qu'un client est sélectionné
        client_id = self.client_combo.currentData()
        if not client_id:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un client.")
            return

        # Vérifier que l'objet est renseigné
        objet = self.objet_input.text()
        if not objet:
            QMessageBox.warning(self, "Erreur", "Veuillez renseigner l'objet du bon de commande.")
            return

        # Récupérer les données du bon de commande
        numero = self.numero_bon.text()
        date_creation = datetime.datetime.now().strftime("%Y-%m-%d")

        # Calculer les totaux
        total_ht = 0
        for row in range(self.articles_table.rowCount()):
            item = self.articles_table.item(row, 4)
            if item and item.text():
                try:
                    total_ht += float(item.text().replace("DH", "").strip())
                except ValueError:
                    pass

        total_tva = total_ht * 0.2  # TVA 20%
        total_ttc = total_ht + total_tva

        # Enregistrer le bon de commande
        cursor = self.db_manager.conn.cursor()
        try:
            cursor.execute(
                """INSERT INTO commandes
                   (numero, date_creation, client_id, total_ht, total_tva, total_ttc, statut, type, notes)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (numero, date_creation, client_id, total_ht, total_tva, total_ttc, "En attente", "commande", objet)
            )

            commande_id = cursor.lastrowid

            # Enregistrer les lignes du bon de commande
            for row in range(self.articles_table.rowCount()):
                designation_combo = self.articles_table.cellWidget(row, 0)
                quantite_spin = self.articles_table.cellWidget(row, 2)
                prix_edit = self.articles_table.cellWidget(row, 3)
                total_item = self.articles_table.item(row, 4)

                if designation_combo and designation_combo.currentText() != "-- Sélectionner un produit --":
                    designation = designation_combo.currentText()
                    produit_data = designation_combo.currentData()
                    produit_id = produit_data["id"] if produit_data else None

                    try:
                        qte = quantite_spin.value() if quantite_spin else 0
                        prix_unitaire = float(prix_edit.text().replace("DH", "").strip()) if prix_edit and prix_edit.text() else 0
                        total_ligne = float(total_item.text().replace("DH", "").strip()) if total_item and total_item.text() else 0

                        if qte > 0 and prix_unitaire > 0:
                            cursor.execute(
                                """INSERT INTO lignes_commande
                                   (commande_id, produit_id, designation, quantite, prix_unitaire_ht, taux_tva, total_ht)
                                   VALUES (?, ?, ?, ?, ?, ?, ?)""",
                                (commande_id, produit_id, designation, qte, prix_unitaire, 20.0, total_ligne)
                            )
                    except ValueError:
                        pass

            self.db_manager.conn.commit()
            QMessageBox.information(self, "Succès", "Bon de commande enregistré avec succès.")
            self.nouveau_bon()

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue lors de l'enregistrement : {str(e)}")

    def exporter_pdf(self):
        """Exporte le bon de commande en PDF"""
        # Vérifier qu'un client est sélectionné
        if not self.client_combo.currentData():
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un client.")
            return

        # Créer le dossier de sortie s'il n'existe pas
        os.makedirs("documents", exist_ok=True)

        # Définir le nom du fichier
        numero = self.numero_bon.text()
        filename = f"documents/bon_commande_{numero.replace('/', '_')}.pdf"

        # Afficher un message de succès (à implémenter réellement avec une bibliothèque PDF)
        QMessageBox.information(self, "Succès", f"Bon de commande exporté avec succès dans le fichier {filename}")

    def imprimer_bon(self):
        """Imprime le bon de commande"""
        # Vérifier qu'un client est sélectionné
        if not self.client_combo.currentData():
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un client.")
            return

        # Afficher un message de succès (à implémenter réellement avec QPrinter)
        QMessageBox.information(self, "Succès", "Bon de commande envoyé à l'imprimante.")
