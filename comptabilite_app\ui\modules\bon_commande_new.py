from PySide6.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox, QFormLayout,
                              QMessageBox, QHeaderView, QGridLayout, QFrame,
                              QDialog)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QIcon, QColor, QBrush
import sqlite3
import datetime
import os

# Importer les composants de base
from ..components.base_module import BaseModule

# Importer les styles et icônes
from ..theme import COLORS, BORDER_RADIUS, SPACING
from ..icons.icons import ORDERS_ICON

# Importer l'exportateur Excel
try:
    from ...utils.excel_exporter import ExcelExporter
    excel_exporter_available = True
except ImportError:
    excel_exporter_available = False

class BonCommandeFormDialog(QDialog):
    """Boîte de dialogue pour ajouter ou modifier un bon de commande"""

    # Signal émis lorsqu'un bon de commande est ajouté ou modifié
    bon_saved = Signal()
    # Signal émis pour actualiser le stock
    stock_updated = Signal()

    def __init__(self, db_manager, bon_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.bon_id = bon_id
        self.is_edit_mode = bon_id is not None

        self.setWindowTitle("Modifier un bon de commande" if self.is_edit_mode else "Ajouter un bon de commande")
        self.setMinimumWidth(900)
        self.setMinimumHeight(700)
        self.setModal(True)

        # Initialiser les variables
        self.numero_bon = None
        self.client_combo = None
        self.objet_input = None
        self.date_creation_input = None
        self.date_livraison_input = None
        self.bon_commande_combo = None
        self.marche_combo = None
        self.articles_table = None
        self.total_ht_label = None
        self.tva_label = None
        self.ttc_label = None

        # Configurer l'interface utilisateur
        self.setup_ui()

        # Charger les données si on est en mode édition
        if self.is_edit_mode:
            self.charger_bon(bon_id)
        else:
            self.nouveau_bon()

    def setup_ui(self):
        """Configure l'interface utilisateur de la boîte de dialogue"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # Titre
        title_label = QLabel("Informations du bon de commande")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #1E3A8A;
            margin-bottom: 10px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # Section des informations générales
        info_layout = QGridLayout()
        info_layout.setSpacing(10)

        # Ligne 1: Date de création et Client
        info_layout.addWidget(QLabel("Date de création:"), 0, 0)
        from ..style import create_styled_date_edit
        self.date_creation_input = create_styled_date_edit()
        info_layout.addWidget(self.date_creation_input, 0, 1)

        info_layout.addWidget(QLabel("Client:"), 0, 2)
        self.client_combo = QComboBox()
        self.client_combo.setStyleSheet("border: 1px solid #E5E7EB; border-radius: 4px; padding: 8px;")
        self.charger_clients()
        info_layout.addWidget(self.client_combo, 0, 3)

        # Ligne 2: Bon de commande
        info_layout.addWidget(QLabel("Bon de commande:"), 1, 0)
        self.bon_commande_combo = QComboBox()
        self.bon_commande_combo.setEditable(True)
        self.bon_commande_combo.setPlaceholderText("Sélectionner ou taper")
        self.bon_commande_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
                padding: 8px;
            }
            QComboBox::drop-down {
                border: none;
                width: 0px;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
        """)
        self.charger_bons_commande()
        info_layout.addWidget(self.bon_commande_combo, 1, 1, 1, 3)

        # Ligne 3: Marché
        info_layout.addWidget(QLabel("Marché:"), 2, 0)
        self.marche_combo = QComboBox()
        self.marche_combo.setEditable(True)
        self.marche_combo.setPlaceholderText("Sélectionner ou taper")
        self.marche_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
                padding: 8px;
            }
            QComboBox::drop-down {
                border: none;
                width: 0px;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
        """)
        self.charger_marches()
        info_layout.addWidget(self.marche_combo, 2, 1, 1, 3)

        # إضافة الاقتراحات التلقائية لحقل المشروع
        try:
            from ..components.autocomplete_widget import setup_autocomplete_for_widget
            if self.marche_combo.lineEdit():
                setup_autocomplete_for_widget(self.marche_combo.lineEdit(), self.db_manager, "marche_reference")
                print("✅ تم تطبيق الاقتراحات التلقائية على حقل المشروع")
        except Exception as e:
            print(f"❌ خطأ في تطبيق الاقتراحات التلقائية على حقل المشروع: {str(e)}")

        # Ligne 4: Objet
        info_layout.addWidget(QLabel("Objet:"), 3, 0)
        self.objet_input = QComboBox()
        self.objet_input.setEditable(True)
        self.objet_input.setPlaceholderText("Sélectionner ou taper")
        self.objet_input.setStyleSheet("""
            QComboBox {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
                padding: 8px;
            }
            QComboBox::drop-down {
                border: none;
                width: 0px;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
        """)
        self.charger_objets()
        info_layout.addWidget(self.objet_input, 3, 1, 1, 3)

        layout.addLayout(info_layout)

        # Tableau des articles
        table_label = QLabel("Articles")
        table_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(table_label)

        self.articles_table = QTableWidget()
        self.articles_table.setColumnCount(6)
        self.articles_table.setHorizontalHeaderLabels([
            "Désignation", "Unité", "Quantité", "Prix unitaire HT", "Prix total HT", "Action"
        ])

        # Configurer les colonnes
        self.articles_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.articles_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)

        self.articles_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
            }
            QHeaderView::section {
                background-color: #1E3A8A;
                color: white;
                padding: 8px;
                font-weight: bold;
                border: none;
            }
        """)

        # إضافة قائمة النقر الأيمن للجدول
        self.articles_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.articles_table.customContextMenuRequested.connect(self.show_table_context_menu)

        layout.addWidget(self.articles_table)

        # إضافة زر إضافة مقال
        add_article_button = QPushButton("إضافة مقال")
        add_article_button.setStyleSheet("""
            QPushButton {
                background-color: #1E3A8A;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #1A56DB;
            }
            QPushButton:disabled {
                background-color: #9CA3AF;
                color: #6B7280;
            }
        """)
        add_article_button.clicked.connect(self.ajouter_ligne_avec_validation)

        # تخطيط للزر
        button_layout = QHBoxLayout()
        button_layout.addWidget(add_article_button)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        # Totaux
        totals_layout = QFormLayout()
        totals_layout.setLabelAlignment(Qt.AlignRight)
        totals_layout.setFormAlignment(Qt.AlignRight)

        self.total_ht_label = QLabel("0.00 DH")
        self.tva_label = QLabel("0.00 DH")
        self.ttc_label = QLabel("0.00 DH")

        self.ttc_label.setStyleSheet("font-weight: bold;")

        totals_layout.addRow("Total HT:", self.total_ht_label)
        totals_layout.addRow("TVA 20%:", self.tva_label)
        totals_layout.addRow("Total TTC:", self.ttc_label)

        # إضافة حقل Montant global
        montant_layout = QHBoxLayout()
        montant_label = QLabel("Montant global:")
        montant_label.setStyleSheet("font-weight: bold; color: #1E3A8A;")

        self.montant_global_input = QDoubleSpinBox()
        self.montant_global_input.setRange(0, 10000000)
        self.montant_global_input.setValue(0)
        self.montant_global_input.setDecimals(2)
        self.montant_global_input.setSuffix(" DH")
        self.montant_global_input.setStyleSheet("""
            QDoubleSpinBox {
                border: 2px solid #1E3A8A;
                border-radius: 6px;
                padding: 8px;
                font-size: 14px;
                font-weight: bold;
                background-color: #F8FAFC;
            }
            QDoubleSpinBox:focus {
                border: 2px solid #3B82F6;
                background-color: white;
            }
        """)

        # ربط التغيير في Montant global بالتحقق
        self.montant_global_input.valueChanged.connect(self.verifier_montant_global)

        montant_layout.addWidget(montant_label)
        montant_layout.addWidget(self.montant_global_input)
        montant_layout.addStretch()

        layout.addLayout(totals_layout)
        layout.addLayout(montant_layout)

        # Boutons d'action
        action_buttons_layout = QHBoxLayout()
        action_buttons_layout.setContentsMargins(0, 15, 0, 0)
        action_buttons_layout.setSpacing(10)

        # Bouton Annuler
        cancel_button = QPushButton("Annuler")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        cancel_button.clicked.connect(self.reject)

        # Bouton Enregistrer
        save_button = QPushButton("Enregistrer")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #1E3A8A;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1A56DB;
            }
        """)
        save_button.clicked.connect(self.save_bon)

        action_buttons_layout.addStretch()
        action_buttons_layout.addWidget(cancel_button)
        action_buttons_layout.addWidget(save_button)

        layout.addLayout(action_buttons_layout)

    def show_table_context_menu(self, position):
        """عرض قائمة النقر الأيمن للجدول"""
        from PySide6.QtWidgets import QMenu
        from PySide6.QtGui import QAction

        menu = QMenu(self)

        # إضافة خيار "إضافة سطر جديد"
        add_action = QAction("إضافة سطر جديد", self)
        add_action.triggered.connect(self.ajouter_ligne_avec_validation)
        menu.addAction(add_action)

        # إضافة خيار "حذف جميع الأسطر"
        delete_all_action = QAction("حذف جميع الأسطر", self)
        delete_all_action.triggered.connect(self.supprimer_toutes_lignes)
        menu.addAction(delete_all_action)

        # إذا كان هناك سطر محدد، إضافة خيار "حذف هذا السطر"
        item = self.articles_table.itemAt(position)
        if item:
            row = item.row()
            delete_row_action = QAction(f"حذف السطر {row + 1}", self)
            delete_row_action.triggered.connect(lambda: self.supprimer_ligne(row))
            menu.addAction(delete_row_action)

        # عرض القائمة
        menu.exec_(self.articles_table.mapToGlobal(position))

    def ajouter_ligne_avec_validation(self):
        """إضافة سطر جديد مع التحقق من المخزون و Montant global"""
        print(f"🖱️ إضافة سطر جديد مع التحقق الشامل")

        # التحقق من وجود مشاكل في المخزون
        if self.has_stock_insuffisant():
            print(f"🔴 لا يمكن إضافة سطر جديد - هناك مشاكل في المخزون (الكميات باللون الأحمر)")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "ERREUR - مشكلة في المخزون",
                "لا يمكن إضافة سطر جديد.\n\n"
                "يرجى تصحيح الكميات التي تتجاوز المخزون المتاح (المعروضة باللون الأحمر) أولاً.")
            return

        # التحقق من Montant global
        if not self.verifier_montant_global_pour_ajout():
            print(f"🔴 لا يمكن إضافة سطر جديد - مشكلة في Montant global")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "ERREUR - Montant global",
                "لا يمكن إضافة سطر جديد.\n\n"
                "Montant global لا يساوي Total TTC.\n"
                "يرجى تصحيح المبلغ أولاً.")
            return

        self.ajouter_ligne()

    def setup_auto_select_for_table_widget(self, widget):
        """إعداد التحديد التلقائي لحقول الجدول"""
        try:
            # إضافة event filter مخصص للحقول في الجدول
            widget.focusInEvent = self.create_focus_in_event(widget)
            widget.setFocusPolicy(Qt.StrongFocus)
            print(f"✅ تم إعداد التحديد التلقائي لـ {type(widget).__name__}")
        except Exception as e:
            print(f"❌ خطأ في إعداد التحديد التلقائي: {str(e)}")

    def create_focus_in_event(self, widget):
        """إنشاء دالة focus event مخصصة للحقل"""
        original_focus_in = widget.focusInEvent

        def custom_focus_in_event(event):
            # استدعاء الدالة الأصلية أولاً
            if original_focus_in:
                original_focus_in(event)

            # تطبيق التحديد التلقائي
            from PySide6.QtCore import QTimer
            QTimer.singleShot(0, lambda: self.select_all_text_in_widget(widget))

        return custom_focus_in_event

    def select_all_text_in_widget(self, widget):
        """تحديد كل النص في الحقل"""
        try:
            from PySide6.QtWidgets import QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox

            if isinstance(widget, QLineEdit):
                widget.selectAll()
                print(f"🔍 تم تحديد النص في QLineEdit: '{widget.text()}'")

            elif isinstance(widget, QSpinBox):
                widget.selectAll()
                print(f"🔍 تم تحديد القيمة في QSpinBox: {widget.value()}")

            elif isinstance(widget, QDoubleSpinBox):
                widget.selectAll()
                print(f"🔍 تم تحديد القيمة في QDoubleSpinBox: {widget.value()}")

            elif isinstance(widget, QComboBox):
                if widget.isEditable() and widget.lineEdit():
                    widget.lineEdit().selectAll()
                    print(f"🔍 تم تحديد النص في QComboBox: '{widget.currentText()}'")
                else:
                    print(f"🔍 QComboBox غير قابل للتحرير: '{widget.currentText()}'")

            else:
                print(f"⚠️ نوع حقل غير مدعوم: {type(widget).__name__}")

        except Exception as e:
            print(f"❌ خطأ في تحديد النص: {str(e)}")

    def verifier_montant_global(self):
        """التحقق من Montant global وتلوين الحقل حسب الحالة"""
        try:
            montant_global = self.montant_global_input.value()
            ttc_text = self.ttc_label.text().replace(" DH", "").replace(",", "")
            total_ttc = float(ttc_text) if ttc_text else 0

            print(f"🔍 التحقق من Montant global: {montant_global:.2f} vs Total TTC: {total_ttc:.2f}")

            if abs(montant_global - total_ttc) < 0.01:  # تساوي (مع هامش خطأ صغير)
                # أخضر - متطابق
                self.montant_global_input.setStyleSheet("""
                    QDoubleSpinBox {
                        border: 2px solid #10B981;
                        border-radius: 6px;
                        padding: 8px;
                        font-size: 14px;
                        font-weight: bold;
                        background-color: #ECFDF5;
                        color: #065F46;
                    }
                    QDoubleSpinBox:focus {
                        border: 2px solid #059669;
                        background-color: white;
                    }
                """)
                print(f"✅ Montant global صحيح - أخضر")
                return True
            else:
                # أحمر - غير متطابق
                self.montant_global_input.setStyleSheet("""
                    QDoubleSpinBox {
                        border: 2px solid #EF4444;
                        border-radius: 6px;
                        padding: 8px;
                        font-size: 14px;
                        font-weight: bold;
                        background-color: #FEF2F2;
                        color: #991B1B;
                    }
                    QDoubleSpinBox:focus {
                        border: 2px solid #DC2626;
                        background-color: white;
                    }
                """)
                print(f"🔴 Montant global خطأ - أحمر")
                return False

        except Exception as e:
            print(f"❌ خطأ في verifier_montant_global: {str(e)}")
            return False

    def verifier_montant_global_pour_ajout(self):
        """التحقق من Montant global قبل إضافة سطر جديد"""
        try:
            montant_global = self.montant_global_input.value()
            ttc_text = self.ttc_label.text().replace(" DH", "").replace(",", "")
            total_ttc = float(ttc_text) if ttc_text else 0

            # إذا كان Montant global = 0، السماح بالإضافة
            if montant_global == 0:
                return True

            # إذا كان متطابق مع Total TTC، السماح بالإضافة
            if abs(montant_global - total_ttc) < 0.01:
                return True

            # إذا كان غير متطابق، منع الإضافة
            return False

        except Exception as e:
            print(f"❌ خطأ في verifier_montant_global_pour_ajout: {str(e)}")
            return True  # في حالة الخطأ، السماح بالإضافة

    def charger_clients(self):
        """Charge la liste des clients dans le QComboBox"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT id, nom FROM clients ORDER BY nom")
            clients = cursor.fetchall()

            self.client_combo.clear()
            self.client_combo.addItem("Sélectionner un client", None)

            for client in clients:
                self.client_combo.addItem(client['nom'], client['id'])
        except sqlite3.Error as e:
            print(f"Erreur lors du chargement des clients: {str(e)}")
            self.client_combo.clear()
            self.client_combo.addItem("Sélectionner un client", None)



    def charger_bons_commande(self):
        """Charge la liste des bons de commande existants"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT DISTINCT numero FROM bons_commande WHERE numero IS NOT NULL ORDER BY numero DESC LIMIT 20")
            bons = cursor.fetchall()

            self.bon_commande_combo.clear()
            self.bon_commande_combo.addItem("", None)  # Option vide pour saisie manuelle

            for bon in bons:
                if bon['numero']:
                    self.bon_commande_combo.addItem(bon['numero'], bon['numero'])
        except sqlite3.Error as e:
            print(f"Erreur lors du chargement des bons de commande: {str(e)}")
            self.bon_commande_combo.clear()

    def charger_marches(self):
        """Charge la liste des marchés existants"""
        try:
            # Créer une liste de marchés prédéfinis + ceux de la base de données
            marches_predefinis = [
                "Marché public",
                "Marché privé",
                "Appel d'offres",
                "Commande directe",
                "Marché cadre"
            ]

            self.marche_combo.clear()
            self.marche_combo.addItem("", None)  # Option vide pour saisie manuelle

            # Ajouter les marchés prédéfinis
            for marche in marches_predefinis:
                self.marche_combo.addItem(marche, marche)

            # Ajouter les marchés de la base de données (si on a une table pour ça)
            # Pour l'instant, on utilise les marchés prédéfinis
        except Exception as e:
            print(f"Erreur lors du chargement des marchés: {str(e)}")
            self.marche_combo.clear()

    def charger_objets(self):
        """Charge la liste des objets existants"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT DISTINCT objet FROM bons_commande WHERE objet IS NOT NULL AND objet != '' ORDER BY objet LIMIT 20")
            objets = cursor.fetchall()

            self.objet_input.clear()
            self.objet_input.addItem("", None)  # Option vide pour saisie manuelle

            for objet in objets:
                if objet['objet']:
                    self.objet_input.addItem(objet['objet'], objet['objet'])
        except sqlite3.Error as e:
            print(f"Erreur lors du chargement des objets: {str(e)}")
            self.objet_input.clear()

    def nouveau_bon(self):
        """Initialise un nouveau bon de commande"""
        self.date_creation_input.setDate(QDate.currentDate())
        self.client_combo.setCurrentIndex(0)
        self.bon_commande_combo.setCurrentText("")
        self.marche_combo.setCurrentText("")
        self.objet_input.setCurrentText("")

        # Vider le tableau
        self.articles_table.setRowCount(0)

        # Réinitialiser les totaux
        self.total_ht_label.setText("0.00 DH")
        self.tva_label.setText("0.00 DH")
        self.ttc_label.setText("0.00 DH")

        # إعادة تعيين Montant global
        if hasattr(self, 'montant_global_input'):
            self.montant_global_input.setValue(0)

    def ajouter_ligne(self):
        """Ajoute une nouvelle ligne au tableau des articles"""
        print(f"🔧 ajouter_ligne appelé - Ajout d'une nouvelle ligne")

        # Vérifier s'il y a des problèmes de stock (pour information seulement)
        if self.has_stock_insuffisant():
            print(f"⚠️ Il y a des problèmes de stock dans les lignes existantes, mais on permet l'ajout d'une nouvelle ligne")

        row = self.articles_table.rowCount()
        self.articles_table.insertRow(row)
        print(f"✅ Nouvelle ligne ajoutée: ligne {row}")

        # Désignation (QComboBox avec liste des produits)
        designation_combo = QComboBox()
        designation_combo.setEditable(True)
        designation_combo.setInsertPolicy(QComboBox.NoInsert)  # منع إدراج عناصر جديدة
        designation_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 6px;
                font-size: 13px;
                background-color: white;
            }
            QComboBox:focus {
                border: 2px solid #1E3A8A;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #ddd;
                background-color: white;
                selection-background-color: #E3F2FD;
                selection-color: black;
                font-size: 13px;
                padding: 4px;
            }
            QComboBox QAbstractItemView::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #F5F5F5;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #1E3A8A;
                color: white;
            }
        """)

        # Charger les produits
        self.charger_produits_dans_combo(designation_combo)

        # Connecter le signal de changement - utiliser currentIndexChanged pour une meilleure détection
        print(f"🔗 Connexion des signaux pour la ligne {row}")
        designation_combo.currentIndexChanged.connect(lambda index, r=row: self.on_produit_index_changed(r, index))
        designation_combo.currentTextChanged.connect(lambda text, r=row: self.on_produit_selected(r, text))
        designation_combo.editTextChanged.connect(lambda text, r=row: self.on_designation_changed(r, text))
        print(f"✅ Signaux connectés pour la ligne {row}")

        self.articles_table.setCellWidget(row, 0, designation_combo)

        # Unité
        self.articles_table.setItem(row, 1, QTableWidgetItem(""))

        # Quantité (QSpinBox)
        quantite_spin = QSpinBox()
        quantite_spin.setRange(1, 10000)
        quantite_spin.setValue(1)
        quantite_spin.setStyleSheet("border: 1px solid #ddd; border-radius: 4px; padding: 4px;")

        # Connecter TOUS les signaux possibles pour s'assurer que la validation fonctionne
        quantite_spin.valueChanged.connect(self.calculer_totaux)
        quantite_spin.valueChanged.connect(lambda value, r=row: self.on_quantity_changed(r, value))
        quantite_spin.editingFinished.connect(lambda r=row: self.on_quantity_editing_finished(r))
        # Ajouter des signaux supplémentaires pour capturer tous les changements
        quantite_spin.textChanged.connect(lambda text, r=row: self.on_quantity_text_changed(r, text))

        # Ajouter un timer pour vérifier le stock après un délai court
        from PySide6.QtCore import QTimer
        timer = QTimer()
        timer.setSingleShot(True)
        timer.timeout.connect(lambda r=row: self.verifier_stock_delayed(r))
        quantite_spin.valueChanged.connect(lambda value, r=row, t=timer: self.start_delayed_check(r, t))

        print(f"🔧 QSpinBox créé pour la ligne {row} avec tous les signaux connectés")
        self.articles_table.setCellWidget(row, 2, quantite_spin)

        # Prix unitaire (QDoubleSpinBox)
        prix_spin = QDoubleSpinBox()
        prix_spin.setRange(0, 1000000)
        prix_spin.setValue(0)
        prix_spin.setDecimals(2)
        prix_spin.setSuffix(" DH")
        prix_spin.setStyleSheet("border: 1px solid #ddd; border-radius: 4px; padding: 4px;")
        prix_spin.valueChanged.connect(self.calculer_totaux)
        self.articles_table.setCellWidget(row, 3, prix_spin)

        # إضافة التحديد التلقائي للحقول في الجدول بطريقة مباشرة
        self.setup_auto_select_for_table_widget(designation_combo)
        self.setup_auto_select_for_table_widget(quantite_spin)
        self.setup_auto_select_for_table_widget(prix_spin)
        print(f"✅ تم تطبيق التحديد التلقائي على حقول الجدول للسطر {row + 1}")

        # Prix total
        self.articles_table.setItem(row, 4, QTableWidgetItem("0.00 DH"))

        # Bouton de suppression
        delete_button = QPushButton("🗑️")
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #EF4444;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #DC2626;
            }
        """)
        delete_button.clicked.connect(lambda: self.supprimer_ligne(row))

        # Créer un widget conteneur pour le bouton
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(2, 2, 2, 2)
        button_layout.addWidget(delete_button)
        button_layout.setAlignment(Qt.AlignCenter)

        self.articles_table.setCellWidget(row, 5, button_container)

        # Mettre à jour les totaux
        self.calculer_totaux()

    def charger_produits_dans_combo(self, combo_box):
        """Charge la liste des produits dans un QComboBox"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT p.id, p.designation, p.unite, p.prix_vente, p.stock, p.prix_achat,
                       f.numero as numero_facture, fo.nom as nom_fournisseur
                FROM produits p
                LEFT JOIN factures_achat f ON p.facture_achat_id = f.id
                LEFT JOIN fournisseurs fo ON f.fournisseur_id = fo.id
                ORDER BY p.designation
            """)
            produits = cursor.fetchall()

            combo_box.clear()
            combo_box.addItem("اختر منتج أو اكتب يدوياً", None)

            print(f"📦 تحميل {len(produits)} منتج في القائمة...")

            for produit in produits:
                # عرض معلومات أكثر في القائمة
                designation = produit['designation'] or 'بدون اسم'
                prix_vente = produit['prix_vente'] or 0
                stock = produit['stock'] or 0
                unite = produit['unite'] or 'وحدة'

                # تنسيق النص المعروض
                display_text = f"{designation} - {prix_vente:.2f} DH"
                if stock > 0:
                    display_text += f" (المخزون: {stock} {unite})"
                else:
                    display_text += f" (نفد المخزون)"

                combo_box.addItem(display_text, produit)
                print(f"   ✅ {designation} - {prix_vente:.2f} DH - مخزون: {stock}")

            print(f"✅ تم تحميل جميع المنتجات بنجاح")

        except sqlite3.Error as e:
            print(f"❌ خطأ في تحميل المنتجات: {str(e)}")
            combo_box.clear()
            combo_box.addItem("خطأ في تحميل المنتجات", None)

    def on_produit_index_changed(self, row, index):
        """Appelé quand l'index du produit change dans le combobox"""
        print(f"🔍 on_produit_index_changed appelée: row={row}, index={index}")

        try:
            designation_combo = self.articles_table.cellWidget(row, 0)
            if not designation_combo:
                print(f"❌ Pas de designation_combo trouvé pour la ligne {row}")
                return

            if index <= 0:  # Index 0 est "Sélectionner un produit..."
                print(f"⚠️ Index {index} ignoré (sélection par défaut)")
                return

            # Récupérer les données du produit sélectionné
            produit_data = designation_combo.currentData()
            print(f"📊 Données du produit: {produit_data}")

            if produit_data and (isinstance(produit_data, dict) or hasattr(produit_data, 'keys')):
                # Gérer à la fois dict et sqlite3.Row
                def get_value(data, key, default=''):
                    if isinstance(data, dict):
                        return data.get(key, default)
                    else:
                        try:
                            return data[key] if data[key] is not None else default
                        except (KeyError, IndexError):
                            return default

                designation = get_value(produit_data, 'designation', 'N/A')
                print(f"🔄 DÉBUT REMPLISSAGE AUTOMATIQUE pour: {designation}")

                # 1. Unité - تعبئة تلقائية
                unite_item = self.articles_table.item(row, 1)
                unite_value = get_value(produit_data, 'unite', '') or ''
                print(f"🔧 Tentative de remplissage Unité: '{unite_value}'")

                if unite_item:
                    unite_item.setText(unite_value)
                    print(f"   ✅ Unité mise à jour dans item existant: '{unite_value}'")
                else:
                    self.articles_table.setItem(row, 1, QTableWidgetItem(unite_value))
                    print(f"   ✅ Nouvel item Unité créé: '{unite_value}'")

                # 2. Quantité - تعيين قيمة افتراضية (1)
                quantite_widget = self.articles_table.cellWidget(row, 2)
                if quantite_widget:
                    stock = get_value(produit_data, 'stock', 0) or 0
                    quantite_widget.setValue(1)  # دائماً 1 كقيمة افتراضية
                    print(f"   ✅ Quantité: 1 (Stock disponible: {stock})")

                # 3. Prix unitaire - تعبئة تلقائية
                prix_widget = self.articles_table.cellWidget(row, 3)
                prix_vente = get_value(produit_data, 'prix_vente', 0)
                print(f"🔧 Tentative de remplissage Prix: {prix_vente}")

                if prix_widget and prix_vente:
                    prix_value = float(prix_vente)
                    prix_widget.setValue(prix_value)
                    print(f"   ✅ Prix unitaire: {prix_value:.2f} DH")

                # 4. Calcul automatique du Prix total HT
                if quantite_widget and prix_widget:
                    quantite = quantite_widget.value()
                    prix = prix_widget.value()
                    total_ligne = quantite * prix
                    self.articles_table.setItem(row, 4, QTableWidgetItem(f"{total_ligne:.2f} DH"))
                    print(f"   ✅ Prix total HT: {total_ligne:.2f} DH")

                # 5. Afficher des informations supplémentaires (optionnel)
                nom_fournisseur = get_value(produit_data, 'nom_fournisseur', None)
                if nom_fournisseur:
                    print(f"   ℹ️  Fournisseur: {nom_fournisseur}")
                numero_facture = get_value(produit_data, 'numero_facture', None)
                if numero_facture:
                    print(f"   ℹ️  Facture d'achat: {numero_facture}")

                # 6. Vérifier le stock pour la quantité par défaut (1)
                self.verifier_stock_et_colorer(row, produit_data['id'], 1)

                # 7. Ajouter une vérification supplémentaire avec un délai
                from PySide6.QtCore import QTimer
                QTimer.singleShot(200, lambda: self.verifier_stock_et_colorer(row, produit_data['id'], 1))

                # 8. Recalculer tous les totaux
                self.calculer_totaux()
                print(f"   ✅ FIN REMPLISSAGE AUTOMATIQUE - Totaux recalculés")
            else:
                print(f"❌ Données du produit invalides: {type(produit_data)} - {produit_data}")

        except Exception as e:
            print(f"❌ ERREUR dans on_produit_index_changed: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_produit_selected(self, row, text):
        """Appelé quand un produit est sélectionné dans la liste (fallback)"""
        # Cette fonction est gardée pour compatibilité mais la logique principale est dans on_produit_index_changed
        pass

    def on_designation_changed(self, row, text):
        """Appelé quand le texte de désignation change (saisie manuelle)"""
        # Cette fonction permet la saisie manuelle sans interférer avec la sélection
        pass

    def on_quantity_changed(self, row, new_quantity):
        """Appelé quand la quantité change - vérifie le stock disponible"""
        try:
            print(f"🔄 on_quantity_changed appelé: row={row}, quantité={new_quantity}")

            # Récupérer les informations du produit
            designation_combo = self.articles_table.cellWidget(row, 0)
            if not designation_combo:
                print(f"❌ Pas de combo de désignation trouvé pour la ligne {row}")
                return

            produit_data = designation_combo.currentData()
            if not produit_data:
                print(f"❌ Pas de données produit pour la ligne {row}")
                return

            # Gérer les objets sqlite3.Row et les dictionnaires
            try:
                if hasattr(produit_data, 'keys'):  # sqlite3.Row ou dict
                    produit_id = produit_data['id']
                else:
                    print(f"❌ Type de données produit non supporté: {type(produit_data)}")
                    return
            except (KeyError, TypeError) as e:
                print(f"❌ Erreur d'accès aux données produit: {e}")
                return
            if not produit_id:
                print(f"❌ Pas d'ID produit trouvé pour la ligne {row}")
                return

            print(f"✅ Produit trouvé: ID={produit_id}, Quantité={new_quantity}")

            # Vérifier le stock disponible et changer la couleur si nécessaire
            self.verifier_stock_et_colorer(row, produit_id, new_quantity)

            designation = produit_data['designation'] if 'designation' in produit_data else 'Produit inconnu'

            # Ajouter une sortie de stock
            self.add_stock_movement(produit_id, designation, new_quantity, 'sortie', f'Bon de commande - Ligne {row + 1}')

            print(f"📤 Sortie de stock ajoutée: {designation} - Quantité: {new_quantity}")

        except Exception as e:
            print(f"❌ Erreur lors de l'ajout de la sortie de stock: {str(e)}")

    def on_quantity_editing_finished(self, row):
        """Appelé quand l'utilisateur finit d'éditer la quantité"""
        try:
            # Récupérer le widget de quantité
            quantite_widget = self.articles_table.cellWidget(row, 2)
            if not quantite_widget:
                return

            # Récupérer les informations du produit
            designation_combo = self.articles_table.cellWidget(row, 0)
            if not designation_combo:
                return

            produit_data = designation_combo.currentData()
            if not produit_data or not isinstance(produit_data, dict):
                return

            # Récupérer l'ID du produit
            produit_id = produit_data.get('id') if hasattr(produit_data, 'get') else produit_data['id'] if 'id' in produit_data else None
            if not produit_id:
                return

            # Vérifier le stock avec la nouvelle quantité
            nouvelle_quantite = quantite_widget.value()
            self.verifier_stock_et_colorer(row, produit_id, nouvelle_quantite)

        except Exception as e:
            print(f"❌ Erreur dans on_quantity_editing_finished: {str(e)}")

    def on_quantity_text_changed(self, row, text):
        """Appelé quand le texte de la quantité change"""
        try:
            print(f"📝 on_quantity_text_changed appelé: row={row}, text='{text}'")

            # Essayer de convertir le texte en nombre
            try:
                quantite = int(text) if text.strip() else 1
            except ValueError:
                print(f"❌ Impossible de convertir '{text}' en nombre")
                return

            # Récupérer les informations du produit
            designation_combo = self.articles_table.cellWidget(row, 0)
            if not designation_combo:
                print(f"❌ Pas de combo de désignation trouvé pour la ligne {row}")
                return

            produit_data = designation_combo.currentData()
            if not produit_data:
                print(f"❌ Pas de données produit pour la ligne {row}")
                return

            # Gérer les objets sqlite3.Row et les dictionnaires
            try:
                if hasattr(produit_data, 'keys'):  # sqlite3.Row ou dict
                    produit_id = produit_data['id']
                else:
                    print(f"❌ Type de données produit non supporté: {type(produit_data)}")
                    return
            except (KeyError, TypeError) as e:
                print(f"❌ Erreur d'accès aux données produit: {e}")
                return
            if not produit_id:
                print(f"❌ Pas d'ID produit trouvé pour la ligne {row}")
                return

            print(f"✅ Validation via text change: ID={produit_id}, Quantité={quantite}")

            # Vérifier le stock
            self.verifier_stock_et_colorer(row, produit_id, quantite)

        except Exception as e:
            print(f"❌ Erreur dans on_quantity_text_changed: {str(e)}")

    def start_delayed_check(self, row, timer):
        """Démarre un timer pour vérifier le stock après un délai"""
        timer.stop()  # Arrêter le timer précédent s'il existe
        timer.start(100)  # Attendre 100ms avant de vérifier

    def verifier_stock_delayed(self, row):
        """Vérifie le stock après un délai (appelé par le timer)"""
        try:
            # Récupérer les informations du produit
            designation_combo = self.articles_table.cellWidget(row, 0)
            if not designation_combo:
                return

            produit_data = designation_combo.currentData()
            if not produit_data:
                return

            # Gérer les objets sqlite3.Row et les dictionnaires
            try:
                if hasattr(produit_data, 'keys'):  # sqlite3.Row ou dict
                    produit_id = produit_data['id']
                else:
                    return
            except (KeyError, TypeError):
                return

            # Récupérer la quantité actuelle
            quantite_widget = self.articles_table.cellWidget(row, 2)
            if not quantite_widget:
                return

            quantite = quantite_widget.value()

            print(f"⏰ Vérification retardée du stock: row={row}, produit_id={produit_id}, quantite={quantite}")

            # Vérifier le stock
            self.verifier_stock_et_colorer(row, produit_id, quantite)

        except Exception as e:
            print(f"❌ Erreur dans verifier_stock_delayed: {str(e)}")

    def add_stock_movement(self, produit_id, designation, quantite, type_mouvement, reference):
        """Ajoute un mouvement de stock"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Vérifier la structure de la table mouvements_stock
            cursor.execute("PRAGMA table_info(mouvements_stock)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            # Ajouter la colonne designation si elle n'existe pas
            if 'designation' not in column_names:
                try:
                    cursor.execute("ALTER TABLE mouvements_stock ADD COLUMN designation TEXT")
                    print("✅ Colonne 'designation' ajoutée à mouvements_stock")
                except sqlite3.Error as e:
                    print(f"⚠️ Erreur lors de l'ajout de la colonne designation: {str(e)}")

            # Ajouter le mouvement
            try:
                cursor.execute("""
                    INSERT INTO mouvements_stock (produit_id, designation, type_mouvement, quantite, reference, date_mouvement)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (produit_id, designation, type_mouvement, quantite, reference, datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
            except sqlite3.Error as e:
                # Si l'insertion avec designation échoue, essayer sans
                print(f"⚠️ Insertion avec designation échouée, essai sans: {str(e)}")
                cursor.execute("""
                    INSERT INTO mouvements_stock (produit_id, type_mouvement, quantite, reference, date_mouvement)
                    VALUES (?, ?, ?, ?, ?)
                """, (produit_id, type_mouvement, quantite, reference, datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')))

            self.db_manager.conn.commit()

            print(f"✅ Mouvement de stock ajouté:")
            print(f"   Produit: {designation}")
            print(f"   Type: {type_mouvement}")
            print(f"   Quantité: {quantite}")
            print(f"   Référence: {reference}")

        except sqlite3.Error as e:
            print(f"❌ Erreur lors de l'ajout du mouvement de stock: {str(e)}")
            self.db_manager.conn.rollback()

    def update_stock_on_quantity_change(self, produit_id, old_quantity, new_quantity):
        """Met à jour le stock du produit quand la quantité change"""
        if not produit_id:
            return

        try:
            # Calculer la différence
            difference = new_quantity - old_quantity

            if difference != 0:
                cursor = self.db_manager.conn.cursor()

                # Récupérer le stock actuel
                cursor.execute("SELECT stock, designation FROM produits WHERE id = ?", (produit_id,))
                result = cursor.fetchone()

                if result:
                    stock_actuel = result['stock'] or 0
                    designation = result['designation']
                    nouveau_stock = stock_actuel - difference  # Soustraire la différence

                    # Vérifier que le stock ne devient pas négatif
                    if nouveau_stock < 0:
                        print(f"⚠️  Attention: Stock insuffisant pour {designation}")
                        print(f"   Stock actuel: {stock_actuel}, Demandé: {new_quantity}")
                        # On peut choisir de limiter la quantité ou afficher un avertissement
                        # Pour l'instant, on permet le stock négatif avec avertissement

                    # Mettre à jour le stock
                    cursor.execute("UPDATE produits SET stock = ? WHERE id = ?", (nouveau_stock, produit_id))
                    self.db_manager.conn.commit()

                    print(f"📦 Stock mis à jour pour {designation}:")
                    print(f"   Ancien stock: {stock_actuel}")
                    print(f"   Quantité ajoutée: +{difference}")
                    print(f"   Nouveau stock: {nouveau_stock}")

        except sqlite3.Error as e:
            print(f"❌ Erreur lors de la mise à jour du stock: {str(e)}")

    def deduire_stock_produit(self, produit_id, quantite):
        """Déduit une quantité du stock d'un produit"""
        if not produit_id or quantite <= 0:
            return False

        try:
            cursor = self.db_manager.conn.cursor()

            # Récupérer le stock actuel
            cursor.execute("SELECT stock, designation FROM produits WHERE id = ?", (produit_id,))
            result = cursor.fetchone()

            if result:
                stock_actuel = result['stock'] or 0
                designation = result['designation']
                nouveau_stock = stock_actuel - quantite

                # Vérifier le stock disponible
                if stock_actuel < quantite:
                    print(f"⚠️  Stock insuffisant pour {designation}")
                    print(f"   Stock disponible: {stock_actuel}, Demandé: {quantite}")
                    # On permet quand même la déduction avec avertissement

                # Mettre à jour le stock
                cursor.execute("UPDATE produits SET stock = ? WHERE id = ?", (nouveau_stock, produit_id))
                self.db_manager.conn.commit()

                print(f"📦 Stock déduit pour {designation}:")
                print(f"   Stock avant: {stock_actuel}")
                print(f"   Quantité déduite: -{quantite}")
                print(f"   Stock après: {nouveau_stock}")

                return True

        except sqlite3.Error as e:
            print(f"❌ Erreur lors de la déduction du stock: {str(e)}")
            return False

    def supprimer_ligne(self, row):
        """Supprime une ligne du tableau"""
        self.articles_table.removeRow(row)
        self.calculer_totaux()
        # Mettre à jour l'état du bouton après suppression
        self.update_add_button_state()

    def supprimer_toutes_lignes(self):
        """Supprime toutes les lignes du tableau"""
        self.articles_table.setRowCount(0)
        self.calculer_totaux()
        # Mettre à jour l'état du bouton après suppression de toutes les lignes
        self.update_add_button_state()

    def calculer_totaux(self, *args):
        """Calcule les totaux du bon de commande"""
        total_ht = 0

        for row in range(self.articles_table.rowCount()):
            # Vérifier si la ligne a une désignation
            designation_widget = self.articles_table.cellWidget(row, 0)
            designation_text = ""

            if designation_widget:
                designation_text = designation_widget.currentText()

            # Ne calculer que si il y a une désignation valide
            if designation_text and designation_text != "Sélectionner un produit ou saisir manuellement":
                quantite_widget = self.articles_table.cellWidget(row, 2)
                prix_widget = self.articles_table.cellWidget(row, 3)

                if quantite_widget and prix_widget:
                    quantite = quantite_widget.value()
                    prix = prix_widget.value()
                    total_ligne = quantite * prix

                    # Mettre à jour le Prix total HT de la ligne
                    self.articles_table.setItem(row, 4, QTableWidgetItem(f"{total_ligne:.2f} DH"))
                    total_ht += total_ligne

                    # Afficher le calcul dans la console pour le débogage
                    print(f"💰 Calcul ligne {row + 1}: {designation_text}")
                    print(f"   Quantité: {quantite} × Prix unitaire: {prix:.2f} DH = {total_ligne:.2f} DH")
                else:
                    # Mettre à jour le total de la ligne à 0
                    self.articles_table.setItem(row, 4, QTableWidgetItem("0.00 DH"))
            else:
                # Mettre à jour le total de la ligne à 0
                self.articles_table.setItem(row, 4, QTableWidgetItem("0.00 DH"))

        # Calculer la TVA et le total TTC
        tva = total_ht * 0.2  # TVA 20%
        ttc = total_ht + tva

        # Mettre à jour les labels
        self.total_ht_label.setText(f"{total_ht:.2f} DH")
        self.tva_label.setText(f"{tva:.2f} DH")
        self.ttc_label.setText(f"{ttc:.2f} DH")

        # Afficher le résumé des totaux
        if total_ht > 0:
            print(f"📊 Totaux calculés:")
            print(f"   Total HT: {total_ht:.2f} DH")
            print(f"   TVA 20%: {tva:.2f} DH")
            print(f"   Total TTC: {ttc:.2f} DH")

        # التحقق من Montant global بعد تحديث التوتالات
        if hasattr(self, 'montant_global_input'):
            self.verifier_montant_global()

    def verifier_stock_et_colorer(self, row, produit_id, quantite_demandee):
        """Vérifie le stock disponible et colore le champ quantité en rouge si insuffisant"""
        try:
            print(f"🔍 DEBUT verifier_stock_et_colorer: row={row}, produit_id={produit_id}, quantite={quantite_demandee}")

            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT stock, designation FROM produits WHERE id = ?", (produit_id,))
            result = cursor.fetchone()

            if result:
                stock_disponible = result['stock'] or 0
                designation = result['designation']

                # Récupérer le widget de quantité
                quantite_widget = self.articles_table.cellWidget(row, 2)
                if not quantite_widget:
                    print(f"❌ Widget quantité non trouvé pour la ligne {row}")
                    return False

                print(f"🔍 Vérification stock pour {designation}:")
                print(f"   Stock disponible: {stock_disponible}")
                print(f"   Quantité demandée: {quantite_demandee}")
                print(f"   Widget trouvé: {type(quantite_widget).__name__}")

                # Vérifier si la quantité demandée dépasse le stock
                if quantite_demandee > stock_disponible:
                    # Colorer en rouge - stock insuffisant
                    quantite_widget.setStyleSheet("""
                        QSpinBox {
                            border: 2px solid #EF4444;
                            border-radius: 4px;
                            padding: 4px;
                            background-color: #FEF2F2;
                            color: #DC2626;
                            font-weight: bold;
                        }
                    """)
                    print(f"🔴 Stock insuffisant pour {designation} - STYLE ROUGE APPLIQUÉ")
                    # Forcer la mise à jour visuelle
                    quantite_widget.update()
                    quantite_widget.repaint()
                    # Mettre à jour l'état du bouton "Ajouter une ligne"
                    self.update_add_button_state()
                    return False
                else:
                    # Colorer en vert - stock suffisant
                    quantite_widget.setStyleSheet("""
                        QSpinBox {
                            border: 2px solid #10B981;
                            border-radius: 4px;
                            padding: 4px;
                            background-color: #F0FDF4;
                            color: #059669;
                            font-weight: bold;
                        }
                    """)
                    print(f"🟢 Stock suffisant pour {designation} - STYLE VERT APPLIQUÉ")
                    # Forcer la mise à jour visuelle
                    quantite_widget.update()
                    quantite_widget.repaint()
                    # Mettre à jour l'état du bouton "Ajouter une ligne"
                    self.update_add_button_state()
                    return True
            else:
                print(f"❌ Aucun produit trouvé avec l'ID {produit_id}")

            return True

        except sqlite3.Error as e:
            print(f"❌ Erreur lors de la vérification du stock: {str(e)}")
            return True  # En cas d'erreur, on permet la saisie
        except Exception as e:
            print(f"❌ Erreur générale dans verifier_stock_et_colorer: {str(e)}")
            import traceback
            traceback.print_exc()
            return True

    def has_stock_insuffisant(self):
        """Vérifie s'il y a des lignes avec stock insuffisant (quantité en rouge)"""
        try:
            for row in range(self.articles_table.rowCount()):
                # Récupérer le widget de quantité
                quantite_widget = self.articles_table.cellWidget(row, 2)
                if quantite_widget:
                    # Vérifier si le style contient du rouge (stock insuffisant)
                    style = quantite_widget.styleSheet()
                    if "#EF4444" in style or "#DC2626" in style:
                        return True
            return False
        except Exception as e:
            print(f"❌ Erreur dans has_stock_insuffisant: {str(e)}")
            return False

    def update_add_button_state(self):
        """تحديث حالة إضافة الأسطر (لم تعد هناك أزرار، فقط للتوافق)"""
        # هذه الدالة لم تعد ضرورية لأننا أزلنا الأزرار
        # ولكن نحتفظ بها للتوافق مع الكود الموجود
        pass

    def save_bon(self):
        """Enregistre le bon de commande"""
        try:
            # Validation des données
            if self.client_combo.currentData() is None:
                QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un client.")
                return

            # Vérification du stock (quantités en rouge = interdiction de sauvegarder)
            if self.has_stock_insuffisant():
                QMessageBox.critical(self, "ERREUR - Stock insuffisant",
                    "Impossible d'enregistrer le bon de commande.\n\n"
                    "Une ou plusieurs quantités dépassent le stock disponible (affichées en rouge).\n"
                    "Veuillez corriger les quantités avant de sauvegarder.")
                return

            # Vérification du Montant global
            if hasattr(self, 'montant_global_input'):
                montant_global = self.montant_global_input.value()
                if montant_global > 0:  # Si Montant global est défini
                    ttc_text = self.ttc_label.text().replace(" DH", "").replace(",", "")
                    total_ttc = float(ttc_text) if ttc_text else 0

                    if abs(montant_global - total_ttc) >= 0.01:  # Pas égal
                        QMessageBox.critical(self, "ERREUR - Montant global",
                            f"Impossible d'enregistrer le bon de commande.\n\n"
                            f"Montant global ({montant_global:.2f} DH) ne correspond pas au Total TTC ({total_ttc:.2f} DH).\n"
                            f"Veuillez corriger le montant avant de sauvegarder.")
                        return

            # Récupérer les données du formulaire
            client_id = self.client_combo.currentData()
            objet = self.objet_input.currentText()
            date_creation = self.date_creation_input.date().toString("yyyy-MM-dd")

            # Calculer les totaux
            total_ht = float(self.total_ht_label.text().replace("DH", "").strip())
            total_tva = float(self.tva_label.text().replace("DH", "").strip())
            total_ttc = float(self.ttc_label.text().replace("DH", "").strip())

            cursor = self.db_manager.conn.cursor()

            if self.is_edit_mode:
                # Modifier le bon existant
                cursor.execute("""
                    UPDATE bons_commande SET
                        client_id = ?, objet = ?, date_creation = ?, total_ht = ?, total_tva = ?, total_ttc = ?
                    WHERE id = ?
                """, (client_id, objet, date_creation, total_ht, total_tva, total_ttc, self.bon_id))

                # Supprimer les anciennes lignes
                cursor.execute("DELETE FROM lignes_bon_commande WHERE bon_id = ?", (self.bon_id,))
                bon_id = self.bon_id
            else:
                # Créer un nouveau bon - générer un numéro automatique
                numero = self.db_manager.generer_numero_bon_commande()
                cursor.execute("""
                    INSERT INTO bons_commande (numero, client_id, objet, date_creation, total_ht, total_tva, total_ttc)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (numero, client_id, objet, date_creation, total_ht, total_tva, total_ttc))

                bon_id = cursor.lastrowid

            # Enregistrer les lignes du bon et déduire du stock
            for row in range(self.articles_table.rowCount()):
                designation_widget = self.articles_table.cellWidget(row, 0)
                unite_item = self.articles_table.item(row, 1)
                quantite_widget = self.articles_table.cellWidget(row, 2)
                prix_widget = self.articles_table.cellWidget(row, 3)

                # Récupérer la désignation depuis le QComboBox
                designation = ""
                produit_id = None
                if designation_widget:
                    designation = designation_widget.currentText()
                    # Récupérer l'ID du produit si c'est un produit de la liste
                    produit_data = designation_widget.currentData()
                    if produit_data and isinstance(produit_data, dict):
                        produit_id = produit_data.get('id') if hasattr(produit_data, 'get') else produit_data['id'] if 'id' in produit_data else None

                if designation and designation.strip() and designation != "Sélectionner un produit ou saisir manuellement":
                    unite = unite_item.text() if unite_item else ""
                    quantite = quantite_widget.value() if quantite_widget else 1
                    prix_unitaire = prix_widget.value() if prix_widget else 0
                    total_ligne = quantite * prix_unitaire

                    # Enregistrer la ligne
                    cursor.execute("""
                        INSERT INTO lignes_bon_commande (bon_id, produit_id, designation, unite, quantite, prix_unitaire_ht, total_ht)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (bon_id, produit_id, designation, unite, quantite, prix_unitaire, total_ligne))

                    # Déduire du stock si c'est un produit existant
                    if produit_id and not self.bon_id:  # Seulement pour les nouveaux bons
                        self.deduire_stock_produit(produit_id, quantite)
                        print(f"📦 Stock déduit: {designation} - Quantité: {quantite}")

            self.db_manager.conn.commit()

            # Émettre le signal de sauvegarde
            self.bon_saved.emit()

            # Émettre le signal pour actualiser le stock
            self.stock_updated.emit()
            print("📤 Signal d'actualisation du stock émis")

            # Fermer la boîte de dialogue
            self.accept()

            QMessageBox.information(self, "Succès", "Bon de commande enregistré avec succès.")

        except sqlite3.Error as e:
            self.db_manager.conn.rollback()
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement du bon de commande: {str(e)}")

    def charger_bon(self, bon_id):
        """Charge un bon de commande existant"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Récupérer les informations du bon
            cursor.execute("""
                SELECT b.*, c.nom as client_nom
                FROM bons_commande b
                LEFT JOIN clients c ON b.client_id = c.id
                WHERE b.id = ?
            """, (bon_id,))
            bon = cursor.fetchone()

            if not bon:
                QMessageBox.warning(self, "Erreur", "Bon de commande introuvable.")
                return

            # Convertir les dates
            if bon['date_creation']:
                date_obj = datetime.datetime.strptime(bon['date_creation'], '%Y-%m-%d')
                self.date_creation_input.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))

            # Sélectionner le client
            for i in range(self.client_combo.count()):
                if self.client_combo.itemData(i) == bon['client_id']:
                    self.client_combo.setCurrentIndex(i)
                    break

            # Mettre à jour les autres champs
            self.objet_input.setCurrentText(bon['objet'] or "")

            # Charger les lignes du bon
            self.articles_table.setRowCount(0)

            cursor.execute("""
                SELECT * FROM lignes_bon_commande
                WHERE bon_id = ?
                ORDER BY id
            """, (bon_id,))
            lignes = cursor.fetchall()

            for ligne in lignes:
                self.ajouter_ligne_existante(ligne)

            # Mettre à jour les totaux
            self.calculer_totaux()

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement du bon de commande: {str(e)}")

    def ajouter_ligne_existante(self, ligne):
        """Ajoute une ligne existante au tableau des articles"""
        row = self.articles_table.rowCount()
        self.articles_table.insertRow(row)

        # Désignation (QComboBox avec liste des produits)
        designation_combo = QComboBox()
        designation_combo.setEditable(True)
        designation_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px;
            }
            QComboBox::drop-down {
                border: none;
                width: 0px;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
        """)

        # Charger les produits
        self.charger_produits_dans_combo(designation_combo)

        # Définir la désignation existante
        designation_combo.setCurrentText(ligne['designation'])

        # Connecter le signal de changement - utiliser currentIndexChanged pour une meilleure détection
        designation_combo.currentIndexChanged.connect(lambda index, r=row: self.on_produit_index_changed(r, index))
        designation_combo.currentTextChanged.connect(lambda text, r=row: self.on_produit_selected(r, text))
        designation_combo.editTextChanged.connect(lambda text, r=row: self.on_designation_changed(r, text))

        self.articles_table.setCellWidget(row, 0, designation_combo)

        # Unité
        self.articles_table.setItem(row, 1, QTableWidgetItem(ligne['unite'] or ""))

        # Quantité (QSpinBox)
        quantite_spin = QSpinBox()
        quantite_spin.setRange(1, 10000)
        quantite_spin.setValue(ligne['quantite'])
        quantite_spin.setStyleSheet("border: 1px solid #ddd; border-radius: 4px; padding: 4px;")
        quantite_spin.valueChanged.connect(self.calculer_totaux)
        quantite_spin.valueChanged.connect(lambda value, r=row: self.on_quantity_changed(r, value))
        self.articles_table.setCellWidget(row, 2, quantite_spin)

        # Vérifier le stock pour cette ligne existante si c'est un produit avec ID
        try:
            produit_id = ligne['produit_id'] if 'produit_id' in ligne.keys() else None
            if produit_id:
                self.verifier_stock_et_colorer(row, produit_id, ligne['quantite'])
        except (KeyError, TypeError):
            # Pas de produit_id ou erreur d'accès, ignorer la vérification du stock
            pass

        # Prix unitaire (QDoubleSpinBox)
        prix_spin = QDoubleSpinBox()
        prix_spin.setRange(0, 1000000)
        prix_spin.setValue(ligne['prix_unitaire_ht'])
        prix_spin.setDecimals(2)
        prix_spin.setSuffix(" DH")
        prix_spin.setStyleSheet("border: 1px solid #ddd; border-radius: 4px; padding: 4px;")
        prix_spin.valueChanged.connect(self.calculer_totaux)
        self.articles_table.setCellWidget(row, 3, prix_spin)

        # Prix total
        self.articles_table.setItem(row, 4, QTableWidgetItem(f"{ligne['total_ht']:.2f} DH"))

        # Bouton de suppression
        delete_button = QPushButton("🗑️")
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #EF4444;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #DC2626;
            }
        """)
        delete_button.clicked.connect(lambda: self.supprimer_ligne(row))

        # Créer un widget conteneur pour le bouton
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(2, 2, 2, 2)
        button_layout.addWidget(delete_button)
        button_layout.setAlignment(Qt.AlignCenter)

        self.articles_table.setCellWidget(row, 5, button_container)

        # إضافة التحديد التلقائي للحقول في الجدول بطريقة مباشرة
        self.setup_auto_select_for_table_widget(designation_combo)
        self.setup_auto_select_for_table_widget(quantite_spin)
        self.setup_auto_select_for_table_widget(prix_spin)
        print(f"✅ تم تطبيق التحديد التلقائي على حقول الجدول للسطر الموجود {row + 1}")


class BonCommandeModuleNew(BaseModule):
    """Module de gestion des bons de commande avec liste et formulaire séparés"""

    def __init__(self, db_manager, signals=None):
        super().__init__(
            db_manager=db_manager,
            signals=signals,
            title="Bons de commande",
            description="Gérez vos bons de commande",
            icon=ORDERS_ICON
        )

        # Ajouter bouton d'exportation
        self.add_export_button()

        self.setup_table()
        self.load_bons()

    def add_export_button(self):
        """Ajouter le bouton d'exportation Excel"""
        if excel_exporter_available:
            export_button = QPushButton("📊 Exporter vers Excel")
            export_button.setStyleSheet("""
                QPushButton {
                    background-color: #10B981;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    font-weight: bold;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #059669;
                }
            """)
            export_button.clicked.connect(self.exporter_vers_excel)

            # Ajouter le bouton à côté du bouton d'ajout
            self.header_layout.addWidget(export_button)

    def exporter_vers_excel(self):
        """Exporter les bons de commande vers Excel"""
        try:
            if excel_exporter_available:
                exporter = ExcelExporter(self.db_manager)
                success = exporter.export_bons_commande(self)

                if success:
                    QMessageBox.information(self, "Succès", "Les bons de commande ont été exportés avec succès vers Excel!")
            else:
                QMessageBox.warning(self, "Erreur", "Le module d'exportation Excel n'est pas disponible.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'exportation: {str(e)}")

    def setup_table(self):
        """Configure le tableau des bons de commande"""
        self.items_table.setColumnCount(7)  # Supprimer la colonne Actions
        self.items_table.setHorizontalHeaderLabels([
            "Numéro", "Date", "Client", "Objet", "Total HT", "Total TTC", "Statut"
        ])

        # Configuration du tableau
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Numéro
        self.items_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Date
        self.items_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)  # Client
        self.items_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)  # Objet
        self.items_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Total HT
        self.items_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Total TTC
        self.items_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Statut

        # Activer le menu contextuel (clic droit)
        self.items_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.items_table.customContextMenuRequested.connect(self.show_context_menu)

    def show_context_menu(self, position):
        """Affiche le menu contextuel au clic droit"""
        from PySide6.QtWidgets import QMenu
        from PySide6.QtGui import QAction

        # Vérifier qu'une ligne est sélectionnée
        item = self.items_table.itemAt(position)
        if not item:
            return

        row = item.row()

        # Récupérer les données de la ligne
        bon_id_item = self.items_table.item(row, 0)
        if not bon_id_item:
            return

        bon_numero = bon_id_item.text()
        bon_id = bon_id_item.data(Qt.UserRole)

        if not bon_id:
            return

        # Créer le menu contextuel
        menu = QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                padding: 4px;
            }
            QMenu::item {
                padding: 8px 16px;
                border-radius: 4px;
                margin: 2px;
            }
            QMenu::item:selected {
                background-color: #F3F4F6;
            }
            QMenu::separator {
                height: 1px;
                background-color: #E5E7EB;
                margin: 4px 8px;
            }
        """)

        # Action Modifier
        edit_action = QAction("✏️ Modifier", self)
        edit_action.triggered.connect(lambda: self.show_edit_dialog(bon_id))
        menu.addAction(edit_action)

        menu.addSeparator()

        # Action Imprimer
        print_action = QAction("🖨️ Imprimer", self)
        print_action.triggered.connect(lambda: self.imprimer_bon(bon_id))
        menu.addAction(print_action)

        # Action PDF
        pdf_action = QAction("📄 Exporter PDF", self)
        pdf_action.triggered.connect(lambda: self.exporter_pdf(bon_id))
        menu.addAction(pdf_action)

        menu.addSeparator()

        # Action Supprimer
        delete_action = QAction("🗑️ Supprimer", self)
        delete_action.triggered.connect(lambda: self.delete_item(bon_id, bon_numero))
        menu.addAction(delete_action)

        # Afficher le menu à la position du curseur
        menu.exec(self.items_table.mapToGlobal(position))

    def load_bons(self):
        """Charge la liste des bons de commande"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT b.*, c.nom as client_nom
                FROM bons_commande b
                LEFT JOIN clients c ON b.client_id = c.id
                ORDER BY b.date_creation DESC
            """)
            bons = cursor.fetchall()

            self.items_table.setRowCount(len(bons))

            for row, bon in enumerate(bons):
                # Numéro
                numero_item = QTableWidgetItem(bon['numero'] or "")
                numero_item.setData(Qt.UserRole, bon['id'])  # Stocker l'ID du bon de commande
                self.items_table.setItem(row, 0, numero_item)

                # Date
                date_str = ""
                if bon['date_creation']:
                    try:
                        date_obj = datetime.datetime.strptime(bon['date_creation'], '%Y-%m-%d')
                        date_str = date_obj.strftime('%d/%m/%Y')
                    except:
                        date_str = bon['date_creation']
                self.items_table.setItem(row, 1, QTableWidgetItem(date_str))

                # Client
                self.items_table.setItem(row, 2, QTableWidgetItem(bon['client_nom'] or ""))

                # Objet
                objet = bon['objet'] or ""
                if len(objet) > 50:
                    objet = objet[:50] + "..."
                self.items_table.setItem(row, 3, QTableWidgetItem(objet))

                # Total HT
                total_ht = f"{bon['total_ht']:.2f} DH" if bon['total_ht'] else "0.00 DH"
                self.items_table.setItem(row, 4, QTableWidgetItem(total_ht))

                # Total TTC
                total_ttc = f"{bon['total_ttc']:.2f} DH" if bon['total_ttc'] else "0.00 DH"
                self.items_table.setItem(row, 5, QTableWidgetItem(total_ttc))

                # Statut
                statut_item = QTableWidgetItem("En cours")
                # Utiliser setBackground et setForeground au lieu de setStyleSheet
                statut_item.setBackground(QBrush(QColor("#FEF3C7")))
                statut_item.setForeground(QBrush(QColor("#92400E")))
                self.items_table.setItem(row, 6, statut_item)



        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des bons de commande: {str(e)}")



    def show_add_dialog(self):
        """Affiche la boîte de dialogue pour ajouter un bon de commande"""
        dialog = BonCommandeFormDialog(self.db_manager, parent=self)
        dialog.bon_saved.connect(self.load_bons)
        dialog.stock_updated.connect(self.on_stock_updated)
        dialog.exec()

    def show_edit_dialog(self, bon_id):
        """Affiche la boîte de dialogue pour modifier un bon de commande"""
        try:
            dialog = BonCommandeFormDialog(self.db_manager, bon_id=bon_id, parent=self)
            dialog.bon_saved.connect(self.load_bons)
            dialog.stock_updated.connect(self.on_stock_updated)
            dialog.exec()
        except Exception as e:
            print(f"❌ Erreur lors de l'ouverture de la boîte de dialogue: {str(e)}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ouverture du bon de commande:\n{str(e)}")

    def on_stock_updated(self):
        """Appelé quand le stock est mis à jour"""
        print("📦 Signal de mise à jour du stock reçu dans BonCommandeModuleNew")
        # Émettre un signal global si disponible
        if self.signals and hasattr(self.signals, 'stock_changed'):
            self.signals.stock_changed.emit()
            print("📤 Signal global stock_changed émis")

    def delete_item(self, bon_id, bon_numero=None):
        """Supprime un bon de commande"""
        try:
            # Demander confirmation
            message = f"Êtes-vous sûr de vouloir supprimer le bon de commande {bon_numero}?"
            reply = QMessageBox.question(self, "Confirmation", message,
                                        QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

            if reply == QMessageBox.Yes:
                cursor = self.db_manager.conn.cursor()

                # Supprimer d'abord les lignes du bon
                cursor.execute("DELETE FROM lignes_bon_commande WHERE bon_id = ?", (bon_id,))

                # Puis supprimer le bon
                cursor.execute("DELETE FROM bons_commande WHERE id = ?", (bon_id,))

                self.db_manager.conn.commit()
                self.load_bons()

                # Émettre le signal de mise à jour si disponible
                if self.signals and hasattr(self.signals, 'bons_changed'):
                    self.signals.bons_changed.emit()

                QMessageBox.information(self, "Succès", "Bon de commande supprimé avec succès.")

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression du bon de commande: {str(e)}")

    def imprimer_bon(self, bon_id):
        """Imprime un bon de commande"""
        QMessageBox.information(self, "Impression", "Fonctionnalité d'impression en cours de développement.")

    def exporter_pdf(self, bon_id):
        """Exporte un bon de commande en PDF"""
        QMessageBox.information(self, "Export PDF", "Fonctionnalité d'export PDF en cours de développement.")

