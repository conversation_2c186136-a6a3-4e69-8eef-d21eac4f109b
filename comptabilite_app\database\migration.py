import sqlite3
import os
from .migration_unite import migrate_unite_column

def migrate_database(db_path="database/comptabilite.db"):
    """
    Migre la base de données existante vers la nouvelle structure
    """
    if not os.path.exists(db_path):
        print(f"Base de données {db_path} introuvable. Aucune migration nécessaire.")
        return

    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Migration de la table clients
    migrate_clients_table(cursor)

    # Migration de la table fournisseurs
    migrate_fournisseurs_table(cursor)

    # Migration de la table produits
    migrate_produits_table(cursor)

    # Création des tables pour les factures d'achat
    create_factures_achat_tables(cursor)

    # Création de la table mouvements_stock
    create_mouvements_stock_table(cursor)

    conn.commit()
    conn.close()

    # Migration pour ajouter la colonne unite à la table lignes_facture
    migrate_unite_column()

def migrate_clients_table(cursor):
    """Migre la table clients vers la nouvelle structure"""
    # Vérifier si la table clients existe déjà
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='clients'")
    table_exists = cursor.fetchone() is not None

    if table_exists:
        # Vérifier la structure actuelle de la table clients
        cursor.execute("PRAGMA table_info(clients)")
        columns = {col['name']: col for col in cursor.fetchall()}

        # Vérifier si la migration est nécessaire
        if 'code' not in columns or 'ice' not in columns or 'if_fiscal' not in columns or 'contact' not in columns:
            print("Migration de la table clients nécessaire...")

            # Créer une table temporaire avec la nouvelle structure
            cursor.execute('''
            CREATE TABLE clients_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE,
                nom TEXT NOT NULL,
                ice TEXT,
                if_fiscal TEXT,
                adresse TEXT,
                telephone TEXT,
                email TEXT,
                contact TEXT
            )
            ''')

            # Copier les données existantes
            if 'siret' in columns:
                cursor.execute('''
                INSERT INTO clients_new (id, nom, adresse, telephone, email)
                SELECT id, nom, adresse, telephone, email FROM clients
                ''')
            else:
                cursor.execute('''
                INSERT INTO clients_new (id, nom, adresse, telephone, email)
                SELECT id, nom, adresse, telephone, email FROM clients
                ''')

            # Générer des codes clients pour les entrées existantes
            cursor.execute("SELECT id FROM clients_new ORDER BY id")
            clients = cursor.fetchall()

            for i, client in enumerate(clients):
                code = f"C{(i+1):03d}"
                cursor.execute("UPDATE clients_new SET code = ? WHERE id = ?", (code, client['id']))

            # Supprimer l'ancienne table et renommer la nouvelle
            cursor.execute("DROP TABLE clients")
            cursor.execute("ALTER TABLE clients_new RENAME TO clients")

            print("Migration de la table clients terminée avec succès.")
        else:
            print("La table clients est déjà à jour. Aucune migration nécessaire.")

def migrate_fournisseurs_table(cursor):
    """Migre la table fournisseurs vers la nouvelle structure"""
    # Vérifier si la table fournisseurs existe déjà
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='fournisseurs'")
    table_exists = cursor.fetchone() is not None

    if table_exists:
        # Vérifier la structure actuelle de la table fournisseurs
        cursor.execute("PRAGMA table_info(fournisseurs)")
        columns = {col['name']: col for col in cursor.fetchall()}

        # Vérifier si la migration est nécessaire
        if 'code' not in columns or 'ice' not in columns or 'if_fiscal' not in columns or 'contact' not in columns:
            print("Migration de la table fournisseurs nécessaire...")

            # Créer une table temporaire avec la nouvelle structure
            cursor.execute('''
            CREATE TABLE fournisseurs_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE,
                nom TEXT NOT NULL,
                ice TEXT,
                if_fiscal TEXT,
                adresse TEXT,
                telephone TEXT,
                email TEXT,
                contact TEXT
            )
            ''')

            # Copier les données existantes
            if 'siret' in columns:
                cursor.execute('''
                INSERT INTO fournisseurs_new (id, nom, adresse, telephone, email)
                SELECT id, nom, adresse, telephone, email FROM fournisseurs
                ''')
            else:
                cursor.execute('''
                INSERT INTO fournisseurs_new (id, nom, adresse, telephone, email)
                SELECT id, nom, adresse, telephone, email FROM fournisseurs
                ''')

            # Générer des codes fournisseurs pour les entrées existantes
            cursor.execute("SELECT id FROM fournisseurs_new ORDER BY id")
            fournisseurs = cursor.fetchall()

            for i, fournisseur in enumerate(fournisseurs):
                code = f"F{(i+1):03d}"
                cursor.execute("UPDATE fournisseurs_new SET code = ? WHERE id = ?", (code, fournisseur['id']))

            # Supprimer l'ancienne table et renommer la nouvelle
            cursor.execute("DROP TABLE fournisseurs")
            cursor.execute("ALTER TABLE fournisseurs_new RENAME TO fournisseurs")

            print("Migration de la table fournisseurs terminée avec succès.")
        else:
            print("La table fournisseurs est déjà à jour. Aucune migration nécessaire.")

def migrate_produits_table(cursor):
    """Migre la table produits vers la nouvelle structure"""
    # Vérifier si la table produits existe déjà
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='produits'")
    table_exists = cursor.fetchone() is not None

    if table_exists:
        # Vérifier la structure actuelle de la table produits
        cursor.execute("PRAGMA table_info(produits)")
        columns = {col['name']: col for col in cursor.fetchall()}

        # Vérifier si la migration est nécessaire
        if 'code' not in columns or 'reference' in columns:
            print("Migration de la table produits nécessaire...")

            # Créer une table temporaire avec la nouvelle structure
            cursor.execute('''
            CREATE TABLE produits_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE,
                designation TEXT NOT NULL,
                unite TEXT,
                prix_achat REAL,
                prix_vente REAL,
                stock INTEGER DEFAULT 0
            )
            ''')

            # Copier les données existantes
            if 'reference' in columns:
                cursor.execute('''
                INSERT INTO produits_new (id, code, designation, unite, prix_achat, prix_vente, stock)
                SELECT id, reference, designation, unite, prix_achat_ht, prix_vente_ht, stock FROM produits
                ''')
            else:
                cursor.execute('''
                INSERT INTO produits_new (id, designation, unite, prix_achat, prix_vente, stock)
                SELECT id, designation, unite, prix_achat, prix_vente, stock FROM produits
                ''')

            # Générer des codes produits pour les entrées sans code
            cursor.execute("SELECT id FROM produits_new WHERE code IS NULL ORDER BY id")
            produits = cursor.fetchall()

            for i, produit in enumerate(produits):
                code = f"P{(i+1):03d}"
                cursor.execute("UPDATE produits_new SET code = ? WHERE id = ?", (code, produit['id']))

            # Supprimer l'ancienne table et renommer la nouvelle
            cursor.execute("DROP TABLE produits")
            cursor.execute("ALTER TABLE produits_new RENAME TO produits")

            print("Migration de la table produits terminée avec succès.")
        else:
            print("La table produits est déjà à jour. Aucune migration nécessaire.")

def create_factures_achat_tables(cursor):
    """Crée les tables pour les factures d'achat si elles n'existent pas"""
    # Vérifier si les tables existent déjà
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='factures_achat'")
    factures_achat_exists = cursor.fetchone() is not None

    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='lignes_facture_achat'")
    lignes_facture_achat_exists = cursor.fetchone() is not None

    if not factures_achat_exists:
        print("Création de la table factures_achat...")
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS factures_achat (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            numero TEXT NOT NULL,
            date_creation TEXT NOT NULL,
            fournisseur_id INTEGER,
            mode_paiement TEXT NOT NULL,
            total_ht REAL NOT NULL,
            notes TEXT,
            FOREIGN KEY (fournisseur_id) REFERENCES fournisseurs (id)
        )
        ''')

    if not lignes_facture_achat_exists:
        print("Création de la table lignes_facture_achat...")
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS lignes_facture_achat (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            facture_achat_id INTEGER NOT NULL,
            produit_id INTEGER,
            designation TEXT NOT NULL,
            unite TEXT,
            quantite INTEGER NOT NULL,
            prix_unitaire_ht REAL NOT NULL,
            total_ht REAL NOT NULL,
            FOREIGN KEY (facture_achat_id) REFERENCES factures_achat (id),
            FOREIGN KEY (produit_id) REFERENCES produits (id)
        )
        ''')

    if not factures_achat_exists or not lignes_facture_achat_exists:
        print("Tables pour les factures d'achat créées avec succès.")
    else:
        print("Les tables pour les factures d'achat existent déjà.")

def create_mouvements_stock_table(cursor):
    """Crée la table mouvements_stock si elle n'existe pas"""
    # Vérifier si la table existe déjà
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='mouvements_stock'")
    table_exists = cursor.fetchone() is not None

    if not table_exists:
        print("Création de la table mouvements_stock...")
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS mouvements_stock (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            produit_id INTEGER NOT NULL,
            type_mouvement TEXT CHECK (type_mouvement IN ('entree', 'sortie')) NOT NULL,
            quantite INTEGER NOT NULL,
            prix_unitaire REAL,
            date_mouvement TEXT DEFAULT CURRENT_DATE,
            reference TEXT,
            notes TEXT,
            FOREIGN KEY (produit_id) REFERENCES produits (id)
        )
        ''')
        print("Table mouvements_stock créée avec succès.")
    else:
        print("La table mouvements_stock existe déjà.")

if __name__ == "__main__":
    migrate_database()
