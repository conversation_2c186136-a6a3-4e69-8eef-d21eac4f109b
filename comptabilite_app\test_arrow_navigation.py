#!/usr/bin/env python3
"""
Test simple pour la navigation par flèches
"""

import sys
from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QLineEdit, QLabel, QPushButton
from PySide6.QtCore import Qt

# Importer notre composant
from ui.components.auto_select_widget import apply_auto_select_to_window

class TestWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Navigation par Flèches")
        self.setGeometry(100, 100, 400, 300)
        
        layout = QVBoxLayout()
        
        # Instructions
        instructions = QLabel("""
Instructions:
1. Cliquez dans le premier champ
2. Utilisez les flèches ↑↓ ou ←→ pour naviguer
3. Le texte doit être sélectionné automatiquement
4. Tapez pour remplacer le texte sélectionné
        """)
        layout.addWidget(instructions)
        
        # Champs de test
        self.field1 = QLineEdit("Champ 1 - Nom")
        self.field2 = QLineEdit("Champ 2 - Prénom") 
        self.field3 = QLineEdit("Champ 3 - Email")
        self.field4 = QLineEdit("Champ 4 - Téléphone")
        
        layout.addWidget(QLabel("Nom:"))
        layout.addWidget(self.field1)
        layout.addWidget(QLabel("Prénom:"))
        layout.addWidget(self.field2)
        layout.addWidget(QLabel("Email:"))
        layout.addWidget(self.field3)
        layout.addWidget(QLabel("Téléphone:"))
        layout.addWidget(self.field4)
        
        # Bouton de fermeture
        close_btn = QPushButton("Fermer")
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
        
        self.setLayout(layout)
        
        # Appliquer la navigation par flèches
        apply_auto_select_to_window(self)
        
        print("🧪 Fenêtre de test créée avec navigation par flèches")
        print("📝 Essayez de naviguer entre les champs avec les flèches")

def main():
    app = QApplication(sys.argv)
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
