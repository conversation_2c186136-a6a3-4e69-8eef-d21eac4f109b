from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QFormLayout, QMessageBox, QGridLayout,
                              QFrame, QScrollArea)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon

# Importer les styles modernes
from ..theme import COLORS, BORDER_RADIUS, SPACING

class BaseFormDialog(QDialog):
    """
    Composant de base pour tous les formulaires modaux
    Fournit une interface standardisée avec:
    - Un en-tête avec titre
    - Une zone de formulaire
    - Des boutons d'action (Annuler, Enregistrer)
    """
    
    # Signal émis lorsque le formulaire est soumis avec succès
    form_submitted = Signal()
    
    def __init__(self, db_manager, title="", is_edit_mode=False, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.title = title
        self.is_edit_mode = is_edit_mode
        
        # Configuration de la boîte de dialogue
        self.setWindowTitle(title)
        self.setMinimumWidth(600)
        self.setMinimumHeight(400)
        self.setModal(True)
        
        # Mise en page
        self.setup_ui()
    
    def setup_ui(self):
        """Configure l'interface utilisateur de base"""
        layout = QVBoxLayout(self)
        layout.setSpacing(int(SPACING['md'].replace('px', '')))
        layout.setContentsMargins(int(SPACING['md'].replace('px', '')),
                                 int(SPACING['md'].replace('px', '')),
                                 int(SPACING['md'].replace('px', '')),
                                 int(SPACING['md'].replace('px', '')))
        
        # En-tête avec titre
        header_container = QFrame()
        header_container.setObjectName("form_header")
        header_container.setStyleSheet(f"""
            #form_header {{
                border-bottom: 1px solid {COLORS['divider']};
                padding-bottom: 10px;
                margin-bottom: 10px;
            }}
        """)
        header_layout = QVBoxLayout(header_container)
        header_layout.setContentsMargins(0, 0, 0, 10)
        
        # Titre du formulaire
        title_label = QLabel(self.title)
        title_label.setObjectName("form_title")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #1A56DB;
        """)
        
        header_layout.addWidget(title_label)
        layout.addWidget(header_container)
        
        # Zone de défilement pour le formulaire
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # Widget conteneur pour le formulaire
        self.form_container = QFrame()
        self.form_container.setObjectName("form_container")
        
        # Layout pour le formulaire (à remplir dans les classes dérivées)
        self.form_layout = QGridLayout(self.form_container)
        self.form_layout.setContentsMargins(0, 0, 0, 0)
        self.form_layout.setSpacing(15)
        
        # Ajouter le conteneur de formulaire à la zone de défilement
        scroll_area.setWidget(self.form_container)
        layout.addWidget(scroll_area)
        
        # Boutons d'action
        buttons_container = QFrame()
        buttons_container.setObjectName("form_buttons")
        buttons_container.setStyleSheet(f"""
            #form_buttons {{
                border-top: 1px solid {COLORS['divider']};
                padding-top: 10px;
                margin-top: 10px;
            }}
        """)
        buttons_layout = QHBoxLayout(buttons_container)
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        # Bouton Annuler
        self.cancel_button = QPushButton("Annuler")
        self.cancel_button.setObjectName("cancel_button")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #F3F4F6;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)
        
        # Bouton Enregistrer
        self.save_button = QPushButton("Enregistrer")
        self.save_button.setObjectName("save_button")
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #1A56DB;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1E40AF;
            }
        """)
        self.save_button.clicked.connect(self.submit_form)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.save_button)
        
        layout.addWidget(buttons_container)
    
    def create_form_field(self, label_text, widget, required=False, row=0, col=0, colspan=1):
        """Crée un champ de formulaire avec un label"""
        # Label
        label = QLabel(label_text + ("*" if required else ""))
        label.setStyleSheet("""
            font-weight: bold;
            color: #374151;
        """)
        self.form_layout.addWidget(label, row, col * 2)
        
        # Widget
        self.form_layout.addWidget(widget, row, col * 2 + 1, 1, colspan * 2 - 1)
        
        return widget
    
    def submit_form(self):
        """Méthode appelée lorsque le formulaire est soumis"""
        # Valider le formulaire
        if not self.validate_form():
            return
        
        # Enregistrer les données
        try:
            self.save_data()
            self.form_submitted.emit()
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
    
    def validate_form(self):
        """Valide les données du formulaire (à implémenter dans les classes dérivées)"""
        return True
    
    def save_data(self):
        """Enregistre les données du formulaire (à implémenter dans les classes dérivées)"""
        pass
    
    def load_data(self, item_id):
        """Charge les données d'un élément existant (à implémenter dans les classes dérivées)"""
        pass
