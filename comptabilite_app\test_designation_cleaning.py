#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test du nettoyage des désignations pour résoudre le problème de recherche
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager
from stock_integration_solution import StockIntegrationManager
import datetime

def test_designation_cleaning():
    """Test le nettoyage des désignations"""
    
    print("🧪 Test du nettoyage des désignations")
    print("=" * 50)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    stock_manager = StockIntegrationManager(db_manager.conn)
    
    # Test des différents formats de désignation
    test_cases = [
        "LAMPE LED 50W - 55.00 DH (Stock: 150)",
        "LAMPE LED 50W - 55.00 DH",
        "LAMPE LED 50W (Stock: 150)",
        "LAMPE LED 50W",
        "DISJONCTEUR - 20000.00 DH (Stock: 20)",
        "DISJONCTEUR",
        "CAMERA - 1000.00 DH (Stock: 10)",
        "CAMERA"
    ]
    
    print("\n🔍 Test du nettoyage:")
    for designation in test_cases:
        clean = stock_manager.clean_designation(designation)
        print(f"Original: '{designation}'")
        print(f"Nettoyé:  '{clean}'")
        print("-" * 40)
    
    print("\n🔍 Test de recherche de produits:")
    for designation in test_cases:
        produit_id = stock_manager.find_product_by_designation(designation)
        if produit_id:
            print(f"✅ '{designation}' -> Produit ID: {produit_id}")
        else:
            print(f"❌ '{designation}' -> Non trouvé")
        print("-" * 40)
    
    # Test d'intégration complète
    print("\n🧪 Test d'intégration complète:")
    
    # Créer une facture test
    cursor = db_manager.conn.cursor()
    cursor.execute("""
        INSERT INTO factures_vente (numero, date_creation, client_id, total_ht, total_ttc)
        VALUES (?, ?, ?, ?, ?)
    """, ("F-TEST-CLEAN-001", datetime.datetime.now().strftime('%Y-%m-%d'), 1, 100.0, 120.0))
    
    facture_id = cursor.lastrowid
    print(f"✅ Facture créée avec ID: {facture_id}")
    
    # Lignes avec différents formats
    lignes_facture = [
        {
            'designation': 'LAMPE LED 50W - 55.00 DH (Stock: 150)',
            'quantite': 2
        },
        {
            'designation': 'DISJONCTEUR - 20000.00 DH (Stock: 20)',
            'quantite': 1
        }
    ]
    
    # Ajouter les lignes à la base
    for ligne in lignes_facture:
        cursor.execute("""
            INSERT INTO lignes_facture (facture_id, designation, unite, quantite, prix_unitaire_ht, taux_tva, total_ht)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (facture_id, ligne['designation'], 'U', ligne['quantite'], 50.0, 20.0, 50.0))
    
    db_manager.conn.commit()
    
    # Tester l'intégration
    success = stock_manager.add_stock_movements_for_invoice(facture_id, lignes_facture)
    
    if success:
        print("✅ Intégration réussie avec nettoyage des désignations")
        
        # Vérifier les résultats
        cursor.execute("""
            SELECT 
                p.designation,
                COALESCE(SUM(CASE WHEN m.type_mouvement = 'sortie' THEN m.quantite ELSE 0 END), 0) as sorties
            FROM produits p
            LEFT JOIN mouvements_stock m ON p.id = m.produit_id
            WHERE p.designation IN ('LAMPE LED 50W', 'DISJONCTEUR')
            GROUP BY p.id, p.designation
        """)
        
        results = cursor.fetchall()
        for result in results:
            print(f"📊 {result[0]}: Sorties = {result[1]}")
    else:
        print("❌ Échec de l'intégration")
    
    # Nettoyer
    try:
        cursor.execute("DELETE FROM mouvements_stock WHERE facture_id = ?", (facture_id,))
        cursor.execute("DELETE FROM lignes_facture WHERE facture_id = ?", (facture_id,))
        cursor.execute("DELETE FROM factures_vente WHERE id = ?", (facture_id,))
        db_manager.conn.commit()
        print("🧹 Données de test nettoyées")
    except:
        pass

if __name__ == "__main__":
    test_designation_cleaning()
