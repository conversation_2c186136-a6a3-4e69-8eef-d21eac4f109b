#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات حقول التاريخ CustomTkinter مع دعم الكتابة بلوحة المفاتيح
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    import customtkinter as ctk
    from datetime import datetime, timedelta
    from run_app import create_enhanced_date_entry
    
    class CTkDateTestApp(ctk.CTk):
        """تطبيق اختبار حقول التاريخ المحسنة لـ CustomTkinter"""
        
        def __init__(self):
            super().__init__()
            
            # إعداد النافذة
            self.title("🗓️ اختبار حقول التاريخ المحسنة - CustomTkinter")
            self.geometry("700x600")
            
            # تطبيق المظهر الداكن
            ctk.set_appearance_mode("light")
            ctk.set_default_color_theme("blue")
            
            self.setup_ui()
            
        def setup_ui(self):
            """إعداد واجهة المستخدم"""
            
            # العنوان الرئيسي
            title_label = ctk.CTkLabel(
                self, 
                text="🗓️ اختبار حقول التاريخ المحسنة",
                font=ctk.CTkFont(size=24, weight="bold")
            )
            title_label.pack(pady=20)
            
            # إطار النموذج
            form_frame = ctk.CTkFrame(self)
            form_frame.pack(fill="both", expand=True, padx=20, pady=10)
            
            # تكوين الشبكة
            form_frame.grid_columnconfigure(1, weight=1)
            
            # حقول التاريخ المختلفة
            row = 0
            
            # 1. حقل تاريخ عادي
            ctk.CTkLabel(form_frame, text="📅 تاريخ عادي:", font=ctk.CTkFont(weight="bold")).grid(
                row=row, column=0, padx=10, pady=10, sticky="w"
            )
            self.date_normal = create_enhanced_date_entry(
                form_frame, 
                placeholder_text="اختر تاريخ 📅"
            )
            self.date_normal.grid(row=row, column=1, padx=10, pady=10, sticky="ew")
            row += 1
            
            # 2. حقل تاريخ الشراء (للمنتجات)
            ctk.CTkLabel(form_frame, text="🛒 تاريخ الشراء:", font=ctk.CTkFont(weight="bold")).grid(
                row=row, column=0, padx=10, pady=10, sticky="w"
            )
            self.date_purchase = create_enhanced_date_entry(
                form_frame, 
                placeholder_text="تاريخ شراء المنتج 📅"
            )
            self.date_purchase.grid(row=row, column=1, padx=10, pady=10, sticky="ew")
            row += 1
            
            # 3. حقل تاريخ الدفع
            ctk.CTkLabel(form_frame, text="💳 تاريخ الدفع:", font=ctk.CTkFont(weight="bold")).grid(
                row=row, column=0, padx=10, pady=10, sticky="w"
            )
            self.date_payment = create_enhanced_date_entry(
                form_frame, 
                placeholder_text="تاريخ دفع الفاتورة 💰"
            )
            self.date_payment.grid(row=row, column=1, padx=10, pady=10, sticky="ew")
            row += 1
            
            # 4. حقل تاريخ التسليم
            ctk.CTkLabel(form_frame, text="🚚 تاريخ التسليم:", font=ctk.CTkFont(weight="bold")).grid(
                row=row, column=0, padx=10, pady=10, sticky="w"
            )
            self.date_delivery = create_enhanced_date_entry(
                form_frame, 
                placeholder_text="تاريخ تسليم الطلب 🚚"
            )
            self.date_delivery.grid(row=row, column=1, padx=10, pady=10, sticky="ew")
            row += 1
            
            # 5. حقل تاريخ الفاتورة
            ctk.CTkLabel(form_frame, text="🧾 تاريخ الفاتورة:", font=ctk.CTkFont(weight="bold")).grid(
                row=row, column=0, padx=10, pady=10, sticky="w"
            )
            self.date_invoice = create_enhanced_date_entry(
                form_frame, 
                placeholder_text="تاريخ إصدار الفاتورة 🧾"
            )
            self.date_invoice.grid(row=row, column=1, padx=10, pady=10, sticky="ew")
            row += 1
            
            # إطار التعليمات
            instructions_frame = ctk.CTkFrame(form_frame)
            instructions_frame.grid(row=row, column=0, columnspan=2, padx=10, pady=20, sticky="ew")
            
            instructions_text = """
💡 تعليمات الاستخدام:

✅ الكتابة المباشرة: اكتب التاريخ بصيغة DD/MM/YYYY
✅ التنسيق التلقائي: سيتم إضافة الشرطات المائلة تلقائياً
✅ التقويم: انقر على 📅 لفتح التقويم
✅ السنة المختصرة: يمكنك كتابة 25 بدلاً من 2025
✅ مثال: اكتب 15122024 وسيصبح 15/12/2024

🎯 جرب كتابة التواريخ مباشرة بلوحة المفاتيح!
            """
            
            instructions_label = ctk.CTkLabel(
                instructions_frame,
                text=instructions_text,
                font=ctk.CTkFont(size=12),
                justify="left"
            )
            instructions_label.pack(padx=15, pady=15)
            row += 1
            
            # إطار الأزرار
            buttons_frame = ctk.CTkFrame(self)
            buttons_frame.pack(fill="x", padx=20, pady=10)
            
            # زر عرض القيم
            show_btn = ctk.CTkButton(
                buttons_frame,
                text="📋 عرض القيم المدخلة",
                command=self.show_values,
                font=ctk.CTkFont(weight="bold")
            )
            show_btn.pack(side="left", padx=10, pady=10)
            
            # زر مسح الحقول
            clear_btn = ctk.CTkButton(
                buttons_frame,
                text="🗑️ مسح الحقول",
                command=self.clear_fields,
                font=ctk.CTkFont(weight="bold"),
                fg_color="red",
                hover_color="darkred"
            )
            clear_btn.pack(side="left", padx=10, pady=10)
            
            # زر تعيين تواريخ تجريبية
            test_btn = ctk.CTkButton(
                buttons_frame,
                text="🎯 تواريخ تجريبية",
                command=self.set_test_dates,
                font=ctk.CTkFont(weight="bold"),
                fg_color="purple",
                hover_color="darkviolet"
            )
            test_btn.pack(side="left", padx=10, pady=10)
            
            # زر اختبار منتج
            product_btn = ctk.CTkButton(
                buttons_frame,
                text="📦 اختبار منتج",
                command=self.test_product_scenario,
                font=ctk.CTkFont(weight="bold"),
                fg_color="green",
                hover_color="darkgreen"
            )
            product_btn.pack(side="left", padx=10, pady=10)
            
        def show_values(self):
            """عرض القيم المدخلة في جميع الحقول"""
            print("=" * 60)
            print("📋 القيم المدخلة في حقول التاريخ:")
            print("=" * 60)
            
            fields = [
                ("📅 التاريخ العادي", self.date_normal),
                ("🛒 تاريخ الشراء", self.date_purchase),
                ("💳 تاريخ الدفع", self.date_payment),
                ("🚚 تاريخ التسليم", self.date_delivery),
                ("🧾 تاريخ الفاتورة", self.date_invoice)
            ]
            
            for label, field in fields:
                try:
                    value = field.get() if hasattr(field, 'get') else "غير متاح"
                    if value and value.strip():
                        print(f"{label}: {value}")
                    else:
                        print(f"{label}: غير محدد")
                except:
                    print(f"{label}: خطأ في القراءة")
            
            print("=" * 60)
            
        def clear_fields(self):
            """مسح جميع الحقول"""
            fields = [
                self.date_normal,
                self.date_purchase,
                self.date_payment,
                self.date_delivery,
                self.date_invoice
            ]
            
            for field in fields:
                try:
                    if hasattr(field, 'delete'):
                        field.delete(0, 'end')
                    elif hasattr(field, 'set'):
                        field.set("")
                except:
                    pass
            
            print("🗑️ تم مسح جميع الحقول")
            
        def set_test_dates(self):
            """تعيين تواريخ تجريبية"""
            today = datetime.now()
            
            test_dates = [
                (self.date_normal, today.strftime("%d/%m/%Y")),
                (self.date_purchase, (today - timedelta(days=5)).strftime("%d/%m/%Y")),
                (self.date_payment, (today + timedelta(days=7)).strftime("%d/%m/%Y")),
                (self.date_delivery, (today + timedelta(days=14)).strftime("%d/%m/%Y")),
                (self.date_invoice, today.strftime("%d/%m/%Y"))
            ]
            
            for field, date_str in test_dates:
                try:
                    if hasattr(field, 'delete'):
                        field.delete(0, 'end')
                        field.insert(0, date_str)
                    elif hasattr(field, 'set'):
                        field.set(date_str)
                except:
                    pass
            
            print("🎯 تم تعيين التواريخ التجريبية")
            
        def test_product_scenario(self):
            """اختبار سيناريو إضافة منتج"""
            today = datetime.now()
            
            # سيناريو: شراء منتج اليوم، دفع بعد أسبوع
            purchase_date = today.strftime("%d/%m/%Y")
            payment_date = (today + timedelta(days=7)).strftime("%d/%m/%Y")
            invoice_date = today.strftime("%d/%m/%Y")
            delivery_date = (today + timedelta(days=3)).strftime("%d/%m/%Y")
            
            scenarios = [
                (self.date_purchase, purchase_date, "تاريخ شراء المنتج"),
                (self.date_payment, payment_date, "تاريخ دفع الفاتورة"),
                (self.date_invoice, invoice_date, "تاريخ الفاتورة"),
                (self.date_delivery, delivery_date, "تاريخ التسليم المتوقع")
            ]
            
            print("📦 اختبار سيناريو إضافة منتج:")
            print("-" * 40)
            
            for field, date_str, description in scenarios:
                try:
                    if hasattr(field, 'delete'):
                        field.delete(0, 'end')
                        field.insert(0, date_str)
                    elif hasattr(field, 'set'):
                        field.set(date_str)
                    print(f"✅ {description}: {date_str}")
                except Exception as e:
                    print(f"❌ خطأ في {description}: {e}")
            
            print("-" * 40)

    def main():
        """الدالة الرئيسية"""
        try:
            app = CTkDateTestApp()
            
            print("🚀 تم تشغيل اختبار حقول التاريخ المحسنة - CustomTkinter")
            print("💡 جرب كتابة التواريخ مباشرة بلوحة المفاتيح!")
            print("🎯 استخدم الأزرار لاختبار الوظائف المختلفة")
            
            app.mainloop()
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل التطبيق: {e}")
            import traceback
            traceback.print_exc()

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("تأكد من تثبيت customtkinter")
    sys.exit(1)
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
