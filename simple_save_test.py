#!/usr/bin/env python3
"""
اختبار بسيط لزر الحفظ
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from comptabilite_app.database.db_manager import DatabaseManager

def simple_save_test():
    """اختبار بسيط لعملية الحفظ"""
    print("🔍 اختبار بسيط لزر الحفظ")
    print("=" * 40)
    
    # Initialize database manager
    db_manager = DatabaseManager()
    cursor = db_manager.conn.cursor()
    
    # 1. فحص قاعدة البيانات
    print("📊 فحص قاعدة البيانات:")
    try:
        cursor.execute("PRAGMA table_info(produits)")
        columns = cursor.fetchall()
        column_names = [col['name'] for col in columns]
        
        required_columns = ['id', 'code', 'designation', 'prix_achat', 'prix_vente', 'stock']
        missing = [col for col in required_columns if col not in column_names]
        
        if missing:
            print(f"   ❌ أعمدة مفقودة: {missing}")
            return False
        else:
            print("   ✅ الأعمدة الأساسية موجودة")
            
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {str(e)}")
        return False
    
    # 2. اختبار إدراج مباشر
    print("\n💾 اختبار الإدراج المباشر:")
    try:
        # عد المنتجات قبل الإدراج
        cursor.execute("SELECT COUNT(*) as count FROM produits")
        count_before = cursor.fetchone()['count']
        print(f"   عدد المنتجات قبل الإدراج: {count_before}")
        
        # إدراج منتج اختباري
        cursor.execute("""
            INSERT INTO produits (code, designation, unite, prix_achat, prix_vente, stock)
            VALUES (?, ?, ?, ?, ?, ?)
        """, ("TEST-001", "منتج اختبار بسيط", "قطعة", 100.0, 150.0, 5))
        
        db_manager.conn.commit()
        
        # عد المنتجات بعد الإدراج
        cursor.execute("SELECT COUNT(*) as count FROM produits")
        count_after = cursor.fetchone()['count']
        print(f"   عدد المنتجات بعد الإدراج: {count_after}")
        
        if count_after > count_before:
            print("   ✅ الإدراج نجح!")
            
            # التحقق من البيانات
            cursor.execute("SELECT * FROM produits WHERE code = 'TEST-001'")
            product = cursor.fetchone()
            if product:
                print(f"   ✅ المنتج موجود: {product['designation']}")
                
                # حذف البيانات الاختبارية
                cursor.execute("DELETE FROM produits WHERE code = 'TEST-001'")
                db_manager.conn.commit()
                print("   🧹 تم حذف البيانات الاختبارية")
                
        else:
            print("   ❌ الإدراج فشل!")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في الإدراج: {str(e)}")
        return False
    
    # 3. فحص النموذج
    print("\n🎨 فحص النموذج:")
    try:
        # استيراد النموذج
        from comptabilite_app.ui.forms.produit_form import ProduitForm
        print("   ✅ تم استيراد النموذج بنجاح")
        
        # فحص دالة الحفظ
        form_class = ProduitForm
        if hasattr(form_class, 'save_produit'):
            print("   ✅ دالة save_produit موجودة")
        else:
            print("   ❌ دالة save_produit مفقودة")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في استيراد النموذج: {str(e)}")
        return False
    
    print("\n🎯 النتيجة:")
    print("   ✅ قاعدة البيانات تعمل بشكل صحيح")
    print("   ✅ عمليات الإدراج تعمل")
    print("   ✅ النموذج موجود ودالة الحفظ موجودة")
    print("\n📋 إذا كان زر الحفظ لا يعمل، فالمشكلة قد تكون:")
    print("   1. خطأ في واجهة المستخدم")
    print("   2. خطأ في ربط الزر بالدالة")
    print("   3. خطأ في التحقق من صحة البيانات")
    print("\n🔧 الحل المقترح:")
    print("   1. تشغيل التطبيق الرئيسي")
    print("   2. فتح قسم المنتجات")
    print("   3. محاولة إضافة منتج جديد")
    print("   4. إذا لم يعمل، فحص رسائل الخطأ في وحدة التحكم")
    
    return True

if __name__ == "__main__":
    simple_save_test()
