#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys

def check_database():
    """فحص البيانات في قاعدة البيانات"""
    try:
        conn = sqlite3.connect('comptabilite.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        print("=== فحص جدول المنتجات ===")
        cursor.execute("""
            SELECT id, code, designation, fournisseur, prix_achat, stock, 
                   (prix_achat * stock) as valeur_stock, date_paiement 
            FROM produits 
            WHERE date_paiement IS NOT NULL 
            LIMIT 10
        """)
        products = cursor.fetchall()
        
        for i, p in enumerate(products):
            print(f"المنتج {i+1}:")
            print(f"  ID: {p['id']}")
            print(f"  Code: {p['code']}")
            print(f"  Fournisseur: '{p['fournisseur']}'")
            print(f"  Prix achat: {p['prix_achat']}")
            print(f"  Stock: {p['stock']}")
            print(f"  Valeur stock: {p['valeur_stock']}")
            print(f"  Date paiement: {p['date_paiement']}")
            print("---")

        print("\n=== فحص جدول الموردين ===")
        cursor.execute("SELECT * FROM fournisseurs")
        suppliers = cursor.fetchall()
        
        for s in suppliers:
            print(f"المورد: Code={s['code']}, Nom='{s['nom']}', ICE={s['ice']}")

        print(f"\n=== إحصائيات ===")
        cursor.execute("SELECT COUNT(*) as total FROM produits")
        total_products = cursor.fetchone()['total']
        print(f"إجمالي المنتجات: {total_products}")
        
        cursor.execute("SELECT COUNT(*) as total FROM produits WHERE fournisseur IS NOT NULL AND fournisseur != ''")
        products_with_supplier = cursor.fetchone()['total']
        print(f"المنتجات مع مورد: {products_with_supplier}")
        
        cursor.execute("SELECT COUNT(*) as total FROM produits WHERE date_paiement IS NOT NULL")
        products_with_payment = cursor.fetchone()['total']
        print(f"المنتجات مع تاريخ دفع: {products_with_payment}")

        conn.close()
        
    except Exception as e:
        print(f"خطأ: {e}")

if __name__ == "__main__":
    check_database()
