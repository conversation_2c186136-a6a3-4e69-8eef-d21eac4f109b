from PySide6.QtWidgets import (QTableWidgetItem, QHeaderView, QMessageBox, QDialog, QTableWidget)
from PySide6.QtCore import Qt, QDate
import sqlite3
import datetime

from ..components.list_dialog import ListDialog

class AchatsListDialog(ListDialog):
    """Boîte de dialogue pour afficher la liste des achats de produits"""

    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "Liste des achats de produits", parent)
        self.load_items()

    def setup_table(self):
        """Configure le tableau des achats"""
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "N° Facture", "Date", "Fournisseur", "Total HT", "TVA", "Total TTC", "Actions"
        ])

        self.items_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # N° Facture
        self.items_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Date
        self.items_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Total HT
        self.items_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # TVA
        self.items_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Total TTC
        self.items_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.Fixed)  # Actions
        self.items_table.setColumnWidth(6, 120)

        self.items_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.items_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.items_table.verticalHeader().setVisible(False)
        self.items_table.setAlternatingRowColors(True)

        # Connecter le signal de double-clic pour ouvrir la boîte de dialogue d'édition
        self.items_table.itemDoubleClicked.connect(self.on_item_double_clicked)

    def load_items(self):
        """Charge les achats depuis la base de données"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT a.id, a.numero_facture, a.date_facture, f.nom as fournisseur_nom,
                       a.total_ht, a.total_tva, a.total_ttc
                FROM achats_produits a
                LEFT JOIN fournisseurs f ON a.fournisseur_id = f.id
                ORDER BY a.date_facture DESC
            """)
            achats = cursor.fetchall()

            self.items_table.setRowCount(0)

            for row_index, achat in enumerate(achats):
                self.items_table.insertRow(row_index)

                # Formater la date
                date_obj = datetime.datetime.strptime(achat['date_facture'], '%Y-%m-%d')
                date_str = date_obj.strftime('%d/%m/%Y')

                # Ajouter les données de l'achat
                self.items_table.setItem(row_index, 0, QTableWidgetItem(achat['numero_facture'] or ""))
                self.items_table.setItem(row_index, 1, QTableWidgetItem(date_str))
                self.items_table.setItem(row_index, 2, QTableWidgetItem(achat['fournisseur_nom'] or ""))
                self.items_table.setItem(row_index, 3, QTableWidgetItem(f"{achat['total_ht']:.2f} DH"))
                self.items_table.setItem(row_index, 4, QTableWidgetItem(f"{achat['total_tva']:.2f} DH"))
                self.items_table.setItem(row_index, 5, QTableWidgetItem(f"{achat['total_ttc']:.2f} DH"))

                # Ajouter les boutons d'action
                actions_widget = self.create_action_buttons(row_index, achat['id'])
                self.items_table.setCellWidget(row_index, 6, actions_widget)

                # Stocker l'ID de l'achat dans les données de la première colonne
                self.items_table.item(row_index, 0).setData(Qt.UserRole, achat['id'])

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des achats: {str(e)}")

    def add_item(self):
        """Émet le signal pour créer un nouvel achat"""
        self.item_added.emit()
        self.accept()

    def edit_item(self, achat_id):
        """Émet le signal pour modifier un achat existant"""
        self.item_edited.emit(achat_id)
        self.accept()

    def delete_item(self, achat_id):
        """Supprime un achat"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT numero_facture FROM achats_produits WHERE id = ?", (achat_id,))
            achat = cursor.fetchone()

            if not achat:
                QMessageBox.warning(self, "Erreur", "Achat introuvable.")
                return

            reply = QMessageBox.question(
                self, "Confirmation",
                f"Êtes-vous sûr de vouloir supprimer l'achat {achat['numero_facture']} ?",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Supprimer d'abord les lignes d'achat
                cursor.execute("DELETE FROM lignes_achat WHERE achat_id = ?", (achat_id,))

                # Puis supprimer l'achat
                cursor.execute("DELETE FROM achats_produits WHERE id = ?", (achat_id,))

                self.db_manager.conn.commit()
                self.load_items()
                self.item_deleted.emit(achat_id)
                QMessageBox.information(self, "Succès", "Achat supprimé avec succès.")

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression de l'achat: {str(e)}")

    def on_item_double_clicked(self, item):
        """Gère le double-clic sur un élément du tableau"""
        if item is None:
            return

        # Récupérer l'ID de l'achat depuis la première colonne
        row = item.row()
        first_column_item = self.items_table.item(row, 0)

        if first_column_item:
            # Récupérer l'ID stocké dans les données utilisateur
            achat_id = first_column_item.data(Qt.UserRole)
            if achat_id:
                # Appeler la méthode d'édition
                self.edit_item(achat_id)
