from PySide6.QtWidgets import QTableWidgetItem, QHeaderView, QMessageBox
from PySide6.QtCore import Qt
import sqlite3

# Importer le module de base
from ..components.base_module import BaseModule
from ..icons.icons import PRODUCTS_ICON

# Importer le dialogue produit
from ..components.produit_dialog import ProduitDialog

class ProduitsModule(BaseModule):
    """Module de gestion des produits avec interface simplifiée"""
    
    def __init__(self, db_manager, signals=None):
        super().__init__(
            db_manager=db_manager,
            signals=signals,
            title="Gestion des Produits",
            description="Ajoutez, modifiez et supprimez des produits",
            icon=PRODUCTS_ICON
        )
        
        # Configurer le bouton d'ajout
        self.add_button.setText("Ajouter un produit")
        
        # Configurer le tableau
        self.setup_table()
        
        # Charger les données
        self.load_produits()
    
    def setup_table(self):
        """Configure le tableau des produits"""
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "Code", "Désignation", "Unité", "Prix d'achat", "Prix de vente", "Stock", "Actions"
        ])
        
        # Configuration des colonnes
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Code
        self.items_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)  # Désignation
        self.items_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Unité
        self.items_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Prix d'achat
        self.items_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Prix de vente
        self.items_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Stock
        self.items_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Actions
        
        # Configurer la recherche
        self.search_input.setPlaceholderText("Rechercher un produit...")
    
    def load_produits(self):
        """Charge les produits depuis la base de données"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT * FROM produits ORDER BY code")
        produits = cursor.fetchall()
        
        self.items_table.setRowCount(0)
        
        for row_num, produit in enumerate(produits):
            self.items_table.insertRow(row_num)
            
            # Ajouter les données du produit
            self.items_table.setItem(row_num, 0, QTableWidgetItem(produit['code'] or ""))
            self.items_table.setItem(row_num, 1, QTableWidgetItem(produit['designation']))
            self.items_table.setItem(row_num, 2, QTableWidgetItem(produit['unite'] or ""))
            self.items_table.setItem(row_num, 3, QTableWidgetItem(f"{produit['prix_achat']:.2f} DH" if produit['prix_achat'] else ""))
            self.items_table.setItem(row_num, 4, QTableWidgetItem(f"{produit['prix_vente']:.2f} DH" if produit['prix_vente'] else ""))
            self.items_table.setItem(row_num, 5, QTableWidgetItem(str(produit['stock'] or 0)))
            
            # Créer les boutons d'action
            actions_widget = self.create_action_buttons(row_num, produit['id'], produit['designation'])
            self.items_table.setCellWidget(row_num, 6, actions_widget)
            
            # Stocker l'ID du produit dans la première colonne (invisible)
            self.items_table.item(row_num, 0).setData(Qt.UserRole, produit['id'])
    
    def show_add_dialog(self):
        """Affiche la boîte de dialogue pour ajouter un produit"""
        dialog = ProduitDialog(self.db_manager, parent=self)
        dialog.produit_saved.connect(self.on_produit_saved)
        dialog.exec()
    
    def show_edit_dialog(self, produit_id):
        """Affiche la boîte de dialogue pour modifier un produit"""
        dialog = ProduitDialog(self.db_manager, produit_id=produit_id, parent=self)
        dialog.produit_saved.connect(self.on_produit_saved)
        dialog.exec()
    
    def on_produit_saved(self):
        """Appelé lorsqu'un produit est ajouté ou modifié"""
        self.load_produits()
        
        # Émettre le signal pour informer les autres modules
        if self.signals:
            self.signals.produits_changed.emit()
    
    def delete_item(self, produit_id, produit_name):
        """Supprime un produit"""
        confirm = QMessageBox.question(
            self, "Confirmation",
            f"Êtes-vous sûr de vouloir supprimer le produit {produit_name} ?\nCette action est irréversible.",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if confirm == QMessageBox.Yes:
            try:
                cursor = self.db_manager.conn.cursor()
                cursor.execute("DELETE FROM produits WHERE id = ?", (produit_id,))
                self.db_manager.conn.commit()
                
                self.load_produits()
                
                # Émettre le signal pour informer les autres modules
                if self.signals:
                    self.signals.produits_changed.emit()
                
                QMessageBox.information(self, "Succès", f"Produit {produit_name} supprimé avec succès.")
            except sqlite3.Error as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression du produit: {str(e)}")
