#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pour vérifier que le remplissage automatique de l'unité fonctionne dans Bons de Commande
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager

def test_bon_commande_auto_fill():
    """Test le remplissage automatique dans Bons de Commande"""
    
    print("🧪 Test du remplissage automatique - Bons de Commande")
    print("=" * 60)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    cursor = db_manager.conn.cursor()
    
    # 1. Vérifier les produits disponibles
    print("\n📦 Produits disponibles pour test:")
    cursor.execute("""
        SELECT id, designation, unite, prix_vente, stock
        FROM produits
        ORDER BY designation
    """)
    produits = cursor.fetchall()
    
    if not produits:
        print("❌ Aucun produit trouvé! Ajout de produits de test...")
        
        # Ajouter des produits de test
        test_products = [
            ('CAMERA', 'U', 1800.0, 2000.0, 10),
            ('DISJONCTEUR', 'U', 20000.0, 25000.0, 20),
            ('CABLE', 'ML', 50.0, 60.0, 100),
            ('LAMPE LED', 'U', 45.0, 55.0, 150)
        ]
        
        for designation, unite, prix_achat, prix_vente, stock in test_products:
            cursor.execute("""
                INSERT INTO produits (designation, unite, prix_achat, prix_vente, stock)
                VALUES (?, ?, ?, ?, ?)
            """, (designation, unite, prix_achat, prix_vente, stock))
        
        db_manager.conn.commit()
        print("✅ Produits de test ajoutés")
        
        # Recharger les produits
        cursor.execute("""
            SELECT id, designation, unite, prix_vente, stock
            FROM produits
            ORDER BY designation
        """)
        produits = cursor.fetchall()
    
    for produit in produits:
        print(f"- ID: {produit[0]}, Nom: {produit[1]}, Unité: '{produit[2]}', Prix: {produit[3]:.2f} DH, Stock: {produit[4]}")
    
    # 2. Simuler la sélection d'un produit (CAMERA)
    print("\n🔄 Simulation de sélection de produit: CAMERA")
    
    camera_produit = None
    for produit in produits:
        if produit[1] == 'CAMERA':
            camera_produit = produit
            break
    
    if camera_produit:
        print(f"✅ Produit CAMERA trouvé:")
        print(f"   ID: {camera_produit[0]}")
        print(f"   Désignation: {camera_produit[1]}")
        print(f"   Unité: '{camera_produit[2]}'")
        print(f"   Prix de vente: {camera_produit[3]:.2f} DH")
        print(f"   Stock: {camera_produit[4]}")
        
        # 3. Vérifier que l'unité n'est pas vide
        if camera_produit[2]:
            print(f"✅ L'unité '{camera_produit[2]}' sera remplie automatiquement")
        else:
            print("⚠️ L'unité est vide dans la base de données")
            
            # Mettre à jour l'unité pour CAMERA
            cursor.execute("UPDATE produits SET unite = ? WHERE id = ?", ('U', camera_produit[0]))
            db_manager.conn.commit()
            print("✅ Unité mise à jour pour CAMERA: 'U'")
    else:
        print("❌ Produit CAMERA non trouvé")
    
    # 4. Test de la requête utilisée dans le module
    print("\n🔍 Test de la requête du module Bon de Commande:")
    cursor.execute("""
        SELECT p.id, p.designation, p.unite, p.prix_vente, p.stock, p.prix_achat,
               f.numero as numero_facture, fo.nom as nom_fournisseur
        FROM produits p
        LEFT JOIN factures_achat f ON p.facture_achat_id = f.id
        LEFT JOIN fournisseurs fo ON f.fournisseur_id = fo.id
        WHERE p.designation = 'CAMERA'
    """)
    
    camera_data = cursor.fetchone()
    if camera_data:
        print("✅ Données récupérées par la requête du module:")
        print(f"   ID: {camera_data[0]}")
        print(f"   Désignation: {camera_data[1]}")
        print(f"   Unité: '{camera_data[2]}'")
        print(f"   Prix vente: {camera_data[3]:.2f} DH")
        print(f"   Stock: {camera_data[4]}")
        print(f"   Prix achat: {camera_data[5]:.2f} DH")
        print(f"   Numéro facture: {camera_data[6] or 'N/A'}")
        print(f"   Fournisseur: {camera_data[7] or 'N/A'}")
        
        # Simuler les données comme dans le module
        produit_dict = {
            'id': camera_data[0],
            'designation': camera_data[1],
            'unite': camera_data[2],
            'prix_vente': camera_data[3],
            'stock': camera_data[4],
            'prix_achat': camera_data[5],
            'numero_facture': camera_data[6],
            'nom_fournisseur': camera_data[7]
        }
        
        print("\n🎯 Simulation du remplissage automatique:")
        print(f"   Unité à remplir: '{produit_dict.get('unite', '')}' ✅")
        print(f"   Prix à remplir: {produit_dict.get('prix_vente', 0):.2f} DH ✅")
        print(f"   Stock disponible: {produit_dict.get('stock', 0)} ✅")
        
        if produit_dict.get('unite'):
            print("✅ SUCCESS! L'unité sera remplie automatiquement")
        else:
            print("❌ ÉCHEC! L'unité est vide")
    else:
        print("❌ Aucune donnée récupérée par la requête")
    
    # 5. Vérifier tous les produits
    print("\n📋 Vérification de tous les produits:")
    cursor.execute("""
        SELECT p.id, p.designation, p.unite, p.prix_vente, p.stock
        FROM produits p
        ORDER BY p.designation
    """)
    
    all_products = cursor.fetchall()
    for product in all_products:
        unite_status = "✅" if product[2] else "❌ VIDE"
        prix_status = "✅" if product[3] and product[3] > 0 else "❌ PRIX MANQUANT"
        print(f"- {product[1]}: Unité='{product[2]}' {unite_status}, Prix={product[3]:.2f} DH {prix_status}")
    
    print("\n🎉 Test terminé!")
    print("\n📝 Instructions pour tester dans l'interface:")
    print("1. Ouvrez l'application")
    print("2. Allez dans 'Bons de Commande'")
    print("3. Cliquez 'Ajouter un bon'")
    print("4. Dans le tableau Articles, cliquez sur la liste déroulante 'Désignation'")
    print("5. Sélectionnez 'CAMERA - 1800.00 DH (Stock: 10)'")
    print("6. L'unité 'U' devrait apparaître automatiquement dans la colonne Unité")
    print("7. Le prix '1800.00 DH' devrait apparaître dans Prix unitaire HT")

if __name__ == "__main__":
    test_bon_commande_auto_fill()
