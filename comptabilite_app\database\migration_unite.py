import sqlite3
import os

def migrate_unite_column():
    """
    Script de migration pour ajouter la colonne 'unite' à la table lignes_facture
    si elle n'existe pas déjà.
    """
    try:
        # Chemin de la base de données
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'comptabilite.db')
        
        # Connexion à la base de données
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Vérifier si la table lignes_facture existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='lignes_facture'")
        if cursor.fetchone():
            # Vérifier si la colonne unite existe
            cursor.execute("PRAGMA table_info(lignes_facture)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]  # Le nom est à l'index 1 dans le résultat de PRAGMA
            
            # Si la colonne unite n'existe pas, l'ajouter
            if 'unite' not in column_names:
                cursor.execute("ALTER TABLE lignes_facture ADD COLUMN unite TEXT")
                print("Migration: Colonne 'unite' ajoutée à la table lignes_facture")
                conn.commit()
            else:
                print("Migration: La colonne 'unite' existe déjà dans la table lignes_facture")
        else:
            print("Migration: La table lignes_facture n'existe pas encore")
        
        conn.close()
        print("Migration terminée avec succès.")
        return True
        
    except sqlite3.Error as e:
        print(f"Erreur lors de la migration: {str(e)}")
        if conn:
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    migrate_unite_column()
