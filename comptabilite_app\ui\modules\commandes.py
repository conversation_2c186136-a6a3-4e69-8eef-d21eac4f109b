from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QComboBox, QDateEdit, QDoubleSpinBox, QSpinBox,
                              QFormLayout, QMessageBox, QHeaderView, QDialog)
from PySide6.QtCore import Qt, QDate
from PySide6.QtPrintSupport import QPrinter, QPrintDialog
from PySide6.QtGui import QTextDocument
import datetime

class AjouterLigneDialog(QDialog):
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setWindowTitle("Ajouter une ligne")
        self.setMinimumWidth(400)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        form_layout = QFormLayout()

        # Sélection du produit
        self.produit_combo = QComboBox()
        self.charger_produits()
        self.produit_combo.currentIndexChanged.connect(self.produit_selectionne)
        form_layout.addRow("Produit:", self.produit_combo)

        # Désignation
        self.designation_input = QLineEdit()
        form_layout.addRow("Désignation:", self.designation_input)

        # Quantité
        self.quantite_input = QSpinBox()
        self.quantite_input.setRange(1, 1000)
        self.quantite_input.setValue(1)
        self.quantite_input.valueChanged.connect(self.calculer_total)
        form_layout.addRow("Quantité:", self.quantite_input)

        # Prix unitaire
        self.prix_input = QDoubleSpinBox()
        self.prix_input.setRange(0, 1000000)
        self.prix_input.setDecimals(2)
        self.prix_input.setSuffix(" €")
        self.prix_input.valueChanged.connect(self.calculer_total)
        form_layout.addRow("Prix unitaire HT:", self.prix_input)

        # TVA
        self.tva_input = QDoubleSpinBox()
        self.tva_input.setRange(0, 100)
        self.tva_input.setDecimals(1)
        self.tva_input.setValue(20.0)
        self.tva_input.setSuffix(" %")
        form_layout.addRow("TVA:", self.tva_input)

        # Total HT
        self.total_ht_label = QLabel("0.00 €")
        form_layout.addRow("Total HT:", self.total_ht_label)

        layout.addLayout(form_layout)

        # Boutons
        buttons_layout = QHBoxLayout()

        self.ajouter_btn = QPushButton("Ajouter")
        self.ajouter_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(self.ajouter_btn)

        self.annuler_btn = QPushButton("Annuler")
        self.annuler_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.annuler_btn)

        layout.addLayout(buttons_layout)

    def charger_produits(self):
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT id, code, designation, prix_achat, stock FROM produits ORDER BY code")
        produits = cursor.fetchall()

        self.produit_combo.clear()
        self.produit_combo.addItem("-- Sélectionner un produit --", None)

        for produit in produits:
            self.produit_combo.addItem(
                f"{produit['code']} - {produit['designation']}",
                produit['id']
            )

    def produit_selectionne(self):
        produit_id = self.produit_combo.currentData()

        if produit_id:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT * FROM produits WHERE id = ?", (produit_id,))
            produit = cursor.fetchone()

            if produit:
                self.designation_input.setText(produit['designation'])
                self.prix_input.setValue(produit['prix_achat'] or 0)
                self.tva_input.setValue(20.0)  # Valeur par défaut
                self.calculer_total()

    def calculer_total(self):
        quantite = self.quantite_input.value()
        prix = self.prix_input.value()
        total = quantite * prix
        self.total_ht_label.setText(f"{total:.2f} €")

class CommandesModule(QWidget):
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.setup_ui()
        self.nouvelle_commande()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # Titre du module
        title = QLabel("Gestion des Bons de Commande")
        title.setStyleSheet("font-size: 20px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title)

        # Formulaire de création de commande
        form_container = QWidget()
        form_layout = QFormLayout(form_container)

        # Informations générales de la commande
        self.numero_commande = QLineEdit()
        self.numero_commande.setReadOnly(True)
        form_layout.addRow("Numéro de commande:", self.numero_commande)

        from ..style import create_styled_date_edit
        self.date_commande = create_styled_date_edit()
        form_layout.addRow("Date:", self.date_commande)

        self.date_livraison = create_styled_date_edit(
            current_date=QDate.currentDate().addDays(7)
        )
        form_layout.addRow("Date de livraison prévue:", self.date_livraison)

        self.fournisseur_combo = QComboBox()
        self.charger_fournisseurs()
        form_layout.addRow("Fournisseur:", self.fournisseur_combo)

        # Tableau des lignes de commande
        self.lignes_table = QTableWidget(0, 6)
        self.lignes_table.setHorizontalHeaderLabels(
            ["Produit", "Désignation", "Quantité", "Prix unitaire HT", "TVA %", "Total HT"]
        )
        self.lignes_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # Boutons pour gérer les lignes
        lignes_buttons = QHBoxLayout()

        self.ajouter_ligne_btn = QPushButton("Ajouter une ligne")
        self.ajouter_ligne_btn.clicked.connect(self.ajouter_ligne)
        lignes_buttons.addWidget(self.ajouter_ligne_btn)

        self.supprimer_ligne_btn = QPushButton("Supprimer la ligne")
        self.supprimer_ligne_btn.clicked.connect(self.supprimer_ligne)
        lignes_buttons.addWidget(self.supprimer_ligne_btn)

        # Totaux
        totaux_layout = QFormLayout()

        self.total_ht_label = QLabel("0.00 €")
        totaux_layout.addRow("Total HT:", self.total_ht_label)

        self.total_tva_label = QLabel("0.00 €")
        totaux_layout.addRow("Total TVA:", self.total_tva_label)

        self.total_ttc_label = QLabel("0.00 €")
        self.total_ttc_label.setStyleSheet("font-weight: bold;")
        totaux_layout.addRow("Total TTC:", self.total_ttc_label)

        # Boutons d'action
        actions_layout = QHBoxLayout()

        self.enregistrer_btn = QPushButton("Enregistrer")
        self.enregistrer_btn.clicked.connect(self.enregistrer_commande)
        actions_layout.addWidget(self.enregistrer_btn)

        self.exporter_pdf_btn = QPushButton("Exporter en PDF")
        self.exporter_pdf_btn.clicked.connect(self.exporter_pdf)
        actions_layout.addWidget(self.exporter_pdf_btn)

        self.annuler_btn = QPushButton("Nouvelle commande")
        self.annuler_btn.clicked.connect(self.nouvelle_commande)
        actions_layout.addWidget(self.annuler_btn)

        # Assemblage du layout
        layout.addWidget(form_container)
        layout.addWidget(self.lignes_table)
        layout.addLayout(lignes_buttons)
        layout.addLayout(totaux_layout)
        layout.addLayout(actions_layout)

    def charger_fournisseurs(self):
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT id, nom FROM fournisseurs ORDER BY nom")
        fournisseurs = cursor.fetchall()

        self.fournisseur_combo.clear()
        for fournisseur in fournisseurs:
            self.fournisseur_combo.addItem(fournisseur['nom'], fournisseur['id'])

    def ajouter_ligne(self):
        dialog = AjouterLigneDialog(self.db_manager, self)
        if dialog.exec():
            produit_id = dialog.produit_combo.currentData()
            designation = dialog.designation_input.text()
            quantite = dialog.quantite_input.value()
            prix_unitaire = dialog.prix_input.value()
            taux_tva = dialog.tva_input.value()
            total_ht = quantite * prix_unitaire

            row = self.lignes_table.rowCount()
            self.lignes_table.insertRow(row)

            self.lignes_table.setItem(row, 0, QTableWidgetItem(dialog.produit_combo.currentText()))
            self.lignes_table.setItem(row, 1, QTableWidgetItem(designation))
            self.lignes_table.setItem(row, 2, QTableWidgetItem(str(quantite)))
            self.lignes_table.setItem(row, 3, QTableWidgetItem(f"{prix_unitaire:.2f} €"))
            self.lignes_table.setItem(row, 4, QTableWidgetItem(f"{taux_tva:.1f} %"))
            self.lignes_table.setItem(row, 5, QTableWidgetItem(f"{total_ht:.2f} €"))

            # Stocker les données pour le calcul
            self.lignes_table.item(row, 0).setData(Qt.UserRole, produit_id)
            self.lignes_table.item(row, 2).setData(Qt.UserRole, quantite)
            self.lignes_table.item(row, 3).setData(Qt.UserRole, prix_unitaire)
            self.lignes_table.item(row, 4).setData(Qt.UserRole, taux_tva)
            self.lignes_table.item(row, 5).setData(Qt.UserRole, total_ht)

            self.calculer_totaux()

    def supprimer_ligne(self):
        selected_row = self.lignes_table.currentRow()
        if selected_row >= 0:
            self.lignes_table.removeRow(selected_row)
            self.calculer_totaux()

    def calculer_totaux(self):
        total_ht = 0
        total_tva = 0

        for row in range(self.lignes_table.rowCount()):
            quantite = self.lignes_table.item(row, 2).data(Qt.UserRole)
            prix_unitaire = self.lignes_table.item(row, 3).data(Qt.UserRole)
            taux_tva = self.lignes_table.item(row, 4).data(Qt.UserRole)

            ligne_ht = quantite * prix_unitaire
            ligne_tva = ligne_ht * (taux_tva / 100)

            total_ht += ligne_ht
            total_tva += ligne_tva

        total_ttc = total_ht + total_tva

        self.total_ht_label.setText(f"{total_ht:.2f} €")
        self.total_tva_label.setText(f"{total_tva:.2f} €")
        self.total_ttc_label.setText(f"{total_ttc:.2f} €")

    def enregistrer_commande(self):
        # Vérifier qu'il y a au moins une ligne
        if self.lignes_table.rowCount() == 0:
            QMessageBox.warning(self, "Erreur", "Ajoutez au moins une ligne à la commande.")
            return

        # Vérifier qu'un fournisseur est sélectionné
        if self.fournisseur_combo.count() == 0:
            QMessageBox.warning(self, "Erreur", "Aucun fournisseur disponible. Veuillez en créer un d'abord.")
            return

        # Récupérer les données de la commande
        numero = self.numero_commande.text()
        date_creation = self.date_commande.date().toString("yyyy-MM-dd")
        date_livraison = self.date_livraison.date().toString("yyyy-MM-dd")
        fournisseur_id = self.fournisseur_combo.currentData()

        # Récupérer les totaux
        total_ht = float(self.total_ht_label.text().replace(" €", ""))
        total_tva = float(self.total_tva_label.text().replace(" €", ""))
        total_ttc = float(self.total_ttc_label.text().replace(" €", ""))

        # Enregistrer la commande
        cursor = self.db_manager.conn.cursor()
        cursor.execute(
            """INSERT INTO commandes
               (numero, date_creation, date_livraison, fournisseur_id,
                total_ht, total_tva, total_ttc, statut)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
            (numero, date_creation, date_livraison, fournisseur_id,
             total_ht, total_tva, total_ttc, "En attente")
        )

        commande_id = cursor.lastrowid

        # Enregistrer les lignes de commande
        for row in range(self.lignes_table.rowCount()):
            produit_id = self.lignes_table.item(row, 0).data(Qt.UserRole)
            designation = self.lignes_table.item(row, 1).text()
            quantite = self.lignes_table.item(row, 2).data(Qt.UserRole)
            prix_unitaire = self.lignes_table.item(row, 3).data(Qt.UserRole)
            taux_tva = self.lignes_table.item(row, 4).data(Qt.UserRole)
            total_ligne = self.lignes_table.item(row, 5).data(Qt.UserRole)

            cursor.execute(
                """INSERT INTO lignes_commande
                   (commande_id, produit_id, designation, quantite,
                    prix_unitaire_ht, taux_tva, total_ht)
                   VALUES (?, ?, ?, ?, ?, ?, ?)""",
                (commande_id, produit_id, designation, quantite,
                 prix_unitaire, taux_tva, total_ligne)
            )

        self.db_manager.conn.commit()
        QMessageBox.information(self, "Succès", "Commande enregistrée avec succès.")
        self.nouvelle_commande()

    def exporter_pdf(self):
        # Implémenter la génération de PDF
        pass

    def nouvelle_commande(self):
        # Réinitialiser le formulaire
        self.numero_commande.setText(self.db_manager.generer_numero_commande())
        self.date_commande.setDate(QDate.currentDate())
        self.date_livraison.setDate(QDate.currentDate().addDays(7))

        if self.fournisseur_combo.count() > 0:
            self.fournisseur_combo.setCurrentIndex(0)

        self.lignes_table.setRowCount(0)
        self.calculer_totaux()
