from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QHeaderView, QFrame, QMessageBox, QScrollArea)
from PySide6.QtCore import Qt, QSize, Signal
from PySide6.QtGui import QIcon
import sqlite3

# Importer les styles modernes
from ..theme import COLORS, BORDER_RADIUS, SPACING
from ..icons.icons import *

class BaseDocumentPage(QWidget):
    """
    Composant de base pour toutes les pages de création/édition de documents
    Fournit une interface standardisée avec:
    - Un en-tête avec titre et boutons d'action
    - Une zone de formulaire pour les informations du document
    - Un tableau pour les lignes du document
    """
    
    # Signal émis lorsque le document est enregistré
    document_saved = Signal()
    
    def __init__(self, db_manager, title="", description="", icon=None, is_edit_mode=False):
        super().__init__()
        self.db_manager = db_manager
        self.title = title
        self.description = description
        self.icon = icon
        self.is_edit_mode = is_edit_mode
        self.setup_ui()
    
    def setup_ui(self):
        """Configure l'interface utilisateur de base"""
        layout = QVBoxLayout(self)
        layout.setSpacing(int(SPACING['md'].replace('px', '')))
        layout.setContentsMargins(int(SPACING['md'].replace('px', '')),
                                 int(SPACING['md'].replace('px', '')),
                                 int(SPACING['md'].replace('px', '')),
                                 int(SPACING['md'].replace('px', '')))
        
        # En-tête avec titre et boutons d'action
        header_container = QFrame()
        header_container.setObjectName("document_header")
        header_container.setStyleSheet(f"""
            #document_header {{
                background-color: white;
                border-radius: {BORDER_RADIUS['md']};
                border: 1px solid {COLORS['divider']};
                padding: 15px;
            }}
        """)
        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(15, 15, 15, 15)
        
        # Icône et titre
        if self.icon:
            icon_label = QLabel()
            icon_label.setText(svg_to_icon_html(self.icon, "#1A56DB", 24))
            icon_label.setFixedSize(24, 24)
            header_layout.addWidget(icon_label)
        
        title_container = QWidget()
        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(2)
        
        title_label = QLabel(self.title)
        title_label.setObjectName("document_title")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #1A56DB;
        """)
        
        description_label = QLabel(self.description)
        description_label.setObjectName("document_description")
        description_label.setStyleSheet("""
            font-size: 13px;
            color: #4B5563;
        """)
        
        title_layout.addWidget(title_label)
        title_layout.addWidget(description_label)
        
        header_layout.addWidget(title_container)
        header_layout.addStretch()
        
        # Boutons d'action
        buttons_container = QWidget()
        buttons_layout = QHBoxLayout(buttons_container)
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        buttons_layout.setSpacing(10)
        
        # Bouton Annuler
        self.cancel_button = QPushButton("Annuler")
        self.cancel_button.setObjectName("cancel_button")
        self.cancel_button.setIcon(QIcon(":/icons/cancel.png"))
        self.cancel_button.setIconSize(QSize(16, 16))
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #F3F4F6;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
        """)
        self.cancel_button.clicked.connect(self.on_cancel_clicked)
        
        # Bouton Enregistrer
        self.save_button = QPushButton("Enregistrer")
        self.save_button.setObjectName("save_button")
        self.save_button.setIcon(QIcon(":/icons/save.png"))
        self.save_button.setIconSize(QSize(16, 16))
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #1A56DB;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1E40AF;
            }
        """)
        self.save_button.clicked.connect(self.on_save_clicked)
        
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.save_button)
        
        header_layout.addWidget(buttons_container)
        
        layout.addWidget(header_container)
        
        # Zone de défilement pour le contenu
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # Widget conteneur pour le contenu
        content_container = QWidget()
        content_layout = QVBoxLayout(content_container)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(int(SPACING['md'].replace('px', '')))
        
        # Section des informations du document
        info_container = QFrame()
        info_container.setObjectName("document_info")
        info_container.setStyleSheet(f"""
            #document_info {{
                background-color: white;
                border-radius: {BORDER_RADIUS['md']};
                border: 1px solid {COLORS['divider']};
                padding: 15px;
            }}
        """)
        
        # Layout pour les informations du document (à remplir dans les classes dérivées)
        self.info_layout = QVBoxLayout(info_container)
        self.info_layout.setContentsMargins(15, 15, 15, 15)
        
        content_layout.addWidget(info_container)
        
        # Section des lignes du document
        lines_container = QFrame()
        lines_container.setObjectName("document_lines")
        lines_container.setStyleSheet(f"""
            #document_lines {{
                background-color: white;
                border-radius: {BORDER_RADIUS['md']};
                border: 1px solid {COLORS['divider']};
                padding: 15px;
            }}
        """)
        lines_layout = QVBoxLayout(lines_container)
        lines_layout.setContentsMargins(15, 15, 15, 15)
        
        # Titre de la section
        lines_title = QLabel("Lignes du document")
        lines_title.setObjectName("lines_title")
        lines_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #1A56DB;
            margin-bottom: 10px;
        """)
        lines_layout.addWidget(lines_title)
        
        # Tableau des lignes
        self.lines_table = QTableWidget()
        self.lines_table.setStyleSheet("""
            QTableWidget {
                border: none;
                gridline-color: #E5E7EB;
            }
            QHeaderView::section {
                background-color: #F9FAFB;
                color: #374151;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #E5E7EB;
                font-weight: bold;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E5E7EB;
            }
        """)
        self.lines_table.setAlternatingRowColors(True)
        self.lines_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.lines_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.lines_table.verticalHeader().setVisible(False)  # Cacher les numéros de ligne
        
        lines_layout.addWidget(self.lines_table)
        
        # Boutons pour gérer les lignes
        lines_buttons_container = QWidget()
        lines_buttons_layout = QHBoxLayout(lines_buttons_container)
        lines_buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        # Bouton Ajouter une ligne
        self.add_line_button = QPushButton("Ajouter une ligne")
        self.add_line_button.setObjectName("add_line_button")
        self.add_line_button.setIcon(QIcon(":/icons/add.png"))
        self.add_line_button.setIconSize(QSize(16, 16))
        self.add_line_button.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        self.add_line_button.clicked.connect(self.on_add_line_clicked)
        
        lines_buttons_layout.addWidget(self.add_line_button)
        lines_buttons_layout.addStretch()
        
        lines_layout.addWidget(lines_buttons_container)
        
        content_layout.addWidget(lines_container)
        
        # Section des totaux
        totals_container = QFrame()
        totals_container.setObjectName("document_totals")
        totals_container.setStyleSheet(f"""
            #document_totals {{
                background-color: white;
                border-radius: {BORDER_RADIUS['md']};
                border: 1px solid {COLORS['divider']};
                padding: 15px;
            }}
        """)
        
        # Layout pour les totaux (à remplir dans les classes dérivées)
        self.totals_layout = QVBoxLayout(totals_container)
        self.totals_layout.setContentsMargins(15, 15, 15, 15)
        
        content_layout.addWidget(totals_container)
        
        # Ajouter le conteneur de contenu à la zone de défilement
        scroll_area.setWidget(content_container)
        layout.addWidget(scroll_area)
    
    def on_cancel_clicked(self):
        """Méthode appelée lorsque le bouton Annuler est cliqué"""
        pass
    
    def on_save_clicked(self):
        """Méthode appelée lorsque le bouton Enregistrer est cliqué"""
        pass
    
    def on_add_line_clicked(self):
        """Méthode appelée lorsque le bouton Ajouter une ligne est cliqué"""
        pass
    
    def create_action_buttons(self, row_id, line_id=None):
        """Crée les boutons d'action pour une ligne du tableau"""
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(2, 2, 2, 2)
        actions_layout.setSpacing(6)
        
        # Bouton d'édition
        edit_btn = QPushButton()
        edit_btn.setIcon(QIcon(":/icons/edit.png"))
        edit_btn.setToolTip("Modifier")
        edit_btn.setFixedSize(28, 28)
        edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
        """)
        edit_btn.clicked.connect(lambda: self.on_edit_line_clicked(row_id, line_id))
        
        # Bouton de suppression
        delete_btn = QPushButton()
        delete_btn.setIcon(QIcon(":/icons/delete.png"))
        delete_btn.setToolTip("Supprimer")
        delete_btn.setFixedSize(28, 28)
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #EF4444;
                color: white;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #DC2626;
            }
        """)
        delete_btn.clicked.connect(lambda: self.on_delete_line_clicked(row_id, line_id))
        
        actions_layout.addWidget(edit_btn)
        actions_layout.addWidget(delete_btn)
        
        return actions_widget
    
    def on_edit_line_clicked(self, row_id, line_id=None):
        """Méthode appelée lorsque le bouton d'édition d'une ligne est cliqué"""
        pass
    
    def on_delete_line_clicked(self, row_id, line_id=None):
        """Méthode appelée lorsque le bouton de suppression d'une ligne est cliqué"""
        pass
