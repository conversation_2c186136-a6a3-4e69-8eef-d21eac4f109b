"""
Module de gestion de la caisse - Version Ultra Simple
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QTableWidget, QTableWidgetItem, QPushButton,
                               QHeaderView, QFrame, QMessageBox, QLineEdit)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont
import sqlite3

class CaisseSimpleModule(QWidget):
    """Module de caisse ultra-simple - tout visible sur une page"""

    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        # ضبط اتجاه النص لدعم العربية والفرنسية
        self.setLayoutDirection(Qt.LeftToRight)

        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # أزرار في الأعلى
        buttons_layout = QHBoxLayout()

        caisse_btn = QPushButton("Caisse")
        caisse_btn.setStyleSheet("""
            QPushButton {
                background-color: #5B9BD5;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
        """)

        ajouter_btn = QPushButton("Ajouter")
        ajouter_btn.setStyleSheet("""
            QPushButton {
                background-color: #5B9BD5;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
        """)

        buttons_layout.addWidget(caisse_btn)
        buttons_layout.addWidget(ajouter_btn)
        buttons_layout.addStretch()

        main_layout.addLayout(buttons_layout)

        # جدول الإيرادات
        entrees_section = self.create_table_section("Entrées", True)
        main_layout.addWidget(entrees_section)

        # جدول المصروفات
        sorties_section = self.create_table_section("Sortie", False)
        main_layout.addWidget(sorties_section)

        # قسم الوضعية والرصيد
        bottom_layout = QHBoxLayout()

        # وضعية الصندوق
        situation_frame = self.create_situation_section()
        bottom_layout.addWidget(situation_frame)

        # الرصيد
        solde_frame = self.create_solde_section()
        bottom_layout.addWidget(solde_frame)

        main_layout.addLayout(bottom_layout)

        self.setLayout(main_layout)

    def create_table_section(self, title, is_entrees):
        """إنشاء قسم الجدول"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                border: 1px solid #000000;
                background-color: white;
                margin: 5px;
            }
        """)

        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # إنشاء الجدول
        table = QTableWidget()
        table.setRowCount(4)  # 3 صفوف للبيانات + 1 للمجموع
        table.setColumnCount(5)

        # تحديد العناوين
        headers = ["Date", "Nature (نوع / طبيعة)", "Objet", "Référence (facture / bon ...)", "Montant"]
        table.setHorizontalHeaderLabels(headers)

        # تنسيق الجدول
        table.setStyleSheet("""
            QTableWidget {
                border: none;
                gridline-color: #000000;
                font-family: Arial, Tahoma, sans-serif;
                font-size: 12px;
            }
            QTableWidget::item {
                border: 1px solid #000000;
                padding: 5px;
            }
            QHeaderView::section {
                background-color: white;
                border: 1px solid #000000;
                padding: 5px;
                font-weight: bold;
            }
        """)

        # إضافة صف المجموع
        table.setItem(3, 1, QTableWidgetItem("total"))

        # إضافة حقل الإدخال في آخر عمود
        if is_entrees:
            # حقل إدخال للإيرادات
            self.entrees_input = QLineEdit()
            self.entrees_input.setStyleSheet("""
                QLineEdit {
                    border: 2px solid red;
                    padding: 5px;
                    font-size: 12px;
                }
            """)
            table.setCellWidget(3, 4, self.entrees_input)
        else:
            # حقل إدخال للمصروفات
            self.sorties_input = QLineEdit()
            self.sorties_input.setStyleSheet("""
                QLineEdit {
                    border: 2px solid red;
                    padding: 5px;
                    font-size: 12px;
                }
            """)
            table.setCellWidget(3, 4, self.sorties_input)

        # إضافة النص التوضيحي
        if is_entrees:
            note_text = "عند علاقة بكل فقرة مع رقم المرجع (فاتورة / بون الإدخال فقط)"
        else:
            note_text = "عند علاقة بكل فقرة مع رقم المرجع (فاتورة / بون الإدخال فقط)"

        table.setItem(2, 3, QTableWidgetItem(note_text))

        # تحديد عرض الأعمدة
        table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        table.setMaximumHeight(150)

        layout.addWidget(table)
        frame.setLayout(layout)

        return frame

    def create_situation_section(self):
        """إنشاء قسم وضعية الصندوق"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                border: 1px solid #000000;
                background-color: white;
                margin: 5px;
            }
        """)

        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)

        # العنوان
        title = QLabel("Situation de caisse :")
        title.setStyleSheet("font-weight: bold; font-size: 12px;")
        layout.addWidget(title)

        # جدول الوضعية
        situation_table = QTableWidget()
        situation_table.setRowCount(3)
        situation_table.setColumnCount(2)
        situation_table.setHorizontalHeaderLabels(["", "montant"])

        # البيانات
        situation_table.setItem(0, 0, QTableWidgetItem("Entrées"))
        situation_table.setItem(0, 1, QTableWidgetItem("E"))
        situation_table.setItem(1, 0, QTableWidgetItem("Sortie"))
        situation_table.setItem(1, 1, QTableWidgetItem("S"))
        situation_table.setItem(2, 0, QTableWidgetItem("Solde"))
        situation_table.setItem(2, 1, QTableWidgetItem("= E-S"))

        situation_table.setStyleSheet("""
            QTableWidget {
                border: none;
                gridline-color: #000000;
                font-size: 12px;
            }
            QTableWidget::item {
                border: 1px solid #000000;
                padding: 5px;
            }
            QHeaderView::section {
                background-color: white;
                border: 1px solid #000000;
                padding: 5px;
                font-weight: bold;
            }
        """)

        situation_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        situation_table.setMaximumHeight(120)
        situation_table.setMaximumWidth(200)

        layout.addWidget(situation_table)
        frame.setLayout(layout)

        return frame

    def create_solde_section(self):
        """إنشاء قسم الرصيد"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #5B9BD5;
                border: 1px solid #000000;
                margin: 5px;
            }
        """)

        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)

        label = QLabel("Le solde reste dans la caisse :")
        label.setStyleSheet("""
            color: white;
            font-weight: bold;
            font-size: 14px;
            text-align: center;
        """)
        label.setAlignment(Qt.AlignCenter)

        layout.addWidget(label)
        frame.setLayout(layout)
        frame.setMaximumWidth(300)
        frame.setMinimumHeight(80)

        return frame

    def load_data(self):
        """تحميل البيانات"""
        # تحديث قيم الوضعية
        pass
        """إنشاء جدول العمليات الأخيرة"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #E5E7EB;
                border-radius: 12px;
                padding: 20px;
                margin: 10px;
            }
        """)

        layout = QVBoxLayout()

        # العنوان
        title = QLabel("📊 آخر العمليات")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            color: #374151;
            margin-bottom: 15px;
            font-weight: bold;
            font-family: Arial, Tahoma, sans-serif;
            font-size: 18px;
        """)
        layout.addWidget(title)

        # الجدول
        self.operations_table = QTableWidget()
        self.operations_table.setColumnCount(4)
        self.operations_table.setHorizontalHeaderLabels(["التاريخ", "النوع", "الوصف", "المبلغ"])
        self.operations_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.operations_table.setAlternatingRowColors(True)
        self.operations_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.operations_table.setMaximumHeight(300)
        self.operations_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ccc;
                border-radius: 8px;
                background-color: white;
                gridline-color: #E5E7EB;
                font-family: Arial, Tahoma, sans-serif;
                font-size: 14px;
                color: #000000;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E5E7EB;
                color: #000000;
            }
            QTableWidget::item:selected {
                background-color: #EBF4FF;
                color: #000000;
            }
            QHeaderView::section {
                background-color: #F3F4F6;
                padding: 10px;
                border: none;
                font-weight: bold;
                color: #000000;
                font-family: Arial, Tahoma, sans-serif;
            }
        """)

        layout.addWidget(self.operations_table)
        frame.setLayout(layout)

        return frame

    def add_simple_entree(self, montant_input, description_input):
        """إضافة إيراد بسيط"""
        try:
            montant_str = montant_input.text().strip().replace(',', '.')
            description = description_input.text().strip()

            if not montant_str:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال المبلغ!")
                return

            montant = float(montant_str)
            if montant <= 0:
                QMessageBox.warning(self, "خطأ", "المبلغ يجب أن يكون أكبر من صفر!")
                return

            if not description:
                description = "إيراد"

            # إدخال في قاعدة البيانات
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                INSERT INTO entrees_caisse (date, nature, objet, reference, montant)
                VALUES (date('now'), 'إيراد', ?, '', ?)
            """, (description, montant))

            self.db_manager.conn.commit()

            # مسح الحقول
            montant_input.clear()
            description_input.clear()

            # تحديث البيانات
            self.load_data()

            QMessageBox.information(self, "نجح", f"تم إضافة إيراد {montant:.2f} درهم!")

        except ValueError:
            QMessageBox.warning(self, "خطأ", "المبلغ غير صحيح!")
        except sqlite3.Error as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في قاعدة البيانات: {str(e)}")

    def add_simple_sortie(self, montant_input, description_input):
        """إضافة مصروف بسيط"""
        try:
            montant_str = montant_input.text().strip().replace(',', '.')
            description = description_input.text().strip()

            if not montant_str:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال المبلغ!")
                return

            montant = float(montant_str)
            if montant <= 0:
                QMessageBox.warning(self, "خطأ", "المبلغ يجب أن يكون أكبر من صفر!")
                return

            if not description:
                description = "مصروف"

            # إدخال في قاعدة البيانات
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                INSERT INTO sorties_caisse (date, nature, objet, reference, montant)
                VALUES (date('now'), 'مصروف', ?, '', ?)
            """, (description, montant))

            self.db_manager.conn.commit()

            # مسح الحقول
            montant_input.clear()
            description_input.clear()

            # تحديث البيانات
            self.load_data()

            QMessageBox.information(self, "نجح", f"تم إضافة مصروف {montant:.2f} درهم!")

        except ValueError:
            QMessageBox.warning(self, "خطأ", "المبلغ غير صحيح!")
        except sqlite3.Error as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في قاعدة البيانات: {str(e)}")

    def load_data(self):
        """تحميل جميع البيانات"""
        self.load_operations()
        self.calculate_solde()

    def load_operations(self):
        """تحميل آخر العمليات"""
        try:
            cursor = self.db_manager.conn.cursor()

            # جمع الإيرادات والمصروفات في استعلام واحد
            cursor.execute("""
                SELECT date, nature, objet, montant, 'إيراد' as type, id
                FROM entrees_caisse
                UNION ALL
                SELECT date, nature, objet, -montant, 'مصروف' as type, id
                FROM sorties_caisse
                ORDER BY date DESC, id DESC
                LIMIT 20
            """)
            operations = cursor.fetchall()

            # إذا لم توجد عمليات، أضف رسالة
            if not operations:
                self.operations_table.setRowCount(1)
                self.operations_table.setItem(0, 0, QTableWidgetItem(""))
                self.operations_table.setItem(0, 1, QTableWidgetItem(""))
                self.operations_table.setItem(0, 2, QTableWidgetItem("لا توجد عمليات بعد"))
                self.operations_table.setItem(0, 3, QTableWidgetItem(""))
                return

            self.operations_table.setRowCount(len(operations))
            for row, operation in enumerate(operations):
                # التاريخ
                self.operations_table.setItem(row, 0, QTableWidgetItem(operation[0]))

                # النوع
                self.operations_table.setItem(row, 1, QTableWidgetItem(operation[4]))

                # الوصف
                self.operations_table.setItem(row, 2, QTableWidgetItem(operation[2] or ""))

                # المبلغ
                montant = abs(operation[3])
                self.operations_table.setItem(row, 3, QTableWidgetItem(f"{montant:.2f} DH"))

        except sqlite3.Error as e:
            print(f"خطأ في تحميل العمليات: {e}")
            # في حالة الخطأ، أظهر رسالة خطأ
            self.operations_table.setRowCount(1)
            self.operations_table.setItem(0, 0, QTableWidgetItem(""))
            self.operations_table.setItem(0, 1, QTableWidgetItem(""))
            self.operations_table.setItem(0, 2, QTableWidgetItem(f"خطأ: {str(e)}"))
            self.operations_table.setItem(0, 3, QTableWidgetItem(""))

    def calculate_solde(self):
        """حساب وعرض الرصيد"""
        try:
            cursor = self.db_manager.conn.cursor()

            # مجموع الإيرادات
            cursor.execute("SELECT COALESCE(SUM(montant), 0) FROM entrees_caisse")
            total_entrees = cursor.fetchone()[0]

            # مجموع المصروفات
            cursor.execute("SELECT COALESCE(SUM(montant), 0) FROM sorties_caisse")
            total_sorties = cursor.fetchone()[0]

            # حساب الرصيد
            solde = total_entrees - total_sorties

            # عرض الرصيد مع اللون المناسب
            if solde >= 0:
                color = "#059669"  # أخضر
                self.solde_label.setText(f"+{solde:.2f} DH")
            else:
                color = "#DC2626"  # أحمر
                self.solde_label.setText(f"{solde:.2f} DH")

            self.solde_label.setStyleSheet(f"color: {color}; font-size: 32px; font-weight: bold;")

        except sqlite3.Error as e:
            print(f"خطأ في حساب الرصيد: {e}")
            self.solde_label.setText("خطأ")
            self.solde_label.setStyleSheet("color: #DC2626; font-size: 32px; font-weight: bold;")
