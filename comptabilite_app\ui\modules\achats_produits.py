from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QFormLayout, QMessageBox, QHeaderView, QDoubleSpinBox,
                              QSpinBox, QFrame, QComboBox, QDateEdit, QGroupBox,
                              QScrollArea, QDialog, QSizePolicy)
from PySide6.QtCore import Qt, QDate, QSize
from PySide6.QtGui import QIcon
import sqlite3
from datetime import datetime

# Importer les styles modernes
from ..theme import COLORS, BORDER_RADIUS, SPACING

# Importer les icônes
from ..icons.icons import svg_to_icon_html, EDIT_ICON, DELETE_ICON, PRINT_ICON, LIST_ICON

# Importer la boîte de dialogue de liste des achats
from .achats_list_dialog import AchatsListDialog

# Importer les formulaires
from ..forms import AchatForm, ProduitForm

class AchatsProduitModule(QWidget):
    """Module de gestion des achats de produits"""

    def __init__(self, db_manager, signals=None):
        super().__init__()
        self.db_manager = db_manager
        self.signals = signals  # Signaux pour la communication entre modules

        # Définir les méthodes avant de les utiliser
        self.define_methods()

        self.setup_ui()
        self.initialiser_donnees()

        # Connecter le signal de mise à jour des fournisseurs
        if self.signals:
            self.signals.fournisseurs_changed.connect(self.charger_fournisseurs)

    def define_methods(self):
        """Définit les méthodes de la classe"""

        def show_achats_list(self):
            """Affiche la liste des achats dans une boîte de dialogue"""
            dialog = AchatsListDialog(self.db_manager, self)

            # Connecter les signaux
            dialog.item_selected.connect(self.charger_achat)
            dialog.item_added.connect(self.effacer_formulaire)
            dialog.item_edited.connect(self.charger_achat)

            dialog.exec()

        def charger_achat(self, achat_id):
            """Charge un achat existant"""
            try:
                cursor = self.db_manager.conn.cursor()

                # Récupérer les informations de l'achat
                cursor.execute("""
                    SELECT a.*, f.nom as fournisseur_nom
                    FROM achats_produits a
                    LEFT JOIN fournisseurs f ON a.fournisseur_id = f.id
                    WHERE a.id = ?
                """, (achat_id,))
                achat = cursor.fetchone()

                if not achat:
                    QMessageBox.warning(self, "Erreur", "Achat introuvable.")
                    return

                # Mettre à jour les champs du formulaire
                self.numero_facture_input.setText(achat['numero_facture'])

                # Convertir la date
                date_obj = datetime.strptime(achat['date_facture'], '%Y-%m-%d')
                self.date_facture.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))

                # Sélectionner le fournisseur
                for i in range(self.fournisseur_combo.count()):
                    if self.fournisseur_combo.itemData(i) == achat['fournisseur_id']:
                        self.fournisseur_combo.setCurrentIndex(i)
                        break

                # Sélectionner le mode de paiement
                index = self.mode_paiement_combo.findText(achat['mode_paiement'])
                if index >= 0:
                    self.mode_paiement_combo.setCurrentIndex(index)

                # Charger les lignes de l'achat
                self.articles_table.setRowCount(0)

                cursor.execute("""
                    SELECT * FROM lignes_achat
                    WHERE achat_id = ?
                    ORDER BY id
                """, (achat_id,))
                lignes = cursor.fetchall()

                for ligne in lignes:
                    self.ajouter_ligne_existante(ligne)

                # Mettre à jour le total
                self.calculer_total_facture()

            except sqlite3.Error as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement de l'achat: {str(e)}")

        def ajouter_ligne_existante(self, ligne):
            """Ajoute une ligne existante au tableau des articles"""
            row = self.articles_table.rowCount()
            self.articles_table.insertRow(row)

            # Créer un QLineEdit pour la désignation du produit
            designation_edit = QLineEdit(ligne['designation'])
            designation_edit.setStyleSheet(f"""
                border: none;
                background-color: transparent;
                padding: {SPACING['xs']};
                font-size: 13px;
                min-height: 25px;
            """)
            designation_edit.textChanged.connect(lambda: self.calculer_ligne_total(row))
            self.articles_table.setCellWidget(row, 0, designation_edit)

            # Créer un QLineEdit pour l'unité
            unite_edit = QLineEdit(ligne['unite'] or "")
            unite_edit.setStyleSheet(f"""
                border: none;
                background-color: transparent;
                padding: {SPACING['xs']};
                font-size: 13px;
                min-height: 25px;
            """)
            self.articles_table.setCellWidget(row, 1, unite_edit)

            # Créer un QSpinBox pour la quantité
            quantite_spin = QSpinBox()
            quantite_spin.setRange(1, 1000000)
            quantite_spin.setValue(ligne['quantite'])
            quantite_spin.setStyleSheet(f"""
                border: none;
                background-color: transparent;
                padding: {SPACING['xs']};
                font-size: 13px;
                min-height: 25px;
            """)
            quantite_spin.valueChanged.connect(lambda: self.calculer_ligne_total(row))
            self.articles_table.setCellWidget(row, 2, quantite_spin)

            # Créer un QDoubleSpinBox pour le prix unitaire
            prix_spin = QDoubleSpinBox()
            prix_spin.setRange(0.01, 1000000)
            prix_spin.setDecimals(2)
            prix_spin.setValue(ligne['prix_unitaire'])
            prix_spin.setSuffix(" DH")
            prix_spin.setStyleSheet(f"""
                border: none;
                background-color: transparent;
                padding: {SPACING['xs']};
                font-size: 13px;
                min-height: 25px;
            """)
            prix_spin.valueChanged.connect(lambda: self.calculer_ligne_total(row))
            self.articles_table.setCellWidget(row, 3, prix_spin)

            # Créer un QTableWidgetItem pour le total de la ligne
            total_item = QTableWidgetItem(f"{ligne['total']:.2f} DH")
            self.articles_table.setItem(row, 4, total_item)

        # Attacher les méthodes à l'instance
        self.show_achats_list = show_achats_list.__get__(self)
        self.charger_achat = charger_achat.__get__(self)
        self.ajouter_ligne_existante = ajouter_ligne_existante.__get__(self)

    def setup_ui(self):
        """Configure l'interface utilisateur du module"""
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(int(SPACING['lg'].replace('px', '')))
        main_layout.setContentsMargins(int(SPACING['lg'].replace('px', '')),
                                      int(SPACING['lg'].replace('px', '')),
                                      int(SPACING['lg'].replace('px', '')),
                                      int(SPACING['lg'].replace('px', '')))

        # En-tête avec titre et boutons
        header_container = QWidget()
        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(0, 0, 0, 0)

        # Titre du module
        title = QLabel("Achats de Produits")
        title.setProperty("title", "true")  # Utiliser la propriété title pour appliquer le style
        header_layout.addWidget(title)

        header_layout.addStretch()

        # Bouton Nouveau Achat
        self.nouveau_achat_btn = QPushButton("NOUVEAU ACHAT")
        self.nouveau_achat_btn.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
                margin-right: 10px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        self.nouveau_achat_btn.clicked.connect(self.nouveau_achat)
        header_layout.addWidget(self.nouveau_achat_btn)

        # Bouton Nouveau Produit
        self.nouveau_produit_btn = QPushButton("NOUVEAU PRODUIT")
        self.nouveau_produit_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
                margin-right: 10px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
        """)
        self.nouveau_produit_btn.clicked.connect(self.nouveau_produit)
        header_layout.addWidget(self.nouveau_produit_btn)

        # Bouton Liste des Achats
        self.liste_achats_btn = QPushButton("LISTE DES ACHATS")
        self.liste_achats_btn.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
                margin-right: 10px;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        self.liste_achats_btn.setIcon(QIcon(":/icons/list.png"))
        self.liste_achats_btn.clicked.connect(self.show_achats_list)
        header_layout.addWidget(self.liste_achats_btn)

        main_layout.addWidget(header_container)

        # Création d'un QScrollArea pour assurer que l'interface est responsive
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)

        # Widget conteneur pour le contenu scrollable
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(20)

        # Section Informations de la facture
        self.setup_info_facture_section(scroll_layout)

        # Section Articles achetés
        self.setup_articles_section(scroll_layout)

        # Section Boutons d'action
        self.setup_action_buttons(scroll_layout)

        # Ajouter le widget de contenu au scroll area
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)

    def setup_info_facture_section(self, parent_layout):
        """Configure la section des informations de la facture"""
        # Création du QGroupBox pour les informations de la facture
        info_facture_group = QGroupBox("Informations de la facture")
        # Utiliser les styles définis dans modern_style.py

        # Layout pour le contenu du QGroupBox
        form_layout = QFormLayout(info_facture_group)
        form_layout.setSpacing(15)
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)

        # Fournisseur (QComboBox)
        self.fournisseur_combo = QComboBox()
        self.fournisseur_combo.setPlaceholderText("Sélectionner un fournisseur")
        self.fournisseur_combo.setStyleSheet("""
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            background-color: white;
            min-height: 28px;
            font-size: 13px;
        """)
        form_layout.addRow("Fournisseur:", self.fournisseur_combo)

        # Numéro de facture d'achat (QLineEdit)
        self.numero_facture_input = QLineEdit()
        self.numero_facture_input.setPlaceholderText("Ex: FAC-A-2024-001")
        self.numero_facture_input.setStyleSheet("""
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            background-color: white;
            min-height: 28px;
            font-size: 13px;
        """)
        form_layout.addRow("Numéro de facture d'achat:", self.numero_facture_input)

        # Date (QDateEdit au format français)
        from ..style import create_styled_date_edit
        self.date_facture = create_styled_date_edit()
        form_layout.addRow("Date:", self.date_facture)

        # Mode de paiement (QComboBox)
        self.mode_paiement_combo = QComboBox()
        self.mode_paiement_combo.addItems([
            "Espèces", "Virement", "Chèque", "Carte", "Autre"
        ])
        self.mode_paiement_combo.setStyleSheet("""
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            background-color: white;
            min-height: 28px;
            font-size: 13px;
        """)
        form_layout.addRow("Mode de paiement:", self.mode_paiement_combo)

        # Ajouter le QGroupBox au layout parent
        parent_layout.addWidget(info_facture_group)

    def setup_articles_section(self, parent_layout):
        """Configure la section des articles achetés"""
        # Création du QGroupBox pour les articles achetés
        articles_group = QGroupBox("Articles achetés")
        # Utiliser les styles définis dans modern_style.py

        # Layout pour le contenu du QGroupBox
        articles_layout = QVBoxLayout(articles_group)
        articles_layout.setSpacing(15)

        # Tableau des articles (QTableWidget)
        self.articles_table = QTableWidget(0, 5)
        self.articles_table.setHorizontalHeaderLabels([
            "Désignation", "Unité", "Quantité", "Prix unitaire d'achat", "Total ligne"
        ])

        # Le style du tableau est défini globalement dans modern_style.py

        # Configuration des colonnes
        self.articles_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)  # Désignation
        self.articles_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Unité
        self.articles_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Quantité
        self.articles_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Prix unitaire
        self.articles_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Total

        self.articles_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.articles_table.setSelectionMode(QTableWidget.SingleSelection)
        self.articles_table.verticalHeader().setVisible(False)  # Cacher les numéros de ligne

        articles_layout.addWidget(self.articles_table)

        # Conteneur pour les boutons d'action
        buttons_layout = QHBoxLayout()

        # Bouton Ajouter une ligne
        self.ajouter_ligne_btn = QPushButton("Ajouter une ligne")
        self.ajouter_ligne_btn.setProperty("success", "true")  # Utiliser la propriété success pour appliquer le style
        self.ajouter_ligne_btn.clicked.connect(self.ajouter_ligne)
        buttons_layout.addWidget(self.ajouter_ligne_btn)

        # Bouton Afficher les produits
        self.afficher_produits_btn = QPushButton("Afficher les produits")
        self.afficher_produits_btn.setProperty("secondary", "true")  # Utiliser la propriété secondary pour appliquer le style
        self.afficher_produits_btn.clicked.connect(self.afficher_produits_dialog)
        buttons_layout.addWidget(self.afficher_produits_btn)

        articles_layout.addLayout(buttons_layout)

        # Ajouter le QGroupBox au layout parent
        parent_layout.addWidget(articles_group)

        # Total de la facture
        self.total_facture_label = QLabel("Total: 0.00 DH")
        self.total_facture_label.setProperty("subtitle", "true")  # Utiliser la propriété subtitle pour appliquer le style
        self.total_facture_label.setAlignment(Qt.AlignRight)
        parent_layout.addWidget(self.total_facture_label)

    def setup_action_buttons(self, parent_layout):
        """Configure les boutons d'action en bas de l'interface"""
        # Conteneur pour les boutons
        buttons_container = QFrame()
        buttons_layout = QHBoxLayout(buttons_container)
        buttons_layout.setSpacing(15)

        # Bouton Enregistrer la facture
        self.enregistrer_facture_btn = QPushButton("Enregistrer la facture")
        # Utiliser le style par défaut (primary)
        self.enregistrer_facture_btn.clicked.connect(self.enregistrer_facture)
        buttons_layout.addWidget(self.enregistrer_facture_btn)

        # Bouton Effacer
        self.effacer_btn = QPushButton("Effacer")
        self.effacer_btn.setProperty("flat", "true")  # Utiliser la propriété flat pour appliquer le style
        self.effacer_btn.clicked.connect(self.effacer_formulaire)
        buttons_layout.addWidget(self.effacer_btn)

        # Spacer pour pousser les boutons à gauche
        buttons_layout.addStretch()

        # Ajouter le conteneur des boutons au layout parent
        parent_layout.addWidget(buttons_container)

    def initialiser_donnees(self):
        """Initialise les données du module"""
        # Charger les fournisseurs
        self.charger_fournisseurs()

        # Générer un numéro de facture
        self.generer_numero_facture()

    def charger_fournisseurs(self):
        """Charge la liste des fournisseurs dans le QComboBox"""
        # Déconnecter le signal pour éviter les appels multiples pendant le chargement
        try:
            self.fournisseur_combo.currentIndexChanged.disconnect()
        except:
            pass  # Si le signal n'était pas connecté, ignorer l'erreur

        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT id, nom FROM fournisseurs ORDER BY nom")
        fournisseurs = cursor.fetchall()

        # Sauvegarder les données actuelles
        current_data = self.fournisseur_combo.currentData()

        self.fournisseur_combo.clear()
        self.fournisseur_combo.addItem("-- Sélectionner un fournisseur --", None)

        for fournisseur in fournisseurs:
            self.fournisseur_combo.addItem(fournisseur['nom'], fournisseur['id'])

        # Restaurer l'index précédent si possible
        if current_data:
            # Chercher l'index du fournisseur précédemment sélectionné
            for i in range(self.fournisseur_combo.count()):
                if self.fournisseur_combo.itemData(i) == current_data:
                    self.fournisseur_combo.setCurrentIndex(i)
                    break

        # Reconnecter le signal après avoir chargé les fournisseurs
        try:
            self.fournisseur_combo.currentIndexChanged.connect(self.fournisseur_selectionne)
        except:
            pass  # Si la méthode n'existe pas, ignorer l'erreur

    def fournisseur_selectionne(self):
        """Méthode appelée lorsqu'un fournisseur est sélectionné"""
        # Cette méthode peut être étendue pour mettre à jour d'autres informations
        # basées sur le fournisseur sélectionné si nécessaire
        pass

    def generer_numero_facture(self):
        """Génère un numéro de facture d'achat unique"""
        date_str = datetime.now().strftime("%Y%m")
        cursor = self.db_manager.conn.cursor()

        # Vérifier si la table factures_achat existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='factures_achat'")
        if not cursor.fetchone():
            self.numero_facture_input.setText(f"FAC-A-{date_str}-001")
            return

        cursor.execute("SELECT COUNT(*) FROM factures_achat WHERE numero LIKE ?", (f"FAC-A-{date_str}%",))
        count = cursor.fetchone()[0] + 1
        self.numero_facture_input.setText(f"FAC-A-{date_str}-{count:03d}")

    def ajouter_ligne(self):
        """Ajoute une nouvelle ligne au tableau des articles"""
        # Ajouter une nouvelle ligne au tableau
        row = self.articles_table.rowCount()
        self.articles_table.insertRow(row)

        # Créer un QLineEdit pour la désignation du produit (saisie libre)
        designation_edit = QLineEdit()
        designation_edit.setStyleSheet(f"""
            border: none;
            background-color: transparent;
            padding: {SPACING['xs']};
            font-size: 13px;
            min-height: 25px;
        """)
        designation_edit.textChanged.connect(lambda: self.calculer_ligne_total(row))
        self.articles_table.setCellWidget(row, 0, designation_edit)

        # Créer un QLineEdit pour l'unité (saisie libre)
        unite_edit = QLineEdit()
        unite_edit.setStyleSheet(f"""
            border: none;
            background-color: transparent;
            padding: {SPACING['xs']};
            font-size: 13px;
            min-height: 25px;
        """)
        self.articles_table.setCellWidget(row, 1, unite_edit)

        # Créer un QSpinBox pour la quantité
        quantite_spin = QSpinBox()
        quantite_spin.setRange(1, 1000000)
        quantite_spin.setValue(1)
        quantite_spin.setStyleSheet(f"""
            border: none;
            background-color: transparent;
            padding: {SPACING['xs']};
            font-size: 13px;
            min-height: 25px;
        """)
        quantite_spin.valueChanged.connect(lambda: self.calculer_ligne_total(row))
        self.articles_table.setCellWidget(row, 2, quantite_spin)

        # Créer un QDoubleSpinBox pour le prix unitaire
        prix_spin = QDoubleSpinBox()
        prix_spin.setRange(0.01, 1000000)
        prix_spin.setDecimals(2)
        prix_spin.setValue(0.00)
        prix_spin.setSuffix(" DH")
        prix_spin.setStyleSheet(f"""
            border: none;
            background-color: transparent;
            padding: {SPACING['xs']};
            font-size: 13px;
            min-height: 25px;
        """)
        prix_spin.valueChanged.connect(lambda: self.calculer_ligne_total(row))
        self.articles_table.setCellWidget(row, 3, prix_spin)

        # Créer un QTableWidgetItem pour le total de la ligne
        total_item = QTableWidgetItem("0.00 DH")
        self.articles_table.setItem(row, 4, total_item)

    def charger_produits_combo(self, combo):
        """Charge la liste des produits dans un QComboBox"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT id, code, designation FROM produits ORDER BY code")
        produits = cursor.fetchall()

        combo.clear()
        combo.addItem("-- Sélectionner un produit --", None)

        for produit in produits:
            combo.addItem(f"{produit['code']} - {produit['designation']}", produit['id'])

    def calculer_ligne_total(self, row):
        """Calcule le total d'une ligne lorsque la quantité ou le prix change"""
        quantite_spin = self.articles_table.cellWidget(row, 2)
        prix_spin = self.articles_table.cellWidget(row, 3)

        if quantite_spin and prix_spin:
            quantite = quantite_spin.value()
            prix = prix_spin.value()
            total = quantite * prix

            # Vérifier si l'item existe déjà, sinon le créer
            if not self.articles_table.item(row, 4):
                self.articles_table.setItem(row, 4, QTableWidgetItem("0.00 DH"))

            self.articles_table.item(row, 4).setText(f"{total:.2f} DH")

            # Mettre à jour le total de la facture
            self.calculer_total_facture()

    def calculer_total_facture(self):
        """Calcule le total de la facture d'achat"""
        total = 0
        for row in range(self.articles_table.rowCount()):
            if self.articles_table.item(row, 4):
                total_text = self.articles_table.item(row, 4).text().replace(" DH", "")
                try:
                    total += float(total_text)
                except ValueError:
                    pass

        self.total_facture_label.setText(f"Total: {total:.2f} DH")

    def generer_code_produit(self):
        """Génère un code de produit unique au format P001, P002, etc."""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM produits")
        count = cursor.fetchone()[0] + 1
        return f"P{count:03d}"

    def effacer_formulaire(self):
        """Réinitialise tous les champs du formulaire"""
        # Réinitialiser les champs de la facture
        self.fournisseur_combo.setCurrentIndex(0)
        self.generer_numero_facture()
        self.date_facture.setDate(QDate.currentDate())
        self.mode_paiement_combo.setCurrentIndex(0)

        # Vider le tableau des articles
        self.articles_table.setRowCount(0)

        # Réinitialiser le total
        self.calculer_total_facture()

    def enregistrer_facture(self):
        """Enregistre la facture d'achat et met à jour le stock des produits"""
        # Vérifier qu'il y a au moins une ligne
        if self.articles_table.rowCount() == 0:
            QMessageBox.warning(self, "Erreur", "Ajoutez au moins un article à la facture.")
            return

        # Vérifier qu'un fournisseur est sélectionné
        fournisseur_id = self.fournisseur_combo.currentData()
        if not fournisseur_id:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un fournisseur.")
            return

        # Vérifier que le numéro de facture est renseigné
        numero = self.numero_facture_input.text().strip()
        if not numero:
            QMessageBox.warning(self, "Erreur", "Veuillez renseigner le numéro de facture d'achat.")
            return

        # Récupérer les données de la facture
        date_creation = self.date_facture.date().toString("yyyy-MM-dd")
        mode_paiement = self.mode_paiement_combo.currentText()

        # Calculer le total
        total_ht = 0
        for row in range(self.articles_table.rowCount()):
            total_text = self.articles_table.item(row, 4).text().replace(" DH", "")
            try:
                total_ht += float(total_text)
            except ValueError:
                pass

        # Enregistrer la facture d'achat
        cursor = self.db_manager.conn.cursor()
        try:
            # Vérifier si la table factures_achat existe, sinon la créer
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS factures_achat (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    numero TEXT NOT NULL,
                    date_creation TEXT NOT NULL,
                    fournisseur_id INTEGER,
                    mode_paiement TEXT NOT NULL,
                    total_ht REAL NOT NULL,
                    notes TEXT,
                    FOREIGN KEY (fournisseur_id) REFERENCES fournisseurs (id)
                )
            """)

            # Vérifier si la table lignes_facture_achat existe, sinon la créer
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS lignes_facture_achat (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    facture_achat_id INTEGER NOT NULL,
                    produit_id INTEGER,
                    designation TEXT NOT NULL,
                    unite TEXT,
                    quantite INTEGER NOT NULL,
                    prix_unitaire_ht REAL NOT NULL,
                    total_ht REAL NOT NULL,
                    FOREIGN KEY (facture_achat_id) REFERENCES factures_achat (id),
                    FOREIGN KEY (produit_id) REFERENCES produits (id)
                )
            """)

            # Insérer la facture
            cursor.execute(
                """INSERT INTO factures_achat
                   (numero, date_creation, fournisseur_id, mode_paiement, total_ht)
                   VALUES (?, ?, ?, ?, ?)""",
                (numero, date_creation, fournisseur_id, mode_paiement, total_ht)
            )

            facture_id = cursor.lastrowid

            # Insérer les lignes de facture
            for row in range(self.articles_table.rowCount()):
                designation_edit = self.articles_table.cellWidget(row, 0)
                unite_edit = self.articles_table.cellWidget(row, 1)

                if not designation_edit or not designation_edit.text().strip():
                    continue  # Ignorer les lignes sans désignation

                designation = designation_edit.text().strip()
                unite = unite_edit.text().strip() if unite_edit else ""

                quantite_spin = self.articles_table.cellWidget(row, 2)
                prix_spin = self.articles_table.cellWidget(row, 3)

                if not quantite_spin or not prix_spin:
                    continue  # Ignorer les lignes sans quantité ou prix

                quantite = quantite_spin.value()
                prix_unitaire = prix_spin.value()
                total_ligne = quantite * prix_unitaire

                # Vérifier si le produit existe déjà dans la base de données
                cursor.execute("SELECT id FROM produits WHERE designation = ?", (designation,))
                produit = cursor.fetchone()

                produit_id = None
                if produit:
                    produit_id = produit['id']
                    # Mettre à jour le stock et le prix d'achat du produit existant
                    cursor.execute(
                        """UPDATE produits SET
                           stock = stock + ?,
                           prix_achat = ?,
                           unite = ?
                           WHERE id = ?""",
                        (quantite, prix_unitaire, unite, produit_id)
                    )
                else:
                    # Créer un nouveau produit
                    cursor.execute(
                        """INSERT INTO produits
                           (code, designation, unite, prix_achat, prix_vente, stock)
                           VALUES (?, ?, ?, ?, ?, ?)""",
                        (self.generer_code_produit(), designation, unite, prix_unitaire, prix_unitaire * 1.2, quantite)
                    )
                    produit_id = cursor.lastrowid

                # Insérer la ligne de facture
                cursor.execute(
                    """INSERT INTO lignes_facture_achat
                       (facture_achat_id, produit_id, designation, unite, quantite, prix_unitaire_ht, total_ht)
                       VALUES (?, ?, ?, ?, ?, ?, ?)""",
                    (facture_id, produit_id, designation, unite, quantite, prix_unitaire, total_ligne)
                )

            self.db_manager.conn.commit()
            QMessageBox.information(self, "Succès", f"Facture d'achat n°{numero} enregistrée avec succès.\nTotal: {total_ht:.2f} DH")

            # Réinitialiser le formulaire
            self.effacer_formulaire()

        except sqlite3.Error as e:
            self.db_manager.conn.rollback()
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement de la facture: {str(e)}")

    def afficher_produits_dialog(self):
        """Affiche une fenêtre popup avec les produits enregistrés"""
        dialog = ProduitsDialog(self.db_manager, self)
        dialog.exec()

    def nouveau_achat(self):
        """Ouvre le formulaire d'achat de produits"""
        dialog = AchatForm(self.db_manager, self)
        if dialog.exec() == QDialog.Accepted:
            # Mettre à jour l'interface si nécessaire
            self.effacer_formulaire()
            if self.signals:
                self.signals.produits_changed.emit()

    def nouveau_produit(self):
        """Ouvre le formulaire d'ajout de produit"""
        dialog = ProduitForm(self.db_manager, self)
        if dialog.exec() == QDialog.Accepted:
            # Mettre à jour l'interface si nécessaire
            if self.signals:
                self.signals.produits_changed.emit()


class ProduitsDialog(QDialog):
    """Dialogue pour afficher les produits enregistrés"""

    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.charger_produits()

    def setup_ui(self):
        """Configure l'interface utilisateur du dialogue"""
        self.setWindowTitle("Liste des produits enregistrés")
        self.setMinimumSize(1000, 600)

        # Layout principal
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title = QLabel("Produits enregistrés")
        title.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)

        # Tableau des produits
        self.produits_table = QTableWidget(0, 10)
        self.produits_table.setHorizontalHeaderLabels([
            "Désignation", "Unité", "PU HT", "P.Total HT", "Prix Vente",
            "Numéro de facture d'achat", "Date d'achat", "Fournisseur",
            "Mode de paiement", "Action"
        ])

        # Style du tableau
        self.produits_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background-color: white;
                gridline-color: #e9ecef;
                selection-background-color: #e3f2fd;
                selection-color: #212529;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #e9ecef;
            }
        """)

        # Configuration des colonnes
        self.produits_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)  # Désignation
        self.produits_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Unité
        self.produits_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)  # PU HT
        self.produits_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # P.Total HT
        self.produits_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Prix Vente
        self.produits_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Numéro facture
        self.produits_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Date
        self.produits_table.horizontalHeader().setSectionResizeMode(7, QHeaderView.ResizeToContents)  # Fournisseur
        self.produits_table.horizontalHeader().setSectionResizeMode(8, QHeaderView.ResizeToContents)  # Mode paiement
        self.produits_table.horizontalHeader().setSectionResizeMode(9, QHeaderView.ResizeToContents)  # Action

        self.produits_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.produits_table.setSelectionMode(QTableWidget.SingleSelection)
        self.produits_table.verticalHeader().setVisible(False)  # Cacher les numéros de ligne

        # Connecter le signal de double-clic pour ouvrir la boîte de dialogue d'édition
        self.produits_table.itemDoubleClicked.connect(self.on_item_double_clicked)

        layout.addWidget(self.produits_table)

        # Boutons
        buttons_layout = QHBoxLayout()
        buttons_layout.setAlignment(Qt.AlignRight)

        self.fermer_btn = QPushButton("Fermer")
        self.fermer_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:pressed {
                background-color: #545b62;
            }
        """)
        self.fermer_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(self.fermer_btn)

        layout.addLayout(buttons_layout)

    def charger_produits(self):
        """Charge les produits depuis la base de données"""
        cursor = self.db_manager.conn.cursor()

        # Requête pour récupérer les produits avec les informations de facture
        query = """
        SELECT
            p.designation,
            p.unite,
            lfa.prix_unitaire_ht,
            lfa.total_ht,
            p.prix_vente,
            fa.numero,
            fa.date_creation,
            f.nom AS fournisseur_nom,
            fa.mode_paiement,
            p.id
        FROM
            produits p
        LEFT JOIN
            lignes_facture_achat lfa ON p.id = lfa.produit_id
        LEFT JOIN
            factures_achat fa ON lfa.facture_achat_id = fa.id
        LEFT JOIN
            fournisseurs f ON fa.fournisseur_id = f.id
        ORDER BY
            p.designation
        """

        try:
            cursor.execute(query)
            produits = cursor.fetchall()

            self.produits_table.setRowCount(0)

            for row_num, produit in enumerate(produits):
                self.produits_table.insertRow(row_num)

                # Désignation
                designation_item = QTableWidgetItem(produit['designation'] or "")
                designation_item.setData(Qt.UserRole, produit['id'])  # Stocker l'ID du produit
                self.produits_table.setItem(row_num, 0, designation_item)

                # Unité
                self.produits_table.setItem(row_num, 1, QTableWidgetItem(produit['unite'] or ""))

                # PU HT
                prix_unitaire = produit['prix_unitaire_ht'] or 0
                self.produits_table.setItem(row_num, 2, QTableWidgetItem(f"{prix_unitaire:.2f} DH"))

                # P.Total HT
                total_ht = produit['total_ht'] or 0
                self.produits_table.setItem(row_num, 3, QTableWidgetItem(f"{total_ht:.2f} DH"))

                # Prix Vente
                prix_vente = produit['prix_vente'] or 0
                self.produits_table.setItem(row_num, 4, QTableWidgetItem(f"{prix_vente:.2f} DH"))

                # Numéro de facture d'achat
                self.produits_table.setItem(row_num, 5, QTableWidgetItem(produit['numero'] or ""))

                # Date d'achat
                self.produits_table.setItem(row_num, 6, QTableWidgetItem(produit['date_creation'] or ""))

                # Fournisseur
                self.produits_table.setItem(row_num, 7, QTableWidgetItem(produit['fournisseur_nom'] or ""))

                # Mode de paiement
                self.produits_table.setItem(row_num, 8, QTableWidgetItem(produit['mode_paiement'] or ""))

                # Action (bouton)
                action_cell = QWidget()
                action_layout = QHBoxLayout(action_cell)
                action_layout.setContentsMargins(2, 2, 2, 2)
                action_layout.setSpacing(5)

                edit_btn = QPushButton("✏️")
                edit_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #4CAF50;
                        color: white;
                        border: none;
                        border-radius: 3px;
                        min-width: 24px;
                        min-height: 24px;
                        max-width: 24px;
                        max-height: 24px;
                    }
                    QPushButton:hover {
                        background-color: #45a049;
                    }
                """)
                edit_btn.clicked.connect(lambda _, id=produit['id']: self.editer_produit(id))
                action_layout.addWidget(edit_btn)

                self.produits_table.setCellWidget(row_num, 9, action_cell)

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des produits: {str(e)}")

    def editer_produit(self, produit_id):
        """Ouvre une boîte de dialogue pour éditer un produit"""
        QMessageBox.information(self, "Information", f"Édition du produit ID: {produit_id} - Fonctionnalité à implémenter")

    def on_item_double_clicked(self, item):
        """Gère le double-clic sur un élément du tableau"""
        if item is None:
            return

        # Récupérer l'ID du produit depuis la première colonne
        row = item.row()
        first_column_item = self.produits_table.item(row, 0)

        if first_column_item:
            # Récupérer l'ID stocké dans les données utilisateur
            produit_id = first_column_item.data(Qt.UserRole)
            if produit_id:
                # Appeler la méthode d'édition
                self.editer_produit(produit_id)
