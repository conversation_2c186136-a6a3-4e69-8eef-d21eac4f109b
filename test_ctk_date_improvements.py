#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تحسينات خانات التواريخ في run_app.py
"""

import customtkinter as ctk
from date_picker_ctk import create_enhanced_date_field

class DateTestApp(ctk.CTk):
    """تطبيق اختبار خانات التواريخ المحسنة"""
    
    def __init__(self):
        super().__init__()
        
        self.title("اختبار خانات التواريخ المحسنة - CustomTkinter")
        self.geometry("600x500")
        
        # إعداد الثيم
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان رئيسي
        title = ctk.CTkLabel(
            self, 
            text="🗓️ اختبار خانات التواريخ المحسنة",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=20)
        
        # إطار النماذج
        forms_frame = ctk.CTkFrame(self)
        forms_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # نموذج الصفقات
        self.create_marche_form(forms_frame)
        
        # نموذج المنتجات
        self.create_product_form(forms_frame)
        
        # نموذج الفواتير
        self.create_invoice_form(forms_frame)
        
        # أزرار الاختبار
        self.create_test_buttons()
        
    def create_marche_form(self, parent):
        """نموذج الصفقات"""
        marche_frame = ctk.CTkFrame(parent)
        marche_frame.pack(fill="x", padx=10, pady=5)
        
        title = ctk.CTkLabel(marche_frame, text="📋 نموذج الصفقات", font=ctk.CTkFont(size=16, weight="bold"))
        title.pack(pady=10)
        
        # شبكة الحقول
        fields_frame = ctk.CTkFrame(marche_frame)
        fields_frame.pack(fill="x", padx=10, pady=10)
        fields_frame.grid_columnconfigure((0, 1), weight=1)
        
        # تاريخ الإشعار
        ctk.CTkLabel(fields_frame, text="تاريخ إشعار الموافقة:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.date_notification = create_enhanced_date_field(fields_frame, "اختر تاريخ الإشعار 📅")
        self.date_notification.grid(row=1, column=0, padx=5, pady=5, sticky="ew")
        
        # تاريخ أمر الخدمة
        ctk.CTkLabel(fields_frame, text="تاريخ أمر الخدمة:").grid(row=0, column=1, padx=5, pady=5, sticky="w")
        self.date_ordre = create_enhanced_date_field(fields_frame, "اختر تاريخ أمر الخدمة 📅")
        self.date_ordre.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        
    def create_product_form(self, parent):
        """نموذج المنتجات"""
        product_frame = ctk.CTkFrame(parent)
        product_frame.pack(fill="x", padx=10, pady=5)
        
        title = ctk.CTkLabel(product_frame, text="📦 نموذج المنتجات", font=ctk.CTkFont(size=16, weight="bold"))
        title.pack(pady=10)
        
        # حقول المنتج
        fields_frame = ctk.CTkFrame(product_frame)
        fields_frame.pack(fill="x", padx=10, pady=10)
        fields_frame.grid_columnconfigure((0, 1), weight=1)
        
        # تاريخ الشراء
        ctk.CTkLabel(fields_frame, text="تاريخ الشراء:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.date_achat = create_enhanced_date_field(fields_frame, "اختر تاريخ الشراء 📅")
        self.date_achat.grid(row=1, column=0, padx=5, pady=5, sticky="ew")
        
        # تاريخ انتهاء الصلاحية
        ctk.CTkLabel(fields_frame, text="تاريخ انتهاء الصلاحية:").grid(row=0, column=1, padx=5, pady=5, sticky="w")
        self.date_expiry = create_enhanced_date_field(fields_frame, "اختر تاريخ انتهاء الصلاحية 📅")
        self.date_expiry.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        
    def create_invoice_form(self, parent):
        """نموذج الفواتير"""
        invoice_frame = ctk.CTkFrame(parent)
        invoice_frame.pack(fill="x", padx=10, pady=5)
        
        title = ctk.CTkLabel(invoice_frame, text="🧾 نموذج الفواتير", font=ctk.CTkFont(size=16, weight="bold"))
        title.pack(pady=10)
        
        # حقول الفاتورة
        fields_frame = ctk.CTkFrame(invoice_frame)
        fields_frame.pack(fill="x", padx=10, pady=10)
        fields_frame.grid_columnconfigure((0, 1), weight=1)
        
        # تاريخ الفاتورة
        ctk.CTkLabel(fields_frame, text="تاريخ الفاتورة:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.date_facture = create_enhanced_date_field(fields_frame, "اختر تاريخ الفاتورة 📅")
        self.date_facture.grid(row=1, column=0, padx=5, pady=5, sticky="ew")
        
        # تاريخ الاستحقاق
        ctk.CTkLabel(fields_frame, text="تاريخ الاستحقاق:").grid(row=0, column=1, padx=5, pady=5, sticky="w")
        self.date_echeance = create_enhanced_date_field(fields_frame, "اختر تاريخ الاستحقاق 📅")
        self.date_echeance.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        
    def create_test_buttons(self):
        """أزرار الاختبار"""
        buttons_frame = ctk.CTkFrame(self)
        buttons_frame.pack(fill="x", padx=20, pady=10)
        
        # زر عرض التواريخ المحددة
        show_btn = ctk.CTkButton(
            buttons_frame,
            text="عرض التواريخ المحددة",
            command=self.show_selected_dates
        )
        show_btn.pack(side="left", padx=10, pady=10)
        
        # زر مسح جميع التواريخ
        clear_btn = ctk.CTkButton(
            buttons_frame,
            text="مسح جميع التواريخ",
            command=self.clear_all_dates
        )
        clear_btn.pack(side="left", padx=10, pady=10)
        
        # زر تعيين تواريخ تجريبية
        test_btn = ctk.CTkButton(
            buttons_frame,
            text="تعيين تواريخ تجريبية",
            command=self.set_test_dates
        )
        test_btn.pack(side="left", padx=10, pady=10)
        
    def show_selected_dates(self):
        """عرض التواريخ المحددة"""
        dates = {
            "تاريخ الإشعار": self.date_notification.get_date_string(),
            "تاريخ أمر الخدمة": self.date_ordre.get_date_string(),
            "تاريخ الشراء": self.date_achat.get_date_string(),
            "تاريخ انتهاء الصلاحية": self.date_expiry.get_date_string(),
            "تاريخ الفاتورة": self.date_facture.get_date_string(),
            "تاريخ الاستحقاق": self.date_echeance.get_date_string()
        }
        
        # إنشاء نافذة عرض
        result_window = ctk.CTkToplevel(self)
        result_window.title("التواريخ المحددة")
        result_window.geometry("400x300")
        result_window.transient(self)
        
        # عرض التواريخ
        text_widget = ctk.CTkTextbox(result_window)
        text_widget.pack(fill="both", expand=True, padx=20, pady=20)
        
        result_text = "📅 التواريخ المحددة:\n\n"
        for name, date in dates.items():
            if date:
                result_text += f"• {name}: {date}\n"
            else:
                result_text += f"• {name}: غير محدد\n"
                
        text_widget.insert("1.0", result_text)
        text_widget.configure(state="disabled")
        
    def clear_all_dates(self):
        """مسح جميع التواريخ"""
        self.date_notification.set_date(None)
        self.date_ordre.set_date(None)
        self.date_achat.set_date(None)
        self.date_expiry.set_date(None)
        self.date_facture.set_date(None)
        self.date_echeance.set_date(None)
        
    def set_test_dates(self):
        """تعيين تواريخ تجريبية"""
        from datetime import datetime, timedelta
        
        today = datetime.now()
        
        self.date_notification.set_date(today)
        self.date_ordre.set_date(today + timedelta(days=7))
        self.date_achat.set_date(today - timedelta(days=30))
        self.date_expiry.set_date(today + timedelta(days=365))
        self.date_facture.set_date(today)
        self.date_echeance.set_date(today + timedelta(days=30))


def main():
    """تشغيل التطبيق"""
    app = DateTestApp()
    app.mainloop()


if __name__ == "__main__":
    main()
