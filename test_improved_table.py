#!/usr/bin/env python3
"""
اختبار الجدول المحسن للمنتجات
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from run_app import ComptabiliteApp
    import customtkinter as ctk
    
    def main():
        print("🚀 بدء تشغيل التطبيق مع الجدول المحسن...")
        
        # تكوين CustomTkinter
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # إنشاء التطبيق
        app = ComptabiliteApp()
        
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("📋 انتقل إلى قسم 'المنتجات' لرؤية الجدول المحسن")
        print("🎨 التحسينات الجديدة:")
        print("   - رؤوس أعمدة ملونة")
        print("   - صفو<PERSON> متناوبة الألوان")
        print("   - ألوان تحذيرية للمخزون المنخفض")
        print("   - تأثيرات التمرير بالماوس")
        print("   - إحصائيات في أسفل الجدول")
        print("   - تنسيق محسن للأسعار والوحدات")
        
        # تشغيل التطبيق
        app.mainloop()
        
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("تأكد من تثبيت customtkinter")
except Exception as e:
    print(f"❌ خطأ في تشغيل التطبيق: {e}")
