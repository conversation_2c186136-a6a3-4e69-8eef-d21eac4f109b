from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QTableWidget, QTableWidgetItem,
                              QHeaderView, QFrame, QMessageBox, QComboBox,
                              QLineEdit, QDateEdit, QTextEdit, QDialog, QFormLayout,
                              QDoubleSpinBox)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QColor, QFont
import sqlite3
from datetime import datetime

class CaisseModule(QWidget):
    """Module de gestion de caisse"""

    # Signal pour notifier les changements de caisse
    caisse_updated = Signal()

    def __init__(self, db_manager, signals):
        super().__init__()
        self.db_manager = db_manager
        self.signals = signals
        self.setup_caisse_ui()
        self.create_caisse_tables()
        self.load_caisse_data()

    def setup_caisse_ui(self):
        """Configure l'interface utilisateur de la caisse"""
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # En-tête avec boutons
        self.setup_header_buttons(main_layout)

        # Section Entrées
        self.setup_entrees_section(main_layout)

        # Section Sorties
        self.setup_sorties_section(main_layout)

        # Section Situation de caisse
        self.setup_situation_caisse(main_layout)

    def setup_header_buttons(self, layout):
        """Configure les boutons d'en-tête"""
        buttons_container = QWidget()
        buttons_layout = QHBoxLayout(buttons_container)
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        buttons_layout.setSpacing(10)

        # Bouton Caisse
        self.caisse_button = QPushButton("Caisse")
        self.caisse_button.setStyleSheet("""
            QPushButton {
                background-color: #5B9BD5;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #4A90C2;
            }
        """)
        self.caisse_button.clicked.connect(self.refresh_caisse_data)

        # Bouton Ajouter
        self.ajouter_button = QPushButton("Ajouter")
        self.ajouter_button.setStyleSheet("""
            QPushButton {
                background-color: #5B9BD5;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #4A90C2;
            }
        """)
        self.ajouter_button.clicked.connect(self.show_add_dialog)

        buttons_layout.addWidget(self.caisse_button)
        buttons_layout.addWidget(self.ajouter_button)
        buttons_layout.addStretch()

        layout.addWidget(buttons_container)

    def setup_entrees_section(self, layout):
        """Configure la section des entrées"""
        # En-tête avec titre et bouton d'ajout
        entrees_header_layout = QHBoxLayout()

        # Titre Entrées
        entrees_label = QLabel("Entrées")
        entrees_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #1E3A8A;
                margin-bottom: 10px;
            }
        """)
        entrees_header_layout.addWidget(entrees_label)

        # Bouton d'ajout d'entrée
        self.add_entree_btn = QPushButton("+ Ajouter")
        self.add_entree_btn.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11px;
                max-width: 80px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        self.add_entree_btn.clicked.connect(self.show_add_entree_dialog)
        entrees_header_layout.addWidget(self.add_entree_btn)
        entrees_header_layout.addStretch()

        layout.addLayout(entrees_header_layout)

        # Tableau des entrées
        self.entrees_table = QTableWidget()
        self.entrees_table.setColumnCount(5)
        self.entrees_table.setHorizontalHeaderLabels([
            "Date", "Nature (بنك / بيع)", "Objet", "Référence (facture, bon...)", "Montant"
        ])

        # Configuration du tableau
        self.entrees_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.entrees_table.setAlternatingRowColors(True)
        self.entrees_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                background-color: white;
                gridline-color: #E5E7EB;
            }
            QHeaderView::section {
                background-color: #F3F4F6;
                color: #374151;
                padding: 10px;
                font-weight: bold;
                border: 1px solid #E5E7EB;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E5E7EB;
            }
        """)

        # Rendre le tableau éditable
        self.entrees_table.setEditTriggers(QTableWidget.DoubleClicked | QTableWidget.EditKeyPressed)
        self.entrees_table.itemChanged.connect(self.on_entrees_item_changed)

        # Ajouter le menu contextuel
        self.entrees_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.entrees_table.customContextMenuRequested.connect(self.show_entrees_context_menu)

        layout.addWidget(self.entrees_table)

    def setup_sorties_section(self, layout):
        """Configure la section des sorties"""
        # En-tête avec titre et bouton d'ajout
        sorties_header_layout = QHBoxLayout()

        # Titre Sorties
        sorties_label = QLabel("Sortie")
        sorties_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #1E3A8A;
                margin-top: 20px;
                margin-bottom: 10px;
            }
        """)
        sorties_header_layout.addWidget(sorties_label)

        # Bouton d'ajout de sortie
        self.add_sortie_btn = QPushButton("+ Ajouter")
        self.add_sortie_btn.setStyleSheet("""
            QPushButton {
                background-color: #EF4444;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11px;
                max-width: 80px;
            }
            QPushButton:hover {
                background-color: #DC2626;
            }
        """)
        self.add_sortie_btn.clicked.connect(self.show_add_sortie_dialog)
        sorties_header_layout.addWidget(self.add_sortie_btn)
        sorties_header_layout.addStretch()

        layout.addLayout(sorties_header_layout)

        # Tableau des sorties
        self.sorties_table = QTableWidget()
        self.sorties_table.setColumnCount(5)
        self.sorties_table.setHorizontalHeaderLabels([
            "Date", "Nature (achat / déplacement)", "Objet", "Référence (facture, bon...)", "Montant"
        ])

        # Configuration du tableau
        self.sorties_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.sorties_table.setAlternatingRowColors(True)
        self.sorties_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                background-color: white;
                gridline-color: #E5E7EB;
            }
            QHeaderView::section {
                background-color: #F3F4F6;
                color: #374151;
                padding: 10px;
                font-weight: bold;
                border: 1px solid #E5E7EB;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E5E7EB;
            }
        """)

        # Rendre le tableau éditable
        self.sorties_table.setEditTriggers(QTableWidget.DoubleClicked | QTableWidget.EditKeyPressed)
        self.sorties_table.itemChanged.connect(self.on_sorties_item_changed)

        # Ajouter le menu contextuel
        self.sorties_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.sorties_table.customContextMenuRequested.connect(self.show_sorties_context_menu)

        layout.addWidget(self.sorties_table)

    def setup_situation_caisse(self, layout):
        """Configure la section situation de caisse"""
        # Container principal
        situation_container = QWidget()
        situation_layout = QHBoxLayout(situation_container)
        situation_layout.setContentsMargins(0, 20, 0, 0)
        situation_layout.setSpacing(20)

        # Tableau situation de caisse
        situation_frame = QFrame()
        situation_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #1E3A8A;
                border-radius: 6px;
                background-color: white;
                padding: 15px;
                min-width: 450px;
                min-height: 200px;
            }
        """)
        situation_frame_layout = QVBoxLayout(situation_frame)
        situation_frame_layout.setContentsMargins(10, 10, 10, 10)

        # Titre
        situation_title = QLabel("Situation de caisse :")
        situation_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #374151;
                margin-bottom: 10px;
            }
        """)
        situation_frame_layout.addWidget(situation_title)

        # Tableau des montants - Version simplifiée
        self.situation_table = QTableWidget(3, 2)
        self.situation_table.setHorizontalHeaderLabels(["Type", "Montant"])

        # Configuration simple
        self.situation_table.setFixedHeight(120)
        self.situation_table.setFixedWidth(400)
        self.situation_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.situation_table.verticalHeader().setVisible(False)

        # Style simple
        self.situation_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #1E3A8A;
                background-color: white;
                font-size: 14px;
                font-weight: bold;
            }
            QHeaderView::section {
                background-color: #1E3A8A;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
            QTableWidget::item {
                padding: 10px;
                text-align: center;
            }
        """)

        # Initialiser les valeurs avec les noms complets
        self.situation_table.setItem(0, 0, QTableWidgetItem("Entrées"))
        self.situation_table.setItem(1, 0, QTableWidgetItem("Sorties"))
        self.situation_table.setItem(2, 0, QTableWidgetItem("Solde"))

        # Rendre les montants éditables
        for row in range(3):
            item = QTableWidgetItem("0.00")
            item.setTextAlignment(Qt.AlignCenter)
            if row < 2:  # Entrées et Sorties éditables
                item.setFlags(item.flags() | Qt.ItemIsEditable)
            else:  # Solde calculé automatiquement
                item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                item.setBackground(QColor("#F3F4F6"))
            self.situation_table.setItem(row, 1, item)

        self.situation_table.itemChanged.connect(self.calculate_solde)

        # Ajouter le tableau au layout
        situation_frame_layout.addWidget(self.situation_table)

        # S'assurer que le tableau est visible
        self.situation_table.show()

        situation_layout.addWidget(situation_frame)

        # Section "Le solde reste dans la caisse"
        solde_frame = QFrame()
        solde_frame.setStyleSheet("""
            QFrame {
                background-color: #5B9BD5;
                border-radius: 6px;
                padding: 20px;
            }
        """)
        solde_frame_layout = QVBoxLayout(solde_frame)

        solde_text = QLabel("Le solde reste dans la caisse :")
        solde_text.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                text-align: center;
            }
        """)
        solde_text.setAlignment(Qt.AlignCenter)

        self.solde_amount_label = QLabel("0.00 DH")
        self.solde_amount_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                text-align: center;
                margin-top: 10px;
            }
        """)
        self.solde_amount_label.setAlignment(Qt.AlignCenter)

        solde_frame_layout.addWidget(solde_text)
        solde_frame_layout.addWidget(self.solde_amount_label)

        situation_layout.addWidget(solde_frame)
        layout.addWidget(situation_container)

    def create_caisse_tables(self):
        """Crée les tables de caisse dans la base de données"""
        if not self.db_manager:
            return

        try:
            cursor = self.db_manager.conn.cursor()

            # Table des entrées de caisse
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS caisse_entrees (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    nature TEXT,
                    objet TEXT,
                    reference TEXT,
                    montant REAL DEFAULT 0.0,
                    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Table des sorties de caisse
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS caisse_sorties (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    nature TEXT,
                    objet TEXT,
                    reference TEXT,
                    montant REAL DEFAULT 0.0,
                    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            self.db_manager.conn.commit()
            print("✅ Tables de caisse créées avec succès")

        except Exception as e:
            print(f"❌ Erreur lors de la création des tables de caisse: {str(e)}")

    def load_caisse_data(self):
        """Charge les données de caisse"""
        self.load_entrees_data()
        self.load_sorties_data()
        self.calculate_totals()

    def load_entrees_data(self):
        """Charge les données des entrées"""
        if not self.db_manager:
            return

        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT date, nature, objet, reference, montant
                FROM caisse_entrees
                ORDER BY date DESC
            """)

            entrees = cursor.fetchall()

            # Ajouter une ligne vide pour permettre l'ajout
            self.entrees_table.setRowCount(len(entrees) + 1)

            for row, entree in enumerate(entrees):
                for col, value in enumerate(entree):
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFlags(item.flags() | Qt.ItemIsEditable)
                    self.entrees_table.setItem(row, col, item)

            # Ligne vide pour ajout
            for col in range(5):
                item = QTableWidgetItem("")
                item.setTextAlignment(Qt.AlignCenter)
                item.setFlags(item.flags() | Qt.ItemIsEditable)
                self.entrees_table.setItem(len(entrees), col, item)

            # Ajouter ligne total
            self.add_total_row_entrees()

        except Exception as e:
            print(f"❌ Erreur lors du chargement des entrées: {str(e)}")

    def load_sorties_data(self):
        """Charge les données des sorties"""
        if not self.db_manager:
            return

        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT date, nature, objet, reference, montant
                FROM caisse_sorties
                ORDER BY date DESC
            """)

            sorties = cursor.fetchall()

            # Ajouter une ligne vide pour permettre l'ajout
            self.sorties_table.setRowCount(len(sorties) + 1)

            for row, sortie in enumerate(sorties):
                for col, value in enumerate(sortie):
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFlags(item.flags() | Qt.ItemIsEditable)
                    self.sorties_table.setItem(row, col, item)

            # Ligne vide pour ajout
            for col in range(5):
                item = QTableWidgetItem("")
                item.setTextAlignment(Qt.AlignCenter)
                item.setFlags(item.flags() | Qt.ItemIsEditable)
                self.sorties_table.setItem(len(sorties), col, item)

            # Ajouter ligne total
            self.add_total_row_sorties()

        except Exception as e:
            print(f"❌ Erreur lors du chargement des sorties: {str(e)}")

    def add_total_row_entrees(self):
        """Ajoute une ligne total au tableau des entrées"""
        row_count = self.entrees_table.rowCount()
        self.entrees_table.insertRow(row_count)

        # Cellule "TOTAL"
        total_item = QTableWidgetItem("TOTAL")
        total_item.setTextAlignment(Qt.AlignCenter)
        total_item.setBackground(QColor("#F3F4F6"))
        total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
        self.entrees_table.setItem(row_count, 3, total_item)

        # Calcul du total
        total_amount = self.calculate_total_entrees()
        total_amount_item = QTableWidgetItem(f"{total_amount:.2f}")
        total_amount_item.setTextAlignment(Qt.AlignCenter)
        total_amount_item.setBackground(QColor("#E8F5E8"))
        total_amount_item.setFlags(total_amount_item.flags() & ~Qt.ItemIsEditable)
        self.entrees_table.setItem(row_count, 4, total_amount_item)

    def add_total_row_sorties(self):
        """Ajoute une ligne total au tableau des sorties"""
        row_count = self.sorties_table.rowCount()
        self.sorties_table.insertRow(row_count)

        # Cellule "TOTAL"
        total_item = QTableWidgetItem("TOTAL")
        total_item.setTextAlignment(Qt.AlignCenter)
        total_item.setBackground(QColor("#F3F4F6"))
        total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
        self.sorties_table.setItem(row_count, 3, total_item)

        # Calcul du total
        total_amount = self.calculate_total_sorties()
        total_amount_item = QTableWidgetItem(f"{total_amount:.2f}")
        total_amount_item.setTextAlignment(Qt.AlignCenter)
        total_amount_item.setBackground(QColor("#FFE8E8"))
        total_amount_item.setFlags(total_amount_item.flags() & ~Qt.ItemIsEditable)
        self.sorties_table.setItem(row_count, 4, total_amount_item)

    def calculate_total_entrees(self):
        """Calcule le total des entrées"""
        if not self.db_manager:
            return 0.0

        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT SUM(montant) FROM caisse_entrees")
            result = cursor.fetchone()
            return float(result[0]) if result[0] else 0.0
        except:
            return 0.0

    def calculate_total_sorties(self):
        """Calcule le total des sorties"""
        if not self.db_manager:
            return 0.0

        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT SUM(montant) FROM caisse_sorties")
            result = cursor.fetchone()
            return float(result[0]) if result[0] else 0.0
        except:
            return 0.0

    def calculate_totals(self):
        """Calcule et met à jour tous les totaux"""
        total_entrees = self.calculate_total_entrees()
        total_sorties = self.calculate_total_sorties()

        # Mettre à jour la situation de caisse
        if self.situation_table.item(0, 1):
            self.situation_table.item(0, 1).setText(f"{total_entrees:.2f}")

        if self.situation_table.item(1, 1):
            self.situation_table.item(1, 1).setText(f"{total_sorties:.2f}")

        # Calculer le solde
        self.calculate_solde()

    def calculate_solde(self):
        """Calcule le solde de caisse"""
        try:
            total_entrees = self.calculate_total_entrees()
            total_sorties = self.calculate_total_sorties()
            solde = total_entrees - total_sorties

            # Mettre à jour le solde dans le tableau
            self.situation_table.item(2, 1).setText(f"{solde:.2f}")

            # Mettre à jour l'affichage du solde
            self.solde_amount_label.setText(f"{solde:.2f} DH")

            # Couleur selon le solde
            if solde >= 0:
                self.solde_amount_label.setStyleSheet("color: white; font-size: 18px; font-weight: bold;")
            else:
                self.solde_amount_label.setStyleSheet("color: #FF6B6B; font-size: 18px; font-weight: bold;")

        except:
            pass

    def on_entrees_item_changed(self, item):
        """Gère les changements dans le tableau des entrées"""
        if item.column() == 4:  # Colonne montant
            self.update_total_entrees()
            self.calculate_totals()

        # Sauvegarder automatiquement
        self.save_entree_item(item)

    def on_sorties_item_changed(self, item):
        """Gère les changements dans le tableau des sorties"""
        if item.column() == 4:  # Colonne montant
            self.update_total_sorties()
            self.calculate_totals()

        # Sauvegarder automatiquement
        self.save_sortie_item(item)

    def update_total_entrees(self):
        """Met à jour la ligne total des entrées"""
        total = self.calculate_total_entrees()
        last_row = self.entrees_table.rowCount() - 1
        total_item = self.entrees_table.item(last_row, 4)
        if total_item:
            total_item.setText(f"{total:.2f}")

    def update_total_sorties(self):
        """Met à jour la ligne total des sorties"""
        total = self.calculate_total_sorties()
        last_row = self.sorties_table.rowCount() - 1
        total_item = self.sorties_table.item(last_row, 4)
        if total_item:
            total_item.setText(f"{total:.2f}")

    def save_entree_item(self, item):
        """Sauvegarde un élément d'entrée"""
        # Cette fonction sera implémentée pour sauvegarder en base de données
        pass

    def save_sortie_item(self, item):
        """Sauvegarde un élément de sortie"""
        # Cette fonction sera implémentée pour sauvegarder en base de données
        pass

    def show_add_dialog(self):
        """Affiche la boîte de dialogue d'ajout"""
        QMessageBox.information(self, "Ajouter", "Fonction d'ajout en cours de développement")

    def refresh_caisse_data(self):
        """Actualise les données de caisse"""
        self.load_caisse_data()
        QMessageBox.information(self, "Caisse", "Données de caisse actualisées")

    def add_cash_entry(self, montant, nature, objet, reference=""):
        """Ajoute une entrée de caisse"""
        if not self.db_manager:
            return

        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                INSERT INTO caisse_entrees (date, nature, objet, reference, montant)
                VALUES (?, ?, ?, ?, ?)
            """, (datetime.now().strftime("%Y-%m-%d"), nature, objet, reference, montant))

            self.db_manager.conn.commit()
            self.load_caisse_data()
            self.caisse_updated.emit()

        except:
            pass

    def add_cash_exit(self, montant, nature, objet, reference=""):
        """Ajoute une sortie de caisse"""
        if not self.db_manager:
            return

        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                INSERT INTO caisse_sorties (date, nature, objet, reference, montant)
                VALUES (?, ?, ?, ?, ?)
            """, (datetime.now().strftime("%Y-%m-%d"), nature, objet, reference, montant))

            self.db_manager.conn.commit()
            self.load_caisse_data()
            self.caisse_updated.emit()

        except:
            pass

    def handle_cash_payment_sale(self, facture_numero, montant):
        """Gère les paiements en espèces pour les ventes (Entrées)"""
        self.add_cash_entry(
            montant=montant,
            nature="بيع",
            objet=f"بيع بالنقد - فاتورة {facture_numero}",
            reference=facture_numero
        )
        print(f"💰 Entrée de caisse ajoutée: {montant} DH pour la facture {facture_numero}")

    def handle_cash_payment_purchase(self, bon_numero, montant):
        """Gère les paiements en espèces pour les achats (Sorties)"""
        self.add_cash_exit(
            montant=montant,
            nature="شراء",
            objet=f"شراء بالنقد - بون {bon_numero}",
            reference=bon_numero
        )
        print(f"💸 Sortie de caisse ajoutée: {montant} DH pour le bon {bon_numero}")

    def get_cash_balance(self):
        """Retourne le solde actuel de la caisse"""
        try:
            total_entrees = self.calculate_total_entrees()
            total_sorties = self.calculate_total_sorties()
            return total_entrees - total_sorties
        except:
            return 0.0

    def show_entrees_context_menu(self, position):
        """Affiche le menu contextuel pour les entrées"""
        from PySide6.QtWidgets import QMenu
        from PySide6.QtGui import QAction

        item = self.entrees_table.itemAt(position)
        if not item:
            return

        row = item.row()
        # Ne pas afficher le menu pour la ligne total
        if row >= self.entrees_table.rowCount() - 1:
            return

        menu = QMenu(self)

        # Action Modifier
        edit_action = QAction("✏️ Modifier", self)
        edit_action.triggered.connect(lambda: self.edit_entree_row(row))
        menu.addAction(edit_action)

        # Action Supprimer
        delete_action = QAction("🗑️ Supprimer", self)
        delete_action.triggered.connect(lambda: self.delete_entree_row(row))
        menu.addAction(delete_action)

        # Afficher le menu
        menu.exec(self.entrees_table.mapToGlobal(position))

    def show_sorties_context_menu(self, position):
        """Affiche le menu contextuel pour les sorties"""
        from PySide6.QtWidgets import QMenu
        from PySide6.QtGui import QAction

        item = self.sorties_table.itemAt(position)
        if not item:
            return

        row = item.row()
        # Ne pas afficher le menu pour la ligne total
        if row >= self.sorties_table.rowCount() - 1:
            return

        menu = QMenu(self)

        # Action Modifier
        edit_action = QAction("✏️ Modifier", self)
        edit_action.triggered.connect(lambda: self.edit_sortie_row(row))
        menu.addAction(edit_action)

        # Action Supprimer
        delete_action = QAction("🗑️ Supprimer", self)
        delete_action.triggered.connect(lambda: self.delete_sortie_row(row))
        menu.addAction(delete_action)

        # Afficher le menu
        menu.exec(self.sorties_table.mapToGlobal(position))

    def edit_entree_row(self, row):
        """Modifie une ligne d'entrée"""
        QMessageBox.information(self, "Modifier", f"Modification de la ligne {row + 1} des entrées")

    def delete_entree_row(self, row):
        """Supprime une ligne d'entrée"""
        reply = QMessageBox.question(
            self, "Confirmation",
            f"Êtes-vous sûr de vouloir supprimer cette entrée ?",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # Récupérer la référence pour la suppression en base
                reference_item = self.entrees_table.item(row, 3)
                reference = reference_item.text() if reference_item else ""

                # Supprimer de la base de données
                if reference:
                    cursor = self.db_manager.conn.cursor()
                    cursor.execute("DELETE FROM caisse_entrees WHERE reference = ?", (reference,))
                    self.db_manager.conn.commit()

                # Supprimer la ligne du tableau
                self.entrees_table.removeRow(row)

                # Recalculer les totaux
                self.calculate_totals()

                QMessageBox.information(self, "Succès", "Entrée supprimée avec succès")

            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression: {str(e)}")

    def edit_sortie_row(self, row):
        """Modifie une ligne de sortie"""
        QMessageBox.information(self, "Modifier", f"Modification de la ligne {row + 1} des sorties")

    def delete_sortie_row(self, row):
        """Supprime une ligne de sortie"""
        reply = QMessageBox.question(
            self, "Confirmation",
            f"Êtes-vous sûr de vouloir supprimer cette sortie ?",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # Récupérer la référence pour la suppression en base
                reference_item = self.sorties_table.item(row, 3)
                reference = reference_item.text() if reference_item else ""

                # Supprimer de la base de données
                if reference:
                    cursor = self.db_manager.conn.cursor()
                    cursor.execute("DELETE FROM caisse_sorties WHERE reference = ?", (reference,))
                    self.db_manager.conn.commit()

                # Supprimer la ligne du tableau
                self.sorties_table.removeRow(row)

                # Recalculer les totaux
                self.calculate_totals()

                QMessageBox.information(self, "Succès", "Sortie supprimée avec succès")

            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression: {str(e)}")

    def show_add_entree_dialog(self):
        """Affiche la boîte de dialogue pour ajouter une entrée"""
        dialog = AddEntreeDialog(self)
        if dialog.exec() == QDialog.Accepted:
            # Récupérer les données du dialogue
            data = dialog.get_data()

            # Ajouter à la base de données
            try:
                cursor = self.db_manager.conn.cursor()
                cursor.execute("""
                    INSERT INTO caisse_entrees (date, nature, objet, reference, montant)
                    VALUES (?, ?, ?, ?, ?)
                """, (data['date'], data['nature'], data['objet'], data['reference'], data['montant']))

                self.db_manager.conn.commit()

                # Recharger les données
                self.load_caisse_data()

                QMessageBox.information(self, "Succès", "Entrée ajoutée avec succès!")

            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {str(e)}")

    def show_add_sortie_dialog(self):
        """Affiche la boîte de dialogue pour ajouter une sortie"""
        dialog = AddSortieDialog(self)
        if dialog.exec() == QDialog.Accepted:
            # Récupérer les données du dialogue
            data = dialog.get_data()

            # Ajouter à la base de données
            try:
                cursor = self.db_manager.conn.cursor()
                cursor.execute("""
                    INSERT INTO caisse_sorties (date, nature, objet, reference, montant)
                    VALUES (?, ?, ?, ?, ?)
                """, (data['date'], data['nature'], data['objet'], data['reference'], data['montant']))

                self.db_manager.conn.commit()

                # Recharger les données
                self.load_caisse_data()

                QMessageBox.information(self, "Succès", "Sortie ajoutée avec succès!")

            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {str(e)}")


class AddEntreeDialog(QDialog):
    """Dialogue pour ajouter une entrée de caisse"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Ajouter une entrée de caisse")
        self.setModal(True)
        self.resize(400, 300)
        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)

        # Titre
        title = QLabel("💰 Nouvelle entrée de caisse")
        title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #1E3A8A;
                padding: 10px;
                background-color: #E0E7FF;
                border-radius: 6px;
                margin-bottom: 15px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # Formulaire
        form_layout = QFormLayout()

        # Date
        from ..style import create_styled_date_edit
        self.date_input = create_styled_date_edit()
        form_layout.addRow("Date:", self.date_input)

        # Nature
        self.nature_combo = QComboBox()
        self.nature_combo.addItems([
            "بيع",
            "بنك",
            "استرداد",
            "أخرى"
        ])
        form_layout.addRow("Nature:", self.nature_combo)

        # Objet
        self.objet_input = QLineEdit()
        self.objet_input.setPlaceholderText("وصف العملية...")
        form_layout.addRow("Objet:", self.objet_input)

        # Référence
        self.reference_input = QLineEdit()
        self.reference_input.setPlaceholderText("رقم الفاتورة أو المرجع...")
        form_layout.addRow("Référence:", self.reference_input)

        # Montant
        self.montant_input = QDoubleSpinBox()
        self.montant_input.setRange(0.0, 999999.99)
        self.montant_input.setDecimals(2)
        self.montant_input.setSuffix(" DH")
        form_layout.addRow("Montant:", self.montant_input)

        layout.addLayout(form_layout)

        # Boutons
        buttons_layout = QHBoxLayout()

        self.save_btn = QPushButton("💾 Enregistrer")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        self.save_btn.clicked.connect(self.accept)

        self.cancel_btn = QPushButton("❌ Annuler")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)

        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        layout.addLayout(buttons_layout)

    def get_data(self):
        """Retourne les données du formulaire"""
        return {
            'date': self.date_input.date().toString("yyyy-MM-dd"),
            'nature': self.nature_combo.currentText(),
            'objet': self.objet_input.text(),
            'reference': self.reference_input.text(),
            'montant': self.montant_input.value()
        }


class AddSortieDialog(QDialog):
    """Dialogue pour ajouter une sortie de caisse"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Ajouter une sortie de caisse")
        self.setModal(True)
        self.resize(400, 300)
        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)

        # Titre
        title = QLabel("💸 Nouvelle sortie de caisse")
        title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #1E3A8A;
                padding: 10px;
                background-color: #FEE2E2;
                border-radius: 6px;
                margin-bottom: 15px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # Formulaire
        form_layout = QFormLayout()

        # Date
        from ..style import create_styled_date_edit
        self.date_input = create_styled_date_edit()
        form_layout.addRow("Date:", self.date_input)

        # Nature
        self.nature_combo = QComboBox()
        self.nature_combo.addItems([
            "شراء",
            "مصروفات",
            "رواتب",
            "أخرى"
        ])
        form_layout.addRow("Nature:", self.nature_combo)

        # Objet
        self.objet_input = QLineEdit()
        self.objet_input.setPlaceholderText("وصف العملية...")
        form_layout.addRow("Objet:", self.objet_input)

        # Référence
        self.reference_input = QLineEdit()
        self.reference_input.setPlaceholderText("رقم الفاتورة أو المرجع...")
        form_layout.addRow("Référence:", self.reference_input)

        # Montant
        self.montant_input = QDoubleSpinBox()
        self.montant_input.setRange(0.0, 999999.99)
        self.montant_input.setDecimals(2)
        self.montant_input.setSuffix(" DH")
        form_layout.addRow("Montant:", self.montant_input)

        layout.addLayout(form_layout)

        # Boutons
        buttons_layout = QHBoxLayout()

        self.save_btn = QPushButton("💾 Enregistrer")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #EF4444;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #DC2626;
            }
        """)
        self.save_btn.clicked.connect(self.accept)

        self.cancel_btn = QPushButton("❌ Annuler")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)

        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        layout.addLayout(buttons_layout)

    def get_data(self):
        """Retourne les données du formulaire"""
        return {
            'date': self.date_input.date().toString("yyyy-MM-dd"),
            'nature': self.nature_combo.currentText(),
            'objet': self.objet_input.text(),
            'reference': self.reference_input.text(),
            'montant': self.montant_input.value()
        }