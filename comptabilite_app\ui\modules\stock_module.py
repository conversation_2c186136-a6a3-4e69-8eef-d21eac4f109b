from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QTableWidget, QTableWidgetItem,
                              QHeaderView)
from PySide6.QtCore import Qt
from PySide6.QtGui import QColor

class StockModule(QWidget):
    """Module de gestion du stock"""

    def __init__(self, db_manager, signals):
        super().__init__()
        self.db_manager = db_manager
        self.signals = signals
        self.setup_stock_ui()
        self.load_stock_data()

    def setup_stock_ui(self):
        """Configure l'interface utilisateur du stock"""
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # Section de recherche
        self.setup_search_section(main_layout)

        # Tableau du stock
        self.setup_stock_table(main_layout)

        # Section du résumé (Total stock en DH)
        self.setup_summary_section(main_layout)

    def setup_search_section(self, layout):
        """Configure la section de recherche"""
        # Container principal
        main_container = QWidget()
        main_container.setStyleSheet("""
            QWidget {
                background-color: white;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        main_layout = QVBoxLayout(main_container)
        main_layout.setSpacing(20)

        # Section RECHERCHE PRODUITS
        search_container = QWidget()
        search_container.setStyleSheet("""
            QWidget {
                background-color: #5B9BD5;
                border-radius: 8px;
                padding: 15px;
            }
        """)

        search_layout = QVBoxLayout(search_container)

        # Titre de la section
        title_label = QLabel("Etat de stock")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                text-align: center;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        search_layout.addWidget(title_label)

        # Section du bouton Produits
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(30)

        # Bouton Actualisé
        produits_button = QPushButton("Actualisé")
        produits_button.setStyleSheet("""
            QPushButton {
                background-color: #5B9BD5;
                color: white;
                border: 2px solid white;
                padding: 12px 30px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #4A90C2;
            }
        """)
        produits_button.clicked.connect(self.show_produits_list)

        buttons_layout.addStretch()
        buttons_layout.addWidget(produits_button)
        buttons_layout.addStretch()

        search_layout.addLayout(buttons_layout)
        main_layout.addWidget(search_container)

        layout.addWidget(main_container)

    def setup_stock_table(self, layout):
        """Configure le tableau du stock"""
        # Créer le tableau
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(8)
        self.items_table.setHorizontalHeaderLabels([
            "Désignation de l'article", "Unité", "Stock initial", "Entrées",
            "Sorties", "Stock final", "Stock EN Dh", "Date de mise à jour"
        ])

        # Configuration des colonnes
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Désignation
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Unité
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Stock initial
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Entrées
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Sorties
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Stock final
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Stock EN DH
        header.setSectionResizeMode(7, QHeaderView.Stretch)  # Date

        # Style du tableau
        self.items_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #5B9BD5;
                border-radius: 8px;
                background-color: white;
                gridline-color: #5B9BD5;
                font-size: 14px;
                margin: 10px;
                selection-background-color: #E3F2FD;
            }
            QHeaderView::section {
                background-color: #5B9BD5;
                color: white;
                padding: 15px 10px;
                font-weight: bold;
                border: 1px solid #4A90C2;
                font-size: 14px;
                text-align: center;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border: 1px solid #5B9BD5;
                text-align: center;
            }
            QTableWidget::item:selected {
                background-color: #E3F2FD;
                color: #1976D2;
            }
            QTableWidget::item:hover {
                background-color: #F0F8FF;
            }
        """)

        self.items_table.setShowGrid(True)
        self.items_table.setAlternatingRowColors(True)

        # جعل الجدول قابل للتعديل
        self.items_table.setEditTriggers(QTableWidget.DoubleClicked | QTableWidget.EditKeyPressed)

        # ربط إشارات التعديل
        self.items_table.itemChanged.connect(self.on_item_changed)

        layout.addWidget(self.items_table)

    def setup_summary_section(self, layout):
        """Configure la section du résumé"""
        summary_container = QWidget()
        summary_container.setStyleSheet("""
            QWidget {
                background-color: #5B9BD5;
                border-radius: 8px;
                padding: 15px;
            }
        """)

        summary_layout = QVBoxLayout(summary_container)

        # Titre
        title_label = QLabel("Total stock en DH")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                text-align: center;
                margin-bottom: 10px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        summary_layout.addWidget(title_label)

        # Valeur du total
        value_layout = QHBoxLayout()
        self.total_stock_label = QLabel("0.00 DH")
        self.total_stock_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 24px;
                color: #333;
                background-color: white;
                padding: 15px 30px;
                border-radius: 8px;
                text-align: center;
                min-width: 200px;
            }
        """)
        self.total_stock_label.setAlignment(Qt.AlignCenter)

        value_layout.addStretch()
        value_layout.addWidget(self.total_stock_label)
        value_layout.addStretch()

        summary_layout.addLayout(value_layout)
        layout.addWidget(summary_container)

    def load_stock_data(self):
        """Charge les données du stock"""
        print("🔄 Chargement des données du stock...")
        try:
            # Vérifier que le tableau existe
            if not hasattr(self, 'items_table') or self.items_table is None:
                print("❌ Tableau non initialisé!")
                return

            print(f"📊 Tableau trouvé avec {self.items_table.columnCount()} colonnes")
            self.items_table.setRowCount(0)

            # Ajouter les données d'exemple fixes
            sample_data = [
                ("LAMPE", "U", 10, 100, 30, 80, "= STOCK FINAL *\nPRIX ACHAT", "آخر تاريخ للبيع"),
                ("CAMERA", "U", 5, 5, 10, 0, "", "")
            ]

            print(f"📝 Ajout de {len(sample_data)} lignes de données...")

            for row_num, (designation, unite, stock_initial, entrees, sorties, stock_final, stock_dh, date_str) in enumerate(sample_data):
                self.items_table.insertRow(row_num)
                print(f"   Ligne {row_num}: {designation}")

                # Remplir les colonnes avec alignement centré وجعلها قابلة للتعديل
                designation_item = QTableWidgetItem(designation)
                designation_item.setTextAlignment(Qt.AlignCenter)
                designation_item.setFlags(designation_item.flags() | Qt.ItemIsEditable)
                self.items_table.setItem(row_num, 0, designation_item)

                unite_item = QTableWidgetItem(unite)
                unite_item.setTextAlignment(Qt.AlignCenter)
                unite_item.setFlags(unite_item.flags() | Qt.ItemIsEditable)
                self.items_table.setItem(row_num, 1, unite_item)

                stock_initial_item = QTableWidgetItem(str(stock_initial))
                stock_initial_item.setTextAlignment(Qt.AlignCenter)
                stock_initial_item.setFlags(stock_initial_item.flags() | Qt.ItemIsEditable)
                self.items_table.setItem(row_num, 2, stock_initial_item)

                entrees_item = QTableWidgetItem(str(entrees))
                entrees_item.setTextAlignment(Qt.AlignCenter)
                entrees_item.setFlags(entrees_item.flags() | Qt.ItemIsEditable)
                self.items_table.setItem(row_num, 3, entrees_item)

                sorties_item = QTableWidgetItem(str(sorties))
                sorties_item.setTextAlignment(Qt.AlignCenter)
                sorties_item.setFlags(sorties_item.flags() | Qt.ItemIsEditable)
                self.items_table.setItem(row_num, 4, sorties_item)

                stock_final_item = QTableWidgetItem(str(stock_final))
                stock_final_item.setTextAlignment(Qt.AlignCenter)
                stock_final_item.setFlags(stock_final_item.flags() | Qt.ItemIsEditable)
                self.items_table.setItem(row_num, 5, stock_final_item)

                # Stock EN Dh
                stock_dh_item = QTableWidgetItem(stock_dh)
                stock_dh_item.setTextAlignment(Qt.AlignCenter)
                stock_dh_item.setFlags(stock_dh_item.flags() | Qt.ItemIsEditable)
                self.items_table.setItem(row_num, 6, stock_dh_item)

                # Date
                date_item = QTableWidgetItem(date_str)
                date_item.setTextAlignment(Qt.AlignCenter)
                date_item.setFlags(date_item.flags() | Qt.ItemIsEditable)
                self.items_table.setItem(row_num, 7, date_item)

            print(f"✅ Données chargées avec succès! Lignes: {self.items_table.rowCount()}")

        except Exception as e:
            print(f"❌ Erreur lors du chargement du stock: {str(e)}")
            import traceback
            traceback.print_exc()

            # عرض البيانات الافتراضية في حالة الخطأ
            try:
                self.items_table.setRowCount(2)

                # الصف الأول
                self.items_table.setItem(0, 0, QTableWidgetItem("LAMPE"))
                self.items_table.setItem(0, 1, QTableWidgetItem("U"))
                self.items_table.setItem(0, 2, QTableWidgetItem("10"))
                self.items_table.setItem(0, 3, QTableWidgetItem("100"))
                self.items_table.setItem(0, 4, QTableWidgetItem("30"))
                self.items_table.setItem(0, 5, QTableWidgetItem("80"))

                stock_dh_item = QTableWidgetItem("= STOCK FINAL *\nPRIX ACHAT")
                stock_dh_item.setTextAlignment(Qt.AlignCenter)
                self.items_table.setItem(0, 6, stock_dh_item)

                date_item = QTableWidgetItem("آخر تاريخ للبيع")
                date_item.setTextAlignment(Qt.AlignCenter)
                self.items_table.setItem(0, 7, date_item)

                # الصف الثاني
                self.items_table.setItem(1, 0, QTableWidgetItem("CAMERA"))
                self.items_table.setItem(1, 1, QTableWidgetItem("U"))
                self.items_table.setItem(1, 2, QTableWidgetItem("5"))
                self.items_table.setItem(1, 3, QTableWidgetItem("5"))
                self.items_table.setItem(1, 4, QTableWidgetItem("10"))
                self.items_table.setItem(1, 5, QTableWidgetItem("0"))
                self.items_table.setItem(1, 6, QTableWidgetItem(""))
                self.items_table.setItem(1, 7, QTableWidgetItem(""))

                print("🔧 البيانات الافتراضية تم تحميلها")
            except Exception as e2:
                print(f"❌ خطأ في تحميل البيانات الافتراضية: {str(e2)}")

    def apply_stock_color(self, item, value):
        """Applique une couleur selon la valeur du stock"""
        try:
            if value < 0:
                # Rouge pour les valeurs négatives
                item.setForeground(QColor("#DC2626"))
                item.setBackground(QColor("#FEF2F2"))
            elif value > 0:
                # Vert pour les valeurs positives
                item.setForeground(QColor("#059669"))
                item.setBackground(QColor("#F0FDF4"))
            else:
                # Gris pour zéro
                item.setForeground(QColor("#6B7280"))
                item.setBackground(QColor("#F9FAFB"))
        except:
            pass

    def apply_total_color(self, total_value):
        """Applique une couleur au total selon sa valeur"""
        if total_value > 0:
            color = "#059669"
            bg_color = "#F0FDF4"
        elif total_value < 0:
            color = "#DC2626"
            bg_color = "#FEF2F2"
        else:
            color = "#6B7280"
            bg_color = "#F9FAFB"

        self.total_stock_label.setStyleSheet(f"""
            QLabel {{
                font-weight: bold;
                font-size: 24px;
                color: {color};
                background-color: {bg_color};
                padding: 15px 30px;
                border-radius: 8px;
                border: 2px solid {color};
                text-align: center;
                min-width: 200px;
            }}
        """)





    def show_produits_list(self):
        """Affiche la liste des produits sauvegardés dans le tableau Stock"""
        print("🔄 Chargement des produits dans le tableau Stock...")

        # Garder les mêmes en-têtes du tableau Stock
        # Pas besoin de changer les en-têtes

        # Charger les données des produits dans le tableau Stock
        self.load_produits_in_stock_table()

    def load_produits_in_stock_table(self):
        """Charge les données des produits dans le tableau Stock avec le format Stock"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Requête pour récupérer tous les produits avec les mouvements de stock
            query = """
                SELECT
                    p.id,
                    p.designation,
                    p.unite,
                    p.stock as stock_actuel,
                    p.prix_achat,
                    COALESCE(SUM(CASE WHEN m.type_mouvement = 'entree' THEN m.quantite ELSE 0 END), 0) as total_entrees,
                    COALESCE(SUM(CASE WHEN m.type_mouvement = 'sortie' THEN m.quantite ELSE 0 END), 0) as total_sorties
                FROM produits p
                LEFT JOIN mouvements_stock m ON p.id = m.produit_id
                GROUP BY p.id, p.designation, p.unite, p.stock, p.prix_achat
                ORDER BY p.designation
            """

            cursor.execute(query)
            produits = cursor.fetchall()

            print(f"📦 Trouvé {len(produits)} produits dans la base de données")

            # Vider le tableau
            self.items_table.setRowCount(0)
            total_stock_value = 0

            for row_num, produit in enumerate(produits):
                self.items_table.insertRow(row_num)

                # Récupérer les données
                designation = str(produit['designation'] or '')
                unite = str(produit['unite'] or '')
                stock_actuel = produit['stock_actuel'] or 0
                prix_achat = produit['prix_achat'] or 0
                total_entrees = produit['total_entrees'] or 0
                total_sorties = produit['total_sorties'] or 0

                # Le stock dans la DB est le stock initial (stock de départ)
                # Calculer le stock final: stock_initial + entrées - sorties
                stock_initial = stock_actuel
                stock_final = stock_initial + total_entrees - total_sorties
                if stock_final < 0:
                    stock_final = 0  # Éviter les valeurs négatives

                print(f"📊 {designation}: Initial={stock_initial}, Entrées={total_entrees}, Sorties={total_sorties}, Final={stock_final}")

                # Désignation de l'article - قابل للتعديل
                designation_item = QTableWidgetItem(designation)
                designation_item.setTextAlignment(Qt.AlignCenter)
                designation_item.setFlags(designation_item.flags() | Qt.ItemIsEditable)
                self.items_table.setItem(row_num, 0, designation_item)

                # Unité - قابل للتعديل
                unite_item = QTableWidgetItem(unite)
                unite_item.setTextAlignment(Qt.AlignCenter)
                unite_item.setFlags(unite_item.flags() | Qt.ItemIsEditable)
                self.items_table.setItem(row_num, 1, unite_item)

                # Stock initial - قابل للتعديل
                stock_initial_item = QTableWidgetItem(str(stock_initial))
                stock_initial_item.setTextAlignment(Qt.AlignCenter)
                stock_initial_item.setFlags(stock_initial_item.flags() | Qt.ItemIsEditable)
                self.items_table.setItem(row_num, 2, stock_initial_item)

                # Entrées - قابل للتعديل
                entrees_item = QTableWidgetItem(str(total_entrees))
                entrees_item.setTextAlignment(Qt.AlignCenter)
                entrees_item.setFlags(entrees_item.flags() | Qt.ItemIsEditable)
                if total_entrees > 0:
                    entrees_item.setBackground(QColor("#F0FDF4"))
                    entrees_item.setForeground(QColor("#059669"))
                self.items_table.setItem(row_num, 3, entrees_item)

                # Sorties - قابل للتعديل
                sorties_item = QTableWidgetItem(str(total_sorties))
                sorties_item.setTextAlignment(Qt.AlignCenter)
                sorties_item.setFlags(sorties_item.flags() | Qt.ItemIsEditable)
                if total_sorties > 0:
                    sorties_item.setBackground(QColor("#FEF2F2"))
                    sorties_item.setForeground(QColor("#DC2626"))
                self.items_table.setItem(row_num, 4, sorties_item)

                # Stock final - قابل للتعديل
                stock_final_item = QTableWidgetItem(str(stock_final))
                stock_final_item.setTextAlignment(Qt.AlignCenter)
                stock_final_item.setFlags(stock_final_item.flags() | Qt.ItemIsEditable)

                # Colorer selon le stock
                if stock_final <= 0:
                    stock_final_item.setBackground(QColor("#FEF2F2"))
                    stock_final_item.setForeground(QColor("#DC2626"))
                elif stock_final < 10:
                    stock_final_item.setBackground(QColor("#FEF3C7"))
                    stock_final_item.setForeground(QColor("#D97706"))
                else:
                    stock_final_item.setBackground(QColor("#F0FDF4"))
                    stock_final_item.setForeground(QColor("#059669"))

                self.items_table.setItem(row_num, 5, stock_final_item)

                # Stock EN Dh - قابل للتعديل
                stock_en_dh = stock_final * prix_achat
                total_stock_value += stock_en_dh
                stock_dh_item = QTableWidgetItem(f"{stock_en_dh:.2f} DH")
                stock_dh_item.setTextAlignment(Qt.AlignCenter)
                stock_dh_item.setFlags(stock_dh_item.flags() | Qt.ItemIsEditable)
                self.items_table.setItem(row_num, 6, stock_dh_item)

                # Date de mise à jour (dernière date de mouvement)
                cursor.execute("""
                    SELECT MAX(date_mouvement) as derniere_date
                    FROM mouvements_stock
                    WHERE produit_id = ?
                """, (produit['id'],))
                derniere_date_result = cursor.fetchone()

                date_str = ''
                if derniere_date_result and derniere_date_result['derniere_date']:
                    try:
                        from datetime import datetime
                        date_obj = datetime.strptime(derniere_date_result['derniere_date'], '%Y-%m-%d %H:%M:%S')
                        date_str = date_obj.strftime('%d/%m/%Y %H:%M')
                    except:
                        date_str = str(derniere_date_result['derniere_date'])
                else:
                    # Si pas de mouvement, afficher la date actuelle
                    from datetime import datetime
                    date_str = datetime.now().strftime('%d/%m/%Y')

                date_item = QTableWidgetItem(date_str)
                date_item.setTextAlignment(Qt.AlignCenter)
                date_item.setFlags(date_item.flags() | Qt.ItemIsEditable)
                self.items_table.setItem(row_num, 7, date_item)

            # Mettre à jour le total du stock
            self.total_stock_label.setText(f"{total_stock_value:.2f} DH")
            self.apply_total_color(total_stock_value)

            print(f"✅ {len(produits)} produits chargés dans le tableau Stock")
            print(f"💰 Valeur totale du stock: {total_stock_value:.2f} DH")

        except Exception as e:
            print(f"❌ Erreur lors du chargement des produits: {str(e)}")
            import traceback
            traceback.print_exc()

            # Afficher un message d'erreur dans le tableau
            self.items_table.setRowCount(1)
            error_item = QTableWidgetItem("Erreur lors du chargement des données")
            error_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(0, 0, error_item)

    def load_produits_in_main_table(self):
        """Charge les données des produits dans le tableau principal"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Requête pour récupérer tous les produits
            query = """
                SELECT
                    id,
                    code_produit,
                    designation,
                    unite,
                    prix_achat,
                    prix_vente,
                    stock,
                    date_creation
                FROM produits
                ORDER BY designation
            """

            cursor.execute(query)
            produits = cursor.fetchall()

            print(f"📦 Trouvé {len(produits)} produits dans la base de données")

            # Vider le tableau
            self.items_table.setRowCount(0)

            for row_num, produit in enumerate(produits):
                self.items_table.insertRow(row_num)

                # Code produit
                code_item = QTableWidgetItem(str(produit['code_produit'] or ''))
                code_item.setTextAlignment(Qt.AlignCenter)
                self.items_table.setItem(row_num, 0, code_item)

                # Désignation
                designation_item = QTableWidgetItem(str(produit['designation'] or ''))
                designation_item.setTextAlignment(Qt.AlignCenter)
                self.items_table.setItem(row_num, 1, designation_item)

                # Unité
                unite_item = QTableWidgetItem(str(produit['unite'] or ''))
                unite_item.setTextAlignment(Qt.AlignCenter)
                self.items_table.setItem(row_num, 2, unite_item)

                # Prix d'achat
                prix_achat = produit['prix_achat'] or 0
                prix_achat_item = QTableWidgetItem(f"{prix_achat:.2f} DH")
                prix_achat_item.setTextAlignment(Qt.AlignCenter)
                self.items_table.setItem(row_num, 3, prix_achat_item)

                # Prix de vente
                prix_vente = produit['prix_vente'] or 0
                prix_vente_item = QTableWidgetItem(f"{prix_vente:.2f} DH")
                prix_vente_item.setTextAlignment(Qt.AlignCenter)
                self.items_table.setItem(row_num, 4, prix_vente_item)

                # Stock
                stock = produit['stock'] or 0
                stock_item = QTableWidgetItem(str(stock))
                stock_item.setTextAlignment(Qt.AlignCenter)

                # Colorer selon le stock
                if stock <= 0:
                    stock_item.setBackground(QColor("#FEF2F2"))
                    stock_item.setForeground(QColor("#DC2626"))
                elif stock < 10:
                    stock_item.setBackground(QColor("#FEF3C7"))
                    stock_item.setForeground(QColor("#D97706"))
                else:
                    stock_item.setBackground(QColor("#F0FDF4"))
                    stock_item.setForeground(QColor("#059669"))

                self.items_table.setItem(row_num, 5, stock_item)

                # Date de création
                date_creation = produit['date_creation'] or ''
                if date_creation:
                    try:
                        from datetime import datetime
                        date_obj = datetime.strptime(date_creation, '%Y-%m-%d')
                        date_str = date_obj.strftime('%d/%m/%Y')
                    except:
                        date_str = str(date_creation)
                else:
                    date_str = ''

                date_item = QTableWidgetItem(date_str)
                date_item.setTextAlignment(Qt.AlignCenter)
                self.items_table.setItem(row_num, 6, date_item)

                # Actions (vide pour l'instant)
                action_item = QTableWidgetItem("")
                action_item.setTextAlignment(Qt.AlignCenter)
                self.items_table.setItem(row_num, 7, action_item)

            print(f"✅ {len(produits)} produits chargés dans le tableau principal")

        except Exception as e:
            print(f"❌ Erreur lors du chargement des produits: {str(e)}")
            import traceback
            traceback.print_exc()

            # Afficher un message d'erreur dans le tableau
            self.items_table.setRowCount(1)
            error_item = QTableWidgetItem("Erreur lors du chargement des données")
            error_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(0, 1, error_item)

    def load_produits_data(self, table):
        """Charge les données des produits dans le tableau"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Requête pour récupérer tous les produits
            query = """
                SELECT
                    code_produit,
                    designation,
                    unite,
                    prix_achat,
                    prix_vente,
                    stock
                FROM produits
                ORDER BY designation
            """

            cursor.execute(query)
            produits = cursor.fetchall()

            print(f"📦 Trouvé {len(produits)} produits dans la base de données")

            table.setRowCount(len(produits))

            for row_num, produit in enumerate(produits):
                # Code produit
                code_item = QTableWidgetItem(str(produit['code_produit'] or ''))
                code_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row_num, 0, code_item)

                # Désignation
                designation_item = QTableWidgetItem(str(produit['designation'] or ''))
                designation_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row_num, 1, designation_item)

                # Unité
                unite_item = QTableWidgetItem(str(produit['unite'] or ''))
                unite_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row_num, 2, unite_item)

                # Prix d'achat
                prix_achat = produit['prix_achat'] or 0
                prix_achat_item = QTableWidgetItem(f"{prix_achat:.2f} DH")
                prix_achat_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row_num, 3, prix_achat_item)

                # Prix de vente
                prix_vente = produit['prix_vente'] or 0
                prix_vente_item = QTableWidgetItem(f"{prix_vente:.2f} DH")
                prix_vente_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row_num, 4, prix_vente_item)

                # Stock
                stock = produit['stock'] or 0
                stock_item = QTableWidgetItem(str(stock))
                stock_item.setTextAlignment(Qt.AlignCenter)

                # Colorer selon le stock
                if stock <= 0:
                    stock_item.setBackground(QColor("#FEF2F2"))
                    stock_item.setForeground(QColor("#DC2626"))
                elif stock < 10:
                    stock_item.setBackground(QColor("#FEF3C7"))
                    stock_item.setForeground(QColor("#D97706"))
                else:
                    stock_item.setBackground(QColor("#F0FDF4"))
                    stock_item.setForeground(QColor("#059669"))

                table.setItem(row_num, 5, stock_item)

            print(f"✅ {len(produits)} produits chargés dans le tableau")

        except Exception as e:
            print(f"❌ Erreur lors du chargement des produits: {str(e)}")
            import traceback
            traceback.print_exc()

            # Afficher un message d'erreur dans le tableau
            table.setRowCount(1)
            error_item = QTableWidgetItem("Erreur lors du chargement des données")
            error_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(0, 1, error_item)



    def on_item_changed(self, item):
        """معالجة تغيير الخلايا وإعادة حساب القيم"""
        if item is None:
            return

        row = item.row()
        col = item.column()

        # إعادة حساب القيم المعتمدة على التغييرات
        self.recalculate_row_values(row)

        # تحديث المجموع الكلي
        self.update_total_stock()

    def recalculate_row_values(self, row):
        """إعادة حساب قيم السطر"""
        try:
            # الحصول على القيم
            stock_initial_item = self.items_table.item(row, 2)
            entrees_item = self.items_table.item(row, 3)
            sorties_item = self.items_table.item(row, 4)

            if stock_initial_item and entrees_item and sorties_item:
                stock_initial = self.extract_numeric_value(stock_initial_item.text())
                entrees = self.extract_numeric_value(entrees_item.text())
                sorties = self.extract_numeric_value(sorties_item.text())

                # حساب المخزون النهائي
                stock_final = stock_initial + entrees - sorties
                if stock_final < 0:
                    stock_final = 0

                # تحديث المخزون النهائي
                stock_final_item = self.items_table.item(row, 5)
                if stock_final_item:
                    # فصل الإشارة مؤقتاً لتجنب التكرار
                    self.items_table.itemChanged.disconnect(self.on_item_changed)
                    stock_final_item.setText(str(stock_final))
                    self.apply_stock_color(stock_final_item, stock_final)
                    self.items_table.itemChanged.connect(self.on_item_changed)

                # حساب قيمة المخزون (افتراض سعر شراء = 100)
                prix_achat = 100  # يمكن تعديله حسب الحاجة
                stock_en_dh = stock_final * prix_achat

                # تحديث قيمة المخزون
                stock_dh_item = self.items_table.item(row, 6)
                if stock_dh_item:
                    self.items_table.itemChanged.disconnect(self.on_item_changed)
                    stock_dh_item.setText(f"{stock_en_dh:.2f} DH")
                    self.items_table.itemChanged.connect(self.on_item_changed)

        except (ValueError, TypeError):
            pass

    def extract_numeric_value(self, text):
        """استخراج القيمة الرقمية من النص"""
        if not text:
            return 0.0

        # إزالة الأحرف غير الرقمية عدا النقطة والسالب
        clean_text = ''.join(c for c in str(text) if c.isdigit() or c in '.-')

        try:
            return float(clean_text) if clean_text else 0.0
        except ValueError:
            return 0.0

    def update_total_stock(self):
        """تحديث المجموع الكلي للمخزون"""
        total_value = 0.0

        for row in range(self.items_table.rowCount()):
            stock_dh_item = self.items_table.item(row, 6)
            if stock_dh_item:
                value = self.extract_numeric_value(stock_dh_item.text())
                total_value += value

        self.total_stock_label.setText(f"{total_value:.2f} DH")
        self.apply_total_color(total_value)

    def show_add_dialog(self):
        """دالة إضافة (غير مستخدمة في هذه الواجهة)"""
        pass

    def show_edit_dialog(self, item_id):
        """دالة تعديل (غير مستخدمة في هذه الواجهة)"""
        pass

    def refresh_stock_data(self):
        """Actualise les données du stock"""
        print("🔄 Actualisation des données du stock...")
        self.load_produits_in_stock_table()
