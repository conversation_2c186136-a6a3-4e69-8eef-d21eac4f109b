from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                              QLabel, QLineEdit, QTextEdit, QPushButton, QMessageBox)
from PySide6.QtCore import Qt, Signal


class FamilleDialog(QDialog):
    """Boîte de dialogue pour ajouter une nouvelle famille de produit"""

    famille_added = Signal(int, str)  # Signal émis avec l'ID et le nom de la famille ajoutée

    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setWindowTitle("Ajouter une famille de produit")
        self.setModal(True)
        self.setMinimumWidth(400)
        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title = QLabel("Nouvelle famille de produit")
        title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #1A56DB;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)

        # Formulaire
        form_layout = QFormLayout()
        form_layout.setSpacing(15)

        # Nom de la famille (obligatoire)
        self.nom_input = QLineEdit()
        form_layout.addRow("Nom de la famille *:", self.nom_input)

        # Description (optionnelle)
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(80)
        self.description_input.setPlaceholderText("Description optionnelle de la famille...")
        form_layout.addRow("Description:", self.description_input)

        layout.addLayout(form_layout)

        # Boutons
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()

        cancel_btn = QPushButton("Annuler")
        cancel_btn.clicked.connect(self.reject)

        save_btn = QPushButton("Ajouter")
        save_btn.clicked.connect(self.save_famille)
        save_btn.setDefault(True)

        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)

        layout.addLayout(buttons_layout)

        # Style des boutons
        self.setStyleSheet("""
            QLineEdit, QTextEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                min-height: 20px;
            }
            QPushButton {
                background-color: #1E3A8A;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
            QPushButton:pressed {
                background-color: #1E40AF;
            }
        """)

    def save_famille(self):
        """Sauvegarde la nouvelle famille"""
        nom = self.nom_input.text().strip()

        if not nom:
            QMessageBox.warning(self, "Erreur", "Le nom de la famille est obligatoire.")
            self.nom_input.setFocus()
            return

        description = self.description_input.toPlainText().strip()
        if not description:
            description = None

        try:
            famille_id = self.db_manager.ajouter_famille_produit(nom, description)
            QMessageBox.information(self, "Succès", f"Famille '{nom}' ajoutée avec succès.")

            # Émettre le signal avec l'ID et le nom de la famille
            self.famille_added.emit(famille_id, nom)

            self.accept()

        except ValueError as e:
            QMessageBox.warning(self, "Erreur", str(e))
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout de la famille: {e}")
