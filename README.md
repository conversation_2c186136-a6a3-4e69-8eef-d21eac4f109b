# 🏢 Système de Comptabilité Avancé

Une application de bureau moderne et complète en français pour la gestion comptable d'entreprise, développée en Python avec **CustomTkinter** et utilisant SQLite comme base de données locale.

## ✨ Caractéristiques

- 🎨 **Interface moderne** avec CustomTkinter (Dark Mode)
- 🇫🇷 **Entièrement en français**
- 📊 **12 modules complets** de gestion
- 🗄️ **Base de données SQLite intégrée**
- 📈 **Statistiques en temps réel**
- 🔍 **Recherche avancée**
- 📋 **Génération de rapports**
- 💾 **Sauvegarde automatique**

## 🚀 Modules Disponibles

### 📊 **Tableau de Bord**
- Vue d'ensemble avec statistiques en temps réel
- Graphiques des dernières factures
- Indicateurs de performance

### 👥 **Gestion des Clients**
- ✅ Ajouter, modifier, supprimer des clients
- 🔍 Recherche et filtrage avancés
- 📋 Historique des transactions
- 📄 Informations complètes (ICE, IF, adresse)

### 🏢 **Gestion des Fournisseurs**
- ✅ Base de données complète des fournisseurs
- 🔍 Recherche et gestion avancées
- 📊 Suivi des commandes et livraisons

### 📦 **Gestion des Produits**
- ✅ Catalogue produits avec codes et désignations
- 💰 Gestion des prix d'achat et de vente
- 📈 Suivi du stock en temps réel
- ⚠️ Alertes de stock faible

### 📄 **Gestion des Factures**
- ✅ Création de factures professionnelles
- 🧮 Calcul automatique de la TVA
- 🖨️ Impression et export PDF
- 📊 Suivi des paiements

### 📋 **Bons de Commande**
- ✅ Création et gestion des bons de commande
- 🔄 Conversion automatique en factures
- 📦 Suivi des livraisons

### 🚚 **Bons de Livraison**
- ✅ Gestion des livraisons
- 📅 Planification et suivi
- 🖨️ Impression des bons de livraison

### 📊 **Gestion du Stock**
- ✅ Inventaire en temps réel
- 💰 Valorisation du stock
- 📈 Mouvements de stock
- 🔍 Recherche et filtrage

### 💰 **Gestion de la Caisse**
- ✅ Suivi des entrées et sorties
- 💳 Gestion des virements
- 📊 Solde en temps réel
- 📋 Historique des mouvements

### 🧾 **Gestion de la TVA**
- ✅ Calcul automatique de la TVA
- 📅 Déclarations périodiques
- 📊 TVA collectée et déductible
- 🖨️ Impression des déclarations

### 📈 **Rapports et Statistiques**
- 📊 Rapport des ventes
- 🛒 Rapport des achats
- 📦 Rapport de stock
- 💰 Rapport financier
- 🧾 Rapport TVA
- 👥 Rapport clients
- 📅 Programmation automatique

### � **Gestion des Marchés**
- ✅ Gestion complète des contrats et appels d'offres
- 📋 Informations générales détaillées (14 champs)
- 💰 Bordereau de prix avec calculs automatiques
- 🚛 Gestion des frais de transport et autres
- 🧮 Calculs automatiques des marges et totaux
- 📊 Suivi des garanties et échéances
- 🖨️ Génération de devis professionnels

### �🔍 **Recherche Avancée**
- 🔍 Recherche globale multi-critères
- 🎯 Filtres par catégories
- 📊 Résultats détaillés
- 💾 Sauvegarde des recherches

## 💻 Prérequis

- **Python 3.8+** ou supérieur
- **CustomTkinter** (interface moderne)
- **SQLite3** (inclus avec Python)
- **ReportLab** (génération de PDF)

## 🚀 Installation

### 1️⃣ **Télécharger le projet**
```bash
git clone https://github.com/votre-utilisateur/systeme-comptabilite.git
cd systeme-comptabilite
```

### 2️⃣ **Créer un environnement virtuel**
```bash
python -m venv .venv

# Windows
.venv\Scripts\activate

# Linux/Mac
source .venv/bin/activate
```

### 3️⃣ **Installer les dépendances**
```bash
pip install -r requirements.txt
```

## 🎯 Utilisation

### **Lancement rapide**
```bash
python run_app.py
```

### **Première utilisation**
1. 🚀 Lancez l'application
2. 📊 Le tableau de bord s'affiche avec les statistiques
3. 👥 Ajoutez vos premiers clients via le menu "Clients"
4. 📦 Créez votre catalogue produits
5. 📄 Commencez à créer vos factures!

## 📁 Structure du Projet

```
📂 systeme-comptabilite/
├── 🚀 run_app.py                 # Fichier principal (CustomTkinter)
├── 📄 requirements.txt          # Dépendances
├── 📄 README.md                # Documentation
├── 📂 database/                # Base de données SQLite
│   └── 🗄️ accounting.db
├── 📂 comptabilite_app/       # Application principale
│   ├── 📂 database/            # Gestion BDD
│   │   ├── 🐍 db_manager.py
│   │   └── 🐍 migrations.py
│   ├── 📂 ui/                  # Interface utilisateur
│   │   ├── 📂 modules/         # Modules métier
│   │   │   ├── 👥 clients.py
│   │   │   ├── 🏢 fournisseurs.py
│   │   │   ├── 📦 produits.py
│   │   │   ├── 📄 factures.py
│   │   │   ├── 📋 commandes.py
│   │   │   ├── 🚚 livraisons.py
│   │   │   ├── 📊 stock.py
│   │   │   ├── 💰 caisse.py
│   │   │   ├── 🧾 tva.py
│   │   │   ├── 📈 rapports.py
│   │   │   └── 🔍 recherche.py
│   │   └── 🖥️ main_window_simple.py
│   ├── 📂 utils/               # Utilitaires
│   │   ├── 📄 pdf_generator.py
│   │   └── 📊 excel_export.py
│   └── 🚀 main.py              # Point d'entrée
├── 📂 assets/                  # Ressources
├── 📂 translations/           # Traductions
└── 📂 documents/              # Documentation
```

## 📊 Fonctionnalités Techniques

### 🎨 **Interface Utilisateur**
- **CustomTkinter** pour une interface moderne
- **Dark Mode** par défaut
- **Design responsive** et intuitif
- **Navigation latérale** avec 12 modules

### 🗄️ **Base de Données**
- **SQLite** intégrée (aucune configuration requise)
- **Création automatique** des tables
- **Données d'exemple** incluses
- **Sauvegarde automatique** de toutes les opérations

### 📊 **Statistiques en Temps Réel**
- **Tableau de bord** avec indicateurs clés
- **Alertes de stock faible** automatiques
- **Calculs automatiques** (TVA, totaux, valorisation)
- **Graphiques** et visualisations

## 🔧 Développement

### **Architecture Modulaire**
Chaque module est indépendant et peut être développé séparément :

- 👥 **Module Clients** - Gestion complète des clients
- 🏢 **Module Fournisseurs** - Base fournisseurs
- 📦 **Module Produits** - Catalogue et stock
- 📄 **Module Factures** - Facturation complète
- 📋 **Module Commandes** - Gestion des commandes
- 🚚 **Module Livraisons** - Suivi des livraisons
- 📊 **Module Stock** - Inventaire et mouvements
- 💰 **Module Caisse** - Trésorerie
- 🧾 **Module TVA** - Gestion fiscale
- 📈 **Module Rapports** - Business Intelligence
- 🔍 **Module Recherche** - Recherche avancée

### **Extensibilité**
```python
# Ajouter un nouveau module
class NouveauModule:
    def __init__(self, parent):
        self.parent = parent
        self.create_interface()

    def create_interface(self):
        # Votre interface ici
        pass
```

### **Tests et Qualité**
```bash
# Exécuter les tests (si disponibles)
python -m pytest tests/

# Vérifier la qualité du code
flake8 .
```

## 📊 Captures d'Écran

### 📊 Tableau de Bord
![Tableau de Bord](screenshots/dashboard.png)

### 👥 Gestion des Clients
![Clients](screenshots/clients.png)

### 📄 Gestion des Factures
![Factures](screenshots/factures.png)

## 🤝 Contribution

Les contributions sont les bienvenues ! Pour contribuer :

1. 🍴 **Fork** le projet
2. 🌱 Créez votre **branche de fonctionnalité** (`git checkout -b feature/AmazingFeature`)
3. 📝 **Committez** vos changements (`git commit -m 'Add some AmazingFeature'`)
4. 🚀 **Push** vers la branche (`git push origin feature/AmazingFeature`)
5. 📫 Ouvrez une **Pull Request**

## 📞 Support

- 💬 **Issues** : [GitHub Issues](https://github.com/votre-utilisateur/systeme-comptabilite/issues)
- 📫 **Email** : <EMAIL>
- 📚 **Documentation** : [Wiki](https://github.com/votre-utilisateur/systeme-comptabilite/wiki)

## 📈 Roadmap

- [ ] 📱 **Version mobile** (Android/iOS)
- [ ] ☁️ **Version cloud** avec synchronisation
- [ ] 🔗 **API REST** pour intégrations
- [ ] 📊 **Tableaux de bord avancés**
- [ ] 📲 **Notifications push**
- [ ] 🌍 **Multi-langues** (Anglais, Arabe)
- [ ] 📊 **Analytics avancés**
- [ ] 🔒 **Authentification multi-utilisateurs**

## 🏆 Remerciements

- **CustomTkinter** - Pour l'interface moderne
- **SQLite** - Pour la base de données fiable
- **Python Community** - Pour l'écosystème incroyable

## 📄 Licence

Ce projet est sous **licence MIT**. Voir le fichier [LICENSE](LICENSE) pour plus de détails.

---

<div align="center">

**🏢 Système de Comptabilité Avancé**

*Une solution complète pour la gestion comptable d'entreprise*

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![CustomTkinter](https://img.shields.io/badge/CustomTkinter-5.2+-green.svg)](https://github.com/TomSchimansky/CustomTkinter)
[![SQLite](https://img.shields.io/badge/SQLite-3+-orange.svg)](https://sqlite.org)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

**[Démo](demo-link) • [Documentation](docs-link) • [Support](support-link)**

</div>
