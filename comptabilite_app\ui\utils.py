"""
Fonctions utilitaires pour l'interface utilisateur
"""

def svg_to_icon_html(svg_content, color="white", size=24):
    """Convertit un SVG en HTML pour QLabel avec la couleur spécifiée"""
    # Remplacer la couleur du trait
    colored_svg = svg_content.replace('stroke="currentColor"', f'stroke="{color}"')

    # Ajouter un attribut fill si nécessaire pour certaines icônes
    if 'fill="none"' in colored_svg:
        colored_svg = colored_svg.replace('fill="none"', f'fill="none" stroke="{color}"')

    # Ajuster la taille si nécessaire
    if size != 24:
        colored_svg = colored_svg.replace('width="24"', f'width="{size}"')
        colored_svg = colored_svg.replace('height="24"', f'height="{size}"')

    # Envelopper le SVG dans une balise HTML pour que QLabel l'interprète correctement
    return f'<html><body style="margin:0; padding:0;">{colored_svg}</body></html>'

def svg_to_pixmap(svg_content, color="white", size=24):
    """Convertit un SVG en QPixmap pour les icônes de boutons"""
    from PySide6.QtSvg import QSvgRenderer
    from PySide6.QtGui import QPixmap, QPainter
    from PySide6.QtCore import QByteArray
    
    # Remplacer la couleur du trait
    colored_svg = svg_content.replace('stroke="currentColor"', f'stroke="{color}"')
    
    # Ajouter un attribut fill si nécessaire pour certaines icônes
    if 'fill="none"' in colored_svg:
        colored_svg = colored_svg.replace('fill="none"', f'fill="none" stroke="{color}"')
    
    # Créer un QPixmap pour dessiner le SVG
    pixmap = QPixmap(size, size)
    pixmap.fill("transparent")  # Fond transparent
    
    # Créer un renderer SVG
    renderer = QSvgRenderer(QByteArray(colored_svg.encode()))
    
    # Dessiner le SVG sur le QPixmap
    painter = QPainter(pixmap)
    renderer.render(painter)
    painter.end()
    
    return pixmap

def format_currency(amount, symbol="DH"):
    """Formate un montant en devise avec séparateurs de milliers"""
    try:
        # Convertir en float si c'est une chaîne
        if isinstance(amount, str):
            amount = float(amount.replace(',', '.').replace(' ', ''))
        
        # Formater avec séparateurs de milliers et 2 décimales
        formatted = f"{amount:,.2f}".replace(',', ' ').replace('.', ',')
        
        # Ajouter le symbole de devise
        if symbol:
            return f"{formatted} {symbol}"
        else:
            return formatted
    except (ValueError, TypeError):
        return f"0,00 {symbol}" if symbol else "0,00"

def parse_currency(amount_str):
    """Convertit une chaîne de devise formatée en nombre"""
    try:
        # Supprimer le symbole de devise et les espaces
        cleaned = amount_str.replace('DH', '').replace(' ', '')
        
        # Remplacer la virgule par un point pour la conversion
        cleaned = cleaned.replace(',', '.')
        
        # Convertir en float
        return float(cleaned)
    except (ValueError, AttributeError):
        return 0.0

def calculate_total(items, price_key='prix'):
    """Calcule le total d'une liste d'éléments"""
    total = 0.0
    for item in items:
        if isinstance(item, dict) and price_key in item:
            try:
                total += float(item[price_key])
            except (ValueError, TypeError):
                pass
    return total

def format_date(date, format_str="%d/%m/%Y"):
    """Formate une date selon le format spécifié"""
    if hasattr(date, 'strftime'):
        return date.strftime(format_str)
    return str(date)

def create_status_label(status):
    """Crée un texte HTML pour afficher un statut avec couleur"""
    status = status.lower() if isinstance(status, str) else ""
    
    if "payée" in status and "partiellement" not in status:
        color = "#2e7d32"  # Vert
        bg_color = "#e6f7e6"
    elif "partiellement" in status:
        color = "#f57f17"  # Jaune foncé
        bg_color = "#fff8e1"
    elif "attente" in status or "non" in status:
        color = "#c62828"  # Rouge
        bg_color = "#ffebee"
    else:
        color = "#333333"  # Gris foncé
        bg_color = "#f5f5f5"
    
    return f"""
    <div style="
        background-color: {bg_color}; 
        color: {color}; 
        padding: 4px 8px; 
        border-radius: 4px; 
        font-weight: bold;
        display: inline-block;
        text-align: center;
    ">
        {status.capitalize()}
    </div>
    """
