from PySide6.QtWidgets import QTableWidgetItem, QHeaderView, QMessageBox
from PySide6.QtCore import Qt, QDate
import sqlite3

# Importer le module de base
from ..components.base_module import BaseModule
from ..icons.icons import PRODUCTS_ICON

# Importer le formulaire produit
from ..forms import ProduitForm

class ProduitsModule(BaseModule):
    """Module de gestion des produits avec interface simplifiée"""

    def __init__(self, db_manager, signals=None):
        super().__init__(
            db_manager=db_manager,
            signals=signals,
            title="Gestion des Produits",
            description="Ajoutez, modifiez et supprimez des produits",
            icon=PRODUCTS_ICON
        )

        # Configurer le bouton d'ajout
        self.add_button.setText("Ajouter un produit")

        # Configurer le tableau
        self.setup_table()

        # Charger les données
        self.load_produits()

    def get_family_abbreviation(self, family_name):
        """تحويل اسم العائلة إلى اختصار (الحرف الأول من كل كلمة)"""
        if not family_name or family_name == "Aucune":
            return "--"

        # تقسيم النص إلى كلمات
        words = family_name.strip().split()

        if not words:
            return "--"

        # إذا كانت كلمة واحدة: الحرف الأول فقط
        if len(words) == 1:
            return words[0][0].upper()

        # إذا كانت عدة كلمات: الحرف الأول من كل كلمة
        abbreviation = ""
        for word in words:
            if word:  # تجاهل الكلمات الفارغة
                abbreviation += word[0].upper()

        return abbreviation if abbreviation else "--"

    def renumber_all_products(self):
        """إعادة ترقيم جميع المنتجات تلقائياً بترتيب متسلسل"""
        try:
            cursor = self.db_manager.conn.cursor()

            # الحصول على جميع المنتجات مرتبة حسب ID
            cursor.execute("SELECT id, code FROM produits ORDER BY id ASC")
            products = cursor.fetchall()

            # إعادة ترقيم كل منتج
            for index, product in enumerate(products, start=1):
                product_id = product['id']
                old_code = product['code']
                new_code = f"P{index:03d}"
                if old_code != new_code:
                    cursor.execute("UPDATE produits SET code = ? WHERE id = ?", (new_code, product_id))
                    print(f"⚙️  تغيير كود المنتج: {old_code} → {new_code}")

            self.db_manager.conn.commit()
            print(f"✅ تم إعادة ترقيم {len(products)} منتج بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إعادة الترقيم: {e}")

    def setup_table(self):
        """Configure le tableau des produits"""
        self.items_table.setColumnCount(13)  # Ajouter plus de colonnes pour TTC
        self.items_table.setHorizontalHeaderLabels([
            "Code", "Désignation", "Unité", "العائلة/Famille",
            "Prix d'achat (HT)", "Prix d'achat (TTC)", "Prix de vente (HT)", "Prix de vente (TTC)",
            "Stock", "Date de paiement", "Fournisseur", "N° Facture", "Mode paiement"
        ])

        # Activer le menu contextuel (clic droit)
        self.items_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.items_table.customContextMenuRequested.connect(self.show_context_menu)

        # Configuration des colonnes
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Code
        self.items_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)  # Désignation
        self.items_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Unité
        self.items_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Prix d'achat
        self.items_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Prix de vente
        self.items_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Stock
        self.items_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Date de paiement

        # Configurer la recherche

    def show_context_menu(self, position):
        """Affiche le menu contextuel au clic droit"""
        from PySide6.QtWidgets import QMenu
        from PySide6.QtGui import QAction

        # Vérifier qu'une ligne est sélectionnée
        item = self.items_table.itemAt(position)
        if not item:
            return

        row = item.row()

        # Récupérer les données de la ligne
        code_item = self.items_table.item(row, 0)
        designation_item = self.items_table.item(row, 1)

        if not code_item or not designation_item:
            return

        produit_code = code_item.text()
        produit_designation = designation_item.text()
        produit_id = code_item.data(Qt.UserRole)

        if not produit_id:
            return

        # Créer le menu contextuel
        menu = QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                padding: 4px;
            }
            QMenu::item {
                padding: 8px 16px;
                border-radius: 4px;
                margin: 2px;
            }
            QMenu::item:selected {
                background-color: #F3F4F6;
            }
            QMenu::separator {
                height: 1px;
                background-color: #E5E7EB;
                margin: 4px 8px;
            }
        """)

        # Action Modifier
        edit_action = QAction("✏️ Modifier", self)
        edit_action.triggered.connect(lambda: self.show_edit_dialog(produit_id))
        menu.addAction(edit_action)

        menu.addSeparator()

        # Action Ajout entrées
        ajout_entrees_action = QAction("📦 Ajout entrées", self)
        ajout_entrees_action.triggered.connect(lambda: self.show_ajout_entrees_dialog(produit_id, produit_designation))
        menu.addAction(ajout_entrees_action)

        menu.addSeparator()

        # Action Supprimer
        delete_action = QAction("🗑️ Supprimer", self)
        delete_action.triggered.connect(lambda: self.delete_item(produit_id, produit_designation))
        menu.addAction(delete_action)

        # Afficher le menu à la position du curseur
        menu.exec(self.items_table.mapToGlobal(position))

    def show_ajout_entrees_dialog(self, produit_id, produit_designation):
        """Affiche la boîte de dialogue pour ajouter des entrées de stock"""
        from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QSpinBox, QPushButton, QMessageBox, QLineEdit, QComboBox

        # Récupérer toutes les informations du produit
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT * FROM produits WHERE id = ?", (produit_id,))
            produit = cursor.fetchone()
            if not produit:
                QMessageBox.warning(self, "Erreur", "Produit non trouvé!")
                return
            stock_actuel = produit[6] if produit[6] else 0

            # Récupérer les informations d'achat existantes
            try:
                fournisseur_actuel = produit['fournisseur'] if 'fournisseur' in produit.keys() and produit['fournisseur'] else ''
                numero_facture_actuel = produit['numero_facture'] if 'numero_facture' in produit.keys() and produit['numero_facture'] else ''
                date_facture_actuelle = produit['date_facture'] if 'date_facture' in produit.keys() and produit['date_facture'] else ''
                mode_paiement_actuel = produit['mode_paiement'] if 'mode_paiement' in produit.keys() and produit['mode_paiement'] else ''
                date_paiement_actuelle = produit['date_paiement'] if 'date_paiement' in produit.keys() and produit['date_paiement'] else ''
            except (KeyError, IndexError):
                fournisseur_actuel = ''
                numero_facture_actuel = ''
                date_facture_actuelle = ''
                mode_paiement_actuel = ''
                date_paiement_actuelle = ''

            print(f"📋 Informations actuelles du produit:")
            print(f"   Fournisseur: {fournisseur_actuel}")
            print(f"   N° Facture: {numero_facture_actuel}")
            print(f"   Date facture: {date_facture_actuelle}")
            print(f"   Mode paiement: {mode_paiement_actuel}")
            print(f"   Date paiement: {date_paiement_actuelle}")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la récupération du produit: {e}")
            return

        dialog = QDialog(self)
        dialog.setWindowTitle(f"Ajout entrées - {produit_designation}")
        dialog.setFixedSize(500, 600)
        layout = QVBoxLayout(dialog)
        layout.setSpacing(10)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title_label = QLabel(f"Ajout entrées - {produit_designation}")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # Informations modifiables du produit
        info_layout = QVBoxLayout()

        # Prix d'achat (HT) - modifiable
        prix_achat_layout = QHBoxLayout()
        prix_achat_label = QLabel("Prix d'achat (HT):")
        prix_achat_spin = QSpinBox()
        prix_achat_spin.setMaximum(999999)
        prix_achat_spin.setValue(int(produit[4]) if produit[4] else 0)
        prix_achat_spin.setSuffix(" DH")
        prix_achat_layout.addWidget(prix_achat_label)
        prix_achat_layout.addWidget(prix_achat_spin)
        info_layout.addLayout(prix_achat_layout)

        # Prix de vente (HT) - modifiable
        prix_vente_layout = QHBoxLayout()
        prix_vente_label = QLabel("Prix de vente (HT):")
        prix_vente_spin = QSpinBox()
        prix_vente_spin.setMaximum(999999)
        prix_vente_spin.setValue(int(produit[5]) if produit[5] else 0)
        prix_vente_spin.setSuffix(" DH")
        prix_vente_layout.addWidget(prix_vente_label)
        prix_vente_layout.addWidget(prix_vente_spin)
        info_layout.addLayout(prix_vente_layout)

        # Stock actuel
        stock_label = QLabel(f"Quantité en stock: {stock_actuel}")
        stock_label.setStyleSheet("font-weight: bold; color: green;")
        info_layout.addWidget(stock_label)

        layout.addLayout(info_layout)

        # Quantité à ajouter
        quantite_layout = QHBoxLayout()
        quantite_label = QLabel("Quantité à ajouter:")
        quantite_spin = QSpinBox()
        quantite_spin.setMinimum(1)
        quantite_spin.setMaximum(99999)
        quantite_spin.setValue(1)
        quantite_layout.addWidget(quantite_label)
        quantite_layout.addWidget(quantite_spin)
        layout.addLayout(quantite_layout)

        # Nouveau stock (calculé automatiquement)
        nouveau_stock_label = QLabel(f"Nouveau stock: {stock_actuel + quantite_spin.value()}")
        nouveau_stock_label.setStyleSheet("font-size: 14px; font-weight: bold; color: blue; margin: 10px 0;")
        layout.addWidget(nouveau_stock_label)

        # Informations d'achat
        achat_group_label = QLabel("Informations d'achat:")
        achat_group_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        layout.addWidget(achat_group_label)

        achat_layout = QVBoxLayout()

        # Fournisseur - ComboBox avec liste des fournisseurs
        fournisseur_layout = QHBoxLayout()
        fournisseur_label = QLabel("Fournisseur:")
        fournisseur_combo = QComboBox()
        fournisseur_combo.setEditable(True)  # Permet d'ajouter de nouveaux fournisseurs

        # Charger la liste des fournisseurs depuis la base de données
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT DISTINCT nom FROM fournisseurs ORDER BY nom")
            fournisseurs = cursor.fetchall()
            fournisseur_combo.addItem("")  # Option vide
            for fournisseur in fournisseurs:
                fournisseur_combo.addItem(fournisseur[0])
        except:
            # Si la table n'existe pas, ajouter quelques options par défaut
            fournisseur_combo.addItems(["", "AZ MAROC", "SCHNEIDER", "LEGRAND", "ABB"])

        # Pré-remplir avec les informations actuelles du produit
        if fournisseur_actuel:
            index = fournisseur_combo.findText(fournisseur_actuel)
            if index >= 0:
                fournisseur_combo.setCurrentIndex(index)
            else:
                # Ajouter le fournisseur s'il n'existe pas dans la liste
                fournisseur_combo.addItem(fournisseur_actuel)
                fournisseur_combo.setCurrentText(fournisseur_actuel)

        fournisseur_layout.addWidget(fournisseur_label)
        fournisseur_layout.addWidget(fournisseur_combo)
        achat_layout.addLayout(fournisseur_layout)

        # Numéro de facture d'achat
        facture_layout = QHBoxLayout()
        facture_label = QLabel("Numéro de facture d'achat:")
        facture_input = QLineEdit()
        # Pré-remplir avec la valeur actuelle
        if numero_facture_actuel:
            facture_input.setText(numero_facture_actuel)
        facture_layout.addWidget(facture_label)
        facture_layout.addWidget(facture_input)
        achat_layout.addLayout(facture_layout)

        # Date
        date_layout = QHBoxLayout()
        date_label = QLabel("Date:")
        from ..style import create_styled_date_edit
        date_input = create_styled_date_edit()
        # Pré-remplir avec la valeur actuelle
        if date_facture_actuelle:
            try:
                date_obj = QDate.fromString(date_facture_actuelle, "yyyy/MM/dd")
                if date_obj.isValid():
                    date_input.setDate(date_obj)
            except:
                pass  # Garder la date par défaut si erreur
        date_layout.addWidget(date_label)
        date_layout.addWidget(date_input)
        achat_layout.addLayout(date_layout)

        # Date de paiement
        date_paiement_layout = QHBoxLayout()
        date_paiement_label = QLabel("Date de paiement:")
        from ..style import create_styled_date_edit
        date_paiement_input = create_styled_date_edit()
        # Pré-remplir avec la valeur actuelle
        if date_paiement_actuelle:
            try:
                date_obj = QDate.fromString(date_paiement_actuelle, "yyyy/MM/dd")
                if date_obj.isValid():
                    date_paiement_input.setDate(date_obj)
            except:
                pass  # Garder la date par défaut si erreur
        date_paiement_layout.addWidget(date_paiement_label)
        date_paiement_layout.addWidget(date_paiement_input)
        achat_layout.addLayout(date_paiement_layout)

        # Mode de paiement - ComboBox avec liste des modes de paiement
        mode_paiement_layout = QHBoxLayout()
        mode_paiement_label = QLabel("Mode de paiement:")
        mode_paiement_combo = QComboBox()
        mode_paiement_combo.setEditable(True)  # Permet d'ajouter de nouveaux modes

        # Ajouter les modes de paiement courants
        mode_paiement_combo.addItems([
            "",
            "Chèque",
            "Espèces",
            "Virement bancaire",
            "Carte bancaire",
            "Traite",
            "Crédit"
        ])

        # Pré-remplir avec la valeur actuelle
        if mode_paiement_actuel:
            index = mode_paiement_combo.findText(mode_paiement_actuel)
            if index >= 0:
                mode_paiement_combo.setCurrentIndex(index)
            else:
                # Ajouter le mode de paiement s'il n'existe pas dans la liste
                mode_paiement_combo.addItem(mode_paiement_actuel)
                mode_paiement_combo.setCurrentText(mode_paiement_actuel)

        mode_paiement_layout.addWidget(mode_paiement_label)
        mode_paiement_layout.addWidget(mode_paiement_combo)
        achat_layout.addLayout(mode_paiement_layout)

        layout.addLayout(achat_layout)

        # Mettre à jour le nouveau stock quand la quantité change
        def update_nouveau_stock():
            nouveau_stock = stock_actuel + quantite_spin.value()
            nouveau_stock_label.setText(f"Nouveau stock: {nouveau_stock}")

        quantite_spin.valueChanged.connect(update_nouveau_stock)

        # Boutons
        buttons_layout = QHBoxLayout()

        cancel_button = QPushButton("Annuler")
        confirm_button = QPushButton("Confirmer")

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

        # Connexions des boutons
        def confirmer_ajout():
            quantite_ajoutee = quantite_spin.value()
            nouveau_prix_achat = prix_achat_spin.value()
            nouveau_prix_vente = prix_vente_spin.value()

            # Informations d'achat
            fournisseur = fournisseur_combo.currentText().strip()
            numero_facture = facture_input.text().strip()
            date_achat = date_input.date().toString("yyyy/MM/dd")
            date_paiement = date_paiement_input.date().toString("yyyy/MM/dd")
            mode_paiement = mode_paiement_combo.currentText().strip()

            try:
                cursor = self.db_manager.conn.cursor()

                # Afficher les valeurs avant la mise à jour
                print(f"🔄 Mise à jour du produit ID {produit_id}:")
                print(f"   Stock actuel: {stock_actuel}")
                print(f"   Quantité à ajouter: {quantite_ajoutee}")
                print(f"   Nouveau prix d'achat: {nouveau_prix_achat}")
                print(f"   Nouveau prix de vente: {nouveau_prix_vente}")
                print(f"   Date de paiement: {date_paiement}")
                print(f"   Fournisseur: {fournisseur}")
                print(f"   Numéro facture: {numero_facture}")
                print(f"   Date facture: {date_achat}")
                print(f"   Mode de paiement: {mode_paiement}")

                # Vérifier et ajouter les colonnes manquantes
                cursor.execute("PRAGMA table_info(produits)")
                columns = [col[1] for col in cursor.fetchall()]

                # Ajouter les colonnes manquantes si nécessaire
                if 'fournisseur' not in columns:
                    cursor.execute("ALTER TABLE produits ADD COLUMN fournisseur TEXT")
                    print("✅ Colonne 'fournisseur' ajoutée à la table produits")

                if 'numero_facture' not in columns:
                    cursor.execute("ALTER TABLE produits ADD COLUMN numero_facture TEXT")
                    print("✅ Colonne 'numero_facture' ajoutée à la table produits")

                if 'date_facture' not in columns:
                    cursor.execute("ALTER TABLE produits ADD COLUMN date_facture TEXT")
                    print("✅ Colonne 'date_facture' ajoutée à la table produits")

                if 'mode_paiement' not in columns:
                    cursor.execute("ALTER TABLE produits ADD COLUMN mode_paiement TEXT")
                    print("✅ Colonne 'mode_paiement' ajoutée à la table produits")

                # Mettre à jour le produit avec toutes les nouvelles informations
                cursor.execute("""
                    UPDATE produits
                    SET stock = stock + ?,
                        prix_achat = ?,
                        prix_vente = ?,
                        date_paiement = ?,
                        fournisseur = ?,
                        numero_facture = ?,
                        date_facture = ?,
                        mode_paiement = ?
                    WHERE id = ?
                """, (quantite_ajoutee, nouveau_prix_achat, nouveau_prix_vente, date_paiement,
                      fournisseur, numero_facture, date_achat, mode_paiement, produit_id))

                print(f"✅ Mise à jour SQL exécutée, lignes affectées: {cursor.rowcount}")

                # Vérifier les valeurs après mise à jour
                cursor.execute("""
                    SELECT stock, prix_achat, prix_vente, fournisseur, numero_facture, date_facture, mode_paiement
                    FROM produits WHERE id = ?
                """, (produit_id,))
                result = cursor.fetchone()
                if result:
                    print(f"📊 Valeurs après mise à jour:")
                    print(f"   Nouveau stock: {result[0]}")
                    print(f"   Prix d'achat: {result[1]}")
                    print(f"   Prix de vente: {result[2]}")
                    print(f"   Fournisseur: {result[3]}")
                    print(f"   Numéro facture: {result[4]}")
                    print(f"   Date facture: {result[5]}")
                    print(f"   Mode de paiement: {result[6]}")

                # Enregistrer l'entrée dans une table d'historique
                # D'abord, vérifier si la table existe et a la bonne structure
                try:
                    # Vérifier si la table existe
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='mouvements_stock'")
                    table_exists = cursor.fetchone()

                    if not table_exists:
                        # Créer la table si elle n'existe pas
                        cursor.execute("""
                            CREATE TABLE mouvements_stock (
                                id INTEGER PRIMARY KEY AUTOINCREMENT,
                                produit_id INTEGER,
                                type_mouvement TEXT,
                                quantite INTEGER,
                                commentaire TEXT,
                                date_mouvement DATETIME,
                                FOREIGN KEY (produit_id) REFERENCES produits (id)
                            )
                        """)
                    else:
                        # Vérifier si la colonne commentaire existe
                        cursor.execute("PRAGMA table_info(mouvements_stock)")
                        columns = cursor.fetchall()
                        has_commentaire = any(col[1] == 'commentaire' for col in columns)

                        if not has_commentaire:
                            # Ajouter la colonne commentaire si elle n'existe pas
                            cursor.execute("ALTER TABLE mouvements_stock ADD COLUMN commentaire TEXT")

                    # Maintenant insérer l'enregistrement
                    cursor.execute("""
                        INSERT INTO mouvements_stock (
                            produit_id, type_mouvement, quantite, commentaire, date_mouvement
                        ) VALUES (?, 'entree', ?, ?, datetime('now'))
                    """, (produit_id, quantite_ajoutee, f"Fournisseur: {fournisseur}, Facture: {numero_facture}"))

                except Exception as e:
                    print(f"Erreur lors de l'enregistrement de l'historique: {e}")
                    # Continuer même si l'historique échoue

                self.db_manager.conn.commit()
                print("✅ Changements commitées dans la base de données")

                # Vérifier la mise à jour
                cursor.execute("SELECT stock, prix_achat, prix_vente FROM produits WHERE id = ?", (produit_id,))
                result = cursor.fetchone()
                if result:
                    print(f"📊 Valeurs après mise à jour:")
                    print(f"   Nouveau stock: {result['stock']}")
                    print(f"   Prix d'achat: {result['prix_achat']}")
                    print(f"   Prix de vente: {result['prix_vente']}")

                # Intégrer avec la caisse si paiement en espèces
                mode_paiement = mode_paiement_combo.currentText()
                if mode_paiement == "Espèces":
                    prix_total = nouveau_prix_achat * quantite_ajoutee
                    self.integrate_with_caisse_purchase(numero_facture, prix_total)

                # Afficher un message de succès avec toutes les informations
                QMessageBox.information(
                    dialog,
                    "Succès",
                    f"Entrée ajoutée avec succès!\n\n"
                    f"📦 Produit: {produit_designation}\n"
                    f"📊 Quantité ajoutée: {quantite_ajoutee}\n"
                    f"📈 Stock avant: {stock_actuel}\n"
                    f"📈 Nouveau stock: {stock_actuel + quantite_ajoutee}\n"
                    f"💰 Prix d'achat: {nouveau_prix_achat:.2f} DH\n"
                    f"💵 Prix de vente: {nouveau_prix_vente:.2f} DH\n\n"
                    f"🏢 Fournisseur: {fournisseur}\n"
                    f"📄 N° Facture: {numero_facture}\n"
                    f"📅 Date facture: {date_achat}\n"
                    f"📅 Date paiement: {date_paiement}\n"
                    f"💳 Mode de paiement: {mode_paiement}"
                )

                # Recharger la liste des produits
                print("🔄 Rechargement de la liste des produits...")
                self.load_produits()
                print("✅ Liste des produits rechargée")

                # Forcer la mise à jour de l'affichage
                self.items_table.viewport().update()
                self.items_table.repaint()

                # Mettre à jour immédiatement le stock affiché pour ce produit
                for row in range(self.items_table.rowCount()):
                    item = self.items_table.item(row, 0)
                    if item and item.data(Qt.UserRole) == produit_id:
                        # Mettre à jour la colonne stock (colonne 5)
                        nouveau_stock_total = stock_actuel + quantite_ajoutee
                        self.items_table.setItem(row, 5, QTableWidgetItem(str(nouveau_stock_total)))

                        # Mettre à jour les prix aussi
                        self.items_table.setItem(row, 3, QTableWidgetItem(f"{nouveau_prix_achat:.2f} DH"))
                        self.items_table.setItem(row, 4, QTableWidgetItem(f"{nouveau_prix_vente:.2f} DH"))

                        # Mettre à jour la date de paiement
                        if date_paiement:
                            self.items_table.setItem(row, 6, QTableWidgetItem(date_paiement))

                        break

                # Émettre le signal pour informer les autres modules
                if self.signals:
                    print("📡 Émission des signaux de mise à jour...")
                    self.signals.produits_changed.emit()
                    self.signals.stock_changed.emit()
                    print("✅ Signaux émis avec succès")
                else:
                    print("❌ Aucun signal disponible")

                dialog.accept()

            except Exception as e:
                QMessageBox.critical(
                    dialog,
                    "Erreur",
                    f"Erreur lors de l'ajout de l'entrée:\n{str(e)}"
                )

        confirm_button.clicked.connect(confirmer_ajout)
        cancel_button.clicked.connect(dialog.reject)

        # Afficher la boîte de dialogue
        dialog.exec()

    def load_produits(self):
        """Charge les produits depuis la base de données"""
        print(f"📦 Chargement des produits depuis la base de données...")
        cursor = self.db_manager.conn.cursor()
        cursor.execute("""
            SELECT p.*, f.nom as famille_nom
            FROM produits p
            LEFT JOIN familles_produits f ON p.famille_id = f.id
            ORDER BY p.code
        """)
        produits = cursor.fetchall()
        print(f"📦 Trouvé {len(produits)} produits dans la base de données")

        self.items_table.setRowCount(0)

        for row_num, produit in enumerate(produits):
            self.items_table.insertRow(row_num)

            # Ajouter les données du produit
            self.items_table.setItem(row_num, 0, QTableWidgetItem(produit['code'] or ""))
            self.items_table.setItem(row_num, 1, QTableWidgetItem(produit['designation']))
            self.items_table.setItem(row_num, 2, QTableWidgetItem(produit['unite'] or ""))
            self.items_table.setItem(row_num, 3, QTableWidgetItem(self.get_family_abbreviation(produit['famille_nom'])))

            # Prix d'achat HT
            self.items_table.setItem(row_num, 4, QTableWidgetItem(f"{produit['prix_achat']:.2f} DH" if produit['prix_achat'] else ""))

            # Prix d'achat TTC (calculer si n'existe pas)
            try:
                prix_achat_ttc = produit['prix_achat_ttc'] if 'prix_achat_ttc' in produit.keys() else None
            except (KeyError, TypeError):
                prix_achat_ttc = None

            if prix_achat_ttc is None and produit['prix_achat']:
                try:
                    tva_rate = produit['tva_rate'] if 'tva_rate' in produit.keys() else 20.0
                    tva_rate = tva_rate / 100
                except (KeyError, TypeError):
                    tva_rate = 0.2
                prix_achat_ttc = produit['prix_achat'] * (1 + tva_rate)
            self.items_table.setItem(row_num, 5, QTableWidgetItem(f"{prix_achat_ttc:.2f} DH" if prix_achat_ttc else ""))

            # Prix de vente HT
            self.items_table.setItem(row_num, 6, QTableWidgetItem(f"{produit['prix_vente']:.2f} DH" if produit['prix_vente'] else ""))

            # Prix de vente TTC (calculer si n'existe pas)
            try:
                prix_vente_ttc = produit['prix_vente_ttc'] if 'prix_vente_ttc' in produit.keys() else None
            except (KeyError, TypeError):
                prix_vente_ttc = None

            if prix_vente_ttc is None and produit['prix_vente']:
                try:
                    tva_rate = produit['tva_rate'] if 'tva_rate' in produit.keys() else 20.0
                    tva_rate = tva_rate / 100
                except (KeyError, TypeError):
                    tva_rate = 0.2
                prix_vente_ttc = produit['prix_vente'] * (1 + tva_rate)
            self.items_table.setItem(row_num, 7, QTableWidgetItem(f"{prix_vente_ttc:.2f} DH" if prix_vente_ttc else ""))

            # Stock
            self.items_table.setItem(row_num, 8, QTableWidgetItem(str(produit['stock'] or 0)))

            # Date de paiement
            date_paiement_text = ""
            if produit['date_paiement']:
                try:
                    date_obj = QDate.fromString(produit['date_paiement'], "yyyy/MM/dd")
                    if date_obj.isValid():
                        date_paiement_text = date_obj.toString("yyyy/MM/dd")
                except:
                    pass

            self.items_table.setItem(row_num, 9, QTableWidgetItem(date_paiement_text))

            # Nouvelles colonnes
            # Fournisseur
            try:
                fournisseur_text = produit['fournisseur'] if 'fournisseur' in produit.keys() and produit['fournisseur'] else ''
            except (KeyError, IndexError):
                fournisseur_text = ''
            self.items_table.setItem(row_num, 10, QTableWidgetItem(fournisseur_text))

            # Numéro de facture
            try:
                numero_facture_text = produit['numero_facture'] if 'numero_facture' in produit.keys() and produit['numero_facture'] else ''
            except (KeyError, IndexError):
                numero_facture_text = ''
            self.items_table.setItem(row_num, 11, QTableWidgetItem(numero_facture_text))

            # Mode de paiement
            try:
                mode_paiement_text = produit['mode_paiement'] if 'mode_paiement' in produit.keys() and produit['mode_paiement'] else ''
            except (KeyError, IndexError):
                mode_paiement_text = ''
            self.items_table.setItem(row_num, 12, QTableWidgetItem(mode_paiement_text))

            # Stocker l'ID du produit dans la première colonne (invisible)
            self.items_table.item(row_num, 0).setData(Qt.UserRole, produit['id'])

    def show_add_dialog(self):
        """Affiche la boîte de dialogue pour ajouter un produit"""
        dialog = ProduitForm(self.db_manager, parent=self)
        if dialog.exec() == ProduitForm.Accepted:
            self.on_produit_saved()

    def show_edit_dialog(self, produit_id):
        """Affiche la boîte de dialogue pour modifier un produit"""
        dialog = ProduitForm(self.db_manager, produit_id=produit_id, parent=self)
        if dialog.exec() == ProduitForm.Accepted:
            self.on_produit_saved()

    def on_produit_saved(self):
        """Appelé lorsqu'un produit est ajouté ou modifié"""
        self.load_produits()

        # Émettre le signal pour informer les autres modules
        if self.signals:
            self.signals.produits_changed.emit()

    def delete_item(self, produit_id, produit_name):
        """Supprime un produit"""
        confirm = QMessageBox.question(
            self, "Confirmation",
            f"Êtes-vous sûr de vouloir supprimer le produit {produit_name} ?\nCette action est irréversible.",
            QMessageBox.Yes | QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                cursor = self.db_manager.conn.cursor()

                # Vérifier si le produit a des entrées de caisse liées et les supprimer
                cursor.execute("SELECT code FROM produits WHERE id = ?", (produit_id,))
                produit_info = cursor.fetchone()

                if produit_info:
                    produit_code = produit_info['code']
                    # Supprimer les sorties de caisse liées à ce produit
                    cursor.execute("DELETE FROM sorties_caisse WHERE reference = ? OR reference LIKE ?",
                                 (f"PROD_{produit_code}", f"PROD_{produit_code}%"))
                    print(f"💸 Sorties de caisse supprimées pour le produit {produit_code}")

                cursor.execute("DELETE FROM produits WHERE id = ?", (produit_id,))
                self.db_manager.conn.commit()

                # إعادة ترقيم جميع المنتجات تلقائياً
                self.renumber_all_products()

                self.load_produits()

                # Émettre le signal pour informer les autres modules
                if self.signals:
                    self.signals.produits_changed.emit()

                QMessageBox.information(self, "Succès", f"Produit {produit_name} supprimé avec succès.")
            except sqlite3.Error as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression du produit: {str(e)}")

    def integrate_with_caisse_purchase(self, numero_facture, montant):
        """Intègre automatiquement avec la caisse pour les achats de produits en espèces"""
        try:
            from datetime import datetime
            # Ajouter une sortie dans la caisse
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                INSERT INTO caisse_sorties (date, nature, objet, reference, montant)
                VALUES (?, ?, ?, ?, ?)
            """, (
                datetime.now().strftime("%Y-%m-%d"),
                "شراء",
                f"شراء منتجات بالنقد - فاتورة {numero_facture}",
                numero_facture,
                montant
            ))

            self.db_manager.conn.commit()
            print(f"💸 Sortie de caisse automatique ajoutée: {montant} DH pour l'achat de produits {numero_facture}")

        except Exception as e:
            print(f"❌ Erreur lors de l'intégration avec la caisse: {str(e)}")
