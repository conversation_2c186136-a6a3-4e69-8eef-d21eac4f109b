"""
Script pour créer les icônes nécessaires pour l'application
"""

import os
from PIL import Image, ImageDraw

def create_down_arrow_icon():
    """Crée une icône de flèche vers le bas"""
    # Taille de l'icône
    size = (24, 24)
    
    # Créer une nouvelle image avec fond transparent
    img = Image.new('RGBA', size, color=(0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Dessiner une flèche vers le bas
    # Points pour un triangle pointant vers le bas
    points = [(6, 8), (18, 8), (12, 16)]
    
    # Dessiner le triangle en blanc
    draw.polygon(points, fill=(255, 255, 255, 255))
    
    # Sauvegarder l'image
    img.save('comptabilite_app/ui/icons/down-arrow.png')
    print("Icône de flèche vers le bas créée avec succès.")

if __name__ == "__main__":
    # C<PERSON>er le dossier icons s'il n'existe pas
    os.makedirs('comptabilite_app/ui/icons', exist_ok=True)
    
    # Créer les icônes
    create_down_arrow_icon()
