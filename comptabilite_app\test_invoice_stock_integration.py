#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test rapide pour vérifier l'intégration factures-stock
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager
from stock_integration_solution import integrate_with_invoice_save, get_updated_stock_data

def test_invoice_stock_integration():
    """Test l'intégration entre une facture et le stock"""
    
    print("🧪 Test d'intégration Facture-Stock")
    print("=" * 50)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    
    # 1. Afficher l'état initial du stock
    print("\n📊 État initial du stock:")
    stock_data = get_updated_stock_data(db_manager)
    for item in stock_data:
        print(f"- {item['designation']}: Stock={item['stock_final']}, Sorties={item['sorties']}")
    
    # 2. Simuler l'ajout d'une facture avec DISJONCTEUR
    print("\n📄 Simulation d'ajout de facture:")
    
    # Créer une facture test
    cursor = db_manager.conn.cursor()
    cursor.execute("""
        INSERT INTO factures_vente (numero, date_creation, client_id, total_ht, total_ttc)
        VALUES (?, ?, ?, ?, ?)
    """, ("TEST-DISJ-001", "2024-12-20", 1, 150.0, 180.0))
    
    facture_id = cursor.lastrowid
    print(f"✅ Facture créée avec ID: {facture_id}")
    
    # Ajouter des lignes avec DISJONCTEUR
    lignes_facture = [
        {
            'designation': 'DISJONCTEUR',
            'unite': 'U',
            'quantite': 3,
            'prix_unitaire': 50.0,
            'taux_tva': 20.0,
            'total_ht': 150.0
        }
    ]
    
    # Ajouter la ligne à la base
    for ligne in lignes_facture:
        cursor.execute("""
            INSERT INTO lignes_facture (facture_id, designation, unite, quantite, prix_unitaire_ht, taux_tva, total_ht)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (facture_id, ligne['designation'], ligne['unite'], ligne['quantite'],
              ligne['prix_unitaire'], ligne['taux_tva'], ligne['total_ht']))
    
    db_manager.conn.commit()
    print(f"✅ Ligne ajoutée: {lignes_facture[0]['designation']} x {lignes_facture[0]['quantite']}")
    
    # 3. Intégrer avec le stock
    print("\n🔄 Intégration avec le stock:")
    success = integrate_with_invoice_save(db_manager, facture_id, lignes_facture)
    
    if success:
        print("✅ Intégration réussie")
    else:
        print("❌ Échec de l'intégration")
    
    # 4. Vérifier le résultat
    print("\n📊 État final du stock:")
    stock_data_final = get_updated_stock_data(db_manager)
    for item in stock_data_final:
        if item['designation'] == 'DISJONCTEUR':
            print(f"🎯 {item['designation']}: Stock={item['stock_final']}, Sorties={item['sorties']} ⬅️ MODIFIÉ")
        else:
            print(f"- {item['designation']}: Stock={item['stock_final']}, Sorties={item['sorties']}")
    
    # 5. Vérifier les mouvements de stock
    print("\n📋 Mouvements de stock pour DISJONCTEUR:")
    cursor.execute("""
        SELECT id, type_mouvement, quantite, reference, date_mouvement, facture_id
        FROM mouvements_stock 
        WHERE designation = 'DISJONCTEUR'
        ORDER BY date_mouvement DESC
        LIMIT 5
    """)
    
    mouvements = cursor.fetchall()
    if mouvements:
        for mouvement in mouvements:
            print(f"- ID: {mouvement[0]}, Type: {mouvement[1]}, Qté: {mouvement[2]}, "
                  f"Ref: {mouvement[3]}, Facture: {mouvement[5]}")
    else:
        print("❌ Aucun mouvement trouvé pour DISJONCTEUR")
    
    # Nettoyer les données de test
    print("\n🧹 Nettoyage des données de test...")
    try:
        cursor.execute("DELETE FROM mouvements_stock WHERE facture_id = ?", (facture_id,))
        cursor.execute("DELETE FROM lignes_facture WHERE facture_id = ?", (facture_id,))
        cursor.execute("DELETE FROM factures_vente WHERE id = ?", (facture_id,))
        db_manager.conn.commit()
        print("✅ Données de test supprimées")
    except Exception as e:
        print(f"⚠️ Erreur lors du nettoyage: {e}")
    
    print("\n✅ Test terminé!")

if __name__ == "__main__":
    test_invoice_stock_integration()
