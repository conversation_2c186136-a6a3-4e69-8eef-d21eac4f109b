from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                               QFrame, QTableWidget, QTableWidgetItem, QHeaderView,
                               QPushButton, QDateEdit, QLineEdit, QSizePolicy)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QColor

from ..theme import *
from ..icons.icons import *

class SimpleStatCard(QFrame):
    """Carte statistique simplifiée"""
    def __init__(self, title, value, parent=None):
        super().__init__(parent)
        self.setObjectName("simple_stat_card")
        self.setStyleSheet(f"""
            #simple_stat_card {{
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 10px;
            }}
            #card_title {{
                font-size: 14px;
                color: #666;
            }}
            #card_value {{
                font-size: 20px;
                font-weight: bold;
                color: #333;
            }}
        """)
        
        # Layout principal
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)
        
        # Titre
        title_label = QLabel(title)
        title_label.setObjectName("card_title")
        layout.addWidget(title_label)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setObjectName("card_value")
        layout.addWidget(value_label)

class DashboardSimple(QWidget):
    """Module de tableau de bord simplifié"""
    def __init__(self, db_manager, signals, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.signals = signals
        self.setup_ui()
        
    def setup_ui(self):
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Titre de la page
        page_title = QLabel("Tableau de Bord")
        page_title.setObjectName("page_title")
        page_title.setStyleSheet("""
            #page_title {
                font-size: 22px;
                font-weight: bold;
                color: #333;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(page_title)
        
        # Cartes statistiques
        stats_container = QWidget()
        stats_layout = QHBoxLayout(stats_container)
        stats_layout.setContentsMargins(0, 0, 0, 0)
        stats_layout.setSpacing(15)
        
        # Carte 1: Total des ventes
        total_sales_card = SimpleStatCard("Total des Ventes", "25 000 DH")
        stats_layout.addWidget(total_sales_card)
        
        # Carte 2: Nombre de fournisseurs
        suppliers_card = SimpleStatCard("Nombre de Fournisseurs", "45")
        stats_layout.addWidget(suppliers_card)
        
        # Carte 3: Total des retours
        returns_card = SimpleStatCard("Total des Retours", "1 500 DH")
        stats_layout.addWidget(returns_card)
        
        # Carte 4: Ventes nettes
        net_sales_card = SimpleStatCard("Ventes Nettes", "23 500 DH")
        stats_layout.addWidget(net_sales_card)
        
        main_layout.addWidget(stats_container)
        
        # Titre de la section factures
        invoices_title = QLabel("Factures de Vente")
        invoices_title.setObjectName("section_title")
        invoices_title.setStyleSheet("""
            #section_title {
                font-size: 18px;
                font-weight: bold;
                color: #333;
                margin-top: 10px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(invoices_title)
        
        # Filtres simples
        filters_container = QWidget()
        filters_layout = QHBoxLayout(filters_container)
        filters_layout.setContentsMargins(0, 0, 0, 10)
        filters_layout.setSpacing(10)
        
        # Date de début
        start_date_label = QLabel("Date début:")
        filters_layout.addWidget(start_date_label)
        
        start_date = QDateEdit()
        start_date.setCalendarPopup(True)
        start_date.setDate(QDate.currentDate().addMonths(-1))
        filters_layout.addWidget(start_date)
        
        # Date de fin
        end_date_label = QLabel("Date fin:")
        filters_layout.addWidget(end_date_label)
        
        end_date = QDateEdit()
        end_date.setCalendarPopup(True)
        end_date.setDate(QDate.currentDate())
        filters_layout.addWidget(end_date)
        
        # Recherche
        search_label = QLabel("Recherche:")
        filters_layout.addWidget(search_label)
        
        search_input = QLineEdit()
        search_input.setPlaceholderText("Rechercher...")
        filters_layout.addWidget(search_input)
        
        # Bouton de recherche
        search_button = QPushButton("Rechercher")
        filters_layout.addWidget(search_button)
        
        filters_layout.addStretch()
        
        main_layout.addWidget(filters_container)
        
        # Tableau des factures
        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(7)
        self.invoices_table.setHorizontalHeaderLabels([
            "N° Facture", "Date", "Client", "Montant Total", "Montant Payé", 
            "Montant Restant", "Statut"
        ])
        
        # Configurer le tableau
        self.invoices_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.invoices_table.setAlternatingRowColors(True)
        self.invoices_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.invoices_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.invoices_table.setSelectionMode(QTableWidget.SingleSelection)
        
        # Ajouter des données de test
        self.populate_test_data()
        
        main_layout.addWidget(self.invoices_table)
        
        # Boutons d'action
        actions_container = QWidget()
        actions_layout = QHBoxLayout(actions_container)
        actions_layout.setContentsMargins(0, 10, 0, 0)
        actions_layout.setSpacing(10)
        
        # Bouton: Nouvelle facture
        new_invoice_btn = QPushButton("Nouvelle Facture")
        actions_layout.addWidget(new_invoice_btn)
        
        # Bouton: Voir les détails
        view_details_btn = QPushButton("Voir les détails")
        actions_layout.addWidget(view_details_btn)
        
        # Bouton: Exporter
        export_btn = QPushButton("Exporter")
        actions_layout.addWidget(export_btn)
        
        actions_layout.addStretch()
        
        main_layout.addWidget(actions_container)
        main_layout.addStretch()
        
        # Appliquer les styles de base
        self.setStyleSheet("""
            QFrame {
                background-color: white;
            }
            QPushButton {
                background-color: #1E3A8A;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                padding: 5px;
                border: 1px solid #ddd;
                font-weight: bold;
            }
            QLineEdit, QDateEdit {
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
        """)
        
    def populate_test_data(self):
        """Ajoute des données de test au tableau des factures"""
        test_data = [
            ["INV-001", "13/05/2023", "Société Alpha Travaux", "5 250,50 DH", "5 250,50 DH", "0,00 DH", "Payée"],
            ["INV-002", "11/05/2023", "Fondation Nour", "3 750,75 DH", "2 000,00 DH", "1 750,75 DH", "Partiellement Payée"],
            ["INV-003", "09/05/2023", "Société Al-Amal", "8 500,00 DH", "0,00 DH", "8 500,00 DH", "En attente"],
            ["INV-004", "07/05/2023", "Fondation Al-Fajr", "4 200,25 DH", "4 200,25 DH", "0,00 DH", "Payée"],
            ["INV-005", "04/05/2023", "Société Al-Jadida", "3 300,00 DH", "1 500,00 DH", "1 800,00 DH", "Partiellement Payée"]
        ]
        
        self.invoices_table.setRowCount(len(test_data))
        
        for row, data in enumerate(test_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                item.setTextAlignment(Qt.AlignCenter)
                
                # Colorer les statuts
                if col == 6:  # Colonne de statut
                    if value == "Payée":
                        item.setBackground(QColor("#e6f7e6"))  # Vert clair
                        item.setForeground(QColor("#2e7d32"))  # Vert foncé
                    elif value == "Partiellement Payée":
                        item.setBackground(QColor("#fff8e1"))  # Jaune clair
                        item.setForeground(QColor("#f57f17"))  # Jaune foncé
                    elif value == "En attente":
                        item.setBackground(QColor("#ffebee"))  # Rouge clair
                        item.setForeground(QColor("#c62828"))  # Rouge foncé
                
                self.invoices_table.setItem(row, col, item)
