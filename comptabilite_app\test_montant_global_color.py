#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pour vérifier que la comparaison Montant Global vs TTC fonctionne avec les couleurs
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_montant_global_color_logic():
    """Test la logique de comparaison Montant Global vs TTC"""
    
    print("🧪 Test de la comparaison Montant Global vs TTC")
    print("=" * 50)
    
    # Cas de test
    test_cases = [
        # (montant_global, ttc, couleur_attendue)
        (1000, 1200, "ROUGE"),    # Montant Global < TTC
        (1500, 1200, "VERT"),     # Montant Global > TTC  
        (1200, 1200, "BLEU"),     # Montant Global = TTC
        (0, 1200, "ROUGE"),       # Montant Global = 0
        (2000, 0, "VERT"),        # TTC = 0
        (500.50, 600.75, "ROUGE"), # Avec décimales
        (750.25, 650.10, "VERT"),  # Avec décimales
    ]
    
    print("\n🔍 Tests de comparaison:")
    for i, (montant_global, ttc, couleur_attendue) in enumerate(test_cases, 1):
        print(f"\nTest {i}:")
        print(f"  Montant Global: {montant_global:.2f} DH")
        print(f"  TTC: {ttc:.2f} DH")
        
        # Logique de comparaison
        if montant_global < ttc:
            couleur_obtenue = "ROUGE"
            emoji = "🔴"
        elif montant_global > ttc:
            couleur_obtenue = "VERT"
            emoji = "🟢"
        else:
            couleur_obtenue = "BLEU"
            emoji = "🔵"
        
        # Vérifier le résultat
        if couleur_obtenue == couleur_attendue:
            print(f"  ✅ {emoji} Couleur: {couleur_obtenue} (Correct)")
        else:
            print(f"  ❌ Couleur: {couleur_obtenue}, Attendue: {couleur_attendue}")
    
    print("\n🎨 Styles CSS générés:")
    
    # Style Rouge (Montant Global < TTC)
    print("\n🔴 Style ROUGE (Montant Global < TTC):")
    print("""
    border: 2px solid #EF4444; 
    border-radius: 4px; 
    padding: 8px; 
    background-color: #FEF2F2; 
    color: #DC2626;
    font-weight: bold;
    """)
    
    # Style Vert (Montant Global > TTC)
    print("\n🟢 Style VERT (Montant Global > TTC):")
    print("""
    border: 2px solid #10B981; 
    border-radius: 4px; 
    padding: 8px; 
    background-color: #F0FDF4; 
    color: #059669;
    font-weight: bold;
    """)
    
    # Style Bleu (Montant Global = TTC)
    print("\n🔵 Style BLEU (Montant Global = TTC):")
    print("""
    border: 2px solid #3B82F6; 
    border-radius: 4px; 
    padding: 8px; 
    background-color: #EFF6FF; 
    color: #1D4ED8;
    font-weight: bold;
    """)
    
    print("\n📝 Instructions pour tester dans l'interface:")
    print("1. Ouvrez l'application")
    print("2. Allez dans 'Bons de Commande'")
    print("3. Cliquez 'Ajouter un bon'")
    print("4. Ajoutez des articles pour avoir un Total TTC (ex: CAMERA = 2160 DH TTC)")
    print("5. Dans le champ 'MONTANT GLOBAL', tapez:")
    print("   - 2000 → Le champ devient ROUGE (2000 < 2160)")
    print("   - 2500 → Le champ devient VERT (2500 > 2160)")
    print("   - 2160 → Le champ devient BLEU (2160 = 2160)")
    
    print("\n🎯 Scénarios de test recommandés:")
    print("• Ajouter CAMERA (1800 DH) → TTC = 2160 DH")
    print("  - Montant Global 2000 → 🔴 ROUGE")
    print("  - Montant Global 2500 → 🟢 VERT")
    print("  - Montant Global 2160 → 🔵 BLEU")
    
    print("• Ajouter DISJONCTEUR (25000 DH) → TTC = 30000 DH")
    print("  - Montant Global 25000 → 🔴 ROUGE")
    print("  - Montant Global 35000 → 🟢 VERT")
    print("  - Montant Global 30000 → 🔵 BLEU")
    
    print("\n✅ Test terminé! La logique de comparaison est correcte.")

if __name__ == "__main__":
    test_montant_global_color_logic()
