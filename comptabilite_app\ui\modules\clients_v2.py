from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QHeaderView, QMessageBox, QDialog)
from PySide6.QtCore import Qt, QRegularExpression, Signal
from PySide6.QtGui import QRegularExpressionValidator
import sqlite3

# Importer les composants de base
from ..components.base_module import BaseModule
from ..components.base_list_view import BaseListView
from ..components.base_form_dialog import BaseFormDialog

# Importer les styles et icônes
from ..theme import COLORS, BORDER_RADIUS, SPACING
from ..icons.icons import CLIENTS_ICON

class ClientFormDialog(BaseFormDialog):
    """Boîte de dialogue pour ajouter ou modifier un client"""

    def __init__(self, db_manager, client_id=None, parent=None):
        self.client_id = client_id
        is_edit_mode = client_id is not None
        title = "Modifier un client" if is_edit_mode else "Ajouter un client"

        super().__init__(db_manager, title, is_edit_mode, parent)

        # Initialiser les champs du formulaire
        self.setup_form()

        # Charger les données si on est en mode édition
        if is_edit_mode:
            self.load_data(client_id)
        else:
            # Générer un nouveau code client
            self.code_input.setText(self.db_manager.generer_code_client())

    def setup_form(self):
        """Configure les champs du formulaire"""
        # Code client
        self.code_input = QLineEdit()
        self.code_input.setReadOnly(True)
        self.code_input.setStyleSheet("background-color: #F3F4F6; color: #6B7280;")
        self.create_form_field("Code client", self.code_input, row=0, col=0)

        # Nom / Raison sociale
        self.nom_input = QLineEdit()
        self.nom_input.setPlaceholderText("Obligatoire")
        self.create_form_field("Nom / Raison sociale", self.nom_input, required=True, row=1, col=0)

        # ICE
        self.ice_input = QLineEdit()
        self.ice_input.setPlaceholderText("Identifiant Commun de l'Entreprise")
        self.create_form_field("ICE", self.ice_input, row=2, col=0)

        # IF
        self.if_input = QLineEdit()
        self.if_input.setPlaceholderText("Identifiant Fiscal")
        self.create_form_field("IF", self.if_input, row=3, col=0)

        # Adresse
        self.adresse_input = QLineEdit()
        self.create_form_field("Adresse", self.adresse_input, row=0, col=1)

        # Téléphone
        self.telephone_input = QLineEdit()
        telephone_regex = QRegularExpression("^[0-9+\\-\\s]{8,15}$")
        self.telephone_input.setValidator(QRegularExpressionValidator(telephone_regex))
        self.create_form_field("Téléphone", self.telephone_input, row=1, col=1)

        # E-mail
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("<EMAIL>")
        self.create_form_field("E-mail", self.email_input, row=2, col=1)

        # Personne à contacter
        self.contact_input = QLineEdit()
        self.create_form_field("Personne à contacter", self.contact_input, row=3, col=1)

    def validate_form(self):
        """Valide les données du formulaire"""
        nom = self.nom_input.text().strip()
        if not nom:
            QMessageBox.warning(self, "Erreur", "Le nom du client est obligatoire.")
            return False

        return True

    def save_data(self):
        """Enregistre les données du formulaire"""
        nom = self.nom_input.text().strip()
        code = self.code_input.text()

        cursor = self.db_manager.conn.cursor()

        try:
            if self.is_edit_mode:
                # Modification d'un client existant
                cursor.execute(
                    """UPDATE clients SET
                       code = ?, nom = ?, ice = ?, if_fiscal = ?, adresse = ?,
                       telephone = ?, email = ?, contact = ?
                       WHERE id = ?""",
                    (code, nom, self.ice_input.text(), self.if_input.text(),
                     self.adresse_input.text(), self.telephone_input.text(),
                     self.email_input.text(), self.contact_input.text(), self.client_id)
                )
                message = f"Client {nom} modifié avec succès."
            else:
                # Ajout d'un nouveau client
                cursor.execute(
                    """INSERT INTO clients
                       (code, nom, ice, if_fiscal, adresse, telephone, email, contact)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                    (code, nom, self.ice_input.text(), self.if_input.text(),
                     self.adresse_input.text(), self.telephone_input.text(),
                     self.email_input.text(), self.contact_input.text())
                )
                message = f"Client {nom} ajouté avec succès.\nCode client: {code}"

            self.db_manager.conn.commit()
            QMessageBox.information(self, "Succès", message)

        except sqlite3.IntegrityError:
            QMessageBox.warning(self, "Erreur", "Ce code client existe déjà. Veuillez utiliser un autre code.")
            raise

    def load_data(self, client_id):
        """Charge les données d'un client existant"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT * FROM clients WHERE id = ?", (client_id,))
        client = cursor.fetchone()

        if client:
            self.code_input.setText(client['code'] or "")
            self.nom_input.setText(client['nom'])
            self.ice_input.setText(client['ice'] or "")
            self.if_input.setText(client['if_fiscal'] or "")
            self.adresse_input.setText(client['adresse'] or "")
            self.telephone_input.setText(client['telephone'] or "")
            self.email_input.setText(client['email'] or "")
            self.contact_input.setText(client['contact'] or "")

class ClientsListView(BaseListView):
    """Vue en liste des clients"""

    def __init__(self, db_manager, parent=None):
        super().__init__(
            db_manager=db_manager,
            title="Liste des Clients",
            description="Consultez et gérez vos clients",
            icon=CLIENTS_ICON,
            add_button_text="Ajouter un client"
        )

        # Configurer le tableau
        self.setup_table()

        # Charger les données
        self.load_clients()

    def setup_table(self):
        """Configure le tableau des clients"""
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "Code", "Nom", "ICE", "IF", "Téléphone", "Adresse", "Actions"
        ])

        # Configuration des colonnes
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Code
        self.items_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)  # Nom
        self.items_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)  # ICE
        self.items_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # IF
        self.items_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Téléphone
        self.items_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Stretch)  # Adresse
        self.items_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Actions

    def load_clients(self):
        """Charge les clients depuis la base de données"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT * FROM clients ORDER BY code")
        clients = cursor.fetchall()

        self.items_table.setRowCount(0)

        for row_num, client in enumerate(clients):
            self.items_table.insertRow(row_num)

            # Ajouter les données du client
            self.items_table.setItem(row_num, 0, QTableWidgetItem(client['code'] or ""))
            self.items_table.setItem(row_num, 1, QTableWidgetItem(client['nom']))
            self.items_table.setItem(row_num, 2, QTableWidgetItem(client['ice'] or ""))
            self.items_table.setItem(row_num, 3, QTableWidgetItem(client['if_fiscal'] or ""))
            self.items_table.setItem(row_num, 4, QTableWidgetItem(client['telephone'] or ""))
            self.items_table.setItem(row_num, 5, QTableWidgetItem(client['adresse'] or ""))

            # Créer les boutons d'action
            actions_widget = self.create_action_buttons(row_num, client['id'], client['nom'])
            self.items_table.setCellWidget(row_num, 6, actions_widget)

            # Stocker l'ID du client dans la première colonne (invisible)
            self.items_table.item(row_num, 0).setData(Qt.UserRole, client['id'])

    def on_add_clicked(self):
        """Affiche la boîte de dialogue pour ajouter un client"""
        dialog = ClientFormDialog(self.db_manager, parent=self)
        dialog.form_submitted.connect(self.on_client_saved)
        dialog.exec()

    def on_edit_clicked(self, client_id):
        """Affiche la boîte de dialogue pour modifier un client"""
        dialog = ClientFormDialog(self.db_manager, client_id=client_id, parent=self)
        dialog.form_submitted.connect(self.on_client_saved)
        dialog.exec()

    def on_delete_clicked(self, client_id, client_name):
        """Supprime un client"""
        confirm = QMessageBox.question(
            self, "Confirmation",
            f"Êtes-vous sûr de vouloir supprimer le client {client_name} ?\nCette action est irréversible.",
            QMessageBox.Yes | QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                cursor = self.db_manager.conn.cursor()
                cursor.execute("DELETE FROM clients WHERE id = ?", (client_id,))
                self.db_manager.conn.commit()

                # إعادة ترقيم جميع العملاء تلقائياً
                self.db_manager.renumber_all_clients()

                self.load_clients()
                self.item_changed.emit()

                QMessageBox.information(self, "Succès", f"Client {client_name} supprimé avec succès.")
            except sqlite3.Error as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression du client: {str(e)}")

    def on_client_saved(self):
        """Appelé lorsqu'un client est ajouté ou modifié"""
        self.load_clients()
        self.item_changed.emit()

class ClientsModule(QWidget):
    """Module de gestion des clients avec interface simplifiée"""

    def __init__(self, db_manager, signals=None):
        super().__init__()
        self.db_manager = db_manager
        self.signals = signals

        # Configurer l'interface
        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Créer la vue en liste
        self.clients_list_view = ClientsListView(self.db_manager, self)
        self.clients_list_view.item_changed.connect(self.on_client_changed)

        # Ajouter la vue en liste au layout
        layout.addWidget(self.clients_list_view)

    def on_client_changed(self):
        """Appelé lorsqu'un client est ajouté, modifié ou supprimé"""
        # Émettre le signal pour informer les autres modules
        if self.signals:
            self.signals.clients_changed.emit()

    def show_add_dialog(self):
        """Affiche la boîte de dialogue pour ajouter un client"""
        self.clients_list_view.on_add_clicked()

    def show_edit_dialog(self, client_id):
        """Affiche la boîte de dialogue pour modifier un client"""
        self.clients_list_view.on_edit_clicked(client_id)
