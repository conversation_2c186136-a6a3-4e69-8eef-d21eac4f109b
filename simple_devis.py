#!/usr/bin/env python3
"""
نموذج عرض أسعار مبسط لاختبار المكتبات
"""

try:
    import openpyxl
    print("✅ openpyxl متوفرة")
except ImportError:
    print("❌ openpyxl غير متوفرة - يجب تثبيتها")
    print("pip install openpyxl")
    exit(1)

from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.datavalidation import DataValidation

def create_simple_devis():
    """إنشاء نموذج عرض أسعار بسيط"""
    
    # إنشاء مصنف جديد
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Devis"
    
    print("🔧 إنشاء النموذج...")
    
    # العنوان الرئيسي
    ws.merge_cells('B2:H4')
    ws['B2'] = "عرض أسعار / DEVIS"
    ws['B2'].font = Font(name='Calibri', size=16, bold=True, color='1E3A8A')
    ws['B2'].alignment = Alignment(horizontal='center', vertical='center')
    ws['B2'].fill = PatternFill(start_color='E8F4FD', end_color='E8F4FD', fill_type='solid')
    
    # معلومات أساسية
    ws['B6'] = "رقم العرض:"
    ws['C6'] = "DEV-2024-001"
    ws['F6'] = "التاريخ:"
    ws['G6'] = "2024-01-01"
    
    # قسم العميل
    ws['B8'] = "نوع العميل:"
    ws['C8'] = "Privé"
    ws['B9'] = "العميل:"
    ws['C9'] = "Client Test"
    
    # جدول المنتجات
    headers = ["N°", "المنتج", "الوحدة", "الكمية", "سعر الشراء", "نسبة الربح", "سعر البيع", "المجموع"]
    for i, header in enumerate(headers):
        col = chr(ord('B') + i)
        ws[f'{col}12'] = header
        ws[f'{col}12'].font = Font(bold=True)
        ws[f'{col}12'].fill = PatternFill(start_color='4A90E2', end_color='4A90E2', fill_type='solid')
    
    # صف مثال
    ws['B13'] = "1"
    ws['C13'] = "منتج تجريبي"
    ws['D13'] = "قطعة"
    ws['E13'] = "10"
    ws['F13'] = "100"
    ws['G13'] = "1.2"
    ws['H13'] = "=F13*G13"
    ws['I13'] = "=E13*H13"
    
    # المجاميع
    ws['H15'] = "المجموع HT:"
    ws['I15'] = "=I13"
    ws['H16'] = "TVA 20%:"
    ws['I16'] = "=I15*0.2"
    ws['H17'] = "المجموع TTC:"
    ws['I17'] = "=I15+I16"
    
    # إعداد عرض الأعمدة
    column_widths = {'B': 5, 'C': 20, 'D': 12, 'E': 10, 'F': 12, 'G': 12, 'H': 15, 'I': 15}
    for col, width in column_widths.items():
        ws.column_dimensions[col].width = width
    
    # إخفاء خطوط الشبكة
    ws.sheet_view.showGridLines = False
    
    # حفظ الملف
    filename = "devis_simple.xlsx"
    wb.save(filename)
    print(f"✅ تم إنشاء النموذج: {filename}")
    
    return wb

if __name__ == "__main__":
    create_simple_devis()
    print("🎉 تم إنشاء نموذج عرض الأسعار البسيط!")
