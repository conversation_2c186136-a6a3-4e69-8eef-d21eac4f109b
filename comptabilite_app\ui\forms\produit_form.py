from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                               QLineEdit, QDoubleSpinBox, QSpinBox, QPushButton,
                               QFormLayout, QMessageBox, QComboBox, QDateEdit,
                               QCompleter)
from PySide6.QtCore import Qt, Signal, QDate, QStringListModel
from PySide6.QtGui import QKeySequence, QShortcut
from ..components.famille_dialog import FamilleDialog

class ProduitForm(QDialog):
    """Formulaire d'ajout/modification de produit"""

    produit_added = Signal()  # Signal émis lorsqu'un produit est ajouté
    produit_updated = Signal(int)  # Signal émis lorsqu'un produit est modifié

    def __init__(self, db_manager, produit_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.produit_id = produit_id  # None pour un nouveau produit, ID pour modification

        # Exécuter la migration TTC si nécessaire
        self.ensure_ttc_columns()

        self.setup_ui()

        # Charger les fournisseurs et les familles
        self.load_fournisseurs()
        self.load_familles()

        if produit_id:
            self.load_produit(produit_id)
            self.setWindowTitle("Modifier un produit")
        else:
            self.generate_product_code()
            self.setWindowTitle("Ajouter un produit")

    def ensure_ttc_columns(self):
        """S'assurer que les colonnes TTC existent dans la table produits"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Vérifier les colonnes existantes
            cursor.execute("PRAGMA table_info(produits)")
            columns = cursor.fetchall()
            column_names = [column['name'] for column in columns]

            # Ajouter les colonnes TTC si elles n'existent pas
            if 'prix_achat_ttc' not in column_names:
                cursor.execute("ALTER TABLE produits ADD COLUMN prix_achat_ttc REAL DEFAULT 0")
                print("✅ Colonne prix_achat_ttc ajoutée")

            if 'prix_vente_ttc' not in column_names:
                cursor.execute("ALTER TABLE produits ADD COLUMN prix_vente_ttc REAL DEFAULT 0")
                print("✅ Colonne prix_vente_ttc ajoutée")

            if 'tva_rate' not in column_names:
                cursor.execute("ALTER TABLE produits ADD COLUMN tva_rate REAL DEFAULT 20.0")
                print("✅ Colonne tva_rate ajoutée")

            self.db_manager.conn.commit()

        except Exception as e:
            print(f"❌ Erreur lors de l'ajout des colonnes TTC: {str(e)}")

    def load_fournisseurs(self):
        """Charger la liste des fournisseurs depuis la base de données"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT id, nom FROM fournisseurs ORDER BY nom")
            fournisseurs = cursor.fetchall()

            self.fournisseur_combo.clear()
            self.fournisseur_combo.addItem("Sélectionner un fournisseur", None)
            for fournisseur in fournisseurs:
                self.fournisseur_combo.addItem(fournisseur['nom'], fournisseur['id'])
        except Exception as e:
            print(f"Erreur lors du chargement des fournisseurs: {str(e)}")

    def load_familles(self):
        """Charger la liste des familles de produits depuis la base de données"""
        try:
            self.famille_combo.clear()
            self.famille_combo.addItem("-- Aucune famille --", None)

            familles = self.db_manager.get_familles_produits()
            for famille in familles:
                self.famille_combo.addItem(famille['nom'], famille['id'])
        except Exception as e:
            print(f"Erreur lors du chargement des familles: {str(e)}")

    def show_add_famille_dialog(self):
        """Affiche la boîte de dialogue pour ajouter une nouvelle famille"""
        dialog = FamilleDialog(self.db_manager, self)
        dialog.famille_added.connect(self.on_famille_added)
        dialog.exec()

    def on_famille_added(self, famille_id, famille_nom):
        """Appelé lorsqu'une nouvelle famille est ajoutée"""
        # Recharger la liste des familles
        self.load_familles()
        # Sélectionner la nouvelle famille
        index = self.famille_combo.findData(famille_id)
        if index >= 0:
            self.famille_combo.setCurrentIndex(index)

    def setup_ui(self):
        self.resize(800, 700)
        self.setStyleSheet("""
            QDialog {
                background-color: white;
            }
            QLabel {
                font-size: 12px;
            }
            QLineEdit, QSpinBox, QDoubleSpinBox {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                min-height: 20px;
                min-width: 200px;
            }
            QPushButton {
                background-color: #1E3A8A;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
            QPushButton#cancel_btn {
                background-color: #6B7280;
            }
            QPushButton#cancel_btn:hover {
                background-color: #4B5563;
            }
            #title_label {
                font-size: 18px;
                font-weight: bold;
                color: #1E3A8A;
                margin-bottom: 20px;
            }
            #required_label {
                color: red;
            }
        """)

        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # Titre
        title_label = QLabel("Informations du produit")
        title_label.setObjectName("title_label")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # Formulaire
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)
        form_layout.setFormAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        form_layout.setSpacing(10)

        # Code produit
        code_label = QLabel("Code produit:")
        self.code_input = QLineEdit()
        self.code_input.setReadOnly(True)  # Le code est généré automatiquement
        form_layout.addRow(code_label, self.code_input)

        # Désignation
        designation_label = QLabel("Désignation*:")
        self.designation_input = QLineEdit()
        form_layout.addRow(designation_label, self.designation_input)

        # Unité
        unite_label = QLabel("Unité:")
        self.unite_input = QLineEdit()
        form_layout.addRow(unite_label, self.unite_input)

        # Famille de produit
        famille_layout = QHBoxLayout()
        self.famille_combo = QComboBox()
        self.famille_combo.setMinimumWidth(200)
        famille_layout.addWidget(self.famille_combo)

        # Bouton pour ajouter une nouvelle famille
        add_famille_btn = QPushButton("+")
        add_famille_btn.setFixedSize(30, 30)
        add_famille_btn.setToolTip("Ajouter une nouvelle famille")
        add_famille_btn.clicked.connect(self.show_add_famille_dialog)
        famille_layout.addWidget(add_famille_btn)
        famille_layout.addStretch()

        form_layout.addRow("العائلة / Famille:", famille_layout)

        # Taux de TVA
        tva_layout = QHBoxLayout()
        self.tva_combo = QComboBox()
        self.tva_combo.addItems(["20%", "10%", "5.5%", "2.1%", "0%"])
        self.tva_combo.setCurrentText("20%")
        self.tva_combo.setMinimumWidth(100)
        self.tva_combo.currentTextChanged.connect(self.calculate_ttc_prices)
        tva_layout.addWidget(self.tva_combo)
        tva_layout.addStretch()
        form_layout.addRow("Taux de TVA:", tva_layout)

        # Prix d'achat HT
        prix_achat_layout = QHBoxLayout()
        self.prix_achat_input = QDoubleSpinBox()
        self.prix_achat_input.setMinimum(0)
        self.prix_achat_input.setMaximum(999999.99)
        self.prix_achat_input.setDecimals(2)
        self.prix_achat_input.setSuffix(" DH")
        self.prix_achat_input.valueChanged.connect(self.calculate_ttc_prices)
        prix_achat_layout.addWidget(self.prix_achat_input)
        prix_achat_layout.addStretch()
        form_layout.addRow("Prix d'achat (HT):", prix_achat_layout)

        # TVA d'achat (calculée automatiquement)
        tva_achat_layout = QHBoxLayout()
        self.tva_achat_input = QDoubleSpinBox()
        self.tva_achat_input.setMinimum(0)
        self.tva_achat_input.setMaximum(999999.99)
        self.tva_achat_input.setDecimals(2)
        self.tva_achat_input.setSuffix(" DH")
        self.tva_achat_input.setReadOnly(True)
        self.tva_achat_input.setStyleSheet("background-color: #e8f4fd; color: #1e40af; font-weight: bold;")
        tva_achat_layout.addWidget(self.tva_achat_input)
        tva_note = QLabel("(Montant TVA)")
        tva_note.setStyleSheet("color: #1e40af; font-size: 10px; font-weight: bold;")
        tva_achat_layout.addWidget(tva_note)
        tva_achat_layout.addStretch()
        form_layout.addRow("TVA d'achat:", tva_achat_layout)

        # Prix d'achat TTC (calculé automatiquement)
        prix_achat_ttc_layout = QHBoxLayout()
        self.prix_achat_ttc_input = QDoubleSpinBox()
        self.prix_achat_ttc_input.setMinimum(0)
        self.prix_achat_ttc_input.setMaximum(999999.99)
        self.prix_achat_ttc_input.setDecimals(2)
        self.prix_achat_ttc_input.setSuffix(" DH")
        self.prix_achat_ttc_input.setReadOnly(True)
        self.prix_achat_ttc_input.setStyleSheet("background-color: #f0f0f0;")
        prix_achat_ttc_layout.addWidget(self.prix_achat_ttc_input)
        ttc_note = QLabel("(Calculé automatiquement)")
        ttc_note.setStyleSheet("color: #666; font-size: 10px;")
        prix_achat_ttc_layout.addWidget(ttc_note)
        prix_achat_ttc_layout.addStretch()
        form_layout.addRow("Prix d'achat (TTC):", prix_achat_ttc_layout)

        # Prix de vente HT
        prix_vente_layout = QHBoxLayout()
        self.prix_vente_input = QDoubleSpinBox()
        self.prix_vente_input.setMinimum(0)
        self.prix_vente_input.setMaximum(999999.99)
        self.prix_vente_input.setDecimals(2)
        self.prix_vente_input.setSuffix(" DH")
        self.prix_vente_input.valueChanged.connect(self.calculate_ttc_prices)
        prix_vente_layout.addWidget(self.prix_vente_input)
        prix_vente_layout.addStretch()
        form_layout.addRow("Prix de vente (HT):", prix_vente_layout)

        # TVA de vente (calculée automatiquement)
        tva_vente_layout = QHBoxLayout()
        self.tva_vente_input = QDoubleSpinBox()
        self.tva_vente_input.setMinimum(0)
        self.tva_vente_input.setMaximum(999999.99)
        self.tva_vente_input.setDecimals(2)
        self.tva_vente_input.setSuffix(" DH")
        self.tva_vente_input.setReadOnly(True)
        self.tva_vente_input.setStyleSheet("background-color: #e8f4fd; color: #1e40af; font-weight: bold;")
        tva_vente_layout.addWidget(self.tva_vente_input)
        tva_note_vente = QLabel("(Montant TVA)")
        tva_note_vente.setStyleSheet("color: #1e40af; font-size: 10px; font-weight: bold;")
        tva_vente_layout.addWidget(tva_note_vente)
        tva_vente_layout.addStretch()
        form_layout.addRow("TVA de vente:", tva_vente_layout)

        # Prix de vente TTC (calculé automatiquement)
        prix_vente_ttc_layout = QHBoxLayout()
        self.prix_vente_ttc_input = QDoubleSpinBox()
        self.prix_vente_ttc_input.setMinimum(0)
        self.prix_vente_ttc_input.setMaximum(999999.99)
        self.prix_vente_ttc_input.setDecimals(2)
        self.prix_vente_ttc_input.setSuffix(" DH")
        self.prix_vente_ttc_input.setReadOnly(True)
        self.prix_vente_ttc_input.setStyleSheet("background-color: #f0f0f0;")
        prix_vente_ttc_layout.addWidget(self.prix_vente_ttc_input)
        ttc_note2 = QLabel("(Calculé automatiquement)")
        ttc_note2.setStyleSheet("color: #666; font-size: 10px;")
        prix_vente_ttc_layout.addWidget(ttc_note2)
        prix_vente_ttc_layout.addStretch()
        form_layout.addRow("Prix de vente (TTC):", prix_vente_ttc_layout)

        # Quantité en stock
        stock_layout = QHBoxLayout()
        self.stock_input = QSpinBox()
        self.stock_input.setMinimum(0)
        self.stock_input.setMaximum(999999)
        stock_layout.addWidget(self.stock_input)
        stock_layout.addStretch()
        form_layout.addRow("Quantité en stock:", stock_layout)

        # Séparateur pour les informations d'achat
        separator_label = QLabel("Informations d'achat")
        separator_label.setStyleSheet("font-weight: bold; margin-top: 15px;")
        form_layout.addRow("", separator_label)

        # Fournisseur
        self.fournisseur_combo = QComboBox()
        self.fournisseur_combo.setMinimumHeight(35)
        self.fournisseur_combo.setStyleSheet("""
            QComboBox {
                padding: 5px 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #ddd;
            }
        """)
        form_layout.addRow("Fournisseur:", self.fournisseur_combo)

        # Numéro de facture d'achat
        self.facture_input = QLineEdit()
        self.facture_input.setMinimumHeight(35)
        form_layout.addRow("Numéro de facture d'achat:", self.facture_input)

        # Date
        from ..style import create_styled_date_edit
        self.date_input = create_styled_date_edit()
        self.date_input.setMinimumHeight(35)
        form_layout.addRow("Date:", self.date_input)

        # Date de paiement
        self.date_paiement_input = create_styled_date_edit()
        self.date_paiement_input.setMinimumHeight(35)
        form_layout.addRow("Date de paiement:", self.date_paiement_input)

        # Mode de paiement
        self.payment_combo = QComboBox()
        self.payment_combo.addItem("Sélectionner", None)
        self.payment_combo.addItems(["Espèces", "Chèque", "Virement", "Carte bancaire"])
        self.payment_combo.setCurrentIndex(0)
        self.payment_combo.setMinimumHeight(35)
        self.payment_combo.setStyleSheet("""
            QComboBox {
                padding: 5px 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #ddd;
            }
        """)
        form_layout.addRow("Mode de paiement:", self.payment_combo)

        main_layout.addLayout(form_layout)

        # Note sur les champs obligatoires
        required_label = QLabel("* Champs obligatoires")
        required_label.setObjectName("required_label")
        required_label.setAlignment(Qt.AlignRight)
        main_layout.addWidget(required_label)

        main_layout.addStretch()

        # Boutons d'action
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        cancel_btn = QPushButton("Annuler")
        cancel_btn.setObjectName("cancel_btn")
        cancel_btn.clicked.connect(self.reject)

        save_btn = QPushButton("Enregistrer")
        save_btn.clicked.connect(self.save_produit)

        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)

        main_layout.addLayout(buttons_layout)

    def calculate_ttc_prices(self):
        """Calcule automatiquement les prix TTC et les montants TVA à partir des prix HT et du taux de TVA"""
        try:
            # Récupérer le taux de TVA
            tva_text = self.tva_combo.currentText()
            tva_rate = float(tva_text.replace('%', '')) / 100

            # Calculer pour le prix d'achat
            prix_achat_ht = self.prix_achat_input.value()
            tva_achat_montant = prix_achat_ht * tva_rate
            prix_achat_ttc = prix_achat_ht + tva_achat_montant

            # Mettre à jour les champs d'achat
            self.tva_achat_input.setValue(tva_achat_montant)
            self.prix_achat_ttc_input.setValue(prix_achat_ttc)

            # Calculer pour le prix de vente
            prix_vente_ht = self.prix_vente_input.value()
            tva_vente_montant = prix_vente_ht * tva_rate
            prix_vente_ttc = prix_vente_ht + tva_vente_montant

            # Mettre à jour les champs de vente
            self.tva_vente_input.setValue(tva_vente_montant)
            self.prix_vente_ttc_input.setValue(prix_vente_ttc)

        except (ValueError, AttributeError) as e:
            print(f"Erreur lors du calcul des prix TTC: {str(e)}")

    def generate_product_code(self):
        """Générer un code produit unique"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT MAX(CAST(SUBSTR(code, 2) AS INTEGER)) FROM produits WHERE code LIKE 'P%'")
            result = cursor.fetchone()[0]

            if result is None:
                next_num = 1
            else:
                next_num = result + 1

            self.code_input.setText(f"P{next_num:03d}")
        except Exception as e:
            print(f"Erreur lors de la génération du code produit: {str(e)}")
            self.code_input.setText("P001")

    def load_produit(self, produit_id):
        """Charger les données d'un produit existant"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Récupérer les informations de base du produit avec les nouvelles colonnes
            cursor.execute("""
                SELECT p.code, p.designation, p.unite, p.prix_achat, p.prix_vente, p.stock, p.date_paiement,
                       p.prix_achat_ttc, p.prix_vente_ttc, p.tva_rate,
                       p.fournisseur, p.numero_facture, p.date_facture, p.mode_paiement, p.famille_id,
                       fa.fournisseur_id, fa.numero as fa_numero, fa.date_creation, fa.mode_paiement as fa_mode_paiement
                FROM produits p
                LEFT JOIN factures_achat fa ON p.facture_achat_id = fa.id
                WHERE p.id = ?
            """, (produit_id,))

            produit = cursor.fetchone()

            if produit:
                # Informations de base du produit
                self.code_input.setText(produit['code'])
                self.designation_input.setText(produit['designation'])
                self.unite_input.setText(produit['unite'])
                self.prix_achat_input.setValue(produit['prix_achat'])
                self.prix_vente_input.setValue(produit['prix_vente'])
                self.stock_input.setValue(produit['stock'])

                # Charger le taux de TVA s'il existe
                if produit['tva_rate'] is not None:
                    tva_text = f"{produit['tva_rate']:.1f}%"
                    index = self.tva_combo.findText(tva_text)
                    if index >= 0:
                        self.tva_combo.setCurrentIndex(index)
                    else:
                        # Si le taux n'est pas dans la liste, l'ajouter
                        self.tva_combo.addItem(tva_text)
                        self.tva_combo.setCurrentText(tva_text)

                # Charger les prix TTC s'ils existent, sinon les calculer
                if produit['prix_achat_ttc'] is not None and produit['prix_vente_ttc'] is not None:
                    self.prix_achat_ttc_input.setValue(produit['prix_achat_ttc'])
                    self.prix_vente_ttc_input.setValue(produit['prix_vente_ttc'])

                    # Calculer les montants TVA à partir des prix existants
                    tva_achat = produit['prix_achat_ttc'] - produit['prix_achat']
                    tva_vente = produit['prix_vente_ttc'] - produit['prix_vente']
                    self.tva_achat_input.setValue(tva_achat)
                    self.tva_vente_input.setValue(tva_vente)
                else:
                    # Calculer automatiquement les prix TTC et TVA après avoir chargé les prix HT
                    self.calculate_ttc_prices()

                # Sélectionner la famille
                if produit['famille_id']:
                    index = self.famille_combo.findData(produit['famille_id'])
                    if index >= 0:
                        self.famille_combo.setCurrentIndex(index)

                # Informations d'achat - priorité aux colonnes directes du produit
                # Fournisseur - utiliser d'abord la colonne directe, sinon la table factures_achat
                fournisseur_name = produit['fournisseur']
                if not fournisseur_name and produit['fournisseur_id']:
                    # Récupérer le nom du fournisseur depuis la table fournisseurs
                    cursor.execute("SELECT nom FROM fournisseurs WHERE id = ?", (produit['fournisseur_id'],))
                    fournisseur_result = cursor.fetchone()
                    if fournisseur_result:
                        fournisseur_name = fournisseur_result['nom']

                # Ajouter le fournisseur au combo s'il n'existe pas
                if fournisseur_name:
                    # Chercher d'abord dans la liste existante
                    found_index = -1
                    for i in range(self.fournisseur_combo.count()):
                        if self.fournisseur_combo.itemText(i) == fournisseur_name:
                            found_index = i
                            break

                    if found_index >= 0:
                        self.fournisseur_combo.setCurrentIndex(found_index)
                    else:
                        # Ajouter le fournisseur à la liste
                        self.fournisseur_combo.addItem(fournisseur_name, None)
                        self.fournisseur_combo.setCurrentText(fournisseur_name)

                # Numéro de facture - priorité à la colonne directe
                numero_facture = produit['numero_facture'] or produit['fa_numero'] or ''
                if numero_facture:
                    self.facture_input.setText(numero_facture)

                # Date de facture - priorité à la colonne directe
                date_facture = produit['date_facture'] or produit['date_creation']
                if date_facture:
                    try:
                        date_obj = QDate.fromString(date_facture, "yyyy/MM/dd")
                        self.date_input.setDate(date_obj)
                    except:
                        # En cas d'erreur, définir une date vide
                        self.date_input.setDate(QDate())

                # Mode de paiement - priorité à la colonne directe
                mode_paiement = produit['mode_paiement'] or produit['fa_mode_paiement']
                if mode_paiement:
                    index = self.payment_combo.findText(mode_paiement)
                    if index >= 0:
                        self.payment_combo.setCurrentIndex(index)

                # Date de paiement du produit
                if produit['date_paiement']:
                    try:
                        date_paiement_obj = QDate.fromString(produit['date_paiement'], "yyyy/MM/dd")
                        if date_paiement_obj.isValid():
                            self.date_paiement_input.setDate(date_paiement_obj)
                        else:
                            self.date_paiement_input.setDate(QDate()) # Set to null date if invalid
                    except:
                        self.date_paiement_input.setDate(QDate())  # Date vide si erreur
                else:
                    self.date_paiement_input.setDate(QDate())  # Date vide
            else:
                QMessageBox.warning(self, "Erreur", "Produit non trouvé.")
                self.reject()
        except Exception as e:
            print(f"Erreur lors du chargement du produit: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue: {str(e)}")
            self.reject()

    def save_produit(self):
        """Enregistrer le produit dans la base de données"""
        # Vérifier que tous les champs obligatoires sont remplis
        if not self.validate_form():
            return

        try:
            # Récupérer les données du formulaire
            code = self.code_input.text()
            designation = self.designation_input.text()
            unite = self.unite_input.text()
            prix_achat = self.prix_achat_input.value()
            prix_vente = self.prix_vente_input.value()
            prix_achat_ttc = self.prix_achat_ttc_input.value()
            prix_vente_ttc = self.prix_vente_ttc_input.value()
            tva_rate = float(self.tva_combo.currentText().replace('%', ''))
            stock = self.stock_input.value()

            # Informations d'achat
            fournisseur_id = self.fournisseur_combo.currentData()
            facture_numero = self.facture_input.text()
            date_achat = self.date_input.date().toString("yyyy/MM/dd")
            mode_paiement = self.payment_combo.currentText()
            if self.payment_combo.currentIndex() == 0:
                mode_paiement = None

            # Date de paiement
            date_paiement = None
            if self.date_paiement_input.date().isValid():
                date_paiement = self.date_paiement_input.date().toString("yyyy/MM/dd")

            # Famille de produit
            famille_id = self.famille_combo.currentData()

            cursor = self.db_manager.conn.cursor()

            # Vérifier si une facture d'achat existe déjà avec ce numéro
            facture_achat_id = None
            if facture_numero and fournisseur_id:
                cursor.execute("""
                    SELECT id FROM factures_achat
                    WHERE numero = ? AND fournisseur_id = ?
                """, (facture_numero, fournisseur_id))
                result = cursor.fetchone()

                if result:
                    facture_achat_id = result['id']
                else:
                    # Créer une nouvelle facture d'achat
                    cursor.execute("""
                        INSERT INTO factures_achat (fournisseur_id, numero, date_creation, mode_paiement, total_ht)
                        VALUES (?, ?, ?, ?, ?)
                    """, (fournisseur_id, facture_numero, date_achat, mode_paiement, prix_achat))
                    facture_achat_id = cursor.lastrowid

            if self.produit_id:  # Modification d'un produit existant
                # Récupérer le nom du fournisseur pour les colonnes directes
                fournisseur_name = self.fournisseur_combo.currentText() if self.fournisseur_combo.currentIndex() > 0 else None

                cursor.execute("""
                    UPDATE produits
                    SET designation = ?, unite = ?, prix_achat = ?, prix_vente = ?, stock = ?,
                        prix_achat_ttc = ?, prix_vente_ttc = ?, tva_rate = ?,
                        facture_achat_id = ?, date_paiement = ?, famille_id = ?,
                        fournisseur = ?, numero_facture = ?, date_facture = ?, mode_paiement = ?
                    WHERE id = ?
                """, (designation, unite, prix_achat, prix_vente, stock,
                      prix_achat_ttc, prix_vente_ttc, tva_rate,
                      facture_achat_id, date_paiement, famille_id,
                      fournisseur_name, facture_numero, date_achat, mode_paiement, self.produit_id))

                self.db_manager.conn.commit()
                QMessageBox.information(self, "Succès", "Le produit a été mis à jour avec succès.")
                self.produit_updated.emit(self.produit_id)
            else:  # Ajout d'un nouveau produit
                # Récupérer le nom du fournisseur pour les colonnes directes
                fournisseur_name = self.fournisseur_combo.currentText() if self.fournisseur_combo.currentIndex() > 0 else None

                cursor.execute("""
                    INSERT INTO produits (code, designation, unite, prix_achat, prix_vente, stock,
                                        prix_achat_ttc, prix_vente_ttc, tva_rate,
                                        facture_achat_id, date_paiement, famille_id, fournisseur, numero_facture, date_facture, mode_paiement)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (code, designation, unite, prix_achat, prix_vente, stock,
                      prix_achat_ttc, prix_vente_ttc, tva_rate,
                      facture_achat_id, date_paiement, famille_id,
                      fournisseur_name, facture_numero, date_achat, mode_paiement))

                self.db_manager.conn.commit()

                # Intégrer avec la caisse si paiement en espèces
                if mode_paiement == "Espèces" and stock > 0 and prix_achat > 0:
                    self.integrate_with_caisse_new_product(code, designation, prix_achat, stock, facture_numero)

                QMessageBox.information(self, "Succès", "Le produit a été ajouté avec succès.")
                self.produit_added.emit()

            self.accept()

        except Exception as e:
            self.db_manager.conn.rollback()
            QMessageBox.critical(self, "Erreur", f"Une erreur est survenue lors de l'enregistrement: {str(e)}")

    def validate_form(self):
        """Valider que tous les champs obligatoires sont remplis"""
        if not self.designation_input.text():
            QMessageBox.warning(self, "Champ obligatoire", "Veuillez saisir une désignation pour le produit.")
            self.designation_input.setFocus()
            return False

        return True

    def integrate_with_caisse_new_product(self, code, designation, prix_achat, stock, facture_numero):
        """Intègre automatiquement avec la caisse pour l'achat d'un nouveau produit en espèces"""
        try:
            from datetime import datetime

            # Calculer le montant total (prix d'achat × quantité)
            montant_total = prix_achat * stock

            if montant_total > 0:
                # Créer une référence pour la transaction
                reference = f"PROD_{code}"
                if facture_numero and facture_numero.strip():
                    reference = facture_numero.strip()

                # Ajouter une sortie dans la caisse
                cursor = self.db_manager.conn.cursor()
                cursor.execute("""
                    INSERT INTO sorties_caisse (date, nature, objet, reference, montant)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    datetime.now().strftime("%Y-%m-%d"),
                    "شراء",
                    f"شراء منتج جديد بالنقد - {designation}",
                    reference,
                    montant_total
                ))

                self.db_manager.conn.commit()
                print(f"💸 Sortie de caisse automatique ajoutée: {montant_total:.2f} DH pour le produit {designation}")

        except Exception as e:
            print(f"❌ Erreur lors de l'intégration avec la caisse: {str(e)}")
