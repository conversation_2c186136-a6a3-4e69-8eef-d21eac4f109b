#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار عرض المنتجات
"""

import sqlite3
from pathlib import Path

# إعداد المسار لقاعدة البيانات
DB_PATH = Path(__file__).parent / "database"
DATABASE_FILE = DB_PATH / "accounting.db"

def test_product_display():
    """اختبار عرض المنتجات كما يفعل التطبيق"""
    try:
        conn = sqlite3.connect(str(DATABASE_FILE))
        cursor = conn.cursor()
        
        # نفس الاستعلام المستخدم في التطبيق
        cursor.execute("PRAGMA table_info(produits)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"📋 أعمدة جدول المنتجات: {columns}")
        
        if 'famille_id' in columns:
            cursor.execute("""
                SELECT
                    p.id,
                    p.code,
                    p.designation,
                    p.prix_achat,
                    p.prix_vente,
                    COALESCE(p.qte_achat, 0) as qte_achat,
                    p.stock,
                    p.fournisseur,
                    p.date_creation,
                    p.famille_id,
                    f.nom as famille_nom
                FROM produits p
                LEFT JOIN familles_produits f ON p.famille_id = f.id
                ORDER BY p.date_creation DESC
                LIMIT 5
            """)
        else:
            cursor.execute("""
                SELECT
                    id,
                    code,
                    designation,
                    prix_achat,
                    prix_vente,
                    COALESCE(qte_achat, 0) as qte_achat,
                    stock,
                    fournisseur,
                    date_creation,
                    NULL as famille_id,
                    NULL as famille_nom
                FROM produits
                ORDER BY date_creation DESC
                LIMIT 5
            """)
        
        products = cursor.fetchall()
        
        print(f"\n📋 المنتجات كما يراها التطبيق:")
        for i, product in enumerate(products, 1):
            print(f"  {i}. ID: {product[0]}")
            print(f"     Code: {product[1]}")
            print(f"     Designation: {product[2]}")
            print(f"     Prix Achat: {product[3]}")
            print(f"     Prix Vente: {product[4]}")
            print(f"     QTE Achat: {product[5]}")
            print(f"     Stock: {product[6]}")
            print(f"     Fournisseur: {product[7]}")
            print(f"     Date Creation: {product[8]}")
            print(f"     Famille ID: {product[9]}")
            print(f"     Famille Nom: {product[10]}")
            print()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_product_display()