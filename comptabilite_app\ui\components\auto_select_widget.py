"""
Composant pour la sélection automatique du texte dans les champs de saisie
"""

from PySide6.QtWidgets import QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox, QDateEdit, QApplication
from PySide6.QtCore import QEvent, Qt, QObject, QTimer
from PySide6.QtGui import QFocusEvent, QKeyEvent

class AutoSelectMixin:
    """Mixin pour ajouter la sélection automatique du texte lors du focus"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.installEventFilter(self)

    def eventFilter(self, obj, event):
        """Filtre les événements pour détecter le focus et sélectionner le texte"""
        if obj == self and event.type() == QEvent.FocusIn:
            # Sélectionner tout le texte quand le widget reçoit le focus
            if hasattr(self, 'selectAll'):
                # Pour QLineEdit, QSpinBox, QDoubleSpinBox
                self.selectAll()
            elif hasattr(self, 'lineEdit') and self.lineEdit():
                # Pour QComboBox éditable
                if self.isEditable():
                    self.lineEdit().selectAll()
            elif hasattr(self, 'setSelectedSection'):
                # Pour QDateEdit
                self.setSelectedSection(self.sectionAt(0))

        return super().eventFilter(obj, event) if hasattr(super(), 'eventFilter') else False

class AutoSelectLineEdit(AutoSelectMixin, QLineEdit):
    """QLineEdit avec sélection automatique du texte"""
    pass

class AutoSelectSpinBox(AutoSelectMixin, QSpinBox):
    """QSpinBox avec sélection automatique du texte"""
    pass

class AutoSelectDoubleSpinBox(AutoSelectMixin, QDoubleSpinBox):
    """QDoubleSpinBox avec sélection automatique du texte"""
    pass

class AutoSelectComboBox(AutoSelectMixin, QComboBox):
    """QComboBox avec sélection automatique du texte"""
    pass

class AutoSelectDateEdit(AutoSelectMixin, QDateEdit):
    """QDateEdit avec sélection automatique du texte"""
    pass

def apply_auto_select_to_widget(widget, parent_window=None):
    """Applique la sélection automatique à un widget existant"""
    try:
        if not hasattr(widget, '_auto_select_installed'):
            widget._auto_select_installed = True
            event_filter = AutoSelectEventFilter(widget)
            if parent_window:
                event_filter.set_parent_window(parent_window)
            widget.installEventFilter(event_filter)

            # S'assurer que le widget peut recevoir le focus
            widget.setFocusPolicy(Qt.StrongFocus)

            print(f"✅ Auto-select appliqué à {type(widget).__name__} dans le tableau")
        else:
            print(f"⚠️ Auto-select déjà installé sur {type(widget).__name__}")
    except Exception as e:
        print(f"❌ Erreur lors de l'application auto-select à {type(widget).__name__}: {str(e)}")

class AutoSelectEventFilter(QObject):
    """Filtre d'événements pour appliquer la sélection automatique à n'importe quel widget"""

    def __init__(self, widget):
        super().__init__()
        self.widget = widget
        self.parent_window = None
        self.focusable_widgets = []

    def set_parent_window(self, window):
        """Définit la fenêtre parent et la liste des widgets focusables"""
        self.parent_window = window
        self.update_focusable_widgets()

    def update_focusable_widgets(self):
        """Met à jour la liste des widgets focusables"""
        if self.parent_window:
            # Trouver tous les widgets de saisie
            line_edits = self.parent_window.findChildren(QLineEdit)
            spin_boxes = self.parent_window.findChildren(QSpinBox)
            double_spin_boxes = self.parent_window.findChildren(QDoubleSpinBox)
            combo_boxes = self.parent_window.findChildren(QComboBox)
            date_edits = self.parent_window.findChildren(QDateEdit)

            all_widgets = line_edits + spin_boxes + double_spin_boxes + combo_boxes + date_edits

            # Filtrer les widgets visibles et activés
            self.focusable_widgets = [w for w in all_widgets if w.isVisible() and w.isEnabled()]

            # Trier par position (haut vers bas, gauche vers droite)
            self.focusable_widgets.sort(key=lambda w: (w.y(), w.x()))

    def eventFilter(self, obj, event):
        """Filtre les événements pour détecter le focus et les touches"""
        if obj == self.widget:
            if event.type() == QEvent.FocusIn:
                # Utiliser un timer pour s'assurer que la sélection se fait après que le widget ait reçu le focus
                QTimer.singleShot(0, self.select_all_text)

            elif event.type() == QEvent.KeyPress:
                key = event.key()
                modifiers = event.modifiers()

                # Gérer Tab pour sélectionner tout le texte dans le widget suivant
                if key == Qt.Key_Tab:
                    # Laisser Tab fonctionner normalement pour la navigation
                    # La sélection automatique se fera via FocusIn du widget suivant
                    return False

                # Gérer Shift+Tab pour sélectionner tout le texte dans le widget précédent
                elif key == Qt.Key_Backtab or (key == Qt.Key_Tab and modifiers == Qt.ShiftModifier):
                    # Laisser Shift+Tab fonctionner normalement pour la navigation
                    # La sélection automatique se fera via FocusIn du widget précédent
                    return False

                # Gérer les flèches pour la navigation (seulement si pas de modificateurs)
                elif modifiers == Qt.NoModifier:
                    if key == Qt.Key_Down:
                        self.move_to_next_widget()
                        return True  # Consommer l'événement
                    elif key == Qt.Key_Up:
                        self.move_to_previous_widget()
                        return True  # Consommer l'événement
                    elif key == Qt.Key_Right:
                        # Pour QLineEdit, vérifier si le curseur est à la fin
                        if isinstance(self.widget, QLineEdit):
                            if self.widget.cursorPosition() == len(self.widget.text()):
                                self.move_to_next_widget()
                                return True
                        else:
                            self.move_to_next_widget()
                            return True
                    elif key == Qt.Key_Left:
                        # Pour QLineEdit, vérifier si le curseur est au début
                        if isinstance(self.widget, QLineEdit):
                            if self.widget.cursorPosition() == 0:
                                self.move_to_previous_widget()
                                return True
                        else:
                            self.move_to_previous_widget()
                            return True

        return False

    def move_to_next_widget(self):
        """Passe au widget suivant"""
        try:
            self.update_focusable_widgets()
            if self.widget in self.focusable_widgets:
                current_index = self.focusable_widgets.index(self.widget)
                next_index = (current_index + 1) % len(self.focusable_widgets)
                next_widget = self.focusable_widgets[next_index]
                next_widget.setFocus()
        except:
            pass

    def move_to_previous_widget(self):
        """Passe au widget précédent"""
        try:
            self.update_focusable_widgets()
            if self.widget in self.focusable_widgets:
                current_index = self.focusable_widgets.index(self.widget)
                prev_index = (current_index - 1) % len(self.focusable_widgets)
                prev_widget = self.focusable_widgets[prev_index]
                prev_widget.setFocus()
        except:
            pass

    def select_all_text(self):
        """Sélectionne tout le texte dans le widget"""
        try:
            # Pour QLineEdit
            if isinstance(self.widget, QLineEdit):
                self.widget.selectAll()
                print(f"🔍 Texte sélectionné dans QLineEdit: '{self.widget.text()}'")

            # Pour QSpinBox
            elif isinstance(self.widget, QSpinBox):
                self.widget.selectAll()
                print(f"🔍 Valeur sélectionnée dans QSpinBox: {self.widget.value()}")

            # Pour QDoubleSpinBox
            elif isinstance(self.widget, QDoubleSpinBox):
                self.widget.selectAll()
                print(f"🔍 Valeur sélectionnée dans QDoubleSpinBox: {self.widget.value()}")

            # Pour QComboBox éditable
            elif isinstance(self.widget, QComboBox):
                if self.widget.isEditable() and self.widget.lineEdit():
                    self.widget.lineEdit().selectAll()
                    print(f"🔍 Texte sélectionné dans QComboBox: '{self.widget.currentText()}'")
                else:
                    # Pour QComboBox non éditable, sélectionner l'élément actuel
                    print(f"🔍 QComboBox non éditable: '{self.widget.currentText()}'")

            # Pour QDateEdit
            elif isinstance(self.widget, QDateEdit):
                try:
                    # Sélectionner la première section (jour, mois ou année)
                    self.widget.setSelectedSection(self.widget.sectionAt(0))
                    print(f"🔍 Date sélectionnée dans QDateEdit: {self.widget.date().toString()}")
                except:
                    print(f"🔍 QDateEdit: {self.widget.date().toString()}")

            # Fallback pour autres widgets avec selectAll
            elif hasattr(self.widget, 'selectAll'):
                self.widget.selectAll()
                print(f"🔍 Texte sélectionné dans {type(self.widget).__name__}")

            else:
                print(f"⚠️ Type de widget non supporté: {type(self.widget).__name__}")

        except Exception as e:
            print(f"❌ Erreur lors de la sélection: {str(e)}")
            # Ignorer les erreurs silencieusement en production
            pass

def apply_auto_select_to_layout(layout):
    """Applique la sélection automatique à tous les widgets dans un layout"""
    for i in range(layout.count()):
        item = layout.itemAt(i)
        if item.widget():
            widget = item.widget()
            if isinstance(widget, (QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox, QDateEdit)):
                apply_auto_select_to_widget(widget)
        elif item.layout():
            # Récursion pour les layouts imbriqués
            apply_auto_select_to_layout(item.layout())

def apply_auto_select_to_window(window):
    """Applique la sélection automatique à tous les widgets dans une fenêtre"""
    # Trouver tous les widgets de saisie dans la fenêtre
    line_edits = window.findChildren(QLineEdit)
    spin_boxes = window.findChildren(QSpinBox)
    double_spin_boxes = window.findChildren(QDoubleSpinBox)
    combo_boxes = window.findChildren(QComboBox)
    date_edits = window.findChildren(QDateEdit)

    # Compter par type pour le rapport
    widget_counts = {
        'QLineEdit': len(line_edits),
        'QSpinBox': len(spin_boxes),
        'QDoubleSpinBox': len(double_spin_boxes),
        'QComboBox': len(combo_boxes),
        'QDateEdit': len(date_edits)
    }

    # Appliquer la sélection automatique à tous
    all_widgets = line_edits + spin_boxes + double_spin_boxes + combo_boxes + date_edits

    applied_count = 0
    for widget in all_widgets:
        try:
            apply_auto_select_to_widget(widget, window)
            # S'assurer que le widget peut recevoir le focus
            widget.setFocusPolicy(Qt.StrongFocus)
            applied_count += 1
        except Exception as e:
            print(f"❌ Erreur lors de l'application à {type(widget).__name__}: {str(e)}")

    # Rapport détaillé
    print(f"✅ Sélection automatique appliquée à {applied_count}/{len(all_widgets)} widgets:")
    for widget_type, count in widget_counts.items():
        if count > 0:
            print(f"   📝 {widget_type}: {count}")

    print(f"🔄 Navigation: Tab/Shift+Tab pour passer d'un champ à l'autre")
    print(f"🔍 Sélection: Le texte sera automatiquement sélectionné lors du focus")
    print(f"⌨️  Flèches: Haut/Bas/Gauche/Droite pour navigation alternative")


