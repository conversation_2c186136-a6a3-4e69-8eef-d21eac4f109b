#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pour vérifier que les calculs de stock sont corrects après la correction
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager
from stock_integration_solution import get_updated_stock_data

def test_stock_calculation():
    """Test les calculs de stock après correction"""
    
    print("🧪 Test des calculs de stock corrigés")
    print("=" * 50)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    cursor = db_manager.conn.cursor()
    
    # 1. Afficher l'état actuel des produits dans la DB
    print("\n📊 État actuel des produits dans la base de données:")
    cursor.execute("""
        SELECT id, designation, stock, prix_achat
        FROM produits
        ORDER BY designation
    """)
    produits_db = cursor.fetchall()
    
    for produit in produits_db:
        print(f"- {produit[1]}: Stock DB = {produit[2]}, Prix = {produit[3] or 0}")
    
    # 2. Afficher les mouvements de stock
    print("\n📋 Mouvements de stock existants:")
    cursor.execute("""
        SELECT 
            p.designation,
            m.type_mouvement,
            m.quantite,
            m.reference
        FROM mouvements_stock m
        JOIN produits p ON m.produit_id = p.id
        ORDER BY p.designation, m.date_mouvement
    """)
    mouvements = cursor.fetchall()
    
    if mouvements:
        for mouvement in mouvements:
            print(f"- {mouvement[0]}: {mouvement[1]} x {mouvement[2]} ({mouvement[3]})")
    else:
        print("Aucun mouvement trouvé")
    
    # 3. Calculer avec la nouvelle méthode
    print("\n🔄 Calculs avec la nouvelle méthode:")
    stock_data = get_updated_stock_data(db_manager)
    
    for item in stock_data:
        print(f"📊 {item['designation']}:")
        print(f"   Stock Initial: {item['stock_initial']}")
        print(f"   Entrées: {item['entrees']}")
        print(f"   Sorties: {item['sorties']}")
        print(f"   Stock Final: {item['stock_final']}")
        print(f"   Valeur: {item['valeur_stock']:.2f} DH")
        print("-" * 40)
    
    # 4. Vérification manuelle pour LAMPE LED 50W
    print("\n🔍 Vérification manuelle pour LAMPE LED 50W:")
    cursor.execute("""
        SELECT 
            p.stock as stock_db,
            COALESCE(SUM(CASE WHEN m.type_mouvement = 'entree' THEN m.quantite ELSE 0 END), 0) as entrees,
            COALESCE(SUM(CASE WHEN m.type_mouvement = 'sortie' THEN m.quantite ELSE 0 END), 0) as sorties
        FROM produits p
        LEFT JOIN mouvements_stock m ON p.id = m.produit_id
        WHERE p.designation = 'LAMPE LED 50W'
        GROUP BY p.id, p.stock
    """)
    
    result = cursor.fetchone()
    if result:
        stock_db = result[0]
        entrees = result[1]
        sorties = result[2]
        
        print(f"Stock dans DB: {stock_db}")
        print(f"Entrées totales: {entrees}")
        print(f"Sorties totales: {sorties}")
        
        # Calcul correct: Stock Initial = Stock Final + Sorties - Entrées
        stock_initial_calcule = stock_db + sorties - entrees
        
        print(f"Stock Initial calculé: {stock_db} + {sorties} - {entrees} = {stock_initial_calcule}")
        print(f"Stock Final: {stock_db}")
        
        if stock_initial_calcule == 150:
            print("✅ Calcul correct! Stock initial devrait être 150")
        else:
            print(f"❌ Calcul incorrect! Stock initial devrait être 150, mais calculé: {stock_initial_calcule}")

if __name__ == "__main__":
    test_stock_calculation()
