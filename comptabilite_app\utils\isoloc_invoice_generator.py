import os
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill, Color
from openpyxl.drawing.image import Image
from openpyxl.utils import get_column_letter
import datetime
from copy import copy
import io
from PIL import Image as PILImage

class IsolocInvoiceGenerator:
    """
    Générateur de factures au format ISOLOC.
    Cette classe permet de générer des factures qui correspondent exactement au modèle ISOLOC.
    """

    def __init__(self, logo_path=None):
        """
        Initialise le générateur de factures ISOLOC.

        Args:
            logo_path (str): Chemin vers le logo ISOLOC. Si None, utilise le logo par défaut.
        """
        # Dossier de sortie pour les factures générées
        self.output_dir = "factures"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

        # Chemin vers le logo
        if logo_path is None:
            self.logo_path = os.path.join(self.output_dir, "isoloc_logo.png")
        else:
            self.logo_path = logo_path

        # Couleurs et styles - Exactement comme dans l'image de référence
        self.header_blue = PatternFill(start_color="2B5592", end_color="2B5592", fill_type="solid")  # Bleu marine exact
        self.header_font = Font(name="Calibri", size=11, bold=True, color="FFFFFF")
        self.normal_font = Font(name="Calibri", size=10)
        self.title_font = Font(name="Calibri", size=16, bold=True, color="2B5592")
        self.subtitle_font = Font(name="Calibri", size=10, bold=True)

        # Bordures
        self.thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # Alignements
        self.center_align = Alignment(horizontal='center', vertical='center')
        self.left_align = Alignment(horizontal='left', vertical='center')
        self.right_align = Alignment(horizontal='right', vertical='center')

    def generate_invoice(self, invoice_data):
        """
        Génère une facture au format ISOLOC à partir des données fournies.

        Args:
            invoice_data (dict): Données de la facture avec les clés suivantes:
                - numero (str): Numéro de facture
                - date (str): Date de la facture
                - client (dict): Informations du client (nom, adresse, ice, etc.)
                - objet (str): Objet de la facture
                - lignes (list): Liste des lignes de facture (designation, unite, quantite, prix_unitaire, etc.)
                - totaux (dict): Totaux de la facture (total_ht, total_tva, total_ttc)

        Returns:
            str: Chemin vers le fichier Excel généré
        """
        # Créer un nouveau classeur Excel
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Facture"

        # Configurer les largeurs de colonnes exactement comme dans l'image de référence
        ws.column_dimensions['A'].width = 4   # N°
        ws.column_dimensions['B'].width = 45  # Description
        ws.column_dimensions['C'].width = 8   # Unité
        ws.column_dimensions['D'].width = 10  # Quantité
        ws.column_dimensions['E'].width = 15  # Prix unitaire HT
        ws.column_dimensions['F'].width = 15  # Prix Total (HT)

        # Configurer la hauteur des lignes
        ws.row_dimensions[1].height = 30  # Logo et titre
        ws.row_dimensions[14].height = 25  # En-tête du tableau

        # Ajouter le logo ISOLOC
        self._add_logo(ws)

        # Ajouter l'en-tête de la facture
        self._add_invoice_header(ws, invoice_data)

        # Ajouter les informations du client et l'objet
        self._add_client_info(ws, invoice_data)

        # Ajouter l'en-tête du tableau
        self._add_table_header(ws)

        # Ajouter les lignes de la facture
        row = self._add_invoice_lines(ws, invoice_data['lignes'])

        # Ajouter les totaux
        self._add_totals(ws, invoice_data['totaux'], row)

        # Ajouter le pied de page
        self._add_footer(ws, row + 8)

        # Définir le nom du fichier de sortie
        safe_numero = invoice_data['numero'].replace('/', '_').replace(' ', '_').replace('(', '').replace(')', '')
        output_file = os.path.join(self.output_dir, f"facture_{safe_numero}.xlsx")

        # Enregistrer le fichier
        wb.save(output_file)

        return output_file

    def generate_pdf(self, invoice_data):
        """
        Génère une facture au format PDF à partir des données fournies.

        Args:
            invoice_data (dict): Données de la facture (voir generate_invoice)

        Returns:
            str: Chemin vers le fichier PDF généré
        """
        # D'abord générer le fichier Excel
        excel_file = self.generate_invoice(invoice_data)

        # Définir le nom du fichier PDF
        pdf_file = excel_file.replace('.xlsx', '.pdf')

        # Convertir Excel en PDF
        try:
            # Utiliser win32com pour convertir Excel en PDF (Windows uniquement)
            self._convert_excel_to_pdf(excel_file, pdf_file)
            return pdf_file
        except Exception as e:
            print(f"Erreur lors de la conversion en PDF: {str(e)}")
            # Retourner le fichier Excel si la conversion échoue
            return excel_file

    def _convert_excel_to_pdf(self, excel_file, pdf_file):
        """
        Convertit un fichier Excel en PDF.

        Args:
            excel_file (str): Chemin vers le fichier Excel
            pdf_file (str): Chemin vers le fichier PDF à générer
        """
        try:
            # Méthode utilisant win32com (Windows uniquement)
            import win32com.client
            excel = win32com.client.Dispatch("Excel.Application")
            excel.Visible = False

            wb = excel.Workbooks.Open(os.path.abspath(excel_file))
            ws = wb.Worksheets[0]

            try:
                wb.SaveAs(os.path.abspath(pdf_file), FileFormat=57)  # 57 est le code pour PDF
            except Exception as e:
                print(f"Erreur lors de l'enregistrement en PDF: {str(e)}")
            finally:
                wb.Close()
                excel.Quit()
        except ImportError:
            print("La bibliothèque win32com n'est pas disponible. Conversion en PDF impossible.")
            raise

    def _add_logo(self, worksheet):
        """Ajoute le logo ISOLOC à la facture"""
        try:
            # Utiliser le logo spécifié ou créer un logo par défaut
            if self.logo_path and os.path.exists(self.logo_path):
                img = Image(self.logo_path)
            else:
                # Créer un logo ISOLOC par défaut
                logo_path = os.path.join(self.output_dir, "isoloc_logo.png")
                self._create_default_logo(logo_path)
                img = Image(logo_path)

            # Redimensionner le logo
            img.width = 150
            img.height = 150

            # Ajouter le logo à la feuille
            worksheet.add_image(img, 'A1')
        except Exception as e:
            print(f"Erreur lors de l'ajout du logo: {str(e)}")

    def _create_default_logo(self, output_path):
        """Crée un logo ISOLOC par défaut"""
        try:
            # Créer une image avec le texte "ISOLOC"
            from PIL import Image, ImageDraw, ImageFont

            # Créer une image blanche
            img = Image.new('RGB', (200, 200), color=(255, 255, 255))
            d = ImageDraw.Draw(img)

            # Dessiner un cercle
            d.ellipse((10, 10, 190, 190), outline=(0, 0, 0), width=3)

            # Ajouter le texte "ISOLOC"
            try:
                font = ImageFont.truetype("arial.ttf", 40)
            except:
                font = ImageFont.load_default()

            d.text((40, 80), "ISOLOC", fill=(0, 0, 0), font=font)

            # Enregistrer l'image
            img.save(output_path)
        except Exception as e:
            print(f"Erreur lors de la création du logo par défaut: {str(e)}")

    def _add_invoice_header(self, worksheet, invoice_data):
        """Ajoute l'en-tête de la facture exactement comme dans l'image de référence"""
        # Titre "FACTURE" - Aligné à droite comme dans l'image
        cell = worksheet['E1']
        cell.value = "FACTURE"
        cell.font = self.title_font
        cell.alignment = Alignment(horizontal='right', vertical='center')

        # Référence
        cell = worksheet['E2']
        cell.value = "Référence :"
        cell.font = self.subtitle_font
        cell.alignment = Alignment(horizontal='right', vertical='center')

        # Numéro de facture
        cell = worksheet['F2']
        cell.value = invoice_data['numero']
        cell.font = self.normal_font
        cell.alignment = self.left_align

        # Date
        cell = worksheet['E4']
        cell.value = "Date :"
        cell.font = self.subtitle_font
        cell.alignment = Alignment(horizontal='right', vertical='center')

        # Valeur de la date
        cell = worksheet['F4']
        cell.value = invoice_data['date']
        cell.font = self.normal_font
        cell.alignment = self.left_align

    def _add_client_info(self, worksheet, invoice_data):
        """Ajoute les informations du client et l'objet de la facture exactement comme dans l'image de référence"""
        # Client
        cell = worksheet['A8']
        cell.value = "Client :"
        cell.font = self.subtitle_font
        cell.alignment = self.left_align

        # Nom du client
        cell = worksheet['A9']
        cell.value = invoice_data['client']['nom']
        cell.font = self.normal_font
        cell.alignment = self.left_align

        # Objet
        cell = worksheet['A11']
        cell.value = "Objet :"
        cell.font = self.subtitle_font
        cell.alignment = self.left_align

        # Description de l'objet
        cell = worksheet['A12']
        if 'objet' in invoice_data:
            cell.value = invoice_data['objet']
        else:
            cell.value = ""
        cell.font = self.normal_font
        cell.alignment = self.left_align

        # Ajouter un espace avant le tableau
        worksheet.row_dimensions[13].height = 15

    def _add_table_header(self, worksheet):
        """Ajoute l'en-tête du tableau des articles exactement comme dans l'image de référence"""
        headers = ["N°", "Description", "Unité", "Quantité", "Prix unitaire HT", "Prix Total (HT)"]
        columns = ['A', 'B', 'C', 'D', 'E', 'F']

        # Appliquer le style à toutes les cellules de l'en-tête
        for i, header in enumerate(headers):
            cell = worksheet[f'{columns[i]}14']
            cell.value = header
            cell.font = self.header_font
            cell.fill = self.header_blue
            cell.alignment = self.center_align
            cell.border = self.thin_border

    def _add_invoice_lines(self, worksheet, lines_data):
        """
        Ajoute les lignes de la facture au tableau exactement comme dans l'image de référence

        Returns:
            int: Numéro de la dernière ligne ajoutée
        """
        start_row = 15
        current_row = start_row

        for i, line in enumerate(lines_data):
            # Hauteur de ligne adaptée au contenu
            worksheet.row_dimensions[current_row].height = 30

            # Numéro de ligne
            cell = worksheet[f'A{current_row}']
            cell.value = i + 1
            cell.font = self.normal_font
            cell.alignment = self.center_align
            cell.border = self.thin_border

            # Désignation
            cell = worksheet[f'B{current_row}']
            cell.value = line['designation']
            cell.font = self.normal_font
            cell.alignment = self.left_align
            cell.border = self.thin_border
            cell.alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

            # Unité
            cell = worksheet[f'C{current_row}']
            cell.value = line.get('unite', '')
            cell.font = self.normal_font
            cell.alignment = self.center_align
            cell.border = self.thin_border

            # Quantité
            cell = worksheet[f'D{current_row}']
            cell.value = line['quantite']
            cell.font = self.normal_font
            cell.alignment = self.center_align
            cell.border = self.thin_border

            # Prix unitaire HT
            cell = worksheet[f'E{current_row}']
            cell.value = line['prix_unitaire']
            cell.font = self.normal_font
            cell.alignment = self.right_align
            cell.number_format = '# ##0,00'
            cell.border = self.thin_border

            # Prix Total HT
            cell = worksheet[f'F{current_row}']
            cell.value = line['quantite'] * line['prix_unitaire']
            cell.font = self.normal_font
            cell.alignment = self.right_align
            cell.number_format = '# ##0,00'
            cell.border = self.thin_border

            # Ajouter une ligne de séparation (ligne grise fine)
            current_row += 1
            for col in ['A', 'B', 'C', 'D', 'E', 'F']:
                cell = worksheet[f'{col}{current_row}']
                cell.border = Border(bottom=Side(style='thin', color="DDDDDD"))

            current_row += 1

        # Retourner la dernière ligne utilisée
        return current_row

    def _add_totals(self, worksheet, totals_data, last_row):
        """Ajoute les totaux de la facture exactement comme dans l'image de référence"""
        # Ajouter un espace avant les totaux
        last_row += 1

        # Total HT
        cell = worksheet[f'E{last_row}']
        cell.value = "Total HT"
        cell.font = self.subtitle_font
        cell.alignment = self.right_align

        cell = worksheet[f'F{last_row}']
        cell.value = totals_data['total_ht']
        cell.font = self.subtitle_font
        cell.alignment = self.right_align
        cell.number_format = '# ##0,00'
        cell.border = Border(bottom=Side(style='thin'), right=Side(style='thin'), left=Side(style='thin'))

        # TVA
        cell = worksheet[f'E{last_row+1}']
        cell.value = "TVA 20%"
        cell.font = self.subtitle_font
        cell.alignment = self.right_align

        cell = worksheet[f'F{last_row+1}']
        cell.value = totals_data['total_tva']
        cell.font = self.subtitle_font
        cell.alignment = self.right_align
        cell.number_format = '# ##0,00'
        cell.border = Border(bottom=Side(style='thin'), right=Side(style='thin'), left=Side(style='thin'))

        # Total TTC
        cell = worksheet[f'E{last_row+2}']
        cell.value = "Total TTC"
        cell.font = self.subtitle_font
        cell.alignment = self.right_align

        cell = worksheet[f'F{last_row+2}']
        cell.value = totals_data['total_ttc']
        cell.font = self.subtitle_font
        cell.alignment = self.right_align
        cell.number_format = '# ##0,00'
        cell.border = Border(bottom=Side(style='thin'), right=Side(style='thin'), left=Side(style='thin'))

        # Arrêté la présente facture à la somme de
        cell = worksheet[f'A{last_row+4}']
        cell.value = "Arrête la présente facture à la somme de :"
        cell.font = self.normal_font
        cell.alignment = self.left_align

        # Montant en lettres
        cell = worksheet[f'A{last_row+5}']
        cell.value = self._number_to_text(totals_data['total_ttc']) + " TTC"
        cell.font = self.normal_font
        cell.alignment = self.left_align

    def _add_footer(self, worksheet, row):
        """Ajoute le pied de page avec les informations de l'entreprise exactement comme dans l'image de référence"""
        # Ajouter un espace avant le pied de page
        row += 3

        # Fusionner les cellules pour le pied de page
        worksheet.merge_cells(f'A{row}:F{row+2}')

        # Texte du pied de page
        footer_text = (
            "ISOLOC SERVICE SARL   capital 100 000 dhs - adresse 7 RUE MOHAMED SMIHA 10 ETG APT N°57, Casablanca\n"
            "TP 32104118 – CNSS 3340229 – IF 00252577 – RC 616663 CASABLANCA - ICE 003459559000040\n"
            "RIB 007780000263530000095552 - TÉL 0668929122 - fax 0521344545 - fix 0522067870\n"
            "E-mail : <EMAIL>"
        )

        cell = worksheet[f'A{row}']
        cell.value = footer_text
        cell.font = Font(name="Calibri", size=8)
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

        # Ajouter une bordure bleue en bas
        worksheet.merge_cells(f'A{row+3}:F{row+3}')
        cell = worksheet[f'A{row+3}']
        cell.fill = self.header_blue
        cell.border = Border(top=Side(style='thin', color="FFFFFF"))

    def _number_to_text(self, number):
        """Convertit un nombre en texte en français"""
        if number == 0:
            return "Zéro"

        units = ["", "un", "deux", "trois", "quatre", "cinq", "six", "sept", "huit", "neuf", "dix", "onze", "douze", "treize", "quatorze", "quinze", "seize", "dix-sept", "dix-huit", "dix-neuf"]
        tens = ["", "", "vingt", "trente", "quarante", "cinquante", "soixante", "soixante", "quatre-vingt", "quatre-vingt"]

        # Arrondir à 2 décimales et séparer la partie entière et décimale
        number = round(number, 2)
        int_part = int(number)
        decimal_part = int(round((number - int_part) * 100))

        # Convertir la partie entière
        if int_part < 20:
            text = units[int_part]
        elif int_part < 100:
            ten = int(int_part / 10)
            unit = int_part % 10

            if ten == 7 or ten == 9:
                if unit == 0:
                    text = tens[ten] + "-dix"
                elif unit == 1:
                    text = tens[ten] + "-et-onze"
                else:
                    text = tens[ten] + "-" + units[unit + 10]
            else:
                if unit == 0:
                    text = tens[ten]
                elif unit == 1 and ten != 8:
                    text = tens[ten] + "-et-" + units[unit]
                else:
                    text = tens[ten] + "-" + units[unit]
        elif int_part < 1000:
            hundred = int(int_part / 100)
            rest = int_part % 100

            if hundred == 1:
                text = "cent"
            else:
                text = units[hundred] + " cent"

            if rest > 0:
                text += " " + self._number_to_text(rest)
        elif int_part < 1000000:
            thousand = int(int_part / 1000)
            rest = int_part % 1000

            if thousand == 1:
                text = "mille"
            else:
                text = self._number_to_text(thousand) + " mille"

            if rest > 0:
                text += " " + self._number_to_text(rest)
        else:
            million = int(int_part / 1000000)
            rest = int_part % 1000000

            if million == 1:
                text = "un million"
            else:
                text = self._number_to_text(million) + " millions"

            if rest > 0:
                text += " " + self._number_to_text(rest)

        # Ajouter la partie décimale si nécessaire
        if decimal_part > 0:
            text += " virgule " + self._number_to_text(decimal_part)

        return text.capitalize()
