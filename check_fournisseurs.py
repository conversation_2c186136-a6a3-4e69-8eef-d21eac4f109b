#!/usr/bin/env python3
"""
فحص بيانات الموردين في قاعدة البيانات
"""

import sqlite3
import os

def check_fournisseurs_data():
    db_path = 'comptabilite.db'
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    print("🔍 فحص بيانات الموردين والمنتجات...")
    print("=" * 50)
    
    # فحص جدول المنتجات
    print("\n📦 جدول المنتجات:")
    cursor.execute("PRAGMA table_info(produits)")
    columns = cursor.fetchall()
    print("الأعمدة الموجودة:")
    for col in columns:
        print(f"  - {col['name']} ({col['type']})")
    
    # فحص بيانات المنتجات
    print("\n📋 بيانات المنتجات:")
    cursor.execute("SELECT id, code, designation, fournisseur FROM produits LIMIT 10")
    products = cursor.fetchall()
    
    if products:
        for product in products:
            fournisseur = product['fournisseur'] if product['fournisseur'] else "غير محدد"
            print(f"  {product['code']}: {product['designation']} - مورد: {fournisseur}")
    else:
        print("  لا توجد منتجات")
    
    # فحص جدول الموردين
    print("\n🏢 جدول الموردين:")
    cursor.execute("SELECT COUNT(*) as count FROM sqlite_master WHERE type='table' AND name='fournisseurs'")
    table_exists = cursor.fetchone()['count'] > 0
    
    if table_exists:
        cursor.execute("SELECT id, code, nom FROM fournisseurs")
        fournisseurs = cursor.fetchall()
        
        if fournisseurs:
            print("الموردين الموجودين:")
            for fournisseur in fournisseurs:
                print(f"  {fournisseur['code']}: {fournisseur['nom']}")
        else:
            print("  لا توجد موردين مسجلين")
    else:
        print("  جدول الموردين غير موجود")
    
    conn.close()

if __name__ == "__main__":
    check_fournisseurs_data()
