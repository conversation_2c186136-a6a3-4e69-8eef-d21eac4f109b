from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QHeaderView, QMessageBox, QFormLayout,
                              QComboBox, QDateEdit, QTextEdit, QSpinBox,
                              QDialog)
from PySide6.QtCore import Qt, QDate, Signal
import sqlite3
import datetime

# Importer les composants nécessaires
from ..components.base_module import BaseModule


class BLDialog(QDialog):
    """Dialogue simple pour ajouter/modifier un bon de livraison"""
    bl_saved = Signal()

    def __init__(self, db_manager, bl_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.bl_id = bl_id
        self.setup_ui()
        self.setup_database()
        self.load_clients()
        self.load_devis()
        self.load_bons_commande()

        if bl_id:
            self.load_bl_data()
        else:
            self.generate_numero()
            self.auto_fill_objet()  # Remplir automatiquement l'objet

    def setup_database(self):
        """Créer les tables BL si elles n'existent pas"""
        cursor = self.db_manager.conn.cursor()

        try:
            # Table principale des bons de livraison
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS bons_livraison (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    numero TEXT NOT NULL,
                    date_creation TEXT NOT NULL,
                    date_livraison TEXT,
                    client_id INTEGER,
                    bon_commande TEXT,
                    marche TEXT,
                    objet TEXT,
                    statut TEXT DEFAULT 'En cours',
                    notes TEXT,
                    devis_numero TEXT,
                    FOREIGN KEY (client_id) REFERENCES clients (id)
                )
            """)

            # Table des lignes de bon de livraison
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS lignes_bl (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    bl_id INTEGER NOT NULL,
                    produit_id INTEGER,
                    designation TEXT NOT NULL,
                    unite TEXT,
                    quantite INTEGER NOT NULL DEFAULT 1,
                    FOREIGN KEY (bl_id) REFERENCES bons_livraison (id),
                    FOREIGN KEY (produit_id) REFERENCES produits (id)
                )
            """)

            self.db_manager.conn.commit()

        except sqlite3.Error as e:
            print(f"Erreur lors de la création des tables BL: {str(e)}")

    def setup_ui(self):
        """Configuration simple de l'interface utilisateur"""
        self.setWindowTitle("Bon de Livraison" if not self.bl_id else "Modifier Bon de Livraison")
        self.setMinimumSize(700, 500)
        self.setModal(True)

        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title = QLabel("📦 Bon de Livraison")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #1E3A8A; margin-bottom: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # Formulaire principal
        form_layout = QFormLayout()
        form_layout.setSpacing(10)

        # Numéro BL (يكتب يدوياً)
        numero_container = QHBoxLayout()
        self.numero_input = QLineEdit()
        self.numero_input.setStyleSheet("padding: 8px; border: 1px solid #ddd; border-radius: 4px;")
        numero_container.addWidget(self.numero_input)

        numero_note = QLabel("يكتب يدوياً")
        numero_note.setStyleSheet("color: #6B7280; font-size: 12px; font-style: italic;")
        numero_container.addWidget(numero_note)

        numero_widget = QWidget()
        numero_widget.setLayout(numero_container)
        form_layout.addRow("Numéro BL:", numero_widget)

        # Date de création
        from ..style import create_styled_date_edit
        self.date_creation_input = create_styled_date_edit()
        form_layout.addRow("Date de création:", self.date_creation_input)

        # Date de livraison (QDateEdit avec option vide)
        self.date_livraison_input = create_styled_date_edit(
            optional=True,
            placeholder_text="Date de livraison (optionnel)"
        )
        form_layout.addRow("Date de livraison:", self.date_livraison_input)

        # Client
        self.client_combo = QComboBox()
        self.client_combo.setStyleSheet("padding: 8px; border: 1px solid #ddd; border-radius: 4px;")
        self.client_combo.currentIndexChanged.connect(self.on_client_changed)  # Connecter le signal
        form_layout.addRow("Client:", self.client_combo)

        # Devis (sélectionne ou taper)
        self.devis_combo = QComboBox()
        self.devis_combo.setEditable(True)  # Permet de taper ou sélectionner
        self.devis_combo.setStyleSheet("padding: 8px; border: 1px solid #ddd; border-radius: 4px;")
        self.devis_combo.lineEdit().setPlaceholderText("SÉLECTIONNÉ OU TAPER...")
        self.devis_combo.currentTextChanged.connect(self.on_devis_changed)  # Connecter le signal
        form_layout.addRow("Devis N°:", self.devis_combo)

        # Bon de commande (sélectionne ou taper)
        self.bon_commande_combo = QComboBox()
        self.bon_commande_combo.setEditable(True)  # Permet de taper ou sélectionner
        self.bon_commande_combo.setStyleSheet("padding: 8px; border: 1px solid #ddd; border-radius: 4px;")
        self.bon_commande_combo.lineEdit().setPlaceholderText("Sélectionne ou taper...")
        self.bon_commande_combo.currentTextChanged.connect(self.on_bon_commande_changed)  # Connecter le signal
        form_layout.addRow("Bon de commande:", self.bon_commande_combo)

        # Marché
        self.marche_input = QLineEdit()
        self.marche_input.setStyleSheet("padding: 8px; border: 1px solid #ddd; border-radius: 4px;")
        form_layout.addRow("Marché:", self.marche_input)

        # إضافة الاقتراحات التلقائية لحقل المشروع
        try:
            from ..components.autocomplete_widget import setup_autocomplete_for_widget
            setup_autocomplete_for_widget(self.marche_input, self.db_manager, "marche_reference")
            print("✅ تم تطبيق الاقتراحات التلقائية على حقل المشروع في BL")
        except Exception as e:
            print(f"❌ خطأ في تطبيق الاقتراحات التلقائية على حقل المشروع في BL: {str(e)}")

        # Objet
        self.objet_input = QTextEdit()
        self.objet_input.setMaximumHeight(80)
        self.objet_input.setStyleSheet("padding: 8px; border: 1px solid #ddd; border-radius: 4px;")
        form_layout.addRow("Objet:", self.objet_input)

        layout.addLayout(form_layout)

        # Section des articles
        articles_label = QLabel("📋 Articles à livrer")
        articles_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #1E3A8A; margin-top: 10px;")
        layout.addWidget(articles_label)

        # Bouton ajouter article
        add_article_btn = QPushButton("+ Ajouter Article")
        add_article_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
        """)
        add_article_btn.clicked.connect(self.add_article_row)
        layout.addWidget(add_article_btn)

        # Tableau des articles
        self.articles_table = QTableWidget(0, 4)
        self.articles_table.setHorizontalHeaderLabels([
            "Désignation", "Unité", "Quantité", "Actions"
        ])

        self.articles_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                gridline-color: #ddd;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                color: #333;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #ddd;
                font-weight: bold;
            }
        """)

        self.articles_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.articles_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)

        layout.addWidget(self.articles_table)

        # Boutons
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()

        cancel_btn = QPushButton("Annuler")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        save_btn = QPushButton("Enregistrer")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #1E3A8A;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1A56DB;
            }
        """)
        save_btn.clicked.connect(self.save_bl)
        buttons_layout.addWidget(save_btn)

        layout.addLayout(buttons_layout)

        # Ajouter une ligne d'article par défaut
        self.add_article_row()

    def generate_numero(self):
        """Générer un numéro de BL automatique"""
        try:
            cursor = self.db_manager.conn.cursor()
            date_str = datetime.datetime.now().strftime("%Y%m")
            cursor.execute("SELECT COUNT(*) FROM bons_livraison WHERE numero LIKE ?", (f"BL{date_str}%",))
            count = cursor.fetchone()[0] + 1
            self.numero_input.setText(f"BL{date_str}-{count:03d}")
        except Exception as e:
            print(f"Erreur lors de la génération du numéro: {str(e)}")
            self.numero_input.setText("BL-001")

    def load_clients(self):
        """Charger la liste des clients"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT id, nom FROM clients ORDER BY nom")
            clients = cursor.fetchall()

            self.client_combo.clear()
            self.client_combo.addItem("Sélectionner un client", None)

            for client in clients:
                self.client_combo.addItem(client['nom'], client['id'])

        except Exception as e:
            print(f"Erreur lors du chargement des clients: {str(e)}")

    def load_devis(self):
        """Charger la liste des devis"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Vérifier si la table devis existe
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='devis'")
            if not cursor.fetchone():
                print("Table devis n'existe pas")
                self.devis_combo.clear()
                self.devis_combo.addItem("SÉLECTIONNÉ OU TAPER")
                return

            cursor.execute("SELECT numero, client FROM devis ORDER BY numero DESC")
            devis_list = cursor.fetchall()

            self.devis_combo.clear()
            self.devis_combo.addItem("SÉLECTIONNÉ OU TAPER")

            for devis in devis_list:
                # Format: "DEV001 - Client A"
                display_text = f"{devis['numero']} - {devis['client']}"
                self.devis_combo.addItem(display_text)

        except Exception as e:
            print(f"Erreur lors du chargement des devis: {str(e)}")
            self.devis_combo.clear()
            self.devis_combo.addItem("SÉLECTIONNÉ OU TAPER")

    def load_bons_commande(self, client_id=None):
        """Charger la liste des bons de commande pour un client spécifique"""
        try:
            cursor = self.db_manager.conn.cursor()

            self.bon_commande_combo.clear()
            self.bon_commande_combo.addItem("")  # Option vide

            # Vérifier d'abord si la table existe
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='bons_commande'")
            if not cursor.fetchone():
                print("Table bons_commande n'existe pas")
                return

            if client_id:
                # Charger seulement les bons de commande de ce client
                cursor.execute("""
                    SELECT DISTINCT numero, objet FROM bons_commande
                    WHERE client_id = ?
                    ORDER BY numero DESC
                """, (client_id,))
                bons = cursor.fetchall()

                for bon in bons:
                    # Accès sécurisé aux données
                    numero = None
                    try:
                        numero = bon['numero']
                    except (TypeError, KeyError):
                        try:
                            numero = bon[0]
                        except (IndexError, TypeError):
                            continue

                    if numero:
                        self.bon_commande_combo.addItem(numero)
            else:
                # Charger tous les bons de commande si aucun client sélectionné
                cursor.execute("SELECT DISTINCT numero FROM bons_commande ORDER BY numero DESC")
                bons = cursor.fetchall()

                for bon in bons:
                    # Accès sécurisé aux données
                    numero = None
                    try:
                        numero = bon['numero']
                    except (TypeError, KeyError):
                        try:
                            numero = bon[0]
                        except (IndexError, TypeError):
                            continue

                    if numero:
                        self.bon_commande_combo.addItem(numero)

        except Exception as e:
            print(f"Erreur lors du chargement des bons de commande: {str(e)}")
            import traceback
            traceback.print_exc()

    def auto_fill_objet(self):
        """Remplir automatiquement le champ objet"""
        default_objet = "Livraison des articles selon bon de commande"
        self.objet_input.setPlainText(default_objet)

    def on_client_changed(self):
        """Appelé quand le client change"""
        client_id = self.client_combo.currentData()
        if client_id:
            # Recharger les bons de commande pour ce client
            self.load_bons_commande(client_id)
        else:
            # Vider la liste des bons de commande
            self.bon_commande_combo.clear()
            self.bon_commande_combo.addItem("")

    def on_bon_commande_changed(self):
        """Appelé quand le bon de commande change"""
        bon_numero = self.bon_commande_combo.currentText().strip()
        if bon_numero:
            # Récupérer l'objet du bon de commande et charger ses articles
            try:
                cursor = self.db_manager.conn.cursor()

                # Vérifier d'abord si la table existe
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='bons_commande'")
                if not cursor.fetchone():
                    print("Table bons_commande n'existe pas")
                    self.objet_input.setPlainText(f"Livraison des articles selon bon de commande {bon_numero}")
                    return

                # Récupérer l'objet et l'ID du bon de commande
                cursor.execute("SELECT id, objet FROM bons_commande WHERE numero = ? LIMIT 1", (bon_numero,))
                result = cursor.fetchone()

                if result:
                    # Récupérer l'objet
                    objet_value = None
                    bon_id = None
                    try:
                        bon_id = result['id'] if 'id' in result.keys() else result[0]
                        objet_value = result['objet'] if 'objet' in result.keys() else result[1]
                    except (TypeError, KeyError, IndexError):
                        try:
                            bon_id = result[0]
                            objet_value = result[1]
                        except (IndexError, TypeError):
                            objet_value = None

                    # Mettre à jour l'objet
                    if objet_value and objet_value.strip():
                        self.objet_input.setPlainText(objet_value.strip())
                    else:
                        self.objet_input.setPlainText(f"Livraison des articles selon bon de commande {bon_numero}")

                    # Charger les articles du bon de commande
                    if bon_id:
                        self.load_articles_from_bc(bon_id)

                else:
                    # Aucun résultat trouvé
                    self.objet_input.setPlainText(f"Livraison des articles selon bon de commande {bon_numero}")

            except Exception as e:
                print(f"Erreur lors de la récupération de l'objet: {str(e)}")
                # En cas d'erreur, utiliser l'objet par défaut
                self.objet_input.setPlainText(f"Livraison des articles selon bon de commande {bon_numero}")
        else:
            # Si aucun bon sélectionné, utiliser l'objet par défaut
            self.auto_fill_objet()
            # Vider le tableau des articles
            self.articles_table.setRowCount(0)
            self.add_article_row()

    def on_devis_changed(self):
        """Appelé quand le devis change - remplir automatiquement les champs"""
        devis_text = self.devis_combo.currentText().strip()
        if not devis_text or devis_text == "SÉLECTIONNÉ OU TAPER":
            return

        try:
            # Extraire le numéro du devis (format: "DEV001 - Client A")
            if " - " in devis_text:
                devis_numero = devis_text.split(" - ")[0]
            else:
                devis_numero = devis_text

            cursor = self.db_manager.conn.cursor()

            # Charger toutes les données du devis
            cursor.execute("""
                SELECT client, objet, adresse_livraison, ice_client, montant_ttc, type_marche,
                       numero_bl_auto, numero_manuel, nature_prestation
                FROM devis WHERE numero = ?
            """, (devis_numero,))

            result = cursor.fetchone()
            if result:
                client, objet, adresse_livraison, ice_client, montant_ttc, type_marche, numero_bl_auto, numero_manuel, nature_prestation = result

                # Remplir automatiquement le client si trouvé
                if client:
                    for i in range(self.client_combo.count()):
                        if client.lower() in self.client_combo.itemText(i).lower():
                            self.client_combo.setCurrentIndex(i)
                            break

                # Remplir l'objet
                if objet:
                    self.objet_input.setPlainText(f"Livraison selon devis {devis_numero}: {objet}")
                else:
                    self.objet_input.setPlainText(f"Livraison selon devis {devis_numero}")

                # Remplir le marché avec le type de marché du devis
                if type_marche:
                    self.marche_input.setText(type_marche)

                print(f"✅ Données du devis {devis_numero} chargées automatiquement")

        except Exception as e:
            print(f"⚠️ Erreur lors du chargement du devis: {str(e)}")
            # En cas d'erreur, utiliser un objet par défaut
            self.objet_input.setPlainText(f"Livraison selon devis {devis_numero}")

    def load_products_in_combo(self, combo):
        """Charger les produits dans un ComboBox"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT designation, unite FROM produits ORDER BY designation")
            produits = cursor.fetchall()

            # Ajouter une option vide
            combo.addItem("")

            # Ajouter les produits
            for produit in produits:
                try:
                    designation = produit['designation'] if 'designation' in produit.keys() else produit[0]
                    unite = produit['unite'] if 'unite' in produit.keys() else produit[1]
                    combo.addItem(designation)
                    # Stocker l'unité comme données
                    combo.setItemData(combo.count() - 1, unite)
                except (KeyError, IndexError, TypeError):
                    continue

        except Exception as e:
            print(f"Erreur lors du chargement des produits: {str(e)}")

    def on_product_selected(self, row, text):
        """Appelé quand un produit est sélectionné"""
        if not text.strip():
            return

        try:
            # Récupérer le widget de désignation
            designation_combo = self.articles_table.cellWidget(row, 0)
            if not designation_combo:
                return

            # Trouver l'index du produit sélectionné
            index = designation_combo.findText(text)
            if index >= 0:
                # Récupérer l'unité stockée
                unite = designation_combo.itemData(index)
                if unite:
                    # Mettre à jour le champ unité
                    unite_widget = self.articles_table.cellWidget(row, 1)
                    if unite_widget and hasattr(unite_widget, 'setText'):
                        unite_widget.setText(unite)

        except Exception as e:
            print(f"Erreur lors de la sélection du produit: {str(e)}")

    def load_articles_from_bc(self, bon_id):
        """Charger les articles du bon de commande sélectionné"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Récupérer les lignes du bon de commande
            cursor.execute("""
                SELECT designation, unite, quantite
                FROM lignes_bon_commande
                WHERE bon_id = ?
                ORDER BY id
            """, (bon_id,))
            lignes = cursor.fetchall()

            # Vider le tableau actuel
            self.articles_table.setRowCount(0)

            # Ajouter les articles du BC
            for ligne in lignes:
                row = self.articles_table.rowCount()
                self.articles_table.insertRow(row)

                # Désignation (QLineEdit simple avec la valeur du BC)
                designation_input = QLineEdit()
                try:
                    designation_value = ligne['designation'] if 'designation' in ligne.keys() else ligne[0]
                    designation_input.setText(designation_value or "")
                except (KeyError, IndexError, TypeError):
                    designation_input.setText("")
                designation_input.setStyleSheet("padding: 4px; border: 1px solid #ddd; border-radius: 4px;")
                self.articles_table.setCellWidget(row, 0, designation_input)

                # Unité (QLineEdit simple avec la valeur du BC)
                unite_input = QLineEdit()
                try:
                    unite_value = ligne['unite'] if 'unite' in ligne.keys() else ligne[1]
                    unite_input.setText(unite_value or "")
                except (KeyError, IndexError, TypeError):
                    unite_input.setText("")
                unite_input.setStyleSheet("padding: 4px; border: 1px solid #ddd; border-radius: 4px;")
                self.articles_table.setCellWidget(row, 1, unite_input)

                # Quantité (avec la valeur du BC)
                quantite_input = QSpinBox()
                quantite_input.setMinimum(1)
                quantite_input.setMaximum(9999)
                try:
                    quantite_value = ligne['quantite'] if 'quantite' in ligne.keys() else ligne[2]
                    quantite_input.setValue(int(quantite_value) if quantite_value else 1)
                except (KeyError, IndexError, TypeError, ValueError):
                    quantite_input.setValue(1)
                quantite_input.setStyleSheet("padding: 4px; border: 1px solid #ddd; border-radius: 4px;")
                self.articles_table.setCellWidget(row, 2, quantite_input)

                # Bouton supprimer
                delete_btn = QPushButton("🗑️")
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #EF4444;
                        color: white;
                        border: none;
                        padding: 4px;
                        border-radius: 4px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #DC2626;
                    }
                """)
                delete_btn.clicked.connect(lambda checked, r=row: self.remove_article_row(r))
                self.articles_table.setCellWidget(row, 3, delete_btn)

            # Si aucun article trouvé, ajouter une ligne vide
            if not lignes:
                self.add_article_row()

        except Exception as e:
            print(f"Erreur lors du chargement des articles du BC: {str(e)}")
            # En cas d'erreur, ajouter une ligne vide
            self.articles_table.setRowCount(0)
            self.add_article_row()

    def add_article_row(self):
        """Ajouter une ligne d'article"""
        row = self.articles_table.rowCount()
        self.articles_table.insertRow(row)

        # Désignation (QLineEdit simple)
        designation_input = QLineEdit()
        designation_input.setPlaceholderText("يطلع مباشرة")
        designation_input.setStyleSheet("padding: 4px; border: 1px solid #ddd; border-radius: 4px;")
        self.articles_table.setCellWidget(row, 0, designation_input)

        # Unité (QLineEdit simple)
        unite_input = QLineEdit()
        unite_input.setPlaceholderText("Unité")
        unite_input.setStyleSheet("padding: 4px; border: 1px solid #ddd; border-radius: 4px;")
        self.articles_table.setCellWidget(row, 1, unite_input)

        # Quantité (يطلع مباشرة)
        quantite_input = QSpinBox()
        quantite_input.setMinimum(1)
        quantite_input.setMaximum(9999)
        quantite_input.setValue(1)
        quantite_input.setStyleSheet("padding: 4px; border: 1px solid #ddd; border-radius: 4px;")
        quantite_input.setToolTip("يطلع مباشرة")
        self.articles_table.setCellWidget(row, 2, quantite_input)

        # Bouton supprimer
        delete_btn = QPushButton("🗑️")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #EF4444;
                color: white;
                border: none;
                padding: 4px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #DC2626;
            }
        """)
        delete_btn.clicked.connect(lambda: self.remove_article_row(row))
        self.articles_table.setCellWidget(row, 3, delete_btn)

    def remove_article_row(self, row):
        """Supprimer une ligne d'article"""
        if self.articles_table.rowCount() > 1:
            self.articles_table.removeRow(row)
        else:
            QMessageBox.warning(self, "Attention", "Il faut au moins un article.")

    def save_bl(self):
        """Enregistrer le bon de livraison"""
        try:
            # Validation
            if not self.numero_input.text().strip():
                QMessageBox.warning(self, "Erreur", "Le numéro BL est obligatoire.")
                return

            if self.client_combo.currentData() is None:
                QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un client.")
                return

            # Récupérer les données
            numero = self.numero_input.text().strip()
            date_creation = self.date_creation_input.date().toString("yyyy-MM-dd")

            # Date de livraison (QDateEdit - peut être vide)
            if self.date_livraison_input.date().isValid():
                date_livraison = self.date_livraison_input.date().toString("yyyy-MM-dd")
            else:
                date_livraison = None

            client_id = self.client_combo.currentData()
            bon_commande = self.bon_commande_combo.currentText().strip()  # Utiliser currentText() pour le combo éditable
            marche = self.marche_input.text().strip()
            objet = self.objet_input.toPlainText().strip()
            devis_numero = self.devis_combo.currentText().strip()
            if devis_numero == "SÉLECTIONNÉ OU TAPER":
                devis_numero = None
            elif " - " in devis_numero:
                devis_numero = devis_numero.split(" - ")[0]  # Extraire seulement le numéro

            cursor = self.db_manager.conn.cursor()

            if self.bl_id:
                # Modification
                cursor.execute("""
                    UPDATE bons_livraison
                    SET numero=?, date_creation=?, date_livraison=?, client_id=?,
                        bon_commande=?, marche=?, objet=?, devis_numero=?
                    WHERE id=?
                """, (numero, date_creation, date_livraison, client_id,
                      bon_commande, marche, objet, devis_numero, self.bl_id))

                # Supprimer les anciennes lignes
                cursor.execute("DELETE FROM lignes_bl WHERE bl_id = ?", (self.bl_id,))
                bl_id = self.bl_id
            else:
                # Création
                cursor.execute("""
                    INSERT INTO bons_livraison
                    (numero, date_creation, date_livraison, client_id, bon_commande, marche, objet, devis_numero)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (numero, date_creation, date_livraison, client_id, bon_commande, marche, objet, devis_numero))

                bl_id = cursor.lastrowid

            # Enregistrer les articles
            for row in range(self.articles_table.rowCount()):
                designation_widget = self.articles_table.cellWidget(row, 0)
                unite_widget = self.articles_table.cellWidget(row, 1)
                quantite_widget = self.articles_table.cellWidget(row, 2)

                if designation_widget and unite_widget and quantite_widget:
                    # Récupérer la désignation du QLineEdit
                    designation = designation_widget.text().strip()
                    unite = unite_widget.text().strip()
                    quantite = quantite_widget.value()

                    if designation:  # Seulement si la désignation n'est pas vide
                        cursor.execute("""
                            INSERT INTO lignes_bl (bl_id, designation, unite, quantite)
                            VALUES (?, ?, ?, ?)
                        """, (bl_id, designation, unite, quantite))

            self.db_manager.conn.commit()
            QMessageBox.information(self, "Succès", "Bon de livraison enregistré avec succès.")
            self.bl_saved.emit()
            self.accept()

        except Exception as e:
            self.db_manager.conn.rollback()
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")

    def load_bl_data(self):
        """Charger les données d'un BL existant"""
        if not self.bl_id:
            return

        try:
            cursor = self.db_manager.conn.cursor()

            # Charger les données du BL
            cursor.execute("SELECT * FROM bons_livraison WHERE id = ?", (self.bl_id,))
            bl = cursor.fetchone()

            if bl:
                self.numero_input.setText(bl['numero'])
                self.date_creation_input.setDate(QDate.fromString(bl['date_creation'], "yyyy-MM-dd"))
                # Date de livraison comme QDateEdit
                if bl['date_livraison']:
                    self.date_livraison_input.setDate(QDate.fromString(bl['date_livraison'], "yyyy-MM-dd"))
                else:
                    self.date_livraison_input.setDate(QDate())  # Date vide

                # Sélectionner le client
                for i in range(self.client_combo.count()):
                    if self.client_combo.itemData(i) == bl['client_id']:
                        self.client_combo.setCurrentIndex(i)
                        break

                # Définir le bon de commande dans le combo éditable
                if bl['bon_commande']:
                    self.bon_commande_combo.setCurrentText(bl['bon_commande'])

                self.marche_input.setText(bl['marche'] or "")
                self.objet_input.setPlainText(bl['objet'] or "")

            # Charger les lignes
            cursor.execute("SELECT * FROM lignes_bl WHERE bl_id = ?", (self.bl_id,))
            lignes = cursor.fetchall()

            # Vider le tableau
            self.articles_table.setRowCount(0)

            # Ajouter les lignes
            for ligne in lignes:
                row = self.articles_table.rowCount()
                self.articles_table.insertRow(row)

                # Désignation (QLineEdit simple)
                designation_input = QLineEdit(ligne['designation'])
                designation_input.setStyleSheet("padding: 4px; border: 1px solid #ddd; border-radius: 4px;")
                self.articles_table.setCellWidget(row, 0, designation_input)

                # Unité
                unite_input = QLineEdit(ligne['unite'] or "")
                unite_input.setStyleSheet("padding: 4px; border: 1px solid #ddd; border-radius: 4px;")
                self.articles_table.setCellWidget(row, 1, unite_input)

                # Quantité
                quantite_input = QSpinBox()
                quantite_input.setMinimum(1)
                quantite_input.setMaximum(9999)
                quantite_input.setValue(ligne['quantite'])
                self.articles_table.setCellWidget(row, 2, quantite_input)

                # Bouton supprimer
                delete_btn = QPushButton("🗑️")
                delete_btn.clicked.connect(lambda: self.remove_article_row(row))
                self.articles_table.setCellWidget(row, 3, delete_btn)

            # Ajouter une ligne vide si aucune ligne
            if not lignes:
                self.add_article_row()

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement: {str(e)}")


class BLModule(BaseModule):
    """Module simple de gestion des bons de livraison"""

    def __init__(self, db_manager, signals=None):
        super().__init__(
            db_manager=db_manager,
            signals=signals,
            title="Bons de Livraison",
            description="Gérez vos bons de livraison",
            icon="📦"
        )
        self.setup_table()
        self.load_bls()

    def setup_table(self):
        """Configure le tableau des BL"""
        self.items_table.setColumnCount(6)
        self.items_table.setHorizontalHeaderLabels([
            "N° BL", "Date création", "Client", "Date livraison", "Statut", "Articles"
        ])

        # Activer le menu contextuel
        self.items_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.items_table.customContextMenuRequested.connect(self.show_context_menu)

        # Configurer les colonnes
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.items_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.items_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.items_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.items_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.items_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)

    def load_bls(self):
        """Charge les bons de livraison depuis la base de données"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT b.id, b.numero, b.date_creation, c.nom as client_nom,
                       b.date_livraison, b.statut,
                       COUNT(l.id) as nb_articles
                FROM bons_livraison b
                LEFT JOIN clients c ON b.client_id = c.id
                LEFT JOIN lignes_bl l ON b.id = l.bl_id
                GROUP BY b.id, b.numero, b.date_creation, c.nom, b.date_livraison, b.statut
                ORDER BY b.date_creation DESC
            """)
            bls = cursor.fetchall()

            self.items_table.setRowCount(0)

            for row_index, bl in enumerate(bls):
                self.items_table.insertRow(row_index)

                # Formater la date de création
                date_creation = datetime.datetime.strptime(bl['date_creation'], '%Y-%m-%d').strftime('%d/%m/%Y')

                # Date de livraison comme texte (pas de formatage)
                date_livraison = bl['date_livraison'] or ""

                # Ajouter les données
                self.items_table.setItem(row_index, 0, QTableWidgetItem(bl['numero'] or ""))
                self.items_table.setItem(row_index, 1, QTableWidgetItem(date_creation))
                self.items_table.setItem(row_index, 2, QTableWidgetItem(bl['client_nom'] or ""))
                self.items_table.setItem(row_index, 3, QTableWidgetItem(date_livraison))
                self.items_table.setItem(row_index, 4, QTableWidgetItem(bl['statut'] or "En cours"))
                self.items_table.setItem(row_index, 5, QTableWidgetItem(f"{bl['nb_articles']} articles"))

                # Stocker l'ID dans la première colonne
                self.items_table.item(row_index, 0).setData(Qt.UserRole, bl['id'])

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement: {str(e)}")

    def show_add_dialog(self):
        """Affiche la boîte de dialogue pour ajouter un BL"""
        dialog = BLDialog(self.db_manager, parent=self)
        dialog.bl_saved.connect(self.load_bls)
        dialog.exec()

    def show_edit_dialog(self, bl_id):
        """Affiche la boîte de dialogue pour modifier un BL"""
        dialog = BLDialog(self.db_manager, bl_id=bl_id, parent=self)
        dialog.bl_saved.connect(self.load_bls)
        dialog.exec()

    def show_context_menu(self, position):
        """Affiche le menu contextuel"""
        from PySide6.QtWidgets import QMenu
        from PySide6.QtGui import QAction

        item = self.items_table.itemAt(position)
        if not item:
            return

        row = item.row()
        bl_id = self.items_table.item(row, 0).data(Qt.UserRole)
        if not bl_id:
            return

        menu = QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px;
            }
            QMenu::item {
                padding: 8px 16px;
                border-radius: 4px;
            }
            QMenu::item:selected {
                background-color: #f0f0f0;
            }
        """)

        # Actions
        edit_action = QAction("✏️ Modifier", self)
        edit_action.triggered.connect(lambda: self.show_edit_dialog(bl_id))
        menu.addAction(edit_action)

        delete_action = QAction("🗑️ Supprimer", self)
        delete_action.triggered.connect(lambda: self.delete_item(bl_id))
        menu.addAction(delete_action)

        menu.exec(self.items_table.mapToGlobal(position))

    def delete_item(self, bl_id):
        """Supprime un bon de livraison"""
        reply = QMessageBox.question(
            self, "Confirmation",
            "Êtes-vous sûr de vouloir supprimer ce bon de livraison ?",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                cursor = self.db_manager.conn.cursor()
                cursor.execute("DELETE FROM lignes_bl WHERE bl_id = ?", (bl_id,))
                cursor.execute("DELETE FROM bons_livraison WHERE id = ?", (bl_id,))
                self.db_manager.conn.commit()
                self.load_bls()
                QMessageBox.information(self, "Succès", "Bon de livraison supprimé avec succès.")
            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression: {str(e)}")
