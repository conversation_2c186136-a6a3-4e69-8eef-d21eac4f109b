#!/usr/bin/env python3
"""
اختبار دالة create_products_table
"""

import sqlite3
import os

def test_get_all_products():
    """اختبار دالة get_all_products"""
    print("🔍 اختبار دالة get_all_products")
    print("=" * 40)
    
    db_path = "database/comptabilite.db"
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # نفس الكود من run_app.py
        cursor.execute("PRAGMA table_info(produits)")
        columns = [column[1] for column in cursor.fetchall()]
        
        print(f"📋 أعمدة الجدول: {columns}")
        
        if 'famille_id' in columns:
            print("✅ استخدام استعلام مع العائلات")
            cursor.execute("""
                SELECT p.*, f.nom as famille_nom
                FROM produits p
                LEFT JOIN familles_produits f ON p.famille_id = f.id
                ORDER BY p.date_creation DESC
            """)
        else:
            print("⚠️ استخدام استعلام بسيط")
            cursor.execute("SELECT *, NULL as famille_nom FROM produits ORDER BY date_creation DESC")
        
        products = cursor.fetchall()
        print(f"📦 عدد المنتجات المسترجعة: {len(products)}")
        
        if products:
            print("\n📋 تفاصيل أول منتج:")
            first_product = products[0]
            print(f"   عدد الأعمدة: {len(first_product)}")
            
            for i, value in enumerate(first_product):
                column_name = columns[i] if i < len(columns) else f"عمود_{i}"
                print(f"   [{i}] {column_name}: {value}")
            
            print("\n🔍 محاولة استخراج البيانات:")
            try:
                id = first_product[0]
                code = first_product[1] if len(first_product) > 1 else f"P{id:03d}"
                designation = first_product[2] if len(first_product) > 2 else "Produit sans nom"
                prix_achat = first_product[4] if len(first_product) > 4 and first_product[4] is not None else 0.0
                prix_vente = first_product[5] if len(first_product) > 5 and first_product[5] is not None else 0.0
                stock = first_product[6] if len(first_product) > 6 and first_product[6] is not None else 0
                
                print(f"   ✅ ID: {id}")
                print(f"   ✅ الكود: {code}")
                print(f"   ✅ التسمية: {designation}")
                print(f"   ✅ سعر الشراء: {prix_achat}")
                print(f"   ✅ سعر البيع: {prix_vente}")
                print(f"   ✅ المخزون: {stock}")
                
            except Exception as e:
                print(f"   ❌ خطأ في استخراج البيانات: {str(e)}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")

if __name__ == "__main__":
    test_get_all_products()
