from PySide6.QtWidgets import (QTableWidgetItem, QHeaderView, QMessageBox, QDialog)
from PySide6.QtCore import Qt, QDate
import sqlite3
import datetime

from ..components.list_dialog import ListDialog

class BonsListDialog(ListDialog):
    """Boîte de dialogue pour afficher la liste des bons de commande"""

    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "Liste des bons de commande", parent)
        self.load_items()

    def setup_table(self):
        """Configure le tableau des bons de commande"""
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "N° Bon", "Date", "Client", "Total HT", "TVA", "Total TTC", "Actions"
        ])

        self.items_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # N° Bon
        self.items_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Date
        self.items_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Total HT
        self.items_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # TVA
        self.items_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Total TTC
        self.items_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.Fixed)  # Actions
        self.items_table.setColumnWidth(6, 120)

        self.items_table.setSelectionBehavior(QTableWidgetItem.SelectRows)
        self.items_table.setEditTriggers(QTableWidgetItem.NoEditTriggers)
        self.items_table.verticalHeader().setVisible(False)
        self.items_table.setAlternatingRowColors(True)

        # Connecter le signal de double-clic pour ouvrir la boîte de dialogue d'édition
        self.items_table.itemDoubleClicked.connect(self.on_item_double_clicked)

    def load_items(self):
        """Charge les bons de commande depuis la base de données"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT b.id, b.numero, b.date_creation, c.nom as client_nom,
                       b.total_ht, b.total_tva, b.total_ttc
                FROM bons_commande b
                LEFT JOIN clients c ON b.client_id = c.id
                ORDER BY b.date_creation DESC
            """)
            bons = cursor.fetchall()

            self.items_table.setRowCount(0)

            for row_index, bon in enumerate(bons):
                self.items_table.insertRow(row_index)

                # Formater la date
                date_obj = datetime.datetime.strptime(bon['date_creation'], '%Y-%m-%d')
                date_str = date_obj.strftime('%d/%m/%Y')

                # Ajouter les données du bon de commande
                self.items_table.setItem(row_index, 0, QTableWidgetItem(bon['numero'] or ""))
                self.items_table.setItem(row_index, 1, QTableWidgetItem(date_str))
                self.items_table.setItem(row_index, 2, QTableWidgetItem(bon['client_nom'] or ""))
                self.items_table.setItem(row_index, 3, QTableWidgetItem(f"{bon['total_ht']:.2f} DH"))
                self.items_table.setItem(row_index, 4, QTableWidgetItem(f"{bon['total_tva']:.2f} DH"))
                self.items_table.setItem(row_index, 5, QTableWidgetItem(f"{bon['total_ttc']:.2f} DH"))

                # Ajouter les boutons d'action
                actions_widget = self.create_action_buttons(row_index, bon['id'])
                self.items_table.setCellWidget(row_index, 6, actions_widget)

                # Stocker l'ID du bon dans les données de la première colonne
                self.items_table.item(row_index, 0).setData(Qt.UserRole, bon['id'])

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des bons de commande: {str(e)}")

    def add_item(self):
        """Émet le signal pour créer un nouveau bon de commande"""
        self.item_added.emit()
        self.accept()

    def edit_item(self, bon_id):
        """Émet le signal pour modifier un bon de commande existant"""
        self.item_edited.emit(bon_id)
        self.accept()

    def delete_item(self, bon_id):
        """Supprime un bon de commande"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT numero FROM bons_commande WHERE id = ?", (bon_id,))
            bon = cursor.fetchone()

            if not bon:
                QMessageBox.warning(self, "Erreur", "Bon de commande introuvable.")
                return

            reply = QMessageBox.question(
                self, "Confirmation",
                f"Êtes-vous sûr de vouloir supprimer le bon de commande {bon['numero']} ?",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Supprimer d'abord les lignes du bon
                cursor.execute("DELETE FROM lignes_bon_commande WHERE bon_commande_id = ?", (bon_id,))

                # Puis supprimer le bon
                cursor.execute("DELETE FROM bons_commande WHERE id = ?", (bon_id,))

                self.db_manager.conn.commit()
                self.load_items()
                self.item_deleted.emit(bon_id)
                QMessageBox.information(self, "Succès", "Bon de commande supprimé avec succès.")

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression du bon de commande: {str(e)}")

    def on_item_double_clicked(self, item):
        """Gère le double-clic sur un élément du tableau"""
        if item is None:
            return

        # Récupérer l'ID du bon de commande depuis la première colonne
        row = item.row()
        first_column_item = self.items_table.item(row, 0)

        if first_column_item:
            # Récupérer l'ID stocké dans les données utilisateur
            bon_id = first_column_item.data(Qt.UserRole)
            if bon_id:
                # Appeler la méthode d'édition
                self.edit_item(bon_id)
