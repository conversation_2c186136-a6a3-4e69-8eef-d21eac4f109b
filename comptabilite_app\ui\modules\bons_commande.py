from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QComboBox, QDateEdit, QSpinBox, QFormLayout,
                              QMessageBox, QHeaderView, QGridLayout, QFrame, QDoubleSpinBox,
                              QDialog, QSizePolicy)
from PySide6.QtCore import Qt, QDate, Signal, QSize
from PySide6.QtGui import QColor, QIcon
import sqlite3
import os
from datetime import datetime

# Importer les icônes
from ..icons.icons import svg_to_icon_html, ORDERS_ICON, EDIT_ICON, DELETE_ICON, PRINT_ICON, LIST_ICON

# Importer la boîte de dialogue de liste des bons de commande
from .bons_list_dialog import BonsListDialog

class BonsCommandeModule(QWidget):
    """Module de gestion des bons de commande"""

    # Signal émis lorsqu'un bon de commande est créé ou modifié
    bon_commande_changed = Signal()

    def __init__(self, db_manager, signals=None):
        super().__init__()
        self.db_manager = db_manager
        self.signals = signals  # Signaux pour la communication entre modules
        self.initUI()

        # Connecter le signal de mise à jour des fournisseurs si disponible
        if self.signals:
            self.signals.fournisseurs_changed.connect(self.charger_fournisseurs)

    def initUI(self):
        """Initialise l'interface utilisateur"""
        # Layout principal
        main_layout = QVBoxLayout(self)

        # En-tête avec titre et boutons
        header_container = QWidget()
        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(0, 0, 0, 0)

        # Titre du module
        title_label = QLabel("BON DE COMMANDE")
        title_label.setStyleSheet("font-size: 20px; font-weight: bold; color: #1A56DB;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Bouton Liste des Bons de Commande
        self.liste_bons_btn = QPushButton("LISTE DES BONS")
        self.liste_bons_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
                margin-right: 10px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
        """)
        self.liste_bons_btn.setIcon(QIcon(":/icons/list.png"))
        self.liste_bons_btn.clicked.connect(self.show_bons_list)
        header_layout.addWidget(self.liste_bons_btn)

        main_layout.addWidget(header_container)

        # Formulaire principal
        form_layout = QGridLayout()

        # Première ligne: Numéro de bon de commande, Client, Objet
        form_layout.addWidget(QLabel("N° BON DE COMMANDE:"), 0, 0)
        self.numero_bc = QLineEdit()
        self.numero_bc.setReadOnly(True)
        self.generer_numero_bc()
        form_layout.addWidget(self.numero_bc, 0, 1)

        form_layout.addWidget(QLabel("CLIENT:"), 0, 2)
        self.client_combo = QComboBox()
        self.client_combo.setMinimumWidth(250)
        form_layout.addWidget(self.client_combo, 0, 3)

        form_layout.addWidget(QLabel("OBJET:"), 0, 4)
        self.objet_input = QLineEdit()
        form_layout.addWidget(self.objet_input, 0, 5)

        # Deuxième ligne: Référence client, Ordre de service, Délai d'exécution
        form_layout.addWidget(QLabel("RÉFÉRENCE CLIENT:"), 1, 0)
        self.ref_client_input = QLineEdit()
        form_layout.addWidget(self.ref_client_input, 1, 1)

        form_layout.addWidget(QLabel("ORDRE DE SERVICE:"), 1, 2)
        self.ordre_service_input = QLineEdit()
        form_layout.addWidget(self.ordre_service_input, 1, 3)

        form_layout.addWidget(QLabel("DÉLAI D'EXÉCUTION:"), 1, 4)
        self.delai_execution_input = QLineEdit()
        form_layout.addWidget(self.delai_execution_input, 1, 5)

        # Troisième ligne: Date, Montant global
        form_layout.addWidget(QLabel("DATE:"), 2, 0)
        from ..style import create_styled_date_edit
        self.date_bc = create_styled_date_edit()
        form_layout.addWidget(self.date_bc, 2, 1)

        form_layout.addWidget(QLabel("MONTANT GLOBAL:"), 2, 4)
        self.montant_global_label = QLineEdit()
        self.montant_global_label.setReadOnly(True)
        self.montant_global_label.setText("0.00 DH")
        form_layout.addWidget(self.montant_global_label, 2, 5)

        main_layout.addLayout(form_layout)

        # Séparateur
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(separator)

        # Section détails
        details_label = QLabel("DÉTAILS")
        details_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        main_layout.addWidget(details_label)

        # Tableau des articles
        self.articles_table = QTableWidget(0, 7)
        self.articles_table.setHorizontalHeaderLabels(["DÉSIGNATION", "UNITÉ", "QTÉ", "PRIX UNITAIRE HT", "PRIX TOTAL HT", "ACTION", ""])
        self.articles_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.articles_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)
        self.articles_table.setAlternatingRowColors(True)
        self.articles_table.setStyleSheet("alternate-background-color: #f0f0f0;")
        main_layout.addWidget(self.articles_table)

        # Bouton pour ajouter une ligne
        add_row_btn = QPushButton("Ajouter une ligne")
        add_row_btn.clicked.connect(self.ajouter_ligne)
        main_layout.addWidget(add_row_btn)

        # Totaux
        totals_layout = QFormLayout()

        self.total_ht_label = QLineEdit()
        self.total_ht_label.setReadOnly(True)
        self.total_ht_label.setText("0.00 DH")
        totals_layout.addRow("TOTAL HT:", self.total_ht_label)

        self.tva_label = QLineEdit()
        self.tva_label.setReadOnly(True)
        self.tva_label.setText("0.00 DH")
        totals_layout.addRow("TVA 20%:", self.tva_label)

        self.ttc_label = QLineEdit()
        self.ttc_label.setReadOnly(True)
        self.ttc_label.setText("0.00 DH")
        totals_layout.addRow("TTC:", self.ttc_label)

        main_layout.addLayout(totals_layout)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        self.valider_btn = QPushButton("Valider")
        self.valider_btn.clicked.connect(self.enregistrer_bon_commande)
        buttons_layout.addWidget(self.valider_btn)

        self.exporter_pdf_btn = QPushButton("Exporter PDF")
        self.exporter_pdf_btn.clicked.connect(self.exporter_pdf)
        buttons_layout.addWidget(self.exporter_pdf_btn)

        self.imprimer_btn = QPushButton("Imprimer")
        self.imprimer_btn.clicked.connect(self.imprimer_bon_commande)
        buttons_layout.addWidget(self.imprimer_btn)

        main_layout.addLayout(buttons_layout)

        # Charger les données
        self.charger_clients()

    def charger_clients(self):
        """Charge la liste des clients depuis la base de données"""
        # Déconnecter le signal pour éviter les appels multiples pendant le chargement
        try:
            self.client_combo.currentIndexChanged.disconnect()
        except:
            pass  # Si le signal n'était pas connecté, ignorer l'erreur

        # Sauvegarder les données actuelles
        current_data = self.client_combo.currentData()

        self.client_combo.clear()
        self.client_combo.addItem("", None)  # Option vide

        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT id, nom FROM clients ORDER BY nom")
            clients = cursor.fetchall()

            for client_id, nom in clients:
                self.client_combo.addItem(nom, client_id)

            # Restaurer l'index précédent si possible
            if current_data:
                # Chercher l'index du client précédemment sélectionné
                for i in range(self.client_combo.count()):
                    if self.client_combo.itemData(i) == current_data:
                        self.client_combo.setCurrentIndex(i)
                        break

            # Reconnecter le signal après avoir chargé les clients
            try:
                self.client_combo.currentIndexChanged.connect(self.client_selectionne)
            except:
                pass  # Si la méthode n'existe pas, ignorer l'erreur

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des clients: {str(e)}")

    def client_selectionne(self):
        """Méthode appelée lorsqu'un client est sélectionné"""
        # Cette méthode peut être étendue pour mettre à jour d'autres informations
        # basées sur le client sélectionné si nécessaire
        pass

    def generer_numero_bc(self):
        """Génère un numéro de bon de commande unique"""
        # Format: BC-MMYY-XXX où MM=mois, YY=année, XXX=numéro séquentiel
        now = datetime.now()
        prefix = f"BC-{now.month:02d}{now.year % 100:02d}-"

        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT numero FROM bons_commande WHERE numero LIKE ? ORDER BY numero DESC LIMIT 1",
                          (f"{prefix}%",))
            result = cursor.fetchone()

            if result:
                # Extraire le numéro séquentiel et l'incrémenter
                last_num = result[0]
                seq_num = int(last_num.split('-')[-1]) + 1
            else:
                # Premier bon de commande pour ce mois/année
                seq_num = 1

            # Formater le nouveau numéro
            new_num = f"{prefix}{seq_num:03d}"
            self.numero_bc.setText(new_num)

        except sqlite3.Error as e:
            # En cas d'erreur, utiliser un numéro par défaut
            self.numero_bc.setText(f"{prefix}001")
            print(f"Erreur lors de la génération du numéro de bon de commande: {str(e)}")

    def ajouter_ligne(self):
        """Ajoute une ligne au tableau des articles"""
        row_position = self.articles_table.rowCount()
        self.articles_table.insertRow(row_position)

        # Créer les widgets pour la nouvelle ligne
        designation_combo = QComboBox()
        designation_combo.setEditable(True)
        designation_combo.addItem("-- Sélectionner un produit --")
        self.charger_produits(designation_combo)
        designation_combo.currentIndexChanged.connect(lambda: self.produit_selectionne(row_position))

        unite_edit = QLineEdit()

        quantite_spin = QSpinBox()
        quantite_spin.setMinimum(1)
        quantite_spin.setMaximum(9999)
        quantite_spin.setValue(1)
        quantite_spin.valueChanged.connect(lambda: self.calculer_ligne(row_position))

        prix_edit = QLineEdit()
        prix_edit.setText("0.00 DH")
        prix_edit.textChanged.connect(lambda: self.calculer_ligne(row_position))

        total_edit = QLineEdit()
        total_edit.setReadOnly(True)
        total_edit.setText("0.00 DH")

        supprimer_btn = QPushButton("Supprimer")
        supprimer_btn.clicked.connect(lambda: self.supprimer_ligne(row_position))

        # Ajouter les widgets à la ligne
        self.articles_table.setCellWidget(row_position, 0, designation_combo)
        self.articles_table.setCellWidget(row_position, 1, unite_edit)
        self.articles_table.setCellWidget(row_position, 2, quantite_spin)
        self.articles_table.setCellWidget(row_position, 3, prix_edit)
        self.articles_table.setCellWidget(row_position, 4, total_edit)
        self.articles_table.setCellWidget(row_position, 5, supprimer_btn)

        # Calculer les totaux
        self.calculer_totaux()

    def charger_produits(self, combo):
        """Charge la liste des produits dans le combobox"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT id, code, designation, unite, prix_vente FROM produits ORDER BY designation")
            produits = cursor.fetchall()

            for produit_id, code, designation, unite, prix_vente in produits:
                # Stocker les données du produit comme données utilisateur
                combo.addItem(f"{code} - {designation}", {
                    'id': produit_id,
                    'code': code,
                    'designation': designation,
                    'unite': unite,
                    'prix_vente': prix_vente
                })

        except sqlite3.Error as e:
            print(f"Erreur lors du chargement des produits: {str(e)}")

    def produit_selectionne(self, row):
        """Gère la sélection d'un produit dans le combobox"""
        designation_combo = self.articles_table.cellWidget(row, 0)
        unite_edit = self.articles_table.cellWidget(row, 1)
        prix_edit = self.articles_table.cellWidget(row, 3)

        # Récupérer les données du produit sélectionné
        produit_data = designation_combo.currentData()

        if produit_data and isinstance(produit_data, dict):
            # Remplir les champs avec les données du produit
            unite_edit.setText(produit_data['unite'])
            prix_edit.setText(f"{produit_data['prix_vente']:.2f} DH")

            # Recalculer la ligne
            self.calculer_ligne(row)

    def calculer_ligne(self, row):
        """Calcule le total d'une ligne"""
        quantite_spin = self.articles_table.cellWidget(row, 2)
        prix_edit = self.articles_table.cellWidget(row, 3)
        total_edit = self.articles_table.cellWidget(row, 4)

        if quantite_spin and prix_edit and total_edit:
            try:
                quantite = quantite_spin.value()
                # Récupérer le texte du prix unitaire et nettoyer les caractères non numériques
                prix_text = prix_edit.text()
                # Remplacer tous les caractères non numériques (sauf le point décimal) par une chaîne vide
                prix_text = ''.join(c for c in prix_text if c.isdigit() or c == '.')
                try:
                    prix = float(prix_text or 0)
                except ValueError:
                    # En cas d'erreur, utiliser 0 comme valeur par défaut
                    prix = 0.0
                total = quantite * prix

                total_edit.setText(f"{total:.2f} DH")

                # Mettre à jour les totaux
                self.calculer_totaux()
            except ValueError:
                pass

    def supprimer_ligne(self, row):
        """Supprime une ligne du tableau"""
        self.articles_table.removeRow(row)

        # Recalculer les totaux
        self.calculer_totaux()

    def calculer_totaux(self):
        """Calcule les totaux du bon de commande"""
        total_ht = 0.0

        # Parcourir toutes les lignes du tableau
        for row in range(self.articles_table.rowCount()):
            total_edit = self.articles_table.cellWidget(row, 4)
            if total_edit:
                try:
                    # Récupérer le texte du total et nettoyer les caractères non numériques
                    total_text = total_edit.text()
                    # Remplacer tous les caractères non numériques (sauf le point décimal) par une chaîne vide
                    total_text = ''.join(c for c in total_text if c.isdigit() or c == '.')
                    try:
                        total_ht += float(total_text or 0)
                    except ValueError:
                        # En cas d'erreur, ne pas ajouter au total
                        pass
                except ValueError:
                    pass

        # Calculer la TVA et le total TTC
        tva = total_ht * 0.2
        ttc = total_ht + tva

        # Mettre à jour les labels
        self.total_ht_label.setText(f"{total_ht:.2f} DH")
        self.tva_label.setText(f"{tva:.2f} DH")
        self.ttc_label.setText(f"{ttc:.2f} DH")
        self.montant_global_label.setText(f"{ttc:.2f} DH")

    def enregistrer_bon_commande(self):
        """Enregistre le bon de commande dans la base de données"""
        # Vérifier qu'un client est sélectionné
        client_id = self.client_combo.currentData()
        if not client_id:
            QMessageBox.warning(self, "Erreur", "Veuillez sélectionner un client.")
            return

        # Vérifier qu'il y a au moins une ligne
        if self.articles_table.rowCount() == 0:
            QMessageBox.warning(self, "Erreur", "Veuillez ajouter au moins une ligne au bon de commande.")
            return

        # Récupérer les données du formulaire
        numero = self.numero_bc.text()
        date = self.date_bc.date().toString("yyyy-MM-dd")
        ref_client = self.ref_client_input.text()
        ordre_service = self.ordre_service_input.text()
        delai_execution = self.delai_execution_input.text()
        objet = self.objet_input.text()

        # Récupérer et nettoyer les totaux
        def clean_and_convert(text):
            # Remplacer tous les caractères non numériques (sauf le point décimal) par une chaîne vide
            cleaned = ''.join(c for c in text if c.isdigit() or c == '.')
            try:
                return float(cleaned or 0)
            except ValueError:
                return 0.0

        total_ht = clean_and_convert(self.total_ht_label.text())
        total_tva = clean_and_convert(self.tva_label.text())
        total_ttc = clean_and_convert(self.ttc_label.text())

        # Enregistrer le bon de commande
        cursor = self.db_manager.conn.cursor()
        try:
            self.db_manager.conn.execute("BEGIN TRANSACTION")

            # Insérer le bon de commande
            cursor.execute(
                """INSERT INTO bons_commande
                   (numero, date, client_id, ref_client, ordre_service, delai_execution, objet,
                    total_ht, total_tva, total_ttc)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (numero, date, client_id, ref_client, ordre_service, delai_execution, objet,
                 total_ht, total_tva, total_ttc)
            )

            # Récupérer l'ID du bon de commande inséré
            bon_commande_id = cursor.lastrowid

            # Insérer les lignes du bon de commande
            for row in range(self.articles_table.rowCount()):
                designation_combo = self.articles_table.cellWidget(row, 0)
                unite_edit = self.articles_table.cellWidget(row, 1)
                quantite_spin = self.articles_table.cellWidget(row, 2)
                prix_edit = self.articles_table.cellWidget(row, 3)

                if designation_combo and unite_edit and quantite_spin and prix_edit:
                    # Récupérer les données du produit
                    produit_data = designation_combo.currentData()
                    produit_id = produit_data['id'] if produit_data and isinstance(produit_data, dict) else None

                    designation = designation_combo.currentText()
                    if designation == "-- Sélectionner un produit --":
                        designation = ""

                    unite = unite_edit.text()
                    quantite = quantite_spin.value()

                    # Récupérer le texte du prix unitaire et nettoyer les caractères non numériques
                    prix_text = prix_edit.text()
                    # Remplacer tous les caractères non numériques (sauf le point décimal) par une chaîne vide
                    prix_text = ''.join(c for c in prix_text if c.isdigit() or c == '.')
                    try:
                        prix_unitaire = float(prix_text or 0)
                    except ValueError:
                        # En cas d'erreur, utiliser 0 comme valeur par défaut
                        prix_unitaire = 0.0
                    total_ligne = quantite * prix_unitaire

                    cursor.execute(
                        """INSERT INTO lignes_bon_commande
                           (bon_commande_id, produit_id, designation, unite, quantite,
                            prix_unitaire, total)
                           VALUES (?, ?, ?, ?, ?, ?, ?)""",
                        (bon_commande_id, produit_id, designation, unite, quantite,
                         prix_unitaire, total_ligne)
                    )

            self.db_manager.conn.commit()
            QMessageBox.information(self, "Succès", "Bon de commande enregistré avec succès.")

            # Émettre le signal de changement
            self.bon_commande_changed.emit()

            # Réinitialiser le formulaire
            self.reinitialiser_formulaire()

        except sqlite3.Error as e:
            self.db_manager.conn.rollback()
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement du bon de commande: {str(e)}")

    def reinitialiser_formulaire(self):
        """Réinitialise le formulaire après enregistrement"""
        # Générer un nouveau numéro de bon de commande
        self.generer_numero_bc()

        # Réinitialiser les champs
        self.client_combo.setCurrentIndex(0)
        self.ref_client_input.clear()
        self.ordre_service_input.clear()
        self.delai_execution_input.clear()
        self.objet_input.clear()
        self.date_bc.setDate(QDate.currentDate())

        # Vider le tableau
        while self.articles_table.rowCount() > 0:
            self.articles_table.removeRow(0)

        # Réinitialiser les totaux
        self.total_ht_label.setText("0.00 DH")
        self.tva_label.setText("0.00 DH")
        self.ttc_label.setText("0.00 DH")
        self.montant_global_label.setText("0.00 DH")

    def exporter_pdf(self):
        """Exporte le bon de commande en PDF"""
        # TODO: Implémenter l'export PDF
        QMessageBox.information(self, "Information", "Fonctionnalité d'export PDF à implémenter.")

    def imprimer_bon_commande(self):
        """Imprime le bon de commande"""
        # TODO: Implémenter l'impression
        QMessageBox.information(self, "Information", "Fonctionnalité d'impression à implémenter.")

    def charger_fournisseurs(self):
        """Charge la liste des fournisseurs depuis la base de données"""
        # Cette méthode est appelée quand le signal fournisseurs_changed est émis
        # Actuellement, ce module n'utilise pas directement les fournisseurs,
        # mais cette méthode pourrait être utilisée dans le futur si nécessaire
        pass

    def show_bons_list(self):
        """Affiche la liste des bons de commande dans une boîte de dialogue"""
        dialog = BonsListDialog(self.db_manager, self)

        # Connecter les signaux
        dialog.item_selected.connect(self.charger_bon_commande)
        dialog.item_added.connect(self.reinitialiser_formulaire)
        dialog.item_edited.connect(self.charger_bon_commande)

        dialog.exec()

    def charger_bon_commande(self, bon_id):
        """Charge un bon de commande existant"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Récupérer les informations du bon de commande
            cursor.execute("""
                SELECT b.*, c.nom as client_nom
                FROM bons_commande b
                LEFT JOIN clients c ON b.client_id = c.id
                WHERE b.id = ?
            """, (bon_id,))
            bon = cursor.fetchone()

            if not bon:
                QMessageBox.warning(self, "Erreur", "Bon de commande introuvable.")
                return

            # Mettre à jour les champs du formulaire
            self.numero_bc.setText(bon['numero'])

            # Convertir la date
            date_obj = datetime.strptime(bon['date'], '%Y-%m-%d')
            self.date_bc.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))

            # Sélectionner le client
            for i in range(self.client_combo.count()):
                if self.client_combo.itemData(i) == bon['client_id']:
                    self.client_combo.setCurrentIndex(i)
                    break

            # Mettre à jour les autres champs
            self.ref_client_input.setText(bon['ref_client'] or "")
            self.ordre_service_input.setText(bon['ordre_service'] or "")
            self.delai_execution_input.setText(bon['delai_execution'] or "")
            self.objet_input.setText(bon['objet'] or "")

            # Charger les lignes du bon de commande
            self.articles_table.setRowCount(0)

            cursor.execute("""
                SELECT * FROM lignes_bon_commande
                WHERE bon_commande_id = ?
                ORDER BY id
            """, (bon_id,))
            lignes = cursor.fetchall()

            for ligne in lignes:
                self.ajouter_ligne_existante(ligne)

            # Mettre à jour les totaux
            self.calculer_totaux()

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement du bon de commande: {str(e)}")

    def ajouter_ligne_existante(self, ligne):
        """Ajoute une ligne existante au tableau des articles"""
        row_position = self.articles_table.rowCount()
        self.articles_table.insertRow(row_position)

        # Créer les widgets pour la nouvelle ligne
        designation_combo = QComboBox()
        designation_combo.setEditable(True)
        designation_combo.addItem("-- Sélectionner un produit --")
        self.charger_produits(designation_combo)

        # Sélectionner le produit si disponible
        if ligne['produit_id']:
            for i in range(designation_combo.count()):
                produit_data = designation_combo.itemData(i)
                if produit_data and isinstance(produit_data, dict) and produit_data.get('id') == ligne['produit_id']:
                    designation_combo.setCurrentIndex(i)
                    break
        else:
            # Si pas de produit_id, définir le texte directement
            designation_combo.setCurrentText(ligne['designation'])

        designation_combo.currentIndexChanged.connect(lambda: self.produit_selectionne(row_position))

        unite_edit = QLineEdit(ligne['unite'] or "")

        quantite_spin = QSpinBox()
        quantite_spin.setMinimum(1)
        quantite_spin.setMaximum(9999)
        quantite_spin.setValue(ligne['quantite'])
        quantite_spin.valueChanged.connect(lambda: self.calculer_ligne(row_position))

        prix_edit = QLineEdit(f"{ligne['prix_unitaire']:.2f} DH")
        prix_edit.textChanged.connect(lambda: self.calculer_ligne(row_position))

        total_edit = QLineEdit(f"{ligne['total']:.2f} DH")
        total_edit.setReadOnly(True)

        supprimer_btn = QPushButton("Supprimer")
        supprimer_btn.clicked.connect(lambda: self.supprimer_ligne(row_position))

        # Ajouter les widgets à la ligne
        self.articles_table.setCellWidget(row_position, 0, designation_combo)
        self.articles_table.setCellWidget(row_position, 1, unite_edit)
        self.articles_table.setCellWidget(row_position, 2, quantite_spin)
        self.articles_table.setCellWidget(row_position, 3, prix_edit)
        self.articles_table.setCellWidget(row_position, 4, total_edit)
        self.articles_table.setCellWidget(row_position, 5, supprimer_btn)
