"""
Migration pour améliorer la table des bons de livraison
pour correspondre exactement au formulaire français
"""
import sqlite3
import os

def migrate_bl_enhanced():
    """Migre la base de données pour supporter le formulaire de bon de livraison amélioré"""

    # Chemin vers la base de données
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'database', 'comptabilite.db')

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        print("🔄 Migration du bon de livraison amélioré...")

        # Créer la table bons_livraison si elle n'existe pas
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS bons_livraison (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                numero TEXT NOT NULL,
                date_creation TEXT NOT NULL,
                date_livraison TEXT,
                client_id INTEGER,
                bon_commande TEXT,
                marche TEXT,
                objet TEXT,
                statut TEXT DEFAULT 'En cours',
                notes TEXT,
                type_marche TEXT DEFAULT 'BC',
                devis_number TEXT,
                ice_client TEXT,
                adresse_livraison TEXT,
                mode_selection TEXT DEFAULT 'automatique',
                ligne_grise_non_affiche INTEGER DEFAULT 0,
                reste_bc_marche TEXT,
                qte_livre INTEGER DEFAULT 0,
                reste_en_stock INTEGER DEFAULT 0,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        """)
        print("✅ Table bons_livraison créée/vérifiée")

        # Créer la table lignes_bl si elle n'existe pas
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS lignes_bl (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bl_id INTEGER NOT NULL,
                produit_id INTEGER,
                designation TEXT NOT NULL,
                unite TEXT,
                quantite INTEGER NOT NULL DEFAULT 1,
                auto_manuel TEXT DEFAULT 'AUTO',
                qte_livre INTEGER DEFAULT 0,
                qte_dans_bc INTEGER DEFAULT 0,
                reste_en_stock INTEGER DEFAULT 0,
                FOREIGN KEY (bl_id) REFERENCES bons_livraison (id),
                FOREIGN KEY (produit_id) REFERENCES produits (id)
            )
        """)
        print("✅ Table lignes_bl créée/vérifiée")

        # Vérifier si les nouvelles colonnes existent déjà dans la table existante
        cursor.execute("PRAGMA table_info(bons_livraison)")
        columns = [column[1] for column in cursor.fetchall()]

        # Ajouter les nouvelles colonnes si elles n'existent pas
        new_columns = [
            ('type_marche', 'TEXT DEFAULT "BC"'),
            ('devis_number', 'TEXT'),
            ('ice_client', 'TEXT'),
            ('adresse_livraison', 'TEXT'),
            ('mode_selection', 'TEXT DEFAULT "automatique"'),
            ('ligne_grise_non_affiche', 'INTEGER DEFAULT 0'),
            ('reste_bc_marche', 'TEXT'),
            ('qte_livre', 'INTEGER DEFAULT 0'),
            ('reste_en_stock', 'INTEGER DEFAULT 0')
        ]

        for column_name, column_def in new_columns:
            if column_name not in columns:
                try:
                    cursor.execute(f"ALTER TABLE bons_livraison ADD COLUMN {column_name} {column_def}")
                    print(f"✅ Colonne {column_name} ajoutée")
                except sqlite3.Error as e:
                    print(f"⚠️ Colonne {column_name} existe déjà ou erreur: {e}")

        # Vérifier et ajouter colonnes pour lignes_bl
        cursor.execute("PRAGMA table_info(lignes_bl)")
        lignes_columns = [column[1] for column in cursor.fetchall()]

        lignes_new_columns = [
            ('auto_manuel', 'TEXT DEFAULT "AUTO"'),
            ('qte_livre', 'INTEGER DEFAULT 0'),
            ('qte_dans_bc', 'INTEGER DEFAULT 0'),
            ('reste_en_stock', 'INTEGER DEFAULT 0')
        ]

        for column_name, column_def in lignes_new_columns:
            if column_name not in lignes_columns:
                try:
                    cursor.execute(f"ALTER TABLE lignes_bl ADD COLUMN {column_name} {column_def}")
                    print(f"✅ Colonne lignes_bl.{column_name} ajoutée")
                except sqlite3.Error as e:
                    print(f"⚠️ Colonne lignes_bl.{column_name} existe déjà ou erreur: {e}")

        # Créer une table pour les paramètres du bon de livraison si elle n'existe pas
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS bl_parametres (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom_entreprise TEXT DEFAULT 'VOTRE ENTREPRISE',
                adresse_entreprise TEXT DEFAULT 'Adresse de votre entreprise',
                telephone_entreprise TEXT DEFAULT 'Téléphone',
                email_entreprise TEXT DEFAULT '<EMAIL>',
                logo_path TEXT,
                format_numero TEXT DEFAULT 'BL{YYYYMM}-{NNN}',
                date_creation TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Insérer les paramètres par défaut s'ils n'existent pas
        cursor.execute("SELECT COUNT(*) FROM bl_parametres")
        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                INSERT INTO bl_parametres (nom_entreprise, adresse_entreprise)
                VALUES ('VOTRE ENTREPRISE', 'Adresse de votre entreprise')
            """)
            print("✅ Paramètres par défaut du BL créés")

        conn.commit()
        print("✅ Migration du bon de livraison amélioré terminée avec succès")

    except sqlite3.Error as e:
        print(f"❌ Erreur lors de la migration: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    migrate_bl_enhanced()
