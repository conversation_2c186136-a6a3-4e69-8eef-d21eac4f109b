"""
Module de gestion des marchés et projets
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QTableWidget, QTableWidgetItem, QPushButton,
                               QHeaderView, QFrame, QLineEdit, QMessageBox,
                               QDialog, QDateEdit, QTextEdit, QComboBox,
                               QSpinBox, QDoubleSpinBox, QFormLayout, QTabWidget)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QFont
import sqlite3
from datetime import datetime

class MarcheModule(QWidget):
    """Module de gestion des marchés et projets"""

    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.setup_database()
        self.setup_ui()
        self.load_data()

    def setup_database(self):
        """Créer les tables nécessaires"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Table des marchés/projets
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS marches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date_creation TEXT NOT NULL,
                reference_marche TEXT,
                objet TEXT,
                montant REAL,
                delai_execution TEXT,
                caution_provisoire REAL,
                date_approbation TEXT,
                date_notification TEXT,
                montant_caution_definitif REAL,
                date_enregistrement TEXT,
                date_ordre_service TEXT,
                date_caution_retenu TEXT,
                date_reception_provisoire TEXT,
                statut TEXT DEFAULT 'En cours',
                notes TEXT
            )
            ''')

            # Table des articles du marché
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS marche_articles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                marche_id INTEGER,
                designation TEXT,
                unite TEXT,
                quantite INTEGER,
                prix_unitaire_ht REAL,
                prix_total_ht REAL,
                FOREIGN KEY (marche_id) REFERENCES marches (id)
            )
            ''')

            # Table des charges du marché
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS marche_charges (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                marche_id INTEGER,
                type_charge TEXT,
                description TEXT,
                montant REAL,
                date_charge TEXT,
                mode_paiement TEXT,
                FOREIGN KEY (marche_id) REFERENCES marches (id)
            )
            ''')

            self.db_manager.conn.commit()
            print("✅ Tables des marchés créées")

        except sqlite3.Error as e:
            print(f"❌ Erreur création tables marchés: {e}")

    def setup_ui(self):
        """Configuration de l'interface utilisateur"""
        layout = QVBoxLayout()
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title = QLabel("📋 Gestion des Marchés/BC")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2563EB; margin-bottom: 20px;")
        layout.addWidget(title)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        self.add_marche_btn = QPushButton("📋 Marché/BC")
        self.add_marche_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
        """)
        self.add_marche_btn.clicked.connect(self.show_marche_dialog)

        self.create_btn = QPushButton("➕ Créé")
        self.create_btn.setStyleSheet("""
            QPushButton {
                background-color: #059669;
                color: white;
                border: none;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #047857;
            }
        """)
        self.create_btn.clicked.connect(self.create_new_marche)

        self.refresh_btn = QPushButton("🔄 Actualiser")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        self.refresh_btn.clicked.connect(self.load_data)

        buttons_layout.addWidget(self.add_marche_btn)
        buttons_layout.addWidget(self.create_btn)
        buttons_layout.addWidget(self.refresh_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # Tableau des marchés
        self.create_marches_table()
        layout.addWidget(self.marches_table)

        self.setLayout(layout)

    def create_marches_table(self):
        """Créer le tableau des marchés"""
        self.marches_table = QTableWidget()
        self.marches_table.setColumnCount(8)
        self.marches_table.setHorizontalHeaderLabels([
            "Date", "Référence", "Objet", "Montant", "Délai", "Statut", "Actions", "ID"
        ])

        # Masquer la colonne ID
        self.marches_table.setColumnHidden(7, True)

        # Style du tableau
        self.marches_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                background-color: white;
                gridline-color: #E5E7EB;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E5E7EB;
            }
            QTableWidget::item:selected {
                background-color: #EBF4FF;
            }
            QHeaderView::section {
                background-color: #F3F4F6;
                padding: 10px;
                border: none;
                font-weight: bold;
                color: #374151;
            }
        """)

        self.marches_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.marches_table.setAlternatingRowColors(True)
        self.marches_table.setSelectionBehavior(QTableWidget.SelectRows)

    def show_marche_dialog(self):
        """Afficher la boîte de dialogue pour gérer un marché"""
        # Vérifier s'il y a une ligne sélectionnée
        current_row = self.marches_table.currentRow()
        if current_row >= 0:
            marche_id = self.marches_table.item(current_row, 7).text()
            self.show_marche_details(int(marche_id))
        else:
            QMessageBox.information(self, "Information", "Veuillez sélectionner un marché dans le tableau.")

    def create_new_marche(self):
        """Créer un nouveau marché"""
        dialog = MarcheDialog(self.db_manager, parent=self)
        if dialog.exec() == QDialog.Accepted:
            self.load_data()

    def show_marche_details(self, marche_id):
        """Afficher les détails d'un marché"""
        dialog = MarcheDialog(self.db_manager, marche_id=marche_id, parent=self)
        if dialog.exec() == QDialog.Accepted:
            self.load_data()

    def load_data(self):
        """Charger les données des marchés"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT id, date_creation, reference_marche, objet, montant,
                       delai_execution, statut
                FROM marches
                ORDER BY date_creation DESC
            """)
            marches = cursor.fetchall()

            self.marches_table.setRowCount(len(marches))

            for row, marche in enumerate(marches):
                # Date
                self.marches_table.setItem(row, 0, QTableWidgetItem(marche[1] or ""))

                # Référence
                self.marches_table.setItem(row, 1, QTableWidgetItem(marche[2] or ""))

                # Objet
                self.marches_table.setItem(row, 2, QTableWidgetItem(marche[3] or ""))

                # Montant
                montant_text = f"{marche[4]:.2f} DH" if marche[4] else "0.00 DH"
                self.marches_table.setItem(row, 3, QTableWidgetItem(montant_text))

                # Délai
                self.marches_table.setItem(row, 4, QTableWidgetItem(marche[5] or ""))

                # Statut
                statut_item = QTableWidgetItem(marche[6] or "En cours")
                if marche[6] == "Terminé":
                    statut_item.setBackground(Qt.green)
                elif marche[6] == "En cours":
                    statut_item.setBackground(Qt.yellow)
                else:
                    statut_item.setBackground(Qt.lightGray)
                self.marches_table.setItem(row, 5, statut_item)

                # Actions
                actions_widget = self.create_actions_widget(marche[0])
                self.marches_table.setCellWidget(row, 6, actions_widget)

                # ID (masqué)
                self.marches_table.setItem(row, 7, QTableWidgetItem(str(marche[0])))

        except sqlite3.Error as e:
            print(f"Erreur lors du chargement des marchés: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement: {str(e)}")

    def create_actions_widget(self, marche_id):
        """Créer le widget des actions pour chaque ligne"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(2, 2, 2, 2)

        # Bouton Modifier
        edit_btn = QPushButton("✏️")
        edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                padding: 5px;
                border-radius: 3px;
                font-size: 12px;
            }
        """)
        edit_btn.clicked.connect(lambda: self.show_marche_details(marche_id))

        # Bouton Supprimer
        delete_btn = QPushButton("🗑️")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #DC2626;
                color: white;
                border: none;
                padding: 5px;
                border-radius: 3px;
                font-size: 12px;
            }
        """)
        delete_btn.clicked.connect(lambda: self.delete_marche(marche_id))

        layout.addWidget(edit_btn)
        layout.addWidget(delete_btn)

        return widget

    def delete_marche(self, marche_id):
        """Supprimer un marché"""
        reply = QMessageBox.question(self, "Confirmation",
                                   "Êtes-vous sûr de vouloir supprimer ce marché?",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                cursor = self.db_manager.conn.cursor()

                # Supprimer les articles du marché
                cursor.execute("DELETE FROM marche_articles WHERE marche_id = ?", (marche_id,))

                # Supprimer le marché
                cursor.execute("DELETE FROM marches WHERE id = ?", (marche_id,))

                self.db_manager.conn.commit()
                self.load_data()

                QMessageBox.information(self, "Succès", "Marché supprimé!")

            except sqlite3.Error as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression: {str(e)}")


class MarcheDialog(QDialog):
    """Boîte de dialogue pour créer/modifier un marché"""

    def __init__(self, db_manager, marche_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.marche_id = marche_id
        self.setup_ui()

        if marche_id:
            self.load_marche_data()

    def setup_ui(self):
        """Configuration de l'interface"""
        self.setWindowTitle("Gestion du Marché/BC")
        self.setModal(True)
        self.resize(1000, 700)

        layout = QVBoxLayout()

        # Titre
        title = QLabel("📋 Détails du Marché/BC")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2563EB; margin-bottom: 20px;")
        layout.addWidget(title)

        # Onglets
        self.tabs = QTabWidget()

        # Onglet 1: Informations générales
        self.create_general_tab()
        self.tabs.addTab(self.general_tab, "📋 Informations Générales")

        # Onglet 2: Dates et étapes
        self.create_dates_tab()
        self.tabs.addTab(self.dates_tab, "📅 Dates et Étapes")

        # Onglet 3: Articles
        self.create_articles_tab()
        self.tabs.addTab(self.articles_tab, "📦 Articles")

        # Onglet 4: Charges
        self.create_charges_tab()
        self.tabs.addTab(self.charges_tab, "💰 Charges")

        layout.addWidget(self.tabs)

        # Boutons
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("💾 Enregistrer")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #059669;
                color: white;
                border: none;
                padding: 12px 24px;
                font-weight: bold;
                border-radius: 6px;
            }
        """)
        save_btn.clicked.connect(self.save_marche)

        cancel_btn = QPushButton("❌ Annuler")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                padding: 12px 24px;
                font-weight: bold;
                border-radius: 6px;
            }
        """)
        cancel_btn.clicked.connect(self.reject)

        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def create_general_tab(self):
        """Créer l'onglet des informations générales"""
        self.general_tab = QWidget()
        layout = QFormLayout()

        # Date de création
        self.date_creation = QDateEdit()
        self.date_creation.setDate(QDate.currentDate())
        self.date_creation.setCalendarPopup(True)
        layout.addRow("📅 Date:", self.date_creation)

        # Référence marché/BC
        self.reference_input = QLineEdit()
        self.reference_input.setPlaceholderText("Référence Marché/BC")
        layout.addRow("🔖 Référence Marché/BC:", self.reference_input)

        # Objet
        self.objet_input = QTextEdit()
        self.objet_input.setMaximumHeight(80)
        self.objet_input.setPlaceholderText("Description de l'objet du marché")
        layout.addRow("📝 Objet:", self.objet_input)

        # Montant
        self.montant_input = QDoubleSpinBox()
        self.montant_input.setRange(0, 999999999)
        self.montant_input.setDecimals(2)
        self.montant_input.setSuffix(" DH")
        layout.addRow("💰 Montant:", self.montant_input)

        # Délai d'exécution
        self.delai_input = QLineEdit()
        self.delai_input.setPlaceholderText("Ex: 30 jours, 3 mois...")
        layout.addRow("⏱️ Délai d'Exécution:", self.delai_input)

        # Statut
        self.statut_combo = QComboBox()
        self.statut_combo.addItems(["En cours", "Terminé", "Suspendu", "Annulé"])
        layout.addRow("📊 Statut:", self.statut_combo)

        # Notes
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(100)
        self.notes_input.setPlaceholderText("Notes additionnelles...")
        layout.addRow("📋 Notes:", self.notes_input)

        self.general_tab.setLayout(layout)

    def create_dates_tab(self):
        """Créer l'onglet des dates et étapes"""
        self.dates_tab = QWidget()
        layout = QFormLayout()

        # Caution provisoire
        self.caution_provisoire = QDoubleSpinBox()
        self.caution_provisoire.setRange(0, 999999999)
        self.caution_provisoire.setDecimals(2)
        self.caution_provisoire.setSuffix(" DH")
        layout.addRow("💳 Caution Provisoire:", self.caution_provisoire)

        # Date d'approbation
        self.date_approbation = QDateEdit()
        self.date_approbation.setCalendarPopup(True)
        self.date_approbation.setSpecialValueText("Non définie")
        layout.addRow("✅ Date d'Approbation:", self.date_approbation)

        # Date de notification
        self.date_notification = QDateEdit()
        self.date_notification.setCalendarPopup(True)
        self.date_notification.setSpecialValueText("Non définie")
        layout.addRow("📢 Date de Notification/Approbation:", self.date_notification)

        # Montant caution définitif
        self.montant_caution_definitif = QDoubleSpinBox()
        self.montant_caution_definitif.setRange(0, 999999999)
        self.montant_caution_definitif.setDecimals(2)
        self.montant_caution_definitif.setSuffix(" DH")
        layout.addRow("💰 Montant Caution Définitif:", self.montant_caution_definitif)

        # Date d'enregistrement
        from ..style import create_styled_date_edit
        self.date_enregistrement = create_styled_date_edit(
            optional=True, placeholder_text="Non définie"
        )
        layout.addRow("📝 Date d'Enregistrement:", self.date_enregistrement)

        # Date ordre de service
        self.date_ordre_service = create_styled_date_edit(
            optional=True, placeholder_text="Non définie"
        )
        layout.addRow("🔧 Date Ordre de Service:", self.date_ordre_service)

        # Date caution retenu de garantie
        self.date_caution_retenu = create_styled_date_edit(
            optional=True, placeholder_text="Non définie"
        )
        layout.addRow("🛡️ Date de Caution Retenu de Garantie:", self.date_caution_retenu)

        # Date de réception provisoire
        self.date_reception_provisoire = create_styled_date_edit(
            optional=True, placeholder_text="Non définie"
        )
        layout.addRow("📦 Date de Réception Provisoire:", self.date_reception_provisoire)

        self.dates_tab.setLayout(layout)

    def create_articles_tab(self):
        """Créer l'onglet des articles"""
        self.articles_tab = QWidget()
        layout = QVBoxLayout()

        # Boutons pour les articles
        articles_buttons = QHBoxLayout()

        add_article_btn = QPushButton("➕ Ajouter Article")
        add_article_btn.setStyleSheet("""
            QPushButton {
                background-color: #059669;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 4px;
            }
        """)
        add_article_btn.clicked.connect(self.add_article)

        remove_article_btn = QPushButton("➖ Supprimer Article")
        remove_article_btn.setStyleSheet("""
            QPushButton {
                background-color: #DC2626;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 4px;
            }
        """)
        remove_article_btn.clicked.connect(self.remove_article)

        articles_buttons.addWidget(add_article_btn)
        articles_buttons.addWidget(remove_article_btn)
        articles_buttons.addStretch()

        layout.addLayout(articles_buttons)

        # Tableau des articles
        self.articles_table = QTableWidget()
        self.articles_table.setColumnCount(6)
        self.articles_table.setHorizontalHeaderLabels([
            "N°", "Désignation", "Unité", "Qté", "Prix Unitaire HT", "Prix Total HT"
        ])

        self.articles_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                background-color: white;
                gridline-color: #E5E7EB;
            }
            QHeaderView::section {
                background-color: #F3F4F6;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)

        self.articles_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.articles_table)

        # Totaux
        totaux_layout = QHBoxLayout()
        totaux_layout.addStretch()

        self.total_ht_label = QLabel("TOTAL HT: 0.00 DH")
        self.total_ht_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.total_ht_label.setStyleSheet("color: #059669;")

        self.tva_label = QLabel("TVA 20%: 0.00 DH")
        self.tva_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.tva_label.setStyleSheet("color: #DC2626;")

        self.ttc_label = QLabel("TTC: 0.00 DH")
        self.ttc_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.ttc_label.setStyleSheet("color: #2563EB;")

        totaux_layout.addWidget(self.total_ht_label)
        totaux_layout.addWidget(QLabel("  |  "))
        totaux_layout.addWidget(self.tva_label)
        totaux_layout.addWidget(QLabel("  |  "))
        totaux_layout.addWidget(self.ttc_label)

        layout.addLayout(totaux_layout)
        self.articles_tab.setLayout(layout)

    def create_charges_tab(self):
        """Créer l'onglet des charges"""
        self.charges_tab = QWidget()
        layout = QVBoxLayout()

        # Titre
        title_label = QLabel("💰 Gestion des Charges du Marché")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setStyleSheet("color: #2563EB; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # Boutons pour les charges
        charges_buttons = QHBoxLayout()

        add_charge_btn = QPushButton("➕ Ajouter Charge")
        add_charge_btn.setStyleSheet("""
            QPushButton {
                background-color: #059669;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 4px;
            }
        """)
        add_charge_btn.clicked.connect(self.add_charge)

        remove_charge_btn = QPushButton("➖ Supprimer Charge")
        remove_charge_btn.setStyleSheet("""
            QPushButton {
                background-color: #DC2626;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 4px;
            }
        """)
        remove_charge_btn.clicked.connect(self.remove_charge)

        charges_buttons.addWidget(add_charge_btn)
        charges_buttons.addWidget(remove_charge_btn)
        charges_buttons.addStretch()

        layout.addLayout(charges_buttons)

        # Tableau des charges
        self.charges_table = QTableWidget()
        self.charges_table.setColumnCount(6)
        self.charges_table.setHorizontalHeaderLabels([
            "Type", "Description", "Montant", "Date", "Mode Paiement", "Actions"
        ])

        self.charges_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                background-color: white;
                gridline-color: #E5E7EB;
            }
            QHeaderView::section {
                background-color: #F3F4F6;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)

        self.charges_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.charges_table)

        # Total des charges
        totaux_charges_layout = QHBoxLayout()
        totaux_charges_layout.addStretch()

        self.total_charges_label = QLabel("TOTAL CHARGES: 0.00 DH")
        self.total_charges_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.total_charges_label.setStyleSheet("color: #DC2626;")

        totaux_charges_layout.addWidget(self.total_charges_label)
        layout.addLayout(totaux_charges_layout)

        self.charges_tab.setLayout(layout)

    def add_charge(self):
        """Ajouter une charge au tableau"""
        row = self.charges_table.rowCount()
        self.charges_table.insertRow(row)

        # Type de charge (ComboBox)
        type_combo = QComboBox()
        type_combo.addItems([
            "Caisse", "Banque", "Matériel", "Main d'œuvre",
            "Transport", "Assurance", "Autres"
        ])
        type_combo.currentTextChanged.connect(self.calculate_charges_total)
        self.charges_table.setCellWidget(row, 0, type_combo)

        # Description
        self.charges_table.setItem(row, 1, QTableWidgetItem(""))

        # Montant
        montant_spin = QDoubleSpinBox()
        montant_spin.setRange(0, 999999)
        montant_spin.setDecimals(2)
        montant_spin.setSuffix(" DH")
        montant_spin.valueChanged.connect(self.calculate_charges_total)
        self.charges_table.setCellWidget(row, 2, montant_spin)

        # Date
        date_edit = QDateEdit()
        date_edit.setDate(QDate.currentDate())
        date_edit.setCalendarPopup(True)
        self.charges_table.setCellWidget(row, 3, date_edit)

        # Mode de paiement
        mode_combo = QComboBox()
        mode_combo.addItems([
            "Espèces", "Chèque", "Virement", "Carte bancaire"
        ])
        self.charges_table.setCellWidget(row, 4, mode_combo)

        # Actions
        actions_widget = self.create_charge_actions_widget(row)
        self.charges_table.setCellWidget(row, 5, actions_widget)

        self.calculate_charges_total()

    def remove_charge(self):
        """Supprimer la charge sélectionnée"""
        current_row = self.charges_table.currentRow()
        if current_row >= 0:
            self.charges_table.removeRow(current_row)
            self.calculate_charges_total()

    def create_charge_actions_widget(self, row):
        """Créer le widget des actions pour chaque charge"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(2, 2, 2, 2)

        # Bouton Supprimer
        delete_btn = QPushButton("🗑️")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #DC2626;
                color: white;
                border: none;
                padding: 5px;
                border-radius: 3px;
                font-size: 12px;
            }
        """)
        delete_btn.clicked.connect(lambda: self.remove_charge_row(row))

        layout.addWidget(delete_btn)
        return widget

    def remove_charge_row(self, row):
        """Supprimer une ligne de charge spécifique"""
        self.charges_table.removeRow(row)
        self.calculate_charges_total()

    def calculate_charges_total(self):
        """Calculer le total des charges"""
        total_charges = 0

        for row in range(self.charges_table.rowCount()):
            montant_widget = self.charges_table.cellWidget(row, 2)
            if montant_widget:
                total_charges += montant_widget.value()

        # Mettre à jour le label
        self.total_charges_label.setText(f"TOTAL CHARGES: {total_charges:.2f} DH")

    def add_article(self):
        """Ajouter un article au tableau"""
        row = self.articles_table.rowCount()
        self.articles_table.insertRow(row)

        # N°
        self.articles_table.setItem(row, 0, QTableWidgetItem(str(row + 1)))

        # Désignation
        self.articles_table.setItem(row, 1, QTableWidgetItem(""))

        # Unité
        self.articles_table.setItem(row, 2, QTableWidgetItem(""))

        # Quantité
        qte_spin = QSpinBox()
        qte_spin.setRange(1, 999999)
        qte_spin.setValue(1)
        qte_spin.valueChanged.connect(self.calculate_totals)
        self.articles_table.setCellWidget(row, 3, qte_spin)

        # Prix unitaire
        prix_spin = QDoubleSpinBox()
        prix_spin.setRange(0, 999999)
        prix_spin.setDecimals(2)
        prix_spin.setSuffix(" DH")
        prix_spin.valueChanged.connect(self.calculate_totals)
        self.articles_table.setCellWidget(row, 4, prix_spin)

        # Prix total (calculé automatiquement)
        self.articles_table.setItem(row, 5, QTableWidgetItem("0.00 DH"))

        self.calculate_totals()

    def remove_article(self):
        """Supprimer l'article sélectionné"""
        current_row = self.articles_table.currentRow()
        if current_row >= 0:
            self.articles_table.removeRow(current_row)
            self.renumber_articles()
            self.calculate_totals()

    def renumber_articles(self):
        """Renuméroter les articles"""
        for row in range(self.articles_table.rowCount()):
            self.articles_table.setItem(row, 0, QTableWidgetItem(str(row + 1)))

    def calculate_totals(self):
        """Calculer les totaux"""
        total_ht = 0

        for row in range(self.articles_table.rowCount()):
            qte_widget = self.articles_table.cellWidget(row, 3)
            prix_widget = self.articles_table.cellWidget(row, 4)

            if qte_widget and prix_widget:
                qte = qte_widget.value()
                prix = prix_widget.value()
                total_ligne = qte * prix

                # Mettre à jour le prix total de la ligne
                self.articles_table.setItem(row, 5, QTableWidgetItem(f"{total_ligne:.2f} DH"))

                total_ht += total_ligne

        # Calculer TVA et TTC
        tva = total_ht * 0.20
        ttc = total_ht + tva

        # Mettre à jour les labels
        self.total_ht_label.setText(f"TOTAL HT: {total_ht:.2f} DH")
        self.tva_label.setText(f"TVA 20%: {tva:.2f} DH")
        self.ttc_label.setText(f"TTC: {ttc:.2f} DH")

        # Mettre à jour le montant principal
        self.montant_input.setValue(ttc)

    def load_marche_data(self):
        """Charger les données d'un marché existant"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT * FROM marches WHERE id = ?", (self.marche_id,))
            marche = cursor.fetchone()

            if marche:
                # Charger les informations générales
                if marche['date_creation']:
                    self.date_creation.setDate(QDate.fromString(marche['date_creation'], "yyyy-MM-dd"))

                self.reference_input.setText(marche['reference_marche'] or "")
                self.objet_input.setPlainText(marche['objet'] or "")
                self.montant_input.setValue(marche['montant'] or 0)
                self.delai_input.setText(marche['delai_execution'] or "")
                self.statut_combo.setCurrentText(marche['statut'] or "En cours")
                self.notes_input.setPlainText(marche['notes'] or "")

                # Charger les dates
                self.caution_provisoire.setValue(marche['caution_provisoire'] or 0)
                self.montant_caution_definitif.setValue(marche['montant_caution_definitif'] or 0)

                # Charger les dates si elles existent
                for date_field, date_value in [
                    (self.date_approbation, marche['date_approbation']),
                    (self.date_notification, marche['date_notification']),
                    (self.date_enregistrement, marche['date_enregistrement']),
                    (self.date_ordre_service, marche['date_ordre_service']),
                    (self.date_caution_retenu, marche['date_caution_retenu']),
                    (self.date_reception_provisoire, marche['date_reception_provisoire'])
                ]:
                    if date_value:
                        date_field.setDate(QDate.fromString(date_value, "yyyy-MM-dd"))

                # Charger les articles
                self.load_articles()

                # Charger les charges
                self.load_charges()

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement: {str(e)}")

    def load_articles(self):
        """Charger les articles du marché"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT designation, unite, quantite, prix_unitaire_ht, prix_total_ht
                FROM marche_articles WHERE marche_id = ?
                ORDER BY id
            """, (self.marche_id,))
            articles = cursor.fetchall()

            self.articles_table.setRowCount(0)

            for article in articles:
                row = self.articles_table.rowCount()
                self.articles_table.insertRow(row)

                # N°
                self.articles_table.setItem(row, 0, QTableWidgetItem(str(row + 1)))

                # Désignation
                self.articles_table.setItem(row, 1, QTableWidgetItem(article['designation'] or ""))

                # Unité
                self.articles_table.setItem(row, 2, QTableWidgetItem(article['unite'] or ""))

                # Quantité
                qte_spin = QSpinBox()
                qte_spin.setRange(1, 999999)
                qte_spin.setValue(article['quantite'] or 1)
                qte_spin.valueChanged.connect(self.calculate_totals)
                self.articles_table.setCellWidget(row, 3, qte_spin)

                # Prix unitaire
                prix_spin = QDoubleSpinBox()
                prix_spin.setRange(0, 999999)
                prix_spin.setDecimals(2)
                prix_spin.setSuffix(" DH")
                prix_spin.setValue(article['prix_unitaire_ht'] or 0)
                prix_spin.valueChanged.connect(self.calculate_totals)
                self.articles_table.setCellWidget(row, 4, prix_spin)

                # Prix total
                self.articles_table.setItem(row, 5, QTableWidgetItem(f"{article['prix_total_ht']:.2f} DH"))

            self.calculate_totals()

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des articles: {str(e)}")

    def load_charges(self):
        """Charger les charges du marché"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT type_charge, description, montant, date_charge, mode_paiement
                FROM marche_charges WHERE marche_id = ?
                ORDER BY id
            """, (self.marche_id,))
            charges = cursor.fetchall()

            self.charges_table.setRowCount(0)

            for charge in charges:
                row = self.charges_table.rowCount()
                self.charges_table.insertRow(row)

                # Type de charge
                type_combo = QComboBox()
                type_combo.addItems([
                    "Caisse", "Banque", "Matériel", "Main d'œuvre",
                    "Transport", "Assurance", "Autres"
                ])
                type_combo.setCurrentText(charge['type_charge'] or "Caisse")
                type_combo.currentTextChanged.connect(self.calculate_charges_total)
                self.charges_table.setCellWidget(row, 0, type_combo)

                # Description
                self.charges_table.setItem(row, 1, QTableWidgetItem(charge['description'] or ""))

                # Montant
                montant_spin = QDoubleSpinBox()
                montant_spin.setRange(0, 999999)
                montant_spin.setDecimals(2)
                montant_spin.setSuffix(" DH")
                montant_spin.setValue(charge['montant'] or 0)
                montant_spin.valueChanged.connect(self.calculate_charges_total)
                self.charges_table.setCellWidget(row, 2, montant_spin)

                # Date
                date_edit = QDateEdit()
                if charge['date_charge']:
                    date_edit.setDate(QDate.fromString(charge['date_charge'], "yyyy-MM-dd"))
                else:
                    date_edit.setDate(QDate.currentDate())
                date_edit.setCalendarPopup(True)
                self.charges_table.setCellWidget(row, 3, date_edit)

                # Mode de paiement
                mode_combo = QComboBox()
                mode_combo.addItems([
                    "Espèces", "Chèque", "Virement", "Carte bancaire"
                ])
                mode_combo.setCurrentText(charge['mode_paiement'] or "Espèces")
                self.charges_table.setCellWidget(row, 4, mode_combo)

                # Actions
                actions_widget = self.create_charge_actions_widget(row)
                self.charges_table.setCellWidget(row, 5, actions_widget)

            self.calculate_charges_total()

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des charges: {str(e)}")

    def save_marche(self):
        """Enregistrer le marché"""
        try:
            cursor = self.db_manager.conn.cursor()

            # Préparer les données
            data = {
                'date_creation': self.date_creation.date().toString("yyyy-MM-dd"),
                'reference_marche': self.reference_input.text().strip(),
                'objet': self.objet_input.toPlainText().strip(),
                'montant': self.montant_input.value(),
                'delai_execution': self.delai_input.text().strip(),
                'caution_provisoire': self.caution_provisoire.value(),
                'montant_caution_definitif': self.montant_caution_definitif.value(),
                'statut': self.statut_combo.currentText(),
                'notes': self.notes_input.toPlainText().strip()
            }

            # Ajouter les dates si elles sont définies
            for field_name, date_widget in [
                ('date_approbation', self.date_approbation),
                ('date_notification', self.date_notification),
                ('date_enregistrement', self.date_enregistrement),
                ('date_ordre_service', self.date_ordre_service),
                ('date_caution_retenu', self.date_caution_retenu),
                ('date_reception_provisoire', self.date_reception_provisoire)
            ]:
                if date_widget.date().isValid() and date_widget.date() != QDate():
                    data[field_name] = date_widget.date().toString("yyyy-MM-dd")
                else:
                    data[field_name] = None

            if self.marche_id:
                # Mise à jour
                cursor.execute("""
                    UPDATE marches SET
                    date_creation=?, reference_marche=?, objet=?, montant=?, delai_execution=?,
                    caution_provisoire=?, date_approbation=?, date_notification=?,
                    montant_caution_definitif=?, date_enregistrement=?, date_ordre_service=?,
                    date_caution_retenu=?, date_reception_provisoire=?, statut=?, notes=?
                    WHERE id=?
                """, (
                    data['date_creation'], data['reference_marche'], data['objet'],
                    data['montant'], data['delai_execution'], data['caution_provisoire'],
                    data['date_approbation'], data['date_notification'],
                    data['montant_caution_definitif'], data['date_enregistrement'],
                    data['date_ordre_service'], data['date_caution_retenu'],
                    data['date_reception_provisoire'], data['statut'], data['notes'],
                    self.marche_id
                ))
                marche_id = self.marche_id
            else:
                # Création
                cursor.execute("""
                    INSERT INTO marches (
                        date_creation, reference_marche, objet, montant, delai_execution,
                        caution_provisoire, date_approbation, date_notification,
                        montant_caution_definitif, date_enregistrement, date_ordre_service,
                        date_caution_retenu, date_reception_provisoire, statut, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    data['date_creation'], data['reference_marche'], data['objet'],
                    data['montant'], data['delai_execution'], data['caution_provisoire'],
                    data['date_approbation'], data['date_notification'],
                    data['montant_caution_definitif'], data['date_enregistrement'],
                    data['date_ordre_service'], data['date_caution_retenu'],
                    data['date_reception_provisoire'], data['statut'], data['notes']
                ))
                marche_id = cursor.lastrowid

            # Sauvegarder les articles
            cursor.execute("DELETE FROM marche_articles WHERE marche_id = ?", (marche_id,))

            for row in range(self.articles_table.rowCount()):
                designation = self.articles_table.item(row, 1).text() if self.articles_table.item(row, 1) else ""
                unite = self.articles_table.item(row, 2).text() if self.articles_table.item(row, 2) else ""

                qte_widget = self.articles_table.cellWidget(row, 3)
                prix_widget = self.articles_table.cellWidget(row, 4)

                if qte_widget and prix_widget and designation:
                    quantite = qte_widget.value()
                    prix_unitaire = prix_widget.value()
                    prix_total = quantite * prix_unitaire

                    cursor.execute("""
                        INSERT INTO marche_articles (marche_id, designation, unite, quantite, prix_unitaire_ht, prix_total_ht)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (marche_id, designation, unite, quantite, prix_unitaire, prix_total))

            # Sauvegarder les charges
            cursor.execute("DELETE FROM marche_charges WHERE marche_id = ?", (marche_id,))

            for row in range(self.charges_table.rowCount()):
                type_widget = self.charges_table.cellWidget(row, 0)
                description_item = self.charges_table.item(row, 1)
                montant_widget = self.charges_table.cellWidget(row, 2)
                date_widget = self.charges_table.cellWidget(row, 3)
                mode_widget = self.charges_table.cellWidget(row, 4)

                if type_widget and montant_widget and description_item:
                    type_charge = type_widget.currentText()
                    description = description_item.text()
                    montant = montant_widget.value()
                    date_charge = date_widget.date().toString("yyyy-MM-dd") if date_widget else None
                    mode_paiement = mode_widget.currentText() if mode_widget else "Espèces"

                    if description.strip():  # Seulement si la description n'est pas vide
                        cursor.execute("""
                            INSERT INTO marche_charges (marche_id, type_charge, description, montant, date_charge, mode_paiement)
                            VALUES (?, ?, ?, ?, ?, ?)
                        """, (marche_id, type_charge, description, montant, date_charge, mode_paiement))

            self.db_manager.conn.commit()
            QMessageBox.information(self, "Succès", "Marché enregistré avec succès!")
            self.accept()

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur: {str(e)}")
