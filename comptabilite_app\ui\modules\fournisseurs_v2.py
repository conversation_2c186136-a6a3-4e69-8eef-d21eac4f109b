from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QHeaderView, QMessageBox, QDialog)
from PySide6.QtCore import Qt, QRegularExpression, Signal
from PySide6.QtGui import QRegularExpressionValidator
import sqlite3

# Importer les composants de base
from ..components.base_module import BaseModule
from ..components.base_list_view import BaseListView
from ..components.base_form_dialog import BaseFormDialog

# Importer les styles et icônes
from ..theme import COLORS, BORDER_RADIUS, SPACING
from ..icons.icons import SUPPLIERS_ICON

class FournisseurFormDialog(BaseFormDialog):
    """Boîte de dialogue pour ajouter ou modifier un fournisseur"""

    def __init__(self, db_manager, fournisseur_id=None, parent=None):
        self.fournisseur_id = fournisseur_id
        is_edit_mode = fournisseur_id is not None
        title = "Modifier un fournisseur" if is_edit_mode else "Ajouter un fournisseur"

        super().__init__(db_manager, title, is_edit_mode, parent)

        # Initialiser les champs du formulaire
        self.setup_form()

        # Charger les données si on est en mode édition
        if is_edit_mode:
            self.load_data(fournisseur_id)
        else:
            # Générer un nouveau code fournisseur
            self.code_input.setText(self.db_manager.generer_code_fournisseur())

    def setup_form(self):
        """Configure les champs du formulaire"""
        # Code fournisseur
        self.code_input = QLineEdit()
        self.code_input.setReadOnly(True)
        self.code_input.setStyleSheet("background-color: #F3F4F6; color: #6B7280;")
        self.create_form_field("Code fournisseur", self.code_input, row=0, col=0)

        # Nom / Raison sociale
        self.nom_input = QLineEdit()
        self.nom_input.setPlaceholderText("Obligatoire")
        self.create_form_field("Nom / Raison sociale", self.nom_input, required=True, row=1, col=0)

        # ICE
        self.ice_input = QLineEdit()
        self.ice_input.setPlaceholderText("Identifiant Commun de l'Entreprise")
        self.create_form_field("ICE", self.ice_input, row=2, col=0)

        # IF
        self.if_input = QLineEdit()
        self.if_input.setPlaceholderText("Identifiant Fiscal")
        self.create_form_field("IF", self.if_input, row=3, col=0)

        # Adresse
        self.adresse_input = QLineEdit()
        self.create_form_field("Adresse", self.adresse_input, row=0, col=1)

        # Téléphone
        self.telephone_input = QLineEdit()
        telephone_regex = QRegularExpression("^[0-9+\\-\\s]{8,15}$")
        self.telephone_input.setValidator(QRegularExpressionValidator(telephone_regex))
        self.create_form_field("Téléphone", self.telephone_input, row=1, col=1)

        # E-mail
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("<EMAIL>")
        self.create_form_field("E-mail", self.email_input, row=2, col=1)

        # Personne à contacter
        self.contact_input = QLineEdit()
        self.create_form_field("Personne à contacter", self.contact_input, row=3, col=1)

    def validate_form(self):
        """Valide les données du formulaire"""
        nom = self.nom_input.text().strip()
        if not nom:
            QMessageBox.warning(self, "Erreur", "Le nom du fournisseur est obligatoire.")
            return False

        return True

    def save_data(self):
        """Enregistre les données du formulaire"""
        nom = self.nom_input.text().strip()
        code = self.code_input.text()

        cursor = self.db_manager.conn.cursor()

        try:
            if self.is_edit_mode:
                # Modification d'un fournisseur existant
                cursor.execute(
                    """UPDATE fournisseurs SET
                       code = ?, nom = ?, ice = ?, if_fiscal = ?, adresse = ?,
                       telephone = ?, email = ?, contact = ?
                       WHERE id = ?""",
                    (code, nom, self.ice_input.text(), self.if_input.text(),
                     self.adresse_input.text(), self.telephone_input.text(),
                     self.email_input.text(), self.contact_input.text(), self.fournisseur_id)
                )
                message = f"Fournisseur {nom} modifié avec succès."
            else:
                # Ajout d'un nouveau fournisseur
                cursor.execute(
                    """INSERT INTO fournisseurs
                       (code, nom, ice, if_fiscal, adresse, telephone, email, contact)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                    (code, nom, self.ice_input.text(), self.if_input.text(),
                     self.adresse_input.text(), self.telephone_input.text(),
                     self.email_input.text(), self.contact_input.text())
                )
                message = f"Fournisseur {nom} ajouté avec succès.\nCode fournisseur: {code}"

            self.db_manager.conn.commit()
            QMessageBox.information(self, "Succès", message)

        except sqlite3.IntegrityError:
            QMessageBox.warning(self, "Erreur", "Ce code fournisseur existe déjà. Veuillez utiliser un autre code.")
            raise

    def load_data(self, fournisseur_id):
        """Charge les données d'un fournisseur existant"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT * FROM fournisseurs WHERE id = ?", (fournisseur_id,))
        fournisseur = cursor.fetchone()

        if fournisseur:
            self.code_input.setText(fournisseur['code'] or "")
            self.nom_input.setText(fournisseur['nom'])
            self.ice_input.setText(fournisseur['ice'] or "")
            self.if_input.setText(fournisseur['if_fiscal'] or "")
            self.adresse_input.setText(fournisseur['adresse'] or "")
            self.telephone_input.setText(fournisseur['telephone'] or "")
            self.email_input.setText(fournisseur['email'] or "")
            self.contact_input.setText(fournisseur['contact'] or "")

class FournisseursListView(BaseListView):
    """Vue en liste des fournisseurs"""

    def __init__(self, db_manager, parent=None):
        super().__init__(
            db_manager=db_manager,
            title="Liste des Fournisseurs",
            description="Consultez et gérez vos fournisseurs",
            icon=SUPPLIERS_ICON,
            add_button_text="Ajouter un fournisseur"
        )

        # Configurer le tableau
        self.setup_table()

        # Charger les données
        self.load_fournisseurs()

    def setup_table(self):
        """Configure le tableau des fournisseurs"""
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "Code", "Nom", "ICE", "IF", "Téléphone", "Adresse", "Actions"
        ])

        # Configuration des colonnes
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Code
        self.items_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)  # Nom
        self.items_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)  # ICE
        self.items_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # IF
        self.items_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Téléphone
        self.items_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Stretch)  # Adresse
        self.items_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Actions

    def load_fournisseurs(self):
        """Charge les fournisseurs depuis la base de données"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT * FROM fournisseurs ORDER BY code")
        fournisseurs = cursor.fetchall()

        self.items_table.setRowCount(0)

        for row_num, fournisseur in enumerate(fournisseurs):
            self.items_table.insertRow(row_num)

            # Ajouter les données du fournisseur
            self.items_table.setItem(row_num, 0, QTableWidgetItem(fournisseur['code'] or ""))
            self.items_table.setItem(row_num, 1, QTableWidgetItem(fournisseur['nom']))
            self.items_table.setItem(row_num, 2, QTableWidgetItem(fournisseur['ice'] or ""))
            self.items_table.setItem(row_num, 3, QTableWidgetItem(fournisseur['if_fiscal'] or ""))
            self.items_table.setItem(row_num, 4, QTableWidgetItem(fournisseur['telephone'] or ""))
            self.items_table.setItem(row_num, 5, QTableWidgetItem(fournisseur['adresse'] or ""))

            # Créer les boutons d'action
            actions_widget = self.create_action_buttons(row_num, fournisseur['id'], fournisseur['nom'])
            self.items_table.setCellWidget(row_num, 6, actions_widget)

            # Stocker l'ID du fournisseur dans la première colonne (invisible)
            self.items_table.item(row_num, 0).setData(Qt.UserRole, fournisseur['id'])

    def on_add_clicked(self):
        """Affiche la boîte de dialogue pour ajouter un fournisseur"""
        dialog = FournisseurFormDialog(self.db_manager, parent=self)
        dialog.form_submitted.connect(self.on_fournisseur_saved)
        dialog.exec()

    def on_edit_clicked(self, fournisseur_id):
        """Affiche la boîte de dialogue pour modifier un fournisseur"""
        dialog = FournisseurFormDialog(self.db_manager, fournisseur_id=fournisseur_id, parent=self)
        dialog.form_submitted.connect(self.on_fournisseur_saved)
        dialog.exec()

    def on_delete_clicked(self, fournisseur_id, fournisseur_name):
        """Supprime un fournisseur"""
        confirm = QMessageBox.question(
            self, "Confirmation",
            f"Êtes-vous sûr de vouloir supprimer le fournisseur {fournisseur_name} ?\nCette action est irréversible.",
            QMessageBox.Yes | QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                cursor = self.db_manager.conn.cursor()
                cursor.execute("DELETE FROM fournisseurs WHERE id = ?", (fournisseur_id,))
                self.db_manager.conn.commit()

                # إعادة ترقيم جميع الموردين تلقائياً
                self.db_manager.renumber_all_fournisseurs()

                self.load_fournisseurs()
                self.item_changed.emit()

                QMessageBox.information(self, "Succès", f"Fournisseur {fournisseur_name} supprimé avec succès.")
            except sqlite3.Error as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression du fournisseur: {str(e)}")

    def on_fournisseur_saved(self):
        """Appelé lorsqu'un fournisseur est ajouté ou modifié"""
        self.load_fournisseurs()
        self.item_changed.emit()

class FournisseursModule(QWidget):
    """Module de gestion des fournisseurs avec interface simplifiée"""

    def __init__(self, db_manager, signals=None):
        super().__init__()
        self.db_manager = db_manager
        self.signals = signals

        # Configurer l'interface
        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Créer la vue en liste
        self.fournisseurs_list_view = FournisseursListView(self.db_manager, self)
        self.fournisseurs_list_view.item_changed.connect(self.on_fournisseur_changed)

        # Ajouter la vue en liste au layout
        layout.addWidget(self.fournisseurs_list_view)

    def on_fournisseur_changed(self):
        """Appelé lorsqu'un fournisseur est ajouté, modifié ou supprimé"""
        # Émettre le signal pour informer les autres modules
        if self.signals:
            self.signals.fournisseurs_changed.emit()

    def show_add_dialog(self):
        """Affiche la boîte de dialogue pour ajouter un fournisseur"""
        self.fournisseurs_list_view.on_add_clicked()

    def show_edit_dialog(self, fournisseur_id):
        """Affiche la boîte de dialogue pour modifier un fournisseur"""
        self.fournisseurs_list_view.on_edit_clicked(fournisseur_id)
