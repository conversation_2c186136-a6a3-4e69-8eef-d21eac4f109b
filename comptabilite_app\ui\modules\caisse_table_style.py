"""
Module de gestion de la caisse - Style Table comme dans l'image
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QTableWidget, QTableWidgetItem, QPushButton,
                               QHeaderView, QFrame, QLineEdit)
from PySide6.QtCore import Qt

import sqlite3

class CaisseTableStyleModule(QWidget):
    """Module de caisse avec style table comme dans l'image"""

    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        # ضبط اتجاه النص لدعم العربية والفرنسية
        self.setLayoutDirection(Qt.LeftToRight)

        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # أزرار في الأعلى
        buttons_layout = QHBoxLayout()

        caisse_btn = QPushButton("Caisse")
        caisse_btn.setStyleSheet("""
            QPushButton {
                background-color: #5B9BD5;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
                font-family: Arial, Tahoma, sans-serif;
            }
            QPushButton:hover {
                background-color: #4A90C2;
            }
        """)
        caisse_btn.clicked.connect(self.refresh_data)

        ajouter_btn = QPushButton("Ajouter")
        ajouter_btn.setStyleSheet("""
            QPushButton {
                background-color: #5B9BD5;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
                font-family: Arial, Tahoma, sans-serif;
            }
            QPushButton:hover {
                background-color: #4A90C2;
            }
        """)
        ajouter_btn.clicked.connect(self.show_add_dialog)

        buttons_layout.addWidget(caisse_btn)
        buttons_layout.addWidget(ajouter_btn)
        buttons_layout.addStretch()

        main_layout.addLayout(buttons_layout)

        # جدول الإيرادات
        entrees_section = self.create_table_section("Entrées", True)
        main_layout.addWidget(entrees_section)

        # جدول المصروفات
        sorties_section = self.create_table_section("Sortie", False)
        main_layout.addWidget(sorties_section)

        # قسم الوضعية والرصيد
        bottom_layout = QHBoxLayout()

        # وضعية الصندوق
        situation_frame = self.create_situation_section()
        bottom_layout.addWidget(situation_frame)

        # الرصيد
        solde_frame = self.create_solde_section()
        bottom_layout.addWidget(solde_frame)

        main_layout.addLayout(bottom_layout)

        self.setLayout(main_layout)

    def create_table_section(self, title, is_entrees):
        """إنشاء قسم الجدول"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                border: 1px solid #000000;
                background-color: white;
                margin: 5px;
            }
        """)

        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # إنشاء الجدول
        table = QTableWidget()
        table.setRowCount(4)  # 3 صفوف للبيانات + 1 للمجموع
        table.setColumnCount(5)

        # تحديد العناوين
        headers = ["Date", "Nature (نوع / طبيعة)", "Objet", "Référence (facture / bon ...)", "Montant"]
        table.setHorizontalHeaderLabels(headers)

        # تنسيق الجدول
        table.setStyleSheet("""
            QTableWidget {
                border: none;
                gridline-color: #000000;
                font-family: Arial, Tahoma, sans-serif;
                font-size: 12px;
                color: #000000;
                background-color: white;
            }
            QTableWidget::item {
                border: 1px solid #000000;
                padding: 5px;
                color: #000000;
            }
            QHeaderView::section {
                background-color: white;
                border: 1px solid #000000;
                padding: 5px;
                font-weight: bold;
                color: #000000;
                font-family: Arial, Tahoma, sans-serif;
            }
        """)

        # إضافة صف المجموع
        table.setItem(3, 1, QTableWidgetItem("total"))

        # إضافة حقل الإدخال في آخر عمود
        if is_entrees:
            # حقل إدخال للإيرادات
            self.entrees_input = QLineEdit()
            self.entrees_input.setPlaceholderText("💰 أدخل المبلغ")
            self.entrees_input.setStyleSheet("""
                QLineEdit {
                    border: 2px solid red;
                    padding: 5px;
                    font-size: 12px;
                    color: #000000;
                    background-color: white;
                    font-family: Arial, Tahoma, sans-serif;
                }
            """)
            # ربط Enter للإضافة
            self.entrees_input.returnPressed.connect(self.add_entree)
            table.setCellWidget(3, 4, self.entrees_input)
        else:
            # حقل إدخال للمصروفات
            self.sorties_input = QLineEdit()
            self.sorties_input.setPlaceholderText("💸 أدخل المبلغ")
            self.sorties_input.setStyleSheet("""
                QLineEdit {
                    border: 2px solid red;
                    padding: 5px;
                    font-size: 12px;
                    color: #000000;
                    background-color: white;
                    font-family: Arial, Tahoma, sans-serif;
                }
            """)
            # ربط Enter للإضافة
            self.sorties_input.returnPressed.connect(self.add_sortie)
            table.setCellWidget(3, 4, self.sorties_input)

        # إضافة النص التوضيحي
        note_text = "عند علاقة بكل فقرة مع رقم المرجع (فاتورة / بون الإدخال فقط)"
        table.setItem(2, 3, QTableWidgetItem(note_text))

        # تحديد عرض الأعمدة
        table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        table.setMaximumHeight(150)

        layout.addWidget(table)
        frame.setLayout(layout)

        return frame

    def create_situation_section(self):
        """إنشاء قسم وضعية الصندوق"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                border: 1px solid #000000;
                background-color: white;
                margin: 5px;
            }
        """)

        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)

        # العنوان
        title = QLabel("Situation de caisse :")
        title.setStyleSheet("""
            font-weight: bold;
            font-size: 12px;
            color: #000000;
            font-family: Arial, Tahoma, sans-serif;
        """)
        layout.addWidget(title)

        # جدول الوضعية
        self.situation_table = QTableWidget()
        self.situation_table.setRowCount(3)
        self.situation_table.setColumnCount(2)
        self.situation_table.setHorizontalHeaderLabels(["", "montant"])

        # البيانات
        self.situation_table.setItem(0, 0, QTableWidgetItem("Entrées"))
        self.situation_table.setItem(0, 1, QTableWidgetItem("E"))
        self.situation_table.setItem(1, 0, QTableWidgetItem("Sortie"))
        self.situation_table.setItem(1, 1, QTableWidgetItem("S"))
        self.situation_table.setItem(2, 0, QTableWidgetItem("Solde"))
        self.situation_table.setItem(2, 1, QTableWidgetItem("= E-S"))

        self.situation_table.setStyleSheet("""
            QTableWidget {
                border: none;
                gridline-color: #000000;
                font-size: 12px;
                color: #000000;
                background-color: white;
                font-family: Arial, Tahoma, sans-serif;
            }
            QTableWidget::item {
                border: 1px solid #000000;
                padding: 5px;
                color: #000000;
            }
            QHeaderView::section {
                background-color: white;
                border: 1px solid #000000;
                padding: 5px;
                font-weight: bold;
                color: #000000;
                font-family: Arial, Tahoma, sans-serif;
            }
        """)

        self.situation_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.situation_table.setMaximumHeight(120)
        self.situation_table.setMaximumWidth(200)

        layout.addWidget(self.situation_table)
        frame.setLayout(layout)

        return frame

    def create_solde_section(self):
        """إنشاء قسم الرصيد"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #5B9BD5;
                border: 1px solid #000000;
                margin: 5px;
            }
        """)

        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)

        label = QLabel("Le solde reste dans la caisse :")
        label.setStyleSheet("""
            color: white;
            font-weight: bold;
            font-size: 14px;
            text-align: center;
            font-family: Arial, Tahoma, sans-serif;
        """)
        label.setAlignment(Qt.AlignCenter)

        layout.addWidget(label)
        frame.setLayout(layout)
        frame.setMaximumWidth(300)
        frame.setMinimumHeight(80)

        return frame

    def add_entree(self):
        """إضافة إيراد"""
        try:
            montant_str = self.entrees_input.text().strip().replace(',', '.')

            if not montant_str:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "خطأ", "يرجى إدخال المبلغ!")
                return

            montant = float(montant_str)
            if montant <= 0:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "خطأ", "المبلغ يجب أن يكون أكبر من صفر!")
                return

            # إدخال في قاعدة البيانات
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                INSERT INTO entrees_caisse (date, nature, objet, reference, montant)
                VALUES (date('now'), 'إيراد', 'إيراد نقدي', '', ?)
            """, (montant,))

            self.db_manager.conn.commit()

            # مسح الحقل
            self.entrees_input.clear()

            # تحديث البيانات
            self.load_data()

            from PySide6.QtWidgets import QMessageBox
            QMessageBox.information(self, "نجح", f"تم إضافة إيراد {montant:.2f} درهم!")

        except ValueError:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "خطأ", "المبلغ غير صحيح!")
        except sqlite3.Error as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"خطأ في قاعدة البيانات: {str(e)}")

    def add_sortie(self):
        """إضافة مصروف"""
        try:
            montant_str = self.sorties_input.text().strip().replace(',', '.')

            if not montant_str:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "خطأ", "يرجى إدخال المبلغ!")
                return

            montant = float(montant_str)
            if montant <= 0:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "خطأ", "المبلغ يجب أن يكون أكبر من صفر!")
                return

            # إدخال في قاعدة البيانات
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                INSERT INTO sorties_caisse (date, nature, objet, reference, montant)
                VALUES (date('now'), 'مصروف', 'مصروف نقدي', '', ?)
            """, (montant,))

            self.db_manager.conn.commit()

            # مسح الحقل
            self.sorties_input.clear()

            # تحديث البيانات
            self.load_data()

            from PySide6.QtWidgets import QMessageBox
            QMessageBox.information(self, "نجح", f"تم إضافة مصروف {montant:.2f} درهم!")

        except ValueError:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "خطأ", "المبلغ غير صحيح!")
        except sqlite3.Error as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"خطأ في قاعدة البيانات: {str(e)}")

    def load_data(self):
        """تحميل البيانات"""
        # تحديث قيم الوضعية
        try:
            cursor = self.db_manager.conn.cursor()

            # مجموع الإيرادات
            cursor.execute("SELECT COALESCE(SUM(montant), 0) FROM entrees_caisse")
            total_entrees = cursor.fetchone()[0]

            # مجموع المصروفات
            cursor.execute("SELECT COALESCE(SUM(montant), 0) FROM sorties_caisse")
            total_sorties = cursor.fetchone()[0]

            # تحديث جدول الوضعية
            self.situation_table.setItem(0, 1, QTableWidgetItem(f"{total_entrees:.2f}"))
            self.situation_table.setItem(1, 1, QTableWidgetItem(f"{total_sorties:.2f}"))
            self.situation_table.setItem(2, 1, QTableWidgetItem(f"{total_entrees - total_sorties:.2f}"))

        except sqlite3.Error as e:
            print(f"خطأ في تحميل البيانات: {e}")

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.information(self, "تحديث", "تم تحديث البيانات!")

    def show_add_dialog(self):
        """عرض نافذة إضافة عملية"""
        from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QComboBox, QMessageBox

        dialog = QDialog(self)
        dialog.setWindowTitle("إضافة عملية جديدة")
        dialog.setModal(True)
        dialog.resize(400, 300)

        layout = QVBoxLayout()

        # نوع العملية
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("نوع العملية:"))
        type_combo = QComboBox()
        type_combo.addItems(["إيراد", "مصروف"])
        type_layout.addWidget(type_combo)
        layout.addLayout(type_layout)

        # المبلغ
        amount_layout = QHBoxLayout()
        amount_layout.addWidget(QLabel("المبلغ:"))
        amount_input = QLineEdit()
        amount_input.setPlaceholderText("أدخل المبلغ بالدرهم")
        amount_layout.addWidget(amount_input)
        layout.addLayout(amount_layout)

        # الوصف
        desc_layout = QHBoxLayout()
        desc_layout.addWidget(QLabel("الوصف:"))
        desc_input = QLineEdit()
        desc_input.setPlaceholderText("وصف العملية")
        desc_layout.addWidget(desc_input)
        layout.addLayout(desc_layout)

        # المرجع
        ref_layout = QHBoxLayout()
        ref_layout.addWidget(QLabel("المرجع:"))
        ref_input = QLineEdit()
        ref_input.setPlaceholderText("رقم الفاتورة أو المرجع")
        ref_layout.addWidget(ref_input)
        layout.addLayout(ref_layout)

        # أزرار
        buttons_layout = QHBoxLayout()
        save_btn = QPushButton("حفظ")
        cancel_btn = QPushButton("إلغاء")

        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #059669;
                color: white;
                border: none;
                padding: 10px 20px;
                font-weight: bold;
                border-radius: 5px;
            }
        """)

        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #DC2626;
                color: white;
                border: none;
                padding: 10px 20px;
                font-weight: bold;
                border-radius: 5px;
            }
        """)

        def save_operation():
            try:
                montant_str = amount_input.text().strip().replace(',', '.')
                description = desc_input.text().strip()
                reference = ref_input.text().strip()
                operation_type = type_combo.currentText()

                if not montant_str:
                    QMessageBox.warning(dialog, "خطأ", "يرجى إدخال المبلغ!")
                    return

                montant = float(montant_str)
                if montant <= 0:
                    QMessageBox.warning(dialog, "خطأ", "المبلغ يجب أن يكون أكبر من صفر!")
                    return

                if not description:
                    description = operation_type

                # إدخال في قاعدة البيانات
                cursor = self.db_manager.conn.cursor()

                if operation_type == "إيراد":
                    cursor.execute("""
                        INSERT INTO entrees_caisse (date, nature, objet, reference, montant)
                        VALUES (date('now'), ?, ?, ?, ?)
                    """, (operation_type, description, reference, montant))
                else:
                    cursor.execute("""
                        INSERT INTO sorties_caisse (date, nature, objet, reference, montant)
                        VALUES (date('now'), ?, ?, ?, ?)
                    """, (operation_type, description, reference, montant))

                self.db_manager.conn.commit()
                self.load_data()

                QMessageBox.information(dialog, "نجح", f"تم إضافة {operation_type} {montant:.2f} درهم!")
                dialog.accept()

            except ValueError:
                QMessageBox.warning(dialog, "خطأ", "المبلغ غير صحيح!")
            except sqlite3.Error as e:
                QMessageBox.critical(dialog, "خطأ", f"خطأ في قاعدة البيانات: {str(e)}")

        save_btn.clicked.connect(save_operation)
        cancel_btn.clicked.connect(dialog.reject)

        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)

        dialog.setLayout(layout)
        dialog.exec()
