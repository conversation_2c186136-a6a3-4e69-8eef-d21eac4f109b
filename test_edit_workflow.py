#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سير عمل تعديل المنتجات
"""

import sqlite3
from pathlib import Path

# إعداد المسار لقاعدة البيانات
DB_PATH = Path(__file__).parent / "database"
DATABASE_FILE = DB_PATH / "accounting.db"

def test_edit_workflow():
    """اختبار سير عمل تعديل المنتجات"""
    try:
        conn = sqlite3.connect(str(DATABASE_FILE))
        cursor = conn.cursor()
        
        print("🧪 اختبار سير عمل تعديل المنتجات")
        print("=" * 50)
        
        # 1. عرض المنتجات الحالية
        print("\n📋 المنتجات الحالية:")
        cursor.execute("SELECT id, code, designation, prix_achat, prix_vente, stock FROM produits ORDER BY id")
        products = cursor.fetchall()
        
        for product in products:
            print(f"  ID: {product[0]} | Code: {product[1]} | {product[2]}")
            print(f"      السعر: {product[3]} DH (شراء) - {product[4]} DH (بيع) | المخزون: {product[5]}")
            print()
        
        if not products:
            print("❌ لا توجد منتجات للاختبار")
            return
        
        # 2. اختيار منتج للتعديل
        test_product = products[0]
        product_id = test_product[0]
        original_designation = test_product[2]
        original_prix_achat = test_product[3]
        
        print(f"🎯 سيتم تعديل المنتج: {original_designation} (ID: {product_id})")
        
        # 3. تطبيق التعديل
        new_designation = f"{original_designation} - محدث"
        new_prix_achat = original_prix_achat + 50
        new_stock = 75
        
        print(f"\n🔧 التعديلات المطلوبة:")
        print(f"  التسمية: {original_designation} → {new_designation}")
        print(f"  سعر الشراء: {original_prix_achat} DH → {new_prix_achat} DH")
        print(f"  المخزون: → {new_stock}")
        
        # تنفيذ التحديث
        cursor.execute("""
            UPDATE produits 
            SET designation = ?, prix_achat = ?, stock = ?
            WHERE id = ?
        """, (new_designation, new_prix_achat, new_stock, product_id))
        
        if cursor.rowcount > 0:
            conn.commit()
            print(f"\n✅ تم التحديث بنجاح! ({cursor.rowcount} صف محدث)")
            
            # 4. التحقق من التحديث
            cursor.execute("SELECT id, code, designation, prix_achat, prix_vente, stock FROM produits WHERE id = ?", (product_id,))
            updated_product = cursor.fetchone()
            
            if updated_product:
                print(f"\n🔍 البيانات بعد التحديث:")
                print(f"  ID: {updated_product[0]} | Code: {updated_product[1]} | {updated_product[2]}")
                print(f"  السعر: {updated_product[3]} DH (شراء) - {updated_product[4]} DH (بيع) | المخزون: {updated_product[5]}")
                
                # التحقق من التغييرات
                if updated_product[2] == new_designation:
                    print("  ✅ التسمية محدثة بنجاح")
                else:
                    print("  ❌ التسمية لم تتحدث")
                    
                if updated_product[3] == new_prix_achat:
                    print("  ✅ سعر الشراء محدث بنجاح")
                else:
                    print("  ❌ سعر الشراء لم يتحدث")
                    
                if updated_product[5] == new_stock:
                    print("  ✅ المخزون محدث بنجاح")
                else:
                    print("  ❌ المخزون لم يتحدث")
            else:
                print("❌ لم يتم العثور على المنتج المحدث")
        else:
            print("❌ لم يتم تحديث أي صف")
        
        print(f"\n📋 جميع المنتجات بعد التحديث:")
        cursor.execute("SELECT id, code, designation, prix_achat, prix_vente, stock FROM produits ORDER BY id")
        updated_products = cursor.fetchall()
        
        for product in updated_products:
            print(f"  ID: {product[0]} | Code: {product[1]} | {product[2]}")
            print(f"      السعر: {product[3]} DH (شراء) - {product[4]} DH (بيع) | المخزون: {product[5]}")
            print()
        
        conn.close()
        print("🎉 اختبار سير العمل مكتمل!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_edit_workflow()