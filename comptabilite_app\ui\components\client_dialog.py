from PySide6.QtWidgets import (QLabel, QLineEdit, QMessageBox, QGridLayout)
from PySide6.QtCore import QRegularExpression, Signal
from PySide6.QtGui import QRegularExpressionValidator
import sqlite3

from ..theme import COLOR<PERSON>, BORDER_RADIUS
from ..icons.icons import CLIENTS_ICON
from .modal_dialog import ModalDialog
from .autocomplete_widget import apply_autocomplete_to_dialog

class ClientDialog(ModalDialog):
    """Boîte de dialogue modale pour ajouter ou modifier un client"""

    # Signal émis lorsqu'un client est ajouté ou modifié
    client_saved = Signal()

    def __init__(self, db_manager, client_id=None, parent=None):
        title = "Modifier un client" if client_id is not None else "Ajouter un client"
        super().__init__(title, CLIENTS_ICON, parent)

        self.db_manager = db_manager
        self.client_id = client_id
        self.is_edit_mode = client_id is not None

        self.setup_ui()

        if self.is_edit_mode:
            self.load_client_data()
        else:
            self.code_input.setText(self.db_manager.generer_code_client())

    def setup_ui(self):
        """Configure l'interface utilisateur de la boîte de dialogue"""
        # Formulaire
        form_grid = QGridLayout()
        form_grid.setSpacing(15)
        form_grid.setColumnStretch(1, 1)
        form_grid.setColumnStretch(3, 1)

        # Style commun pour les labels et inputs
        label_style = f"""
            font-weight: bold;
            color: {COLORS['text_secondary']};
            font-size: 13px;
        """

        input_style = f"""
            border: 1px solid {COLORS['divider']};
            border-radius: {BORDER_RADIUS['sm']};
            padding: 10px;
            background-color: {COLORS['surface']};
            min-height: 20px;
        """

        # Première colonne
        code_label = QLabel("Code client:")
        code_label.setStyleSheet(label_style)
        self.code_input = QLineEdit()
        self.code_input.setReadOnly(True)
        self.code_input.setStyleSheet(f"{input_style} background-color: {COLORS['hover_bg']}; color: {COLORS['text_secondary']};")
        form_grid.addWidget(code_label, 0, 0)
        form_grid.addWidget(self.code_input, 0, 1)

        nom_label = QLabel("Nom / Raison sociale*:")
        nom_label.setStyleSheet(label_style)
        self.nom_input = QLineEdit()
        self.nom_input.setPlaceholderText("Obligatoire")
        self.nom_input.setStyleSheet(input_style)
        form_grid.addWidget(nom_label, 1, 0)
        form_grid.addWidget(self.nom_input, 1, 1)

        ice_label = QLabel("ICE:")
        ice_label.setStyleSheet(label_style)
        self.ice_input = QLineEdit()
        self.ice_input.setPlaceholderText("Identifiant Commun de l'Entreprise")
        self.ice_input.setStyleSheet(input_style)
        form_grid.addWidget(ice_label, 2, 0)
        form_grid.addWidget(self.ice_input, 2, 1)

        if_label = QLabel("IF:")
        if_label.setStyleSheet(label_style)
        self.if_input = QLineEdit()
        self.if_input.setPlaceholderText("Identifiant Fiscal")
        self.if_input.setStyleSheet(input_style)
        form_grid.addWidget(if_label, 3, 0)
        form_grid.addWidget(self.if_input, 3, 1)

        # Deuxième colonne
        adresse_label = QLabel("Adresse:")
        adresse_label.setStyleSheet(label_style)
        self.adresse_input = QLineEdit()
        self.adresse_input.setStyleSheet(input_style)
        form_grid.addWidget(adresse_label, 0, 2)
        form_grid.addWidget(self.adresse_input, 0, 3)

        telephone_label = QLabel("Téléphone:")
        telephone_label.setStyleSheet(label_style)
        self.telephone_input = QLineEdit()
        # Ajouter un validateur pour le téléphone
        telephone_regex = QRegularExpression("^[0-9+\\-\\s]{8,15}$")
        self.telephone_input.setValidator(QRegularExpressionValidator(telephone_regex))
        self.telephone_input.setStyleSheet(input_style)
        form_grid.addWidget(telephone_label, 1, 2)
        form_grid.addWidget(self.telephone_input, 1, 3)

        email_label = QLabel("E-mail:")
        email_label.setStyleSheet(label_style)
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("<EMAIL>")
        self.email_input.setStyleSheet(input_style)
        form_grid.addWidget(email_label, 2, 2)
        form_grid.addWidget(self.email_input, 2, 3)

        contact_label = QLabel("Personne à contacter:")
        contact_label.setStyleSheet(label_style)
        self.contact_input = QLineEdit()
        self.contact_input.setStyleSheet(input_style)
        form_grid.addWidget(contact_label, 3, 2)
        form_grid.addWidget(self.contact_input, 3, 3)

        # Ajouter le formulaire au conteneur de contenu
        self.content_layout.addLayout(form_grid)

        # Connecter le bouton d'enregistrement
        self.save_btn.clicked.connect(self.save_client)

        # تطبيق الاقتراحات التلقائية
        apply_autocomplete_to_dialog(self, self.db_manager)

    def load_client_data(self):
        """Charge les données du client à modifier"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT * FROM clients WHERE id = ?", (self.client_id,))
        client = cursor.fetchone()

        if client:
            self.code_input.setText(client['code'] or "")
            self.nom_input.setText(client['nom'])
            self.ice_input.setText(client['ice'] or "")
            self.if_input.setText(client['if_fiscal'] or "")
            self.adresse_input.setText(client['adresse'] or "")
            self.telephone_input.setText(client['telephone'] or "")
            self.email_input.setText(client['email'] or "")
            self.contact_input.setText(client['contact'] or "")

    def save_client(self):
        """Enregistre le client (ajout ou modification)"""
        nom = self.nom_input.text().strip()
        if not nom:
            QMessageBox.warning(self, "Erreur", "Le nom du client est obligatoire.")
            return

        code = self.code_input.text()
        if not code:
            code = self.db_manager.generer_code_client()

        cursor = self.db_manager.conn.cursor()
        try:
            if self.is_edit_mode:
                # Modification d'un client existant
                cursor.execute(
                    """UPDATE clients SET
                       code = ?, nom = ?, ice = ?, if_fiscal = ?, adresse = ?,
                       telephone = ?, email = ?, contact = ?
                       WHERE id = ?""",
                    (code, nom, self.ice_input.text(), self.if_input.text(),
                     self.adresse_input.text(), self.telephone_input.text(),
                     self.email_input.text(), self.contact_input.text(), self.client_id)
                )
                message = f"Client {nom} modifié avec succès."
            else:
                # Ajout d'un nouveau client
                cursor.execute(
                    """INSERT INTO clients
                       (code, nom, ice, if_fiscal, adresse, telephone, email, contact)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                    (code, nom, self.ice_input.text(), self.if_input.text(),
                     self.adresse_input.text(), self.telephone_input.text(),
                     self.email_input.text(), self.contact_input.text())
                )
                message = f"Client {nom} ajouté avec succès.\nCode client: {code}"

            self.db_manager.conn.commit()

            # Émettre le signal pour informer que le client a été enregistré
            self.client_saved.emit()

            QMessageBox.information(self, "Succès", message)
            self.accept()

        except sqlite3.IntegrityError:
            QMessageBox.warning(self, "Erreur", "Ce code client existe déjà. Veuillez utiliser un autre code.")
