#!/usr/bin/env python3
"""
اختبار بسيط لمشكلة عرض المنتجات
"""

import sqlite3
import os

def simple_debug():
    """اختبار بسيط"""
    print("🔍 اختبار بسيط لمشكلة عرض المنتجات")
    print("=" * 50)
    
    # البحث عن قاعدة البيانات
    db_path = "database/comptabilite.db"
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود")
        return
    
    print(f"✅ قاعدة البيانات موجودة: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # عد المنتجات
        cursor.execute("SELECT COUNT(*) FROM produits")
        count = cursor.fetchone()[0]
        print(f"📦 عدد المنتجات: {count}")
        
        # عرض المنتجات
        cursor.execute("SELECT id, code, designation FROM produits ORDER BY id DESC LIMIT 3")
        products = cursor.fetchall()
        
        print("📋 آخر 3 منتجات:")
        for product in products:
            print(f"   ID: {product[0]}, الكود: {product[1]}, التسمية: {product[2]}")
        
        conn.close()
        
        # اختبار دالة get_all_products من run_app.py
        print("\n🔍 اختبار دالة get_all_products:")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # نفس الاستعلام المستخدم في run_app.py
        cursor.execute("PRAGMA table_info(produits)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'famille_id' in columns:
            print("   ✅ استخدام استعلام مع العائلات")
            cursor.execute("""
                SELECT p.*, f.nom as famille_nom
                FROM produits p
                LEFT JOIN familles_produits f ON p.famille_id = f.id
                ORDER BY p.date_creation DESC
            """)
        else:
            print("   ⚠️ استخدام استعلام بسيط")
            cursor.execute("SELECT *, NULL as famille_nom FROM produits ORDER BY date_creation DESC")
        
        products_result = cursor.fetchall()
        print(f"   📦 عدد المنتجات المسترجعة: {len(products_result)}")
        
        if products_result:
            print("   📋 أول منتج:")
            first_product = products_result[0]
            print(f"      عدد الأعمدة: {len(first_product)}")
            print(f"      ID: {first_product[0]}")
            print(f"      الكود: {first_product[1]}")
            print(f"      التسمية: {first_product[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")

if __name__ == "__main__":
    simple_debug()
