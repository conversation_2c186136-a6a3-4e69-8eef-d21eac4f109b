try:
    from PySide6.QtWidgets import (QMain<PERSON><PERSON>ow, QWidget, QVBoxLayout, QHBoxLayout,
                                QPushButton, QLabel, QLineEdit, QMessageBox,
                                QFrame, QApplication)
    from PySide6.QtCore import Qt, Signal, QSize
    from PySide6.QtGui import QIcon, QPixmap
except ImportError:
    from PyQt6.QtWidgets import (QMainWindow, QWidget, Q<PERSON>oxLayout, QHBoxLayout,
                                QPushButton, QLabel, QLineEdit, QMessageBox,
                                QFrame, QApplication)
    from PyQt6.QtCore import Qt, pyqtSignal as Signal, QSize
    from PyQt6.QtGui import QIcon, QPixmap

from .theme import *
from .icons.icons import *

class LoginWindow(QMainWindow):
    """Fenêtre de connexion simple"""
    login_successful = Signal()  # Signal émis lors d'une connexion ré<PERSON>ie

    def __init__(self):
        super().__init__()
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("Connexion - Système de Comptabilité")
        self.setFixedSize(450, 550)
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QLabel {
                color: #333;
            }
            #title_label {
                font-size: 24px;
                font-weight: bold;
                color: #1E3A8A;
                margin-bottom: 20px;
            }
            #login_frame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #ddd;
            }
            QLineEdit {
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                margin-bottom: 10px;
                min-height: 20px;
                min-width: 300px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 1px solid #1E3A8A;
                background-color: #f8f9ff;
            }
            QPushButton {
                background-color: #1E3A8A;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 4px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
            QPushButton:pressed {
                background-color: #1E40AF;
            }
            #info_label {
                font-size: 12px;
                color: #666;
            }
        """)

        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Conteneur de connexion
        login_container = QWidget()
        login_layout = QVBoxLayout(login_container)
        login_layout.setContentsMargins(40, 40, 40, 40)
        login_layout.setSpacing(20)
        login_layout.setAlignment(Qt.AlignCenter)

        # Logo ou icône
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setText(svg_to_icon_html("""
        <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
          <line x1="8" y1="21" x2="16" y2="21"></line>
          <line x1="12" y1="17" x2="12" y2="21"></line>
        </svg>
        """, "#1E3A8A", 64))
        login_layout.addWidget(logo_label)

        # Titre
        title_label = QLabel("Système de Comptabilité")
        title_label.setObjectName("title_label")
        title_label.setAlignment(Qt.AlignCenter)
        login_layout.addWidget(title_label)

        # Cadre de connexion
        login_frame = QFrame()
        login_frame.setObjectName("login_frame")
        login_frame_layout = QVBoxLayout(login_frame)
        login_frame_layout.setContentsMargins(20, 20, 20, 20)
        login_frame_layout.setSpacing(10)

        # Nom d'utilisateur
        username_label = QLabel("Nom d'utilisateur:")
        login_frame_layout.addWidget(username_label)

        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Entrez votre nom d'utilisateur")
        self.username_input.setFixedHeight(40)
        self.username_input.setMinimumWidth(320)
        login_frame_layout.addWidget(self.username_input)

        # Mot de passe
        password_label = QLabel("Mot de passe:")
        login_frame_layout.addWidget(password_label)

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Entrez votre mot de passe")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setFixedHeight(40)
        self.password_input.setMinimumWidth(320)
        login_frame_layout.addWidget(self.password_input)

        # Bouton de connexion
        login_button = QPushButton("Se connecter")
        login_button.clicked.connect(self.attempt_login)
        login_button.setFixedHeight(44)
        login_button.setMinimumWidth(320)
        login_frame_layout.addWidget(login_button)

        # Information
        info_label = QLabel("Utilisez admin/1234 pour vous connecter")
        info_label.setObjectName("info_label")
        info_label.setAlignment(Qt.AlignCenter)
        login_frame_layout.addWidget(info_label)

        login_layout.addWidget(login_frame)

        # Copyright
        copyright_label = QLabel("© 2024 Comptabilité App")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("color: #666; margin-top: 20px;")
        login_layout.addWidget(copyright_label)

        main_layout.addWidget(login_container)

        # Définir le focus initial
        self.username_input.setFocus()

        # Connecter la touche Entrée pour la connexion
        self.username_input.returnPressed.connect(self.password_input.setFocus)
        self.password_input.returnPressed.connect(login_button.click)

    def attempt_login(self):
        """Tente de se connecter avec les identifiants fournis"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        # Vérifier les identifiants (admin/1234)
        if username == "admin" and password == "1234":
            self.login_successful.emit()  # Émettre le signal de connexion réussie
            self.close()  # Fermer la fenêtre de connexion
        else:
            # Afficher un message d'erreur
            QMessageBox.warning(
                self,
                "Échec de connexion",
                "Nom d'utilisateur ou mot de passe incorrect.\nVeuillez réessayer.",
                QMessageBox.Ok
            )

            # Effacer le mot de passe et remettre le focus
            self.password_input.clear()
            self.password_input.setFocus()
