try:
    from PySide6.QtWidgets import (Q<PERSON><PERSON><PERSON><PERSON><PERSON>, QWidget, Q<PERSON>ox<PERSON><PERSON>out, QHBoxLayout,
                                QPushButton, QStackedWidget, QLabel, QApplication,
                                QFrame, QScrollArea, QLineEdit, QMessageBox)
    from PySide6.QtCore import Qt, Signal, QObject
    from PySide6.QtGui import QIcon
except ImportError:
    from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                                QPushButton, QStackedWidget, QLabel, QApplication,
                                QFrame, QScrollArea, QLineEdit, QMessageBox)
    from PyQt6.QtCore import Qt, pyqtSignal as Signal, QObject
    from PyQt6.QtGui import QIcon

from .modules.dashboard_simple import DashboardSimple
from .modules.clients_new import ClientsModule
from .modules.fournisseurs import FournisseursModule
from .modules.recherche import RechercheModule
from .modules.bon_commande_new import BonCommandeModuleNew
from .modules.factures_vente_new import FacturesVenteModule  # Correction du nom de la classe importée
from .modules.bl_module import BLModule
from .modules.bl_enhanced import BLEnhancedModule
from .modules.produits import ProduitsModule
from .modules.tva_module import TVAModule
from .modules.stock_module import StockModule
from .modules.caisse_new import CaisseModule
from .modules.marche_module import MarcheModule
from .components.auto_select_widget import apply_auto_select_to_window
from .components.autocomplete_widget import apply_autocomplete_to_window
from .theme import THEME

class DataUpdateSignals(QObject):
    """Classe pour gérer les signaux de mise à jour des données entre les modules"""
    fournisseurs_changed = Signal()  # Signal émis quand les fournisseurs sont modifiés
    clients_changed = Signal()       # Signal émis quand les clients sont modifiés
    produits_changed = Signal()      # Signal émis quand les produits sont modifiés
    stock_changed = Signal()         # Signal émis quand le stock est modifié

class MainWindowSimple(QMainWindow):
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.signals = DataUpdateSignals()
        self.modules_config = self.get_modules_config()
        self.modules = {}

        self.setup_ui()
        QApplication.instance().setStyleSheet(THEME)

    def get_modules_config(self):
        """Retourne la configuration centralisée des modules."""
        return [
            {"id": "dashboard", "title": "Tableau de bord", "icon": "📊", "module": DashboardSimple, "args": [self.db_manager, self.signals], "nav": True},
            {"id": "clients", "title": "Clients", "icon": "👥", "module": ClientsModule, "args": [self.db_manager, self.signals], "nav": True, "search_placeholder": "Rechercher des clients...", "add_button_text": "+ Ajouter un client"},
            {"id": "fournisseurs", "title": "Fournisseurs", "icon": "🏢", "module": FournisseursModule, "args": [self.db_manager, self.signals], "nav": True, "search_placeholder": "Rechercher des fournisseurs...", "add_button_text": "+ Ajouter un fournisseur"},
            {"id": "produits", "title": "Produits", "icon": "📦", "module": ProduitsModule, "args": [self.db_manager, self.signals], "nav": True, "search_placeholder": "Rechercher des produits...", "add_button_text": "+ Ajouter un produit"},
            {"id": "factures", "title": "Factures", "icon": "📄", "module": FacturesVenteModule, "args": [], "nav": True, "search_placeholder": "Rechercher des factures...", "add_button_text": "+ Ajouter une facture"},
            {"id": "bons_commande", "title": "Bons de commande", "icon": "📋", "module": BonCommandeModuleNew, "args": [self.db_manager, self.signals], "nav": True, "search_placeholder": "Rechercher des bons de commande...", "add_button_text": "+ Ajouter un bon de commande"},
            {"id": "bons_livraison", "title": "Bons de Livraison", "icon": "🚚", "module": BLModule, "args": [self.db_manager, self.signals], "nav": True, "search_placeholder": "Rechercher des bons de livraison...", "add_button_text": "+ Ajouter un bon de livraison"},
            {"id": "bons_livraison_enhanced", "title": "Bons de Livraison (Format Français)", "icon": "📦", "module": BLEnhancedModule, "args": [self.db_manager, self.signals], "nav": True, "search_placeholder": "Rechercher des bons de livraison...", "add_button_text": "+ Nouveau Bon de Livraison"},
            {"id": "tva", "title": "TVA", "icon": "💰", "module": TVAModule, "args": [self.db_manager, self.signals], "nav": True, "search_placeholder": "Rechercher des enregistrements TVA...", "add_button_text": "+ Ajouter un enregistrement"},
            {"id": "stock", "title": "Stock", "icon": "📦", "module": StockModule, "args": [self.db_manager, self.signals], "nav": True, "search_placeholder": "Rechercher dans le stock...", "add_button_text": "+ Ajouter une entrée"},
            {"id": "caisse", "title": "Caisse", "icon": "💳", "module": CaisseModule, "args": [self.db_manager, self.signals], "nav": True, "search_placeholder": "Rechercher dans la caisse...", "add_button_text": "+ Ajouter une transaction"},
            {"id": "recherche", "title": "Recherche", "icon": "🔍", "module": RechercheModule, "args": [self.db_manager, self.signals], "nav": False},
            {"id": "marche", "title": "Marché", "icon": "📈", "module": MarcheModule, "args": [self.db_manager], "nav": True, "search_placeholder": "Rechercher des marchés...", "add_button_text": "+ Ajouter un marché"},
        ]

    def setup_ui(self):
        """Configure l'interface utilisateur de manière dynamique."""
        self.setWindowTitle("AsCompta")
        self.setMinimumSize(1200, 800)

        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # --- Sidebar ---
        sidebar = self.create_sidebar()
        main_layout.addWidget(sidebar)

        # --- Content Area ---
        content_container = self.create_content_area()
        main_layout.addWidget(content_container)

        self.initialize_modules()

        # Select the first navigation item by default
        first_nav_item = next((m for m in self.modules_config if m.get("nav")), None)
        if first_nav_item:
            self.change_page(first_nav_item["id"])
            self.nav_buttons[first_nav_item["id"]].setChecked(True)

        apply_auto_select_to_window(self)
        apply_autocomplete_to_window(self, self.db_manager)

        # Connect signals
        self.signals.stock_changed.connect(self.on_data_changed)
        self.signals.produits_changed.connect(self.on_data_changed)
        if hasattr(self.modules.get("caisse"), 'caisse_updated'):
            self.modules["caisse"].caisse_updated.connect(self.on_caisse_changed)

    def create_sidebar(self):
        """Crée la barre latérale avec les boutons de navigation."""
        sidebar = QFrame()
        sidebar.setObjectName("sidebar")
        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)

        title_container = QFrame()
        title_container.setObjectName("app_title_container")
        title_layout = QHBoxLayout(title_container)
        title_layout.setContentsMargins(20, 20, 20, 20)
        title_layout.setSpacing(12)
        title_layout.addWidget(QLabel("💼"))
        title_layout.addWidget(QLabel("AsCompta", objectName="app_title"))
        sidebar_layout.addWidget(title_container)

        self.nav_buttons = {}
        for config in self.modules_config:
            if config.get("nav"):
                btn_text = f"{config['icon']} {config['title']}"
                btn = QPushButton(btn_text)
                btn.setCheckable(True)
                btn.clicked.connect(lambda checked, id=config["id"]: self.change_page(id))
                sidebar_layout.addWidget(btn)
                self.nav_buttons[config["id"]] = btn

        sidebar_layout.addStretch()
        return sidebar

    def create_content_area(self):
        """Crée la zone de contenu principale avec l'en-tête et le QStackedWidget."""
        content_container = QFrame(objectName="content_container")
        content_layout = QVBoxLayout(content_container)
        content_layout.setContentsMargins(24, 24, 24, 24)
        content_layout.setSpacing(20)

        header_container = self.create_header()
        content_layout.addWidget(header_container)

        self.content_stack = QStackedWidget()
        content_layout.addWidget(self.content_stack)

        return content_container

    def create_header(self):
        """Crée l'en-tête avec le titre de la page et la barre de recherche."""
        header_container = QFrame(objectName="header_frame")
        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(24, 16, 24, 16)

        self.page_title = QLabel("Tableau de bord", objectName="page_title")
        header_layout.addWidget(self.page_title)
        header_layout.addStretch()

        search_container = QFrame()
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(16)

        self.search_input = QLineEdit(objectName="search_input", placeholderText="Rechercher...")
        self.add_button = QPushButton("+ Ajouter", objectName="primary_button")

        search_layout.addWidget(self.search_input)
        search_layout.addWidget(self.add_button)
        header_layout.addWidget(search_container)

        return header_container

    def on_data_changed(self):
        """Gestionnaire de signaux unifié pour la mise à jour des données."""
        print("🔄 Signal de changement de données reçu. Actualisation des modules concernés.")
        if "stock" in self.modules:
            self.modules["stock"].refresh_stock_data()
            print("📦 Module Stock actualisé.")
        if "produits" in self.modules:
            self.modules["produits"].load_produits()
            print("📦 Module Produits actualisé.")

    def on_caisse_changed(self):
        """Appelé quand la caisse est modifiée"""
        print("💰 Signal caisse_updated reçu - Actualisation du module Caisse")

        # Actualiser le module Caisse
        if hasattr(self, 'caisse_module') and self.caisse_module:
            self.caisse_module.load_caisse_data()
            print("💰 Module Caisse actualisé")

    def create_nav_button(self, text, page_index):
        """Crée un bouton de navigation simple"""
        button = QPushButton(text)
        button.setCheckable(True)
        button.clicked.connect(lambda: self.change_page(page_index))
        return button

    def create_web_button(self, text, url):
        """Crée un bouton qui ouvre un site web"""
        button = QPushButton(text)
        button.setStyleSheet("""
            QPushButton {
                background-color: #5B9BD5;
                color: white;
                border: none;
                padding: 12px 15px;
                text-align: left;
                font-weight: bold;
                font-size: 12px;
                margin: 2px 5px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #4A90C2;
            }
            QPushButton:pressed {
                background-color: #3A7BC8;
            }
        """)
        button.clicked.connect(lambda: self.open_website(url))
        return button

    def open_website(self, url):
        """Ouvre un site web dans le navigateur par défaut"""
        import webbrowser
        try:
            webbrowser.open(url)
            print(f"🌐 Ouverture du site: {url}")
        except Exception as e:
            print(f"❌ Erreur lors de l'ouverture du site: {str(e)}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "Erreur", f"Impossible d'ouvrir le site web:\n{url}")

    def change_page(self, page_id):
        """Change la page active et met à jour l'interface utilisateur."""
        module_config = next((m for m in self.modules_config if m["id"] == page_id), None)
        if not module_config:
            return

        index = list(self.modules.keys()).index(page_id)
        self.content_stack.setCurrentIndex(index)

        # Mettre à jour le titre de la page
        self.page_title.setText(module_config["title"])

        # Mettre à jour les contrôles de l'en-tête
        self.search_input.setPlaceholderText(module_config.get("search_placeholder", "Rechercher..."))
        self.add_button.setText(module_config.get("add_button_text", "+ Ajouter"))

        # Gérer la visibilité des contrôles
        show_controls = "search_placeholder" in module_config
        self.search_input.setVisible(show_controls)
        self.add_button.setVisible(show_controls)

        # Mettre à jour l'état des boutons de navigation
        for btn_id, button in self.nav_buttons.items():
            button.setChecked(btn_id == page_id)

    def initialize_modules(self):
        """Initialise tous les modules à partir de la configuration."""
        for config in self.modules_config:
            module_class = config["module"]
            args = config.get("args", [])
            try:
                module_instance = module_class(*args)
                self.modules[config["id"]] = module_instance
                self.content_stack.addWidget(module_instance)
                print(f"✅ Module '{config['id']}' initialisé avec succès.")
            except Exception as e:
                print(f"❌ Erreur lors de l'initialisation du module '{config['id']}': {e}")
                # Optionnel: ajouter un widget d'erreur au stack
                error_widget = QLabel(f"Erreur chargement module:\n{config['title']}\n{e}")
                error_widget.setAlignment(Qt.AlignCenter)
                self.content_stack.addWidget(error_widget)
