#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test réel d'ajout de facture avec DISJONCTEUR pour vérifier l'intégration stock
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager
from stock_integration_solution import integrate_with_invoice_save, get_updated_stock_data
import datetime

def test_real_invoice():
    """Test réel d'ajout de facture avec DISJONCTEUR"""
    
    print("🧪 Test réel d'ajout de facture avec DISJONCTEUR")
    print("=" * 60)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    cursor = db_manager.conn.cursor()
    
    try:
        # 1. Afficher l'état initial du stock pour DISJONCTEUR
        print("\n📊 État initial du stock DISJONCTEUR:")
        cursor.execute("""
            SELECT 
                p.designation,
                p.stock,
                COALESCE(SUM(CASE WHEN m.type_mouvement = 'sortie' THEN m.quantite ELSE 0 END), 0) as sorties_actuelles
            FROM produits p
            LEFT JOIN mouvements_stock m ON p.id = m.produit_id
            WHERE p.designation = 'DISJONCTEUR'
            GROUP BY p.id, p.designation, p.stock
        """)
        result = cursor.fetchone()
        if result:
            print(f"- {result[0]}: Stock={result[1]}, Sorties actuelles={result[2]}")
        else:
            print("❌ DISJONCTEUR non trouvé!")
            return
        
        # 2. Créer une vraie facture
        print("\n📄 Création d'une facture réelle:")
        
        # Insérer la facture
        cursor.execute("""
            INSERT INTO factures_vente (numero, date_creation, client_id, total_ht, total_ttc)
            VALUES (?, ?, ?, ?, ?)
        """, ("F-TEST-DISJ-001", datetime.datetime.now().strftime('%Y-%m-%d'), 1, 150.0, 180.0))
        
        facture_id = cursor.lastrowid
        print(f"✅ Facture créée avec ID: {facture_id}")
        
        # Insérer les lignes de facture
        lignes_facture = [
            {
                'designation': 'DISJONCTEUR',
                'unite': 'U',
                'quantite': 5,
                'prix_unitaire': 30.0,
                'taux_tva': 20.0,
                'total_ht': 150.0
            }
        ]
        
        for ligne in lignes_facture:
            cursor.execute("""
                INSERT INTO lignes_facture (facture_id, designation, unite, quantite, prix_unitaire_ht, taux_tva, total_ht)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (facture_id, ligne['designation'], ligne['unite'], ligne['quantite'],
                  ligne['prix_unitaire'], ligne['taux_tva'], ligne['total_ht']))
        
        db_manager.conn.commit()
        print(f"✅ Ligne ajoutée: {lignes_facture[0]['designation']} x {lignes_facture[0]['quantite']}")
        
        # 3. Appliquer l'intégration stock
        print("\n🔄 Application de l'intégration stock:")
        success = integrate_with_invoice_save(db_manager, facture_id, lignes_facture)
        
        if success:
            print("✅ Intégration stock réussie")
        else:
            print("❌ Échec de l'intégration stock")
        
        # 4. Vérifier le résultat
        print("\n📊 État final du stock DISJONCTEUR:")
        cursor.execute("""
            SELECT 
                p.designation,
                p.stock,
                COALESCE(SUM(CASE WHEN m.type_mouvement = 'sortie' THEN m.quantite ELSE 0 END), 0) as sorties_finales
            FROM produits p
            LEFT JOIN mouvements_stock m ON p.id = m.produit_id
            WHERE p.designation = 'DISJONCTEUR'
            GROUP BY p.id, p.designation, p.stock
        """)
        result = cursor.fetchone()
        if result:
            print(f"🎯 {result[0]}: Stock={result[1]}, Sorties finales={result[2]} ⬅️ DEVRAIT ÊTRE 5")
            
            if result[2] == 5:
                print("✅ SUCCESS! Les sorties ont été mises à jour correctement!")
            else:
                print("❌ ÉCHEC! Les sorties n'ont pas été mises à jour.")
        
        # 5. Vérifier les mouvements de stock
        print("\n📋 Mouvements de stock pour cette facture:")
        cursor.execute("""
            SELECT id, produit_id, designation, type_mouvement, quantite, reference, facture_id
            FROM mouvements_stock 
            WHERE facture_id = ?
            ORDER BY date_mouvement DESC
        """, (facture_id,))
        
        mouvements = cursor.fetchall()
        if mouvements:
            for mouvement in mouvements:
                print(f"- ID: {mouvement[0]}, Produit: {mouvement[1]}, Désignation: {mouvement[2]}")
                print(f"  Type: {mouvement[3]}, Quantité: {mouvement[4]}, Référence: {mouvement[5]}")
                print(f"  Facture ID: {mouvement[6]}")
        else:
            print("❌ Aucun mouvement trouvé pour cette facture!")
        
        # 6. Test avec le résumé du stock
        print("\n📈 Résumé complet du stock:")
        stock_data = get_updated_stock_data(db_manager)
        for item in stock_data:
            if item['designation'] == 'DISJONCTEUR':
                print(f"🎯 {item['designation']}: Initial={item['stock_initial']}, "
                      f"Sorties={item['sorties']}, Final={item['stock_final']}")
                break
        
        print(f"\n✅ Test terminé! Facture ID: {facture_id}")
        print("🔍 Vérifiez maintenant dans l'interface graphique:")
        print("   1. Allez dans le module Stock")
        print("   2. Regardez la ligne DISJONCTEUR")
        print("   3. La colonne Sorties devrait afficher 5")
        
    except Exception as e:
        print(f"❌ Erreur pendant le test: {str(e)}")
        db_manager.conn.rollback()

if __name__ == "__main__":
    test_real_invoice()
