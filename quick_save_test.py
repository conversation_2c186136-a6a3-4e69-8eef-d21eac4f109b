#!/usr/bin/env python3
"""
اختبار سريع لزر الحفظ - يمكن تشغيله بسهولة
"""

import sqlite3
import os

def quick_save_test():
    """اختبار سريع لقاعدة البيانات"""
    print("🔍 اختبار سريع لزر الحفظ")
    print("=" * 40)
    
    # البحث عن ملف قاعدة البيانات
    db_paths = [
        "comptabilite_app/data/comptabilite.db",
        "data/comptabilite.db",
        "comptabilite.db"
    ]
    
    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("❌ لم يتم العثور على ملف قاعدة البيانات")
        print("📋 تأكد من تشغيل التطبيق مرة واحدة على الأقل لإنشاء قاعدة البيانات")
        return False
    
    print(f"✅ تم العثور على قاعدة البيانات: {db_path}")
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # فحص جدول المنتجات
        cursor.execute("SELECT COUNT(*) as count FROM produits")
        count = cursor.fetchone()['count']
        print(f"📊 عدد المنتجات الحالي: {count}")
        
        # اختبار إدراج منتج جديد
        print("\n💾 اختبار إدراج منتج جديد...")
        
        cursor.execute("""
            INSERT INTO produits (code, designation, unite, prix_achat, prix_vente, stock)
            VALUES (?, ?, ?, ?, ?, ?)
        """, ("TEST-QUICK", "اختبار سريع", "قطعة", 50.0, 75.0, 3))
        
        conn.commit()
        
        # التحقق من الإدراج
        cursor.execute("SELECT COUNT(*) as count FROM produits")
        new_count = cursor.fetchone()['count']
        
        if new_count > count:
            print("✅ تم إدراج المنتج بنجاح!")
            
            # حذف المنتج الاختباري
            cursor.execute("DELETE FROM produits WHERE code = 'TEST-QUICK'")
            conn.commit()
            print("🧹 تم حذف المنتج الاختباري")
            
            print("\n🎯 النتيجة: قاعدة البيانات تعمل بشكل صحيح!")
            print("📋 إذا كان زر الحفظ لا يعمل، فالمشكلة في واجهة المستخدم")
            
        else:
            print("❌ فشل في إدراج المنتج")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {str(e)}")
        return False

def show_instructions():
    """عرض التعليمات"""
    print("\n📋 تعليمات استكشاف الأخطاء:")
    print("=" * 50)
    print("1. شغّل التطبيق: python -m comptabilite_app.main")
    print("2. اذهب إلى قسم 'المنتجات'")
    print("3. اضغط على 'إضافة منتج'")
    print("4. املأ الحقول التالية بشكل صحيح:")
    print("   - التسمية: أي نص (مطلوب)")
    print("   - الوحدة: مثل 'قطعة' أو 'كيلو'")
    print("   - سعر الشراء: رقم أكبر من 0")
    print("   - سعر البيع: رقم أكبر من 0")
    print("5. اضغط على 'حفظ' أو 'Enregistrer'")
    print("\n🔍 إذا لم يعمل:")
    print("   - تحقق من رسائل الخطأ")
    print("   - تأكد من ملء جميع الحقول المطلوبة")
    print("   - أعد تشغيل التطبيق")
    print("\n💡 نصائح:")
    print("   - لا تترك حقل التسمية فارغاً")
    print("   - استخدم أرقام صحيحة للأسعار")
    print("   - تأكد من أن التطبيق لديه صلاحيات الكتابة")

if __name__ == "__main__":
    if quick_save_test():
        show_instructions()
    else:
        print("\n❌ هناك مشكلة في قاعدة البيانات")
        print("🔧 جرب إعادة تشغيل التطبيق أو إعادة تثبيته")
