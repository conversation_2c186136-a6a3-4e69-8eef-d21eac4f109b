"""
نظام الاقتراحات التلقائية للحقول
Auto-complete system for input fields
"""

from PySide6.QtWidgets import (QLineEdit, QCompleter, QListWidget, QListWidgetItem,
                               QVBoxLayout, QWidget, QFrame, QApplication)
from PySide6.QtCore import Qt, QStringListModel, Signal, QTimer
from PySide6.QtGui import QFont
import sqlite3
from typing import List, Dict, Optional

class AutoCompleteLineEdit(QLineEdit):
    """حقل إدخال مع اقتراحات تلقائية"""

    def __init__(self, db_manager, field_type: str, table_name: str = None,
                 column_name: str = None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.field_type = field_type
        self.table_name = table_name
        self.column_name = column_name

        # إعداد المكمل التلقائي
        self.completer = QCompleter()
        self.completer.setCaseSensitivity(Qt.CaseInsensitive)
        self.completer.setFilterMode(Qt.MatchContains)
        self.completer.setCompletionMode(QCompleter.PopupCompletion)
        self.setCompleter(self.completer)

        # تحديث البيانات عند التركيز
        self.focusInEvent = self.on_focus_in

        # تحديث البيانات كل 30 ثانية
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_suggestions)
        self.update_timer.start(30000)  # 30 ثانية

        # تحديث أولي
        self.update_suggestions()

    def on_focus_in(self, event):
        """عند التركيز على الحقل"""
        super().focusInEvent(event)
        self.update_suggestions()
        self.selectAll()

    def update_suggestions(self):
        """تحديث قائمة الاقتراحات"""
        try:
            suggestions = self.get_suggestions_for_field()
            if suggestions:
                model = QStringListModel(suggestions)
                self.completer.setModel(model)

        except Exception as e:
            print(f"❌ خطأ في تحديث الاقتراحات: {str(e)}")

    def get_suggestions_for_field(self) -> List[str]:
        """الحصول على الاقتراحات حسب نوع الحقل"""
        try:
            cursor = self.db_manager.conn.cursor()
            suggestions = []

            if self.field_type == "client_name":
                cursor.execute("SELECT DISTINCT nom FROM clients WHERE nom IS NOT NULL AND nom != '' ORDER BY nom")
                suggestions = [row[0] for row in cursor.fetchall()]

            elif self.field_type == "fournisseur_name":
                cursor.execute("SELECT DISTINCT nom FROM fournisseurs WHERE nom IS NOT NULL AND nom != '' ORDER BY nom")
                suggestions = [row[0] for row in cursor.fetchall()]

            elif self.field_type == "produit_designation":
                cursor.execute("SELECT DISTINCT designation FROM produits WHERE designation IS NOT NULL AND designation != '' ORDER BY designation")
                suggestions = [row[0] for row in cursor.fetchall()]

            elif self.field_type == "produit_unite":
                # وحدات شائعة + من قاعدة البيانات
                common_units = ["Pièce", "Kg", "Litre", "Mètre", "Carton", "Boîte", "Paquet", "Tonne", "m²", "m³"]
                cursor.execute("SELECT DISTINCT unite FROM produits WHERE unite IS NOT NULL AND unite != ''")
                db_units = [row[0] for row in cursor.fetchall()]
                suggestions = list(set(common_units + db_units))
                suggestions.sort()

            elif self.field_type == "adresse":
                # عناوين من العملاء والموردين
                cursor.execute("SELECT DISTINCT adresse FROM clients WHERE adresse IS NOT NULL AND adresse != '' UNION SELECT DISTINCT adresse FROM fournisseurs WHERE adresse IS NOT NULL AND adresse != '' ORDER BY adresse")
                suggestions = [row[0] for row in cursor.fetchall()]

            elif self.field_type == "telephone":
                cursor.execute("SELECT DISTINCT telephone FROM clients WHERE telephone IS NOT NULL AND telephone != '' UNION SELECT DISTINCT telephone FROM fournisseurs WHERE telephone IS NOT NULL AND telephone != '' ORDER BY telephone")
                suggestions = [row[0] for row in cursor.fetchall()]

            elif self.field_type == "email":
                cursor.execute("SELECT DISTINCT email FROM clients WHERE email IS NOT NULL AND email != '' UNION SELECT DISTINCT email FROM fournisseurs WHERE email IS NOT NULL AND email != '' ORDER BY email")
                suggestions = [row[0] for row in cursor.fetchall()]

            elif self.field_type == "ice":
                cursor.execute("SELECT DISTINCT ice FROM clients WHERE ice IS NOT NULL AND ice != '' UNION SELECT DISTINCT ice FROM fournisseurs WHERE ice IS NOT NULL AND ice != '' ORDER BY ice")
                suggestions = [row[0] for row in cursor.fetchall()]

            elif self.field_type == "if_number":
                cursor.execute("SELECT DISTINCT if_number FROM fournisseurs WHERE if_number IS NOT NULL AND if_number != '' ORDER BY if_number")
                suggestions = [row[0] for row in cursor.fetchall()]

            elif self.field_type == "objet":
                # مواضيع من الفواتير والطلبات
                cursor.execute("""
                    SELECT DISTINCT objet FROM factures_vente WHERE objet IS NOT NULL AND objet != ''
                    UNION
                    SELECT DISTINCT objet FROM bons_commande WHERE objet IS NOT NULL AND objet != ''
                    UNION
                    SELECT DISTINCT objet FROM marches WHERE objet IS NOT NULL AND objet != ''
                    ORDER BY objet
                """)
                suggestions = [row[0] for row in cursor.fetchall()]

            elif self.field_type == "mode_paiement":
                # طرق الدفع الشائعة
                suggestions = ["Espèces", "Chèque", "Virement", "Carte bancaire", "Traite", "À crédit"]

            elif self.field_type == "reference":
                # مراجع من الفواتير والطلبات
                if "facture" in str(self.table_name).lower():
                    cursor.execute("SELECT DISTINCT numero_facture FROM factures_vente WHERE numero_facture IS NOT NULL ORDER BY numero_facture DESC LIMIT 20")
                elif "bon" in str(self.table_name).lower():
                    cursor.execute("SELECT DISTINCT numero_bon FROM bons_commande WHERE numero_bon IS NOT NULL ORDER BY numero_bon DESC LIMIT 20")
                elif "marche" in str(self.table_name).lower():
                    cursor.execute("SELECT DISTINCT reference_marche FROM marches WHERE reference_marche IS NOT NULL ORDER BY reference_marche DESC LIMIT 20")
                suggestions = [row[0] for row in cursor.fetchall()]

            elif self.field_type == "marche_reference":
                # مراجع المشاريع والصفقات
                cursor.execute("""
                    SELECT DISTINCT reference_marche FROM marches
                    WHERE reference_marche IS NOT NULL AND reference_marche != ''
                    ORDER BY reference_marche DESC LIMIT 50
                """)
                marche_refs = [row[0] for row in cursor.fetchall()]

                # إضافة أيضاً أسماء المشاريع (الموضوع)
                cursor.execute("""
                    SELECT DISTINCT objet FROM marches
                    WHERE objet IS NOT NULL AND objet != ''
                    ORDER BY objet LIMIT 50
                """)
                marche_objets = [row[0] for row in cursor.fetchall()]

                # دمج المراجع والمواضيع
                suggestions = marche_refs + marche_objets
                # إزالة التكرارات والترتيب
                suggestions = list(set(suggestions))
                suggestions.sort()

            elif self.field_type == "custom" and self.table_name and self.column_name:
                # حقل مخصص
                cursor.execute(f"SELECT DISTINCT {self.column_name} FROM {self.table_name} WHERE {self.column_name} IS NOT NULL AND {self.column_name} != '' ORDER BY {self.column_name}")
                suggestions = [row[0] for row in cursor.fetchall()]

            return suggestions[:50]  # حد أقصى 50 اقتراح

        except sqlite3.Error as e:
            print(f"❌ خطأ في قاعدة البيانات: {str(e)}")
            return []
        except Exception as e:
            print(f"❌ خطأ عام: {str(e)}")
            return []


class AutoCompleteManager:
    """مدير الاقتراحات التلقائية للتطبيق"""

    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.field_mappings = {
            # تعيين أنواع الحقول حسب الأسماء
            'nom': 'client_name',
            'client': 'client_name',
            'fournisseur': 'fournisseur_name',
            'designation': 'produit_designation',
            'unite': 'produit_unite',
            'adresse': 'adresse',
            'telephone': 'telephone',
            'email': 'email',
            'ice': 'ice',
            'if': 'if_number',
            'objet': 'objet',
            'mode_paiement': 'mode_paiement',
            'paiement': 'mode_paiement',
            'numero': 'reference',
            'reference': 'reference',
            'marche': 'marche_reference',
            'marché': 'marche_reference',
            'projet': 'marche_reference',
            'bc': 'marche_reference',
            'bon_commande': 'marche_reference'
        }

    def apply_autocomplete_to_widget(self, widget: QLineEdit, field_name: str = None,
                                   table_name: str = None, column_name: str = None):
        """تطبيق الاقتراحات التلقائية على حقل"""
        try:
            if not isinstance(widget, QLineEdit) or widget.isReadOnly():
                return

            # تحديد نوع الحقل
            field_type = self.detect_field_type(widget, field_name)

            if field_type:
                # إنشاء مكمل تلقائي
                completer = QCompleter()
                completer.setCaseSensitivity(Qt.CaseInsensitive)
                completer.setFilterMode(Qt.MatchContains)
                completer.setCompletionMode(QCompleter.PopupCompletion)

                # تخصيص مظهر القائمة
                completer.popup().setStyleSheet("""
                    QListView {
                        border: 1px solid #3B82F6;
                        border-radius: 4px;
                        background-color: white;
                        selection-background-color: #EBF4FF;
                        font-size: 12px;
                        padding: 2px;
                    }
                    QListView::item {
                        padding: 6px 10px;
                        border-bottom: 1px solid #E5E7EB;
                    }
                    QListView::item:hover {
                        background-color: #F3F4F6;
                    }
                    QListView::item:selected {
                        background-color: #3B82F6;
                        color: white;
                    }
                """)

                widget.setCompleter(completer)

                # تحديث البيانات عند التركيز
                original_focus_in = widget.focusInEvent
                def new_focus_in(event):
                    original_focus_in(event)
                    self.update_completer_data(completer, field_type, table_name, column_name)
                    widget.selectAll()

                widget.focusInEvent = new_focus_in

                # تحديث أولي
                self.update_completer_data(completer, field_type, table_name, column_name)

                print(f"✅ اقتراحات تلقائية مطبقة على حقل: {field_type}")

        except Exception as e:
            print(f"❌ خطأ في تطبيق الاقتراحات التلقائية: {str(e)}")

    def detect_field_type(self, widget: QLineEdit, field_name: str = None) -> str:
        """تحديد نوع الحقل تلقائياً"""
        # استخدام اسم الحقل إذا كان متوفراً
        if field_name:
            field_name_lower = field_name.lower()
            for key, field_type in self.field_mappings.items():
                if key in field_name_lower:
                    return field_type

        # استخدام placeholder text
        placeholder = widget.placeholderText().lower()
        for key, field_type in self.field_mappings.items():
            if key in placeholder:
                return field_type

        # استخدام object name
        object_name = widget.objectName().lower()
        for key, field_type in self.field_mappings.items():
            if key in object_name:
                return field_type

        return None

    def update_completer_data(self, completer: QCompleter, field_type: str,
                            table_name: str = None, column_name: str = None):
        """تحديث بيانات المكمل التلقائي"""
        try:
            suggestions = self.get_suggestions_for_field_type(field_type, table_name, column_name)
            if suggestions:
                model = QStringListModel(suggestions)
                completer.setModel(model)

        except Exception as e:
            print(f"❌ خطأ في تحديث بيانات المكمل: {str(e)}")

    def get_suggestions_for_field_type(self, field_type: str, table_name: str = None,
                                     column_name: str = None) -> List[str]:
        """الحصول على الاقتراحات حسب نوع الحقل"""
        try:
            cursor = self.db_manager.conn.cursor()
            suggestions = []

            if field_type == "client_name":
                cursor.execute("SELECT DISTINCT nom FROM clients WHERE nom IS NOT NULL AND nom != '' ORDER BY nom")
                suggestions = [row[0] for row in cursor.fetchall()]

            elif field_type == "fournisseur_name":
                cursor.execute("SELECT DISTINCT nom FROM fournisseurs WHERE nom IS NOT NULL AND nom != '' ORDER BY nom")
                suggestions = [row[0] for row in cursor.fetchall()]

            elif field_type == "produit_designation":
                cursor.execute("SELECT DISTINCT designation FROM produits WHERE designation IS NOT NULL AND designation != '' ORDER BY designation")
                suggestions = [row[0] for row in cursor.fetchall()]

            elif field_type == "produit_unite":
                common_units = ["Pièce", "Kg", "Litre", "Mètre", "Carton", "Boîte", "Paquet", "Tonne", "m²", "m³", "Unité"]
                cursor.execute("SELECT DISTINCT unite FROM produits WHERE unite IS NOT NULL AND unite != ''")
                db_units = [row[0] for row in cursor.fetchall()]
                suggestions = list(set(common_units + db_units))
                suggestions.sort()

            elif field_type == "adresse":
                cursor.execute("""
                    SELECT DISTINCT adresse FROM clients WHERE adresse IS NOT NULL AND adresse != ''
                    UNION
                    SELECT DISTINCT adresse FROM fournisseurs WHERE adresse IS NOT NULL AND adresse != ''
                    ORDER BY adresse
                """)
                suggestions = [row[0] for row in cursor.fetchall()]

            elif field_type == "telephone":
                cursor.execute("""
                    SELECT DISTINCT telephone FROM clients WHERE telephone IS NOT NULL AND telephone != ''
                    UNION
                    SELECT DISTINCT telephone FROM fournisseurs WHERE telephone IS NOT NULL AND telephone != ''
                    ORDER BY telephone
                """)
                suggestions = [row[0] for row in cursor.fetchall()]

            elif field_type == "email":
                cursor.execute("""
                    SELECT DISTINCT email FROM clients WHERE email IS NOT NULL AND email != ''
                    UNION
                    SELECT DISTINCT email FROM fournisseurs WHERE email IS NOT NULL AND email != ''
                    ORDER BY email
                """)
                suggestions = [row[0] for row in cursor.fetchall()]

            elif field_type == "ice":
                cursor.execute("""
                    SELECT DISTINCT ice FROM clients WHERE ice IS NOT NULL AND ice != ''
                    UNION
                    SELECT DISTINCT ice FROM fournisseurs WHERE ice IS NOT NULL AND ice != ''
                    ORDER BY ice
                """)
                suggestions = [row[0] for row in cursor.fetchall()]

            elif field_type == "if_number":
                cursor.execute("SELECT DISTINCT if_number FROM fournisseurs WHERE if_number IS NOT NULL AND if_number != '' ORDER BY if_number")
                suggestions = [row[0] for row in cursor.fetchall()]

            elif field_type == "objet":
                cursor.execute("""
                    SELECT DISTINCT objet FROM factures_vente WHERE objet IS NOT NULL AND objet != ''
                    UNION
                    SELECT DISTINCT objet FROM bons_commande WHERE objet IS NOT NULL AND objet != ''
                    UNION
                    SELECT DISTINCT objet FROM marches WHERE objet IS NOT NULL AND objet != ''
                    ORDER BY objet
                """)
                suggestions = [row[0] for row in cursor.fetchall()]

            elif field_type == "mode_paiement":
                suggestions = ["Espèces", "Chèque", "Virement bancaire", "Carte bancaire", "Traite", "À crédit", "Compensation"]

            elif field_type == "reference":
                # مراجع حديثة
                cursor.execute("""
                    SELECT DISTINCT numero_facture FROM factures_vente WHERE numero_facture IS NOT NULL
                    UNION
                    SELECT DISTINCT numero_bon FROM bons_commande WHERE numero_bon IS NOT NULL
                    UNION
                    SELECT DISTINCT reference_marche FROM marches WHERE reference_marche IS NOT NULL
                    ORDER BY 1 DESC LIMIT 30
                """)
                suggestions = [row[0] for row in cursor.fetchall()]

            elif field_type == "marche_reference":
                # مراجع المشاريع والصفقات
                cursor.execute("""
                    SELECT DISTINCT reference_marche FROM marches
                    WHERE reference_marche IS NOT NULL AND reference_marche != ''
                    ORDER BY reference_marche DESC LIMIT 50
                """)
                marche_refs = [row[0] for row in cursor.fetchall()]

                # إضافة أيضاً أسماء المشاريع (الموضوع)
                cursor.execute("""
                    SELECT DISTINCT objet FROM marches
                    WHERE objet IS NOT NULL AND objet != ''
                    ORDER BY objet LIMIT 50
                """)
                marche_objets = [row[0] for row in cursor.fetchall()]

                # دمج المراجع والمواضيع
                suggestions = marche_refs + marche_objets
                # إزالة التكرارات والترتيب
                suggestions = list(set(suggestions))
                suggestions.sort()

            return suggestions[:50]  # حد أقصى 50 اقتراح

        except sqlite3.Error as e:
            print(f"❌ خطأ في قاعدة البيانات: {str(e)}")
            return []
        except Exception as e:
            print(f"❌ خطأ عام: {str(e)}")
            return []

    def apply_to_window(self, window):
        """تطبيق الاقتراحات التلقائية على جميع حقول النافذة"""
        try:
            line_edits = window.findChildren(QLineEdit)
            applied_count = 0

            for widget in line_edits:
                if not widget.isReadOnly():
                    self.apply_autocomplete_to_widget(widget)
                    applied_count += 1

            print(f"✅ تم تطبيق الاقتراحات التلقائية على {applied_count} حقل")

        except Exception as e:
            print(f"❌ خطأ في تطبيق الاقتراحات على النافذة: {str(e)}")


def apply_autocomplete_to_window(window, db_manager):
    """دالة مساعدة لتطبيق الاقتراحات التلقائية على نافذة"""
    manager = AutoCompleteManager(db_manager)
    manager.apply_to_window(window)


def apply_autocomplete_to_dialog(dialog, db_manager):
    """تطبيق الاقتراحات التلقائية على نافذة حوار"""
    try:
        manager = AutoCompleteManager(db_manager)

        # البحث عن جميع حقول الإدخال في النافذة
        line_edits = dialog.findChildren(QLineEdit)
        applied_count = 0

        for widget in line_edits:
            if not widget.isReadOnly():
                # تحديد نوع الحقل بناءً على اسم الحقل أو placeholder
                field_name = None

                # محاولة الحصول على اسم الحقل من object name
                if hasattr(widget, 'objectName') and widget.objectName():
                    field_name = widget.objectName()

                # أو من placeholder text
                elif widget.placeholderText():
                    field_name = widget.placeholderText()

                manager.apply_autocomplete_to_widget(widget, field_name)
                applied_count += 1

        print(f"✅ تم تطبيق الاقتراحات التلقائية على {applied_count} حقل في النافذة")

    except Exception as e:
        print(f"❌ خطأ في تطبيق الاقتراحات على النافذة: {str(e)}")


# دالة عامة لتطبيق الاقتراحات على أي widget
def setup_autocomplete_for_widget(widget: QLineEdit, db_manager, field_type: str):
    """إعداد الاقتراحات التلقائية لحقل محدد"""
    try:
        manager = AutoCompleteManager(db_manager)
        manager.apply_autocomplete_to_widget(widget, field_type)
    except Exception as e:
        print(f"❌ خطأ في إعداد الاقتراحات للحقل: {str(e)}")
