#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحديث المنتجات
"""

import sqlite3
from pathlib import Path

# إعداد المسار لقاعدة البيانات
DB_PATH = Path(__file__).parent / "database"
DATABASE_FILE = DB_PATH / "accounting.db"

def test_product_update():
    """اختبار تحديث منتج"""
    try:
        conn = sqlite3.connect(str(DATABASE_FILE))
        cursor = conn.cursor()
        
        # عرض جميع المنتجات قبل التحديث
        print("📋 المنتجات قبل التحديث:")
        cursor.execute("SELECT id, code, designation, prix_achat, prix_vente, stock FROM produits LIMIT 5")
        products_before = cursor.fetchall()
        for product in products_before:
            print(f"  ID: {product[0]}, Code: {product[1]}, Designation: {product[2]}, Prix Achat: {product[3]}, Prix Vente: {product[4]}, Stock: {product[5]}")
        
        if not products_before:
            print("❌ لا توجد منتجات في قاعدة البيانات")
            return
        
        # اختيار أول منتج للتحديث
        product_id = products_before[0][0]
        print(f"\n🔧 تحديث المنتج بالمعرف: {product_id}")
        
        # تحديث المنتج
        new_designation = "منتج محدث - اختبار"
        new_prix_achat = 150.0
        new_prix_vente = 200.0
        new_stock = 25
        
        cursor.execute("""
            UPDATE produits 
            SET designation = ?, prix_achat = ?, prix_vente = ?, stock = ?
            WHERE id = ?
        """, (new_designation, new_prix_achat, new_prix_vente, new_stock, product_id))
        
        print(f"📝 عدد الصفوف المحدثة: {cursor.rowcount}")
        
        if cursor.rowcount > 0:
            conn.commit()
            print("✅ تم حفظ التحديث")
            
            # عرض البيانات بعد التحديث
            cursor.execute("SELECT id, code, designation, prix_achat, prix_vente, stock FROM produits WHERE id = ?", (product_id,))
            updated_product = cursor.fetchone()
            if updated_product:
                print(f"\n📋 البيانات بعد التحديث:")
                print(f"  ID: {updated_product[0]}, Code: {updated_product[1]}, Designation: {updated_product[2]}")
                print(f"  Prix Achat: {updated_product[3]}, Prix Vente: {updated_product[4]}, Stock: {updated_product[5]}")
            else:
                print("❌ لم يتم العثور على المنتج المحدث")
        else:
            print("❌ لم يتم تحديث أي صف")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_product_update()