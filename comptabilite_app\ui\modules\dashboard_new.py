from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QFrame, QGridLayout, QSizePolicy)
from PySide6.QtCore import Qt
import sqlite3

# Importer le module de base
from ..components.base_module import BaseModule
from ..icons.icons import DASHBOARD_ICON, CLIENTS_ICON, SUPPLIERS_ICON, PRODUCTS_ICON, INVOICES_ICON
from ..theme import COLORS, BORDER_RADIUS, SPACING

class DashboardModule(BaseModule):
    """Module de tableau de bord avec interface simplifiée"""
    
    def __init__(self, db_manager, signals=None):
        super().__init__(
            db_manager=db_manager,
            signals=signals,
            title="Tableau de Bord",
            description="Aperçu de votre activité",
            icon=DASHBOARD_ICON
        )
        
        # Masquer le bouton d'ajout et la barre de recherche
        self.add_button.setVisible(False)
        self.search_input.parentWidget().setVisible(False)
        
        # Masquer le tableau
        self.items_table.setVisible(False)
        
        # Configurer le tableau de bord
        self.setup_dashboard()
        
        # Connecter les signaux pour mettre à jour le tableau de bord
        if signals:
            signals.clients_changed.connect(self.update_dashboard)
            signals.fournisseurs_changed.connect(self.update_dashboard)
            signals.produits_changed.connect(self.update_dashboard)
    
    def setup_dashboard(self):
        """Configure le tableau de bord"""
        # Créer un layout pour le tableau de bord
        dashboard_layout = QVBoxLayout()
        dashboard_layout.setSpacing(int(SPACING['lg'].replace('px', '')))
        
        # Ajouter les cartes de statistiques
        stats_container = QFrame()
        stats_container.setObjectName("stats_container")
        stats_layout = QGridLayout(stats_container)
        stats_layout.setSpacing(int(SPACING['md'].replace('px', '')))
        
        # Créer les cartes de statistiques
        self.clients_card = self.create_stat_card("Clients", "0", CLIENTS_ICON, "#3B82F6")
        self.fournisseurs_card = self.create_stat_card("Fournisseurs", "0", SUPPLIERS_ICON, "#10B981")
        self.produits_card = self.create_stat_card("Produits", "0", PRODUCTS_ICON, "#F59E0B")
        self.factures_card = self.create_stat_card("Factures", "0", INVOICES_ICON, "#EF4444")
        
        # Ajouter les cartes au layout
        stats_layout.addWidget(self.clients_card, 0, 0)
        stats_layout.addWidget(self.fournisseurs_card, 0, 1)
        stats_layout.addWidget(self.produits_card, 1, 0)
        stats_layout.addWidget(self.factures_card, 1, 1)
        
        dashboard_layout.addWidget(stats_container)
        
        # Ajouter les activités récentes
        recent_container = QFrame()
        recent_container.setObjectName("recent_container")
        recent_container.setStyleSheet(f"""
            #recent_container {{
                background-color: white;
                border-radius: {BORDER_RADIUS['md']};
                border: 1px solid {COLORS['divider']};
                padding: 20px;
            }}
        """)
        recent_layout = QVBoxLayout(recent_container)
        
        # Titre de la section
        recent_title = QLabel("Activités Récentes")
        recent_title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        recent_layout.addWidget(recent_title)
        
        # Liste des activités récentes
        self.recent_activities = QVBoxLayout()
        recent_layout.addLayout(self.recent_activities)
        
        dashboard_layout.addWidget(recent_container)
        
        # Ajouter le layout du tableau de bord au layout principal
        self.layout().addLayout(dashboard_layout)
        
        # Mettre à jour les statistiques
        self.update_dashboard()
    
    def create_stat_card(self, title, value, icon, color):
        """Crée une carte de statistique"""
        card = QFrame()
        card.setObjectName(f"{title.lower()}_card")
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border-radius: {BORDER_RADIUS['md']};
                border: 1px solid {COLORS['divider']};
                padding: 20px;
            }}
        """)
        card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        card_layout = QHBoxLayout(card)
        
        # Icône
        icon_container = QFrame()
        icon_container.setFixedSize(60, 60)
        icon_container.setStyleSheet(f"""
            background-color: {color}20;
            border-radius: 30px;
        """)
        icon_layout = QVBoxLayout(icon_container)
        icon_layout.setAlignment(Qt.AlignCenter)
        
        icon_label = QLabel()
        icon_label.setText(self.svg_to_icon_html(icon, color, 30))
        icon_layout.addWidget(icon_label)
        
        card_layout.addWidget(icon_container)
        
        # Texte
        text_container = QVBoxLayout()
        text_container.setSpacing(5)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 16px;
            color: #6B7280;
        """)
        
        value_label = QLabel(value)
        value_label.setObjectName(f"{title.lower()}_value")
        value_label.setStyleSheet(f"""
            font-size: 24px;
            font-weight: bold;
            color: {color};
        """)
        
        text_container.addWidget(title_label)
        text_container.addWidget(value_label)
        
        card_layout.addLayout(text_container)
        
        return card
    
    def update_dashboard(self):
        """Met à jour les statistiques du tableau de bord"""
        try:
            cursor = self.db_manager.conn.cursor()
            
            # Nombre de clients
            cursor.execute("SELECT COUNT(*) FROM clients")
            clients_count = cursor.fetchone()[0]
            self.clients_card.findChild(QLabel, "clients_value").setText(str(clients_count))
            
            # Nombre de fournisseurs
            cursor.execute("SELECT COUNT(*) FROM fournisseurs")
            fournisseurs_count = cursor.fetchone()[0]
            self.fournisseurs_card.findChild(QLabel, "fournisseurs_value").setText(str(fournisseurs_count))
            
            # Nombre de produits
            cursor.execute("SELECT COUNT(*) FROM produits")
            produits_count = cursor.fetchone()[0]
            self.produits_card.findChild(QLabel, "produits_value").setText(str(produits_count))
            
            # Nombre de factures
            cursor.execute("SELECT COUNT(*) FROM factures_achat")
            factures_achat_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM factures_vente")
            factures_vente_count = cursor.fetchone()[0]
            
            total_factures = factures_achat_count + factures_vente_count
            self.factures_card.findChild(QLabel, "factures_value").setText(str(total_factures))
            
            # Mettre à jour les activités récentes
            self.update_recent_activities()
            
        except sqlite3.Error as e:
            print(f"Erreur lors de la mise à jour du tableau de bord: {str(e)}")
    
    def update_recent_activities(self):
        """Met à jour la liste des activités récentes"""
        # Supprimer les activités existantes
        while self.recent_activities.count():
            item = self.recent_activities.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        try:
            cursor = self.db_manager.conn.cursor()
            
            # Récupérer les 5 derniers clients ajoutés
            cursor.execute("SELECT nom, code FROM clients ORDER BY id DESC LIMIT 5")
            recent_clients = cursor.fetchall()
            
            # Récupérer les 5 derniers fournisseurs ajoutés
            cursor.execute("SELECT nom, code FROM fournisseurs ORDER BY id DESC LIMIT 5")
            recent_fournisseurs = cursor.fetchall()
            
            # Récupérer les 5 derniers produits ajoutés
            cursor.execute("SELECT designation, code FROM produits ORDER BY id DESC LIMIT 5")
            recent_produits = cursor.fetchall()
            
            # Ajouter les activités récentes
            if recent_clients:
                self.add_activity_section("Derniers clients ajoutés", recent_clients)
            
            if recent_fournisseurs:
                self.add_activity_section("Derniers fournisseurs ajoutés", recent_fournisseurs)
            
            if recent_produits:
                self.add_activity_section("Derniers produits ajoutés", recent_produits)
            
        except sqlite3.Error as e:
            print(f"Erreur lors de la mise à jour des activités récentes: {str(e)}")
    
    def add_activity_section(self, title, items):
        """Ajoute une section d'activités récentes"""
        # Titre de la section
        section_title = QLabel(title)
        section_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            margin-top: 15px;
            margin-bottom: 5px;
            color: #1A56DB;
        """)
        self.recent_activities.addWidget(section_title)
        
        # Liste des éléments
        for item in items:
            item_widget = QFrame()
            item_widget.setStyleSheet(f"""
                background-color: {COLORS['background']};
                border-radius: {BORDER_RADIUS['sm']};
                padding: 10px;
                margin-bottom: 5px;
            """)
            item_layout = QHBoxLayout(item_widget)
            item_layout.setContentsMargins(10, 5, 10, 5)
            
            name_label = QLabel(item[0])
            name_label.setStyleSheet("font-weight: bold;")
            
            code_label = QLabel(f"Code: {item[1]}")
            code_label.setStyleSheet("color: #6B7280;")
            
            item_layout.addWidget(name_label)
            item_layout.addStretch()
            item_layout.addWidget(code_label)
            
            self.recent_activities.addWidget(item_widget)
    
    def svg_to_icon_html(self, svg, color, size):
        """Convertit un SVG en HTML pour l'afficher dans un QLabel"""
        return f"""
            <svg width="{size}" height="{size}" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="{svg}" fill="{color}"/>
            </svg>
        """
