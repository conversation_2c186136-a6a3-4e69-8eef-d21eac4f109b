#!/usr/bin/env python3
"""
Simple test to verify TTC calculation functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from comptabilite_app.database.db_manager import DatabaseManager

def test_ttc_database():
    """Test if TTC columns exist in database"""
    print("🔍 Testing TTC columns in database...")
    
    # Initialize database manager
    db_manager = DatabaseManager()
    
    try:
        cursor = db_manager.conn.cursor()
        
        # Check table structure
        cursor.execute("PRAGMA table_info(produits)")
        columns = cursor.fetchall()
        
        print("📋 Current columns in produits table:")
        for col in columns:
            print(f"   - {col['name']}: {col['type']}")
        
        # Check if TTC columns exist
        column_names = [col['name'] for col in columns]
        
        ttc_columns = ['prix_achat_ttc', 'prix_vente_ttc', 'tva_rate']
        missing_columns = [col for col in ttc_columns if col not in column_names]
        
        if missing_columns:
            print(f"❌ Missing TTC columns: {missing_columns}")
        else:
            print("✅ All TTC columns exist!")
            
        # Test TTC calculation
        print("\n🧮 Testing TTC calculation:")
        prix_ht = 100.0
        tva_rate = 20.0
        prix_ttc = prix_ht * (1 + tva_rate / 100)
        print(f"   Prix HT: {prix_ht:.2f} DH")
        print(f"   TVA: {tva_rate}%")
        print(f"   Prix TTC: {prix_ttc:.2f} DH")
        
        # Test database insert with TTC values
        if not missing_columns:
            print("\n💾 Testing database insert with TTC values...")
            try:
                cursor.execute("""
                    INSERT INTO produits (code, designation, prix_achat, prix_vente, 
                                        prix_achat_ttc, prix_vente_ttc, tva_rate, stock)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, ("TEST001", "Test Product TTC", 100.0, 150.0, 120.0, 180.0, 20.0, 10))
                
                db_manager.conn.commit()
                print("✅ Successfully inserted product with TTC values!")
                
                # Retrieve and verify
                cursor.execute("SELECT * FROM produits WHERE code = 'TEST001'")
                product = cursor.fetchone()
                if product:
                    print(f"   Retrieved: {product['designation']}")
                    print(f"   Prix achat HT: {product['prix_achat']:.2f} DH")
                    print(f"   Prix achat TTC: {product['prix_achat_ttc']:.2f} DH")
                    print(f"   Prix vente HT: {product['prix_vente']:.2f} DH")
                    print(f"   Prix vente TTC: {product['prix_vente_ttc']:.2f} DH")
                    print(f"   TVA Rate: {product['tva_rate']:.1f}%")
                
                # Clean up test data
                cursor.execute("DELETE FROM produits WHERE code = 'TEST001'")
                db_manager.conn.commit()
                print("🧹 Test data cleaned up")
                
            except Exception as e:
                print(f"❌ Error testing database insert: {str(e)}")
        
    except Exception as e:
        print(f"❌ Error testing database: {str(e)}")
    
    finally:
        db_manager.close()

if __name__ == "__main__":
    test_ttc_database()
