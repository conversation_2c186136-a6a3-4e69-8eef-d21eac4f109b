#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حقول التاريخ المحمية - يمكن تغيير الأرقام فقط وليس الشرطات المائلة
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    import customtkinter as ctk
    from datetime import datetime, timedelta
    from run_app import create_enhanced_date_entry
    from date_picker_ctk import DatePickerCTK

    class ProtectedDateTestApp(ctk.CTk):
        """تطبيق اختبار حقول التاريخ المحمية"""

        def __init__(self):
            super().__init__()

            # إعداد النافذة
            self.title("🗓️ اختبار حقول التاريخ المحمية")
            self.geometry("800x700")

            # تطبيق المظهر
            ctk.set_appearance_mode("light")
            ctk.set_default_color_theme("blue")

            self.setup_ui()

        def setup_ui(self):
            """إعداد واجهة المستخدم"""

            # العنوان الرئيسي
            title_label = ctk.CTkLabel(
                self,
                text="🗓️ اختبار حقول التاريخ المحمية",
                font=ctk.CTkFont(size=24, weight="bold")
            )
            title_label.pack(pady=20)

            # إطار التعليمات
            instructions_frame = ctk.CTkFrame(self)
            instructions_frame.pack(fill="x", padx=20, pady=10)

            instructions_text = """
🎯 ميزات حقول التاريخ المحمية:

✅ يمكن تغيير الأرقام فقط (0-9)
✅ لا يمكن حذف أو تغيير الشرطات المائلة (/)
✅ عند الحذف، يتم استبدال الرقم بـ (_) بدلاً من حذفه
✅ التنسيق محمي: DD/MM/YYYY
✅ يمكن استخدام لوحة المفاتيح للتنقل

🎮 جرب الآن:
• اكتب أرقام في الحقول
• جرب الحذف بـ Backspace أو Delete
• جرب تغيير الشرطات المائلة (لن يعمل!)
            """

            instructions_label = ctk.CTkLabel(
                instructions_frame,
                text=instructions_text,
                font=ctk.CTkFont(size=12),
                justify="left"
            )
            instructions_label.pack(padx=15, pady=15)

            # إطار النموذج
            form_frame = ctk.CTkFrame(self)
            form_frame.pack(fill="both", expand=True, padx=20, pady=10)

            # تكوين الشبكة
            form_frame.grid_columnconfigure(1, weight=1)

            # حقول التاريخ المختلفة
            row = 0

            # 1. حقل تاريخ عادي (CustomTkinter)
            ctk.CTkLabel(form_frame, text="📅 تاريخ عادي (CTk):", font=ctk.CTkFont(weight="bold")).grid(
                row=row, column=0, padx=10, pady=10, sticky="w"
            )
            self.date_normal = create_enhanced_date_entry(form_frame)
            self.date_normal.grid(row=row, column=1, padx=10, pady=10, sticky="ew")
            row += 1

            # 2. حقل تاريخ الدفع (للمنتجات)
            ctk.CTkLabel(form_frame, text="💳 تاريخ الدفع:", font=ctk.CTkFont(weight="bold")).grid(
                row=row, column=0, padx=10, pady=10, sticky="w"
            )
            self.date_payment = create_enhanced_date_entry(form_frame)
            self.date_payment.grid(row=row, column=1, padx=10, pady=10, sticky="ew")
            row += 1

            # 3. حقل تاريخ الشراء
            ctk.CTkLabel(form_frame, text="🛒 تاريخ الشراء:", font=ctk.CTkFont(weight="bold")).grid(
                row=row, column=0, padx=10, pady=10, sticky="w"
            )
            self.date_purchase = create_enhanced_date_entry(form_frame)
            self.date_purchase.grid(row=row, column=1, padx=10, pady=10, sticky="ew")
            row += 1

            # 4. حقل تاريخ الفاتورة
            ctk.CTkLabel(form_frame, text="🧾 تاريخ الفاتورة:", font=ctk.CTkFont(weight="bold")).grid(
                row=row, column=0, padx=10, pady=10, sticky="w"
            )
            self.date_invoice = create_enhanced_date_entry(form_frame)
            self.date_invoice.grid(row=row, column=1, padx=10, pady=10, sticky="ew")
            row += 1

            # 5. حقل تاريخ التسليم
            ctk.CTkLabel(form_frame, text="🚚 تاريخ التسليم:", font=ctk.CTkFont(weight="bold")).grid(
                row=row, column=0, padx=10, pady=10, sticky="w"
            )
            self.date_delivery = create_enhanced_date_entry(form_frame)
            self.date_delivery.grid(row=row, column=1, padx=10, pady=10, sticky="ew")
            row += 1

            # 6. مكون التاريخ المحسن (DatePickerCTk)
            ctk.CTkLabel(form_frame, text="📅 مكون التاريخ المحسن:", font=ctk.CTkFont(weight="bold")).grid(
                row=row, column=0, padx=10, pady=10, sticky="w"
            )
            self.date_picker = DatePickerCTK(form_frame)
            self.date_picker.grid(row=row, column=1, padx=10, pady=10, sticky="ew")
            row += 1

            # إطار الأزرار
            buttons_frame = ctk.CTkFrame(self)
            buttons_frame.pack(fill="x", padx=20, pady=10)

            # زر عرض القيم
            show_btn = ctk.CTkButton(
                buttons_frame,
                text="📋 عرض القيم المدخلة",
                command=self.show_values,
                font=ctk.CTkFont(weight="bold")
            )
            show_btn.pack(side="left", padx=10, pady=10)

            # زر مسح الحقول
            clear_btn = ctk.CTkButton(
                buttons_frame,
                text="🗑️ مسح الحقول",
                command=self.clear_fields,
                font=ctk.CTkFont(weight="bold"),
                fg_color="red",
                hover_color="darkred"
            )
            clear_btn.pack(side="left", padx=10, pady=10)

            # زر تعيين تواريخ تجريبية
            test_btn = ctk.CTkButton(
                buttons_frame,
                text="🎯 تواريخ تجريبية",
                command=self.set_test_dates,
                font=ctk.CTkFont(weight="bold"),
                fg_color="purple",
                hover_color="darkviolet"
            )
            test_btn.pack(side="left", padx=10, pady=10)

            # زر اختبار سيناريو منتج
            product_btn = ctk.CTkButton(
                buttons_frame,
                text="📦 سيناريو منتج",
                command=self.test_product_scenario,
                font=ctk.CTkFont(weight="bold"),
                fg_color="green",
                hover_color="darkgreen"
            )
            product_btn.pack(side="left", padx=10, pady=10)

        def show_values(self):
            """عرض القيم المدخلة في جميع الحقول"""
            print("=" * 60)
            print("📋 القيم المدخلة في حقول التاريخ المحمية:")
            print("=" * 60)

            fields = [
                ("📅 التاريخ العادي", self.date_normal),
                ("💳 تاريخ الدفع", self.date_payment),
                ("🛒 تاريخ الشراء", self.date_purchase),
                ("🧾 تاريخ الفاتورة", self.date_invoice),
                ("🚚 تاريخ التسليم", self.date_delivery),
                ("📅 مكون التاريخ المحسن", self.date_picker.date_display)
            ]

            for label, field in fields:
                try:
                    value = field.get() if hasattr(field, 'get') else "غير متاح"
                    if value and value.strip() and value != "__/__/____":
                        # التحقق من اكتمال التاريخ
                        if "_" in value:
                            print(f"{label}: {value} (غير مكتمل)")
                        else:
                            print(f"{label}: {value} ✅")
                    else:
                        print(f"{label}: غير محدد")
                except:
                    print(f"{label}: خطأ في القراءة")

            print("=" * 60)

        def clear_fields(self):
            """مسح جميع الحقول"""
            fields = [
                self.date_normal,
                self.date_payment,
                self.date_purchase,
                self.date_invoice,
                self.date_delivery,
                self.date_picker.date_display
            ]

            for field in fields:
                try:
                    if hasattr(field, 'delete'):
                        field.delete(0, 'end')
                        field.insert(0, "__/__/____")
                except:
                    pass

            print("🗑️ تم مسح جميع الحقول وإعادة تعيين التنسيق")

        def set_test_dates(self):
            """تعيين تواريخ تجريبية"""
            today = datetime.now()

            test_dates = [
                (self.date_normal, today.strftime("%d/%m/%Y")),
                (self.date_payment, (today + timedelta(days=7)).strftime("%d/%m/%Y")),
                (self.date_purchase, (today - timedelta(days=5)).strftime("%d/%m/%Y")),
                (self.date_invoice, today.strftime("%d/%m/%Y")),
                (self.date_delivery, (today + timedelta(days=14)).strftime("%d/%m/%Y")),
                (self.date_picker.date_display, (today + timedelta(days=30)).strftime("%d/%m/%Y"))
            ]

            for field, date_str in test_dates:
                try:
                    if hasattr(field, 'delete'):
                        field.delete(0, 'end')
                        field.insert(0, date_str)
                except:
                    pass

            print("🎯 تم تعيين التواريخ التجريبية")

        def test_product_scenario(self):
            """اختبار سيناريو إضافة منتج مع تواريخ"""
            today = datetime.now()

            # سيناريو: شراء منتج اليوم، دفع بعد أسبوع، تسليم بعد 3 أيام
            purchase_date = today.strftime("%d/%m/%Y")
            payment_date = (today + timedelta(days=7)).strftime("%d/%m/%Y")
            invoice_date = today.strftime("%d/%m/%Y")
            delivery_date = (today + timedelta(days=3)).strftime("%d/%m/%Y")

            scenarios = [
                (self.date_purchase, purchase_date, "تاريخ شراء المنتج"),
                (self.date_payment, payment_date, "تاريخ دفع الفاتورة"),
                (self.date_invoice, invoice_date, "تاريخ الفاتورة"),
                (self.date_delivery, delivery_date, "تاريخ التسليم المتوقع")
            ]

            print("📦 اختبار سيناريو إضافة منتج:")
            print("-" * 40)

            for field, date_str, description in scenarios:
                try:
                    if hasattr(field, 'delete'):
                        field.delete(0, 'end')
                        field.insert(0, date_str)
                    print(f"✅ {description}: {date_str}")
                except Exception as e:
                    print(f"❌ خطأ في {description}: {e}")

            print("-" * 40)

    def main():
        """الدالة الرئيسية"""
        try:
            app = ProtectedDateTestApp()

            print("🚀 تم تشغيل اختبار حقول التاريخ المحمية")
            print("🛡️ جرب كتابة التواريخ - لاحظ أن التنسيق محمي!")
            print("🎯 استخدم الأزرار لاختبار الوظائف المختلفة")
            print("📝 ملاحظة: يمكن تغيير الأرقام فقط، الشرطات المائلة محمية")

            app.mainloop()

        except Exception as e:
            print(f"❌ خطأ في تشغيل التطبيق: {e}")
            import traceback
            traceback.print_exc()

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("تأكد من تثبيت customtkinter")
    sys.exit(1)
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
