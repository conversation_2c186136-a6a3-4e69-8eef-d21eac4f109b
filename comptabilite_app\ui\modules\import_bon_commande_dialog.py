from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QTableWidget, QTableWidgetItem, QComboBox,
                              QMessageBox, QHeaderView, QCheckBox, QWidget)
from PySide6.QtCore import Qt
import sqlite3

class ImportBonCommandeDialog(QDialog):
    """Boîte de dialogue pour importer des articles depuis un bon de commande"""

    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.selected_articles = []
        
        self.setWindowTitle("Importer des articles depuis un bon de commande")
        self.setMinimumWidth(900)
        self.setMinimumHeight(600)
        self.setModal(True)
        
        self.setup_ui()
        self.charger_bons_commande()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # Titre
        title_label = QLabel("Sélectionner un bon de commande et les articles à importer")
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #1E3A8A;
            margin-bottom: 10px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # Sélection du bon de commande
        selection_layout = QHBoxLayout()
        
        bon_label = QLabel("Bon de commande:")
        bon_label.setStyleSheet("font-weight: bold;")
        
        self.bon_commande_combo = QComboBox()
        self.bon_commande_combo.setMinimumWidth(400)
        self.bon_commande_combo.currentIndexChanged.connect(self.bon_commande_selectionne)
        
        selection_layout.addWidget(bon_label)
        selection_layout.addWidget(self.bon_commande_combo)
        selection_layout.addStretch()
        
        layout.addLayout(selection_layout)

        # Tableau des articles
        articles_label = QLabel("Articles du bon de commande:")
        articles_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(articles_label)

        self.articles_table = QTableWidget()
        self.articles_table.setColumnCount(6)
        self.articles_table.setHorizontalHeaderLabels([
            "Sélectionner", "Désignation", "Unité", "Quantité", "Prix unitaire HT", "Total HT"
        ])

        # Configurer les colonnes
        self.articles_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.articles_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.articles_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)

        self.articles_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
            }
            QHeaderView::section {
                background-color: #1E3A8A;
                color: white;
                padding: 8px;
                font-weight: bold;
                border: none;
            }
        """)

        layout.addWidget(self.articles_table)

        # Boutons de sélection
        selection_buttons_layout = QHBoxLayout()
        
        select_all_button = QPushButton("Tout sélectionner")
        select_all_button.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        select_all_button.clicked.connect(self.selectionner_tout)
        
        deselect_all_button = QPushButton("Tout désélectionner")
        deselect_all_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        deselect_all_button.clicked.connect(self.deselectionner_tout)
        
        selection_buttons_layout.addWidget(select_all_button)
        selection_buttons_layout.addWidget(deselect_all_button)
        selection_buttons_layout.addStretch()
        
        layout.addLayout(selection_buttons_layout)

        # Boutons d'action
        action_buttons_layout = QHBoxLayout()
        action_buttons_layout.setContentsMargins(0, 15, 0, 0)
        action_buttons_layout.setSpacing(10)

        # Bouton Annuler
        cancel_button = QPushButton("Annuler")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        cancel_button.clicked.connect(self.reject)

        # Bouton Importer
        import_button = QPushButton("Importer les articles sélectionnés")
        import_button.setStyleSheet("""
            QPushButton {
                background-color: #1E3A8A;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1A56DB;
            }
        """)
        import_button.clicked.connect(self.importer_articles)

        action_buttons_layout.addStretch()
        action_buttons_layout.addWidget(cancel_button)
        action_buttons_layout.addWidget(import_button)

        layout.addLayout(action_buttons_layout)

    def charger_bons_commande(self):
        """Charge la liste des bons de commande"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT b.id, b.numero, b.objet, c.nom as client_nom, b.date_creation
                FROM bons_commande b
                LEFT JOIN clients c ON b.client_id = c.id
                ORDER BY b.date_creation DESC
            """)
            bons = cursor.fetchall()

            self.bon_commande_combo.clear()
            self.bon_commande_combo.addItem("Sélectionner un bon de commande", None)

            for bon in bons:
                # Afficher le numéro, le client et la date
                display_text = f"{bon['numero']}"
                if bon['client_nom']:
                    display_text += f" - {bon['client_nom']}"
                if bon['date_creation']:
                    display_text += f" ({bon['date_creation']})"

                self.bon_commande_combo.addItem(display_text, {
                    "id": bon['id'],
                    "numero": bon['numero'],
                    "objet": bon['objet'],
                    "client_nom": bon['client_nom']
                })
        except sqlite3.Error as e:
            print(f"Erreur lors du chargement des bons de commande: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des bons de commande: {str(e)}")

    def bon_commande_selectionne(self):
        """Appelé lorsqu'un bon de commande est sélectionné"""
        bon_data = self.bon_commande_combo.currentData()
        
        if bon_data:
            self.charger_articles_bon_commande(bon_data["id"])
        else:
            # Vider le tableau si aucun bon n'est sélectionné
            self.articles_table.setRowCount(0)

    def charger_articles_bon_commande(self, bon_id):
        """Charge les articles du bon de commande sélectionné"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT designation, unite, quantite, prix_unitaire_ht, total_ht
                FROM lignes_bon_commande
                WHERE bon_id = ?
                ORDER BY id
            """, (bon_id,))
            articles = cursor.fetchall()

            self.articles_table.setRowCount(0)
            
            for row_index, article in enumerate(articles):
                self.articles_table.insertRow(row_index)
                
                # Checkbox de sélection
                checkbox = QCheckBox()
                checkbox_widget = QWidget()
                checkbox_layout = QHBoxLayout(checkbox_widget)
                checkbox_layout.addWidget(checkbox)
                checkbox_layout.setAlignment(Qt.AlignCenter)
                checkbox_layout.setContentsMargins(0, 0, 0, 0)
                self.articles_table.setCellWidget(row_index, 0, checkbox_widget)
                
                # Désignation
                self.articles_table.setItem(row_index, 1, QTableWidgetItem(article['designation']))
                
                # Unité
                self.articles_table.setItem(row_index, 2, QTableWidgetItem(article['unite'] or ""))
                
                # Quantité
                self.articles_table.setItem(row_index, 3, QTableWidgetItem(str(article['quantite'])))
                
                # Prix unitaire HT
                self.articles_table.setItem(row_index, 4, QTableWidgetItem(f"{article['prix_unitaire_ht']:.2f} DH"))
                
                # Total HT
                self.articles_table.setItem(row_index, 5, QTableWidgetItem(f"{article['total_ht']:.2f} DH"))

        except sqlite3.Error as e:
            print(f"Erreur lors du chargement des articles: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des articles: {str(e)}")

    def selectionner_tout(self):
        """Sélectionne tous les articles"""
        for row in range(self.articles_table.rowCount()):
            checkbox_widget = self.articles_table.cellWidget(row, 0)
            if checkbox_widget:
                checkbox = checkbox_widget.findChild(QCheckBox)
                if checkbox:
                    checkbox.setChecked(True)

    def deselectionner_tout(self):
        """Désélectionne tous les articles"""
        for row in range(self.articles_table.rowCount()):
            checkbox_widget = self.articles_table.cellWidget(row, 0)
            if checkbox_widget:
                checkbox = checkbox_widget.findChild(QCheckBox)
                if checkbox:
                    checkbox.setChecked(False)

    def importer_articles(self):
        """Importe les articles sélectionnés"""
        self.selected_articles = []
        
        for row in range(self.articles_table.rowCount()):
            checkbox_widget = self.articles_table.cellWidget(row, 0)
            if checkbox_widget:
                checkbox = checkbox_widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    # Récupérer les données de l'article
                    designation = self.articles_table.item(row, 1).text()
                    unite = self.articles_table.item(row, 2).text()
                    quantite = int(self.articles_table.item(row, 3).text())
                    prix_text = self.articles_table.item(row, 4).text().replace(" DH", "")
                    prix_unitaire_ht = float(prix_text)
                    
                    self.selected_articles.append({
                        'designation': designation,
                        'unite': unite,
                        'quantite': quantite,
                        'prix_unitaire_ht': prix_unitaire_ht
                    })
        
        if not self.selected_articles:
            QMessageBox.warning(self, "Attention", "Veuillez sélectionner au moins un article à importer.")
            return
        
        self.accept()

    def get_selected_articles(self):
        """Retourne la liste des articles sélectionnés"""
        return self.selected_articles
