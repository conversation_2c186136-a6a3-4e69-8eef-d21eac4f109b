from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QFrame, QSizePolicy)
from PySide6.QtCore import Qt, Signal

from ..theme import COLORS, BORDER_RADIUS, SPACING
from ..icons.icons import *

class ListViewContainer(QWidget):
    """
    Conteneur réutilisable pour afficher une liste d'éléments avec un bouton d'ajout.
    Ce composant implémente l'approche minimaliste demandée pour toutes les sections.
    """
    
    # Signal émis lorsque le bouton d'ajout est cliqué
    add_button_clicked = Signal()
    
    def __init__(self, title, icon_svg, add_button_text, parent=None):
        """
        Initialise le conteneur de liste.
        
        Args:
            title (str): Titre de la section
            icon_svg (str): SVG de l'icône à afficher
            add_button_text (str): Texte du bouton d'ajout
            parent (QWidget, optional): Widget parent
        """
        super().__init__(parent)
        self.title = title
        self.icon_svg = icon_svg
        self.add_button_text = add_button_text
        self.setup_ui()
    
    def setup_ui(self):
        """Configure l'interface utilisateur du conteneur"""
        layout = QVBoxLayout(self)
        layout.setSpacing(int(SPACING['lg'].replace('px', '')))
        layout.setContentsMargins(int(SPACING['md'].replace('px', '')),
                                 int(SPACING['md'].replace('px', '')),
                                 int(SPACING['md'].replace('px', '')),
                                 int(SPACING['md'].replace('px', '')))

        # Conteneur principal (carte)
        self.main_container = QFrame()
        self.main_container.setObjectName("card")
        self.main_container.setStyleSheet(f"""
            #card {{
                background-color: {COLORS['surface']};
                border-radius: {BORDER_RADIUS['md']};
                border: 1px solid {COLORS['divider']};
                padding: {SPACING['md']};
            }}
        """)
        main_layout = QVBoxLayout(self.main_container)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # En-tête avec titre et bouton d'ajout
        header_container = QWidget()
        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(0, 0, 0, 0)

        # Icône et titre
        title_container = QWidget()
        title_layout = QHBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(10)

        # Icône
        icon_label = QLabel()
        icon_label.setText(svg_to_icon_html(self.icon_svg, COLORS['primary'], 32))
        icon_label.setFixedSize(32, 32)
        title_layout.addWidget(icon_label)

        # Titre
        title_label = QLabel(f"Liste des {self.title}")
        title_label.setObjectName("page_title")
        title_label.setStyleSheet(f"""
            font-size: 22px;
            font-weight: bold;
            color: {COLORS['primary']};
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        header_layout.addWidget(title_container)
        header_layout.addStretch()

        # Bouton pour ajouter un élément (plus grand et plus visible)
        self.add_button = QPushButton(f"+ {self.add_button_text}")
        self.add_button.setObjectName("primary_button")
        self.add_button.setFixedHeight(44)
        self.add_button.setMinimumWidth(180)
        self.add_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLORS['primary']};
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: {BORDER_RADIUS['md']};
                font-weight: bold;
                font-size: 15px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }}
            QPushButton:hover {{
                background-color: {COLORS['primary_light']};
            }}
            QPushButton:pressed {{
                background-color: {COLORS['primary_dark']};
            }}
        """)
        self.add_button.clicked.connect(self.add_button_clicked.emit)
        header_layout.addWidget(self.add_button)

        main_layout.addWidget(header_container)

        # Barre de recherche
        search_container = QFrame()
        search_container.setObjectName("search_container")
        search_container.setStyleSheet(f"""
            #search_container {{
                background-color: {COLORS['surface']};
                border-radius: {BORDER_RADIUS['md']};
                border: 1px solid {COLORS['divider']};
                padding: 8px;
            }}
        """)
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(10, 10, 10, 10)

        search_icon = QLabel()
        search_icon.setText(svg_to_icon_html(SEARCH_ICON, COLORS['text_secondary'], 20))
        search_icon.setFixedSize(20, 20)
        search_layout.addWidget(search_icon)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(f"Rechercher un {self.title.lower()[:-1]}...")
        self.search_input.setStyleSheet("""
            border: none;
            font-size: 14px;
            padding: 5px;
        """)
        search_layout.addWidget(self.search_input)

        main_layout.addWidget(search_container)

        # Conteneur pour le tableau (sera rempli par la classe dérivée)
        self.table_container = QFrame()
        self.table_container.setObjectName("table_container")
        self.table_container.setStyleSheet(f"""
            #table_container {{
                background-color: {COLORS['surface']};
                border-radius: {BORDER_RADIUS['md']};
                border: 1px solid {COLORS['divider']};
            }}
        """)
        self.table_layout = QVBoxLayout(self.table_container)
        self.table_layout.setContentsMargins(0, 0, 0, 0)
        
        main_layout.addWidget(self.table_container)
        
        # Ajouter le conteneur principal au layout
        layout.addWidget(self.main_container)
    
    def set_table(self, table_widget):
        """
        Définit le tableau à afficher dans le conteneur.
        
        Args:
            table_widget (QTableWidget): Le tableau à afficher
        """
        # Supprimer tous les widgets existants du conteneur de tableau
        while self.table_layout.count():
            item = self.table_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        # Ajouter le nouveau tableau
        self.table_layout.addWidget(table_widget)
    
    def connect_search(self, filter_function):
        """
        Connecte la fonction de filtrage à la barre de recherche.
        
        Args:
            filter_function (function): Fonction à appeler lorsque le texte de recherche change
        """
        self.search_input.textChanged.connect(filter_function)
