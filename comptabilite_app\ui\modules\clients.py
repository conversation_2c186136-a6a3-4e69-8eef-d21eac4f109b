from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                              QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox)
from PySide6.QtCore import Qt

# Importer les styles modernes et les icônes
from ..theme import COLORS
from ..icons.icons import CLIENTS_ICON, EDIT_ICON, DELETE_ICON, svg_to_icon_html

# Importer le dialogue client
from ..components.client_dialog import ClientDialog

# Importer les composants réutilisables
from ..components.list_view_container import ListViewContainer

class ClientsModule(QWidget):
    """
    Module de gestion des clients avec une approche minimaliste.
    Affiche uniquement la liste des clients avec un bouton d'ajout proéminent.
    Les formulaires d'ajout et de modification sont dans des boîtes de dialogue modales.
    """

    def __init__(self, db_manager, signals=None):
        super().__init__()
        self.db_manager = db_manager
        self.signals = signals  # Signaux pour la communication entre modules
        self.setup_ui()
        self.load_clients()

    def setup_ui(self):
        """Configure l'interface utilisateur du module"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Créer le conteneur de liste
        self.list_container = ListViewContainer(
            title="Clients",
            icon_svg=CLIENTS_ICON,
            add_button_text="Ajouter un client"
        )
        self.list_container.add_button_clicked.connect(self.show_add_client_dialog)

        # Créer le tableau des clients
        self.clients_table = QTableWidget(0, 7)
        self.clients_table.setHorizontalHeaderLabels([
            "Code", "Nom", "ICE", "IF", "Téléphone", "Adresse", "Actions"
        ])

        # Appliquer les styles du tableau
        self.clients_table.setStyleSheet(f"""
            QTableWidget {{
                border: none;
                gridline-color: {COLORS['divider']};
            }}
            QHeaderView::section {{
                background-color: {COLORS['hover_bg']};
                color: {COLORS['text_primary']};
                padding: 12px;
                border: none;
                border-bottom: 1px solid {COLORS['divider']};
                font-weight: bold;
            }}
            QTableWidget::item {{
                padding: 12px;
                border-bottom: 1px solid {COLORS['divider']};
            }}
        """)
        self.clients_table.setAlternatingRowColors(True)

        # Configuration des colonnes
        self.clients_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Code
        self.clients_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)  # Nom
        self.clients_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)  # ICE
        self.clients_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # IF
        self.clients_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Téléphone
        self.clients_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Stretch)  # Adresse
        self.clients_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Actions

        self.clients_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.clients_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.clients_table.verticalHeader().setVisible(False)  # Cacher les numéros de ligne

        # Ajouter le tableau au conteneur
        self.list_container.set_table(self.clients_table)

        # Connecter la recherche
        self.list_container.connect_search(self.filter_clients)

        # Ajouter le conteneur au layout principal
        layout.addWidget(self.list_container)

    def load_clients(self):
        """Charge les clients depuis la base de données"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT * FROM clients ORDER BY code")
        clients = cursor.fetchall()

        self.clients_table.setRowCount(0)

        for row_num, client in enumerate(clients):
            self.clients_table.insertRow(row_num)

            # Ajouter les données du client
            self.clients_table.setItem(row_num, 0, QTableWidgetItem(client['code'] or ""))
            self.clients_table.setItem(row_num, 1, QTableWidgetItem(client['nom']))
            self.clients_table.setItem(row_num, 2, QTableWidgetItem(client['ice'] or ""))
            self.clients_table.setItem(row_num, 3, QTableWidgetItem(client['if_fiscal'] or ""))
            self.clients_table.setItem(row_num, 4, QTableWidgetItem(client['telephone'] or ""))
            self.clients_table.setItem(row_num, 5, QTableWidgetItem(client['adresse'] or ""))

            # Créer les boutons d'action
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(2, 2, 2, 2)
            actions_layout.setSpacing(8)

            # Bouton d'édition
            edit_btn = QWidget()
            edit_btn.setFixedSize(36, 36)
            edit_btn.setStyleSheet(f"""
                QWidget {{
                    background-color: {COLORS['surface']};
                    border: 1px solid {COLORS['secondary']};
                    border-radius: 4px;
                }}
                QWidget:hover {{
                    background-color: {COLORS['secondary_light']};
                }}
            """)
            edit_layout = QHBoxLayout(edit_btn)
            edit_layout.setContentsMargins(0, 0, 0, 0)
            edit_icon = QLabel()
            edit_icon.setText(svg_to_icon_html(EDIT_ICON, COLORS['secondary'], 18))
            edit_layout.addWidget(edit_icon)
            edit_btn.mousePressEvent = lambda _, id=client['id']: self.show_edit_client_dialog(id)

            # Bouton de suppression
            delete_btn = QWidget()
            delete_btn.setFixedSize(36, 36)
            delete_btn.setStyleSheet(f"""
                QWidget {{
                    background-color: {COLORS['surface']};
                    border: 1px solid {COLORS['error']};
                    border-radius: 4px;
                }}
                QWidget:hover {{
                    background-color: {COLORS['error']};
                }}
            """)
            delete_layout = QHBoxLayout(delete_btn)
            delete_layout.setContentsMargins(0, 0, 0, 0)
            delete_icon = QLabel()
            delete_icon.setText(svg_to_icon_html(DELETE_ICON, COLORS['error'], 18))
            delete_layout.addWidget(delete_icon)
            delete_btn.mousePressEvent = lambda _, id=client['id'], nom=client['nom']: self.delete_client(id, nom)

            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(delete_btn)
            actions_layout.addStretch()

            self.clients_table.setCellWidget(row_num, 6, actions_widget)

            # Stocker l'ID du client dans la première colonne (invisible)
            self.clients_table.item(row_num, 0).setData(Qt.UserRole, client['id'])

    def show_add_client_dialog(self):
        """Affiche la boîte de dialogue pour ajouter un client"""
        dialog = ClientDialog(self.db_manager, parent=self)
        dialog.client_saved.connect(self.on_client_saved)
        dialog.exec()

    def show_edit_client_dialog(self, client_id):
        """Affiche la boîte de dialogue pour modifier un client"""
        dialog = ClientDialog(self.db_manager, client_id=client_id, parent=self)
        dialog.client_saved.connect(self.on_client_saved)
        dialog.exec()

    def on_client_saved(self):
        """Appelé lorsqu'un client est ajouté ou modifié"""
        self.load_clients()

        # Émettre le signal pour informer les autres modules
        if self.signals:
            self.signals.clients_changed.emit()

    def delete_client(self, client_id, nom):
        """Supprime un client"""
        confirm = QMessageBox.question(
            self, "Confirmation",
            f"Êtes-vous sûr de vouloir supprimer le client {nom} ?\nCette action est irréversible.",
            QMessageBox.Yes | QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("DELETE FROM clients WHERE id = ?", (client_id,))
            self.db_manager.conn.commit()

            self.load_clients()

            # Émettre le signal pour informer les autres modules
            if self.signals:
                self.signals.clients_changed.emit()

            QMessageBox.information(self, "Succès", f"Client {nom} supprimé avec succès.")

    def filter_clients(self, text):
        """Filtre les clients en fonction du texte de recherche"""
        search_text = text.lower()

        for row in range(self.clients_table.rowCount()):
            match_found = False

            for col in range(6):  # Exclure la colonne des actions
                item = self.clients_table.item(row, col)
                if item and search_text in item.text().lower():
                    match_found = True
                    break

            self.clients_table.setRowHidden(row, not match_found)
