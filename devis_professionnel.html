
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض أسعار احترافي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
            color: #2c3e50;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
            font-size: 28px;
            font-weight: bold;
        }
        
        .info-section {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 3px solid #e9ecef;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .info-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-left: 4px solid #4a90e2;
        }
        
        .field-group {
            margin-bottom: 15px;
        }
        
        .field-label {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
            display: block;
        }
        
        .field-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 5px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .field-input:focus {
            outline: none;
            border-color: #4a90e2;
        }
        
        .dropdown {
            background: white;
            cursor: pointer;
        }
        
        .products-section {
            padding: 30px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            padding: 15px;
            background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);
            border-radius: 10px;
        }
        
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .products-table th {
            background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: bold;
            font-size: 14px;
        }
        
        .products-table td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #e9ecef;
            background: white;
        }
        
        .products-table tr:nth-child(even) td {
            background: #f8f9fa;
        }
        
        .products-table tr:hover td {
            background: #e3f2fd;
        }
        
        .products-table input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
        }
        
        .totals-section {
            background: #f8f9fa;
            padding: 30px;
            border-top: 3px solid #e9ecef;
        }
        
        .totals-grid {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 20px;
            align-items: start;
        }
        
        .totals-table {
            margin-left: auto;
            min-width: 300px;
        }
        
        .totals-table tr {
            border-bottom: 1px solid #ddd;
        }
        
        .totals-table td {
            padding: 10px 15px;
            font-size: 16px;
        }
        
        .totals-table .label {
            font-weight: bold;
            text-align: right;
            background: #e9ecef;
        }
        
        .totals-table .value {
            text-align: center;
            background: white;
            font-weight: bold;
        }
        
        .total-final {
            background: linear-gradient(135deg, #d5edda 0%, #c3e6cb 100%) !important;
            font-size: 18px !important;
            color: #155724 !important;
        }
        
        .amount-words {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #4a90e2;
            margin-bottom: 20px;
        }
        
        .amount-words .label {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .amount-words .value {
            font-style: italic;
            color: #7f8c8d;
            font-size: 16px;
        }
        
        .controls {
            padding: 30px;
            background: #f8f9fa;
            text-align: center;
            border-top: 3px solid #e9ecef;
        }
        
        .btn {
            padding: 12px 30px;
            margin: 0 10px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            text-transform: uppercase;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #007bff 0%, #6f42c1 100%);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-size: 12px;
            color: #856404;
        }
        
        .hidden-print {
            background: #ffe6e6 !important;
            border: 2px dashed #ff6b6b !important;
        }
        
        @media print {
            .hidden-print, .note, .controls {
                display: none !important;
            }
            
            body {
                background: white;
                padding: 0;
            }
            
            .container {
                box-shadow: none;
                border-radius: 0;
            }
        }
        
        .final-statement {
            text-align: center;
            font-style: italic;
            color: #7f8c8d;
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- العنوان الرئيسي -->
        <div class="header">
            عرض أسعار / DEVIS
        </div>
        
        <!-- معلومات أساسية -->
        <div class="info-section">
            <div class="info-grid">
                <div class="info-card">
                    <div class="field-group">
                        <label class="field-label">رقم العرض:</label>
                        <input type="text" class="field-input" value="DEV-2024-001" readonly>
                    </div>
                    <div class="field-group">
                        <label class="field-label">التاريخ:</label>
                        <input type="date" class="field-input" value="2024-01-01">
                    </div>
                </div>
                
                <div class="info-card">
                    <div class="field-group">
                        <label class="field-label">نوع العميل:</label>
                        <select class="field-input dropdown" id="clientType" onchange="toggleClientFields()">
                            <option value="Privé">Privé</option>
                            <option value="Public">Public</option>
                        </select>
                        <div class="note">اختيار Privé/Public</div>
                    </div>
                    
                    <div class="field-group" id="serviceNature">
                        <label class="field-label">طبيعة الخدمة:</label>
                        <select class="field-input dropdown">
                            <option value="Travaux">Travaux</option>
                            <option value="Fourniture">Fourniture</option>
                        </select>
                        <div class="note">لا يظهر في الطباعة إذا كان Public</div>
                    </div>
                </div>
                
                <div class="info-card">
                    <div class="field-group">
                        <label class="field-label">العميل:</label>
                        <select class="field-input dropdown" onchange="loadClientData()">
                            <option value="Client A">Client A</option>
                            <option value="Client B">Client B</option>
                            <option value="Client C">Client C</option>
                            <option value="manual">إدخال يدوي</option>
                        </select>
                        <div class="note">اختيار العميل</div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">الموضوع:</label>
                        <input type="text" class="field-input" value="Fourniture matériel informatique">
                    </div>
                </div>
                
                <div class="info-card hidden-print" id="privateFields">
                    <div class="field-group">
                        <label class="field-label">العنوان:</label>
                        <input type="text" class="field-input" value="123 Rue Example" readonly>
                        <div class="note">يظهر تلقائياً إذا كان Privé - خارج الطباعة</div>
                    </div>
                    
                    <div class="field-group">
                        <label class="field-label">ICE:</label>
                        <input type="text" class="field-input" value="ICE001" readonly>
                        <div class="note">يظهر تلقائياً إذا كان Privé - خارج الطباعة</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- جدول المنتجات -->
        <div class="products-section">
            <div class="section-title">تفاصيل المنتجات والخدمات</div>
            
            <table class="products-table">
                <thead>
                    <tr>
                        <th>N°</th>
                        <th>المنتج/الخدمة</th>
                        <th>الوحدة</th>
                        <th>الكمية</th>
                        <th>سعر الشراء HT</th>
                        <th>نسبة الربح</th>
                        <th>سعر البيع HT</th>
                        <th>المجموع HT</th>
                    </tr>
                </thead>
                <tbody id="productsTable">
                    <tr>
                        <td>1</td>
                        <td>
                            <select class="field-input dropdown" onchange="loadProductData(this, 1)">
                                <option value="">اختيار المنتج</option>
                                <option value="product1">منتج 1</option>
                                <option value="product2">منتج 2</option>
                                <option value="product3">منتج 3</option>
                            </select>
                        </td>
                        <td><input type="text" value="تلقائي" readonly></td>
                        <td><input type="number" value="1" onchange="calculateRow(1)"></td>
                        <td><input type="number" value="100" readonly></td>
                        <td><input type="number" value="1.2" step="0.1" onchange="calculateRow(1)"></td>
                        <td><input type="number" id="sellPrice1" value="120" readonly></td>
                        <td><input type="number" id="total1" value="120" readonly></td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>
                            <select class="field-input dropdown" onchange="loadProductData(this, 2)">
                                <option value="">اختيار المنتج</option>
                                <option value="product1">منتج 1</option>
                                <option value="product2">منتج 2</option>
                                <option value="product3">منتج 3</option>
                            </select>
                        </td>
                        <td><input type="text" value="تلقائي" readonly></td>
                        <td><input type="number" value="1" onchange="calculateRow(2)"></td>
                        <td><input type="number" value="50" readonly></td>
                        <td><input type="number" value="1.1" step="0.1" onchange="calculateRow(2)"></td>
                        <td><input type="number" id="sellPrice2" value="55" readonly></td>
                        <td><input type="number" id="total2" value="55" readonly></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- الحسابات النهائية -->
        <div class="totals-section">
            <div class="totals-grid">
                <div class="amount-words">
                    <div class="label">المبلغ بالأحرف:</div>
                    <div class="value" id="amountWords">مائة وخمسة وسبعون درهماً</div>
                </div>
                
                <table class="totals-table">
                    <tr>
                        <td class="label">المجموع HT:</td>
                        <td class="value" id="subtotal">175.00</td>
                    </tr>
                    <tr>
                        <td class="label">TVA 20%:</td>
                        <td class="value" id="tva">35.00</td>
                    </tr>
                    <tr>
                        <td class="label total-final">المجموع TTC:</td>
                        <td class="value total-final" id="total">210.00</td>
                    </tr>
                </table>
            </div>
            
            <div class="final-statement">
                Arrêté le présent devis à la somme de : <strong><span id="finalAmount">210.00</span> DH TTC</strong>
            </div>
        </div>
        
        <!-- أزرار التحكم -->
        <div class="controls hidden-print">
            <button class="btn btn-success" onclick="validateDevis()">Valider</button>
            <button class="btn btn-warning" onclick="clearDevis()">Supprimer</button>
            <button class="btn btn-primary" onclick="printDevis()">Imprimer</button>
        </div>
    </div>
    
    <script>
        function toggleClientFields() {
            const clientType = document.getElementById('clientType').value;
            const privateFields = document.getElementById('privateFields');
            const serviceNature = document.getElementById('serviceNature');
            
            if (clientType === 'Public') {
                privateFields.style.display = 'none';
                serviceNature.style.display = 'none';
            } else {
                privateFields.style.display = 'block';
                serviceNature.style.display = 'block';
            }
        }
        
        function loadClientData() {
            // محاكاة تحميل بيانات العميل
            const clientSelect = event.target;
            const selectedClient = clientSelect.value;
            
            // بيانات العملاء التجريبية
            const clientsData = {
                'Client A': { address: '123 Rue Example', ice: 'ICE001' },
                'Client B': { address: '456 Avenue Test', ice: 'ICE002' },
                'Client C': { address: '789 Boulevard Demo', ice: 'ICE003' }
            };
            
            if (clientsData[selectedClient]) {
                document.querySelector('#privateFields input[type="text"]').value = clientsData[selectedClient].address;
                document.querySelectorAll('#privateFields input[type="text"]')[1].value = clientsData[selectedClient].ice;
            }
        }
        
        function loadProductData(select, rowNum) {
            // محاكاة تحميل بيانات المنتج
            const selectedProduct = select.value;
            const productsData = {
                'product1': { unit: 'قطعة', buyPrice: 100 },
                'product2': { unit: 'متر', buyPrice: 50 },
                'product3': { unit: 'كيلو', buyPrice: 75 }
            };
            
            if (productsData[selectedProduct]) {
                const row = select.closest('tr');
                row.querySelector('input[type="text"]').value = productsData[selectedProduct].unit;
                row.querySelectorAll('input[type="number"]')[1].value = productsData[selectedProduct].buyPrice;
                calculateRow(rowNum);
            }
        }
        
        function calculateRow(rowNum) {
            const row = document.querySelector(`#total${rowNum}`).closest('tr');
            const quantity = parseFloat(row.querySelectorAll('input[type="number"]')[0].value) || 0;
            const buyPrice = parseFloat(row.querySelectorAll('input[type="number"]')[1].value) || 0;
            const margin = parseFloat(row.querySelectorAll('input[type="number"]')[2].value) || 1;
            
            const sellPrice = buyPrice * margin;
            const total = quantity * sellPrice;
            
            document.getElementById(`sellPrice${rowNum}`).value = sellPrice.toFixed(2);
            document.getElementById(`total${rowNum}`).value = total.toFixed(2);
            
            calculateTotals();
        }
        
        function calculateTotals() {
            let subtotal = 0;
            for (let i = 1; i <= 2; i++) {
                const totalElement = document.getElementById(`total${i}`);
                if (totalElement) {
                    subtotal += parseFloat(totalElement.value) || 0;
                }
            }
            
            const tva = subtotal * 0.2;
            const total = subtotal + tva;
            
            document.getElementById('subtotal').textContent = subtotal.toFixed(2);
            document.getElementById('tva').textContent = tva.toFixed(2);
            document.getElementById('total').textContent = total.toFixed(2);
            document.getElementById('finalAmount').textContent = total.toFixed(2);
            
            // تحديث المبلغ بالأحرف (مبسط)
            document.getElementById('amountWords').textContent = numberToWords(total) + ' درهماً';
        }
        
        function numberToWords(num) {
            // تحويل مبسط للأرقام إلى كلمات
            const ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
            const tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
            const hundreds = ['', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'];
            
            if (num < 10) return ones[Math.floor(num)];
            if (num < 100) return tens[Math.floor(num/10)] + ' ' + ones[num%10];
            if (num < 1000) return hundreds[Math.floor(num/100)] + ' ' + numberToWords(num%100);
            
            return Math.floor(num).toString(); // للأرقام الكبيرة
        }
        
        function validateDevis() {
            alert('تم التحقق من صحة عرض الأسعار بنجاح!');
        }
        
        function clearDevis() {
            if (confirm('هل أنت متأكد من حذف جميع البيانات؟')) {
                location.reload();
            }
        }
        
        function printDevis() {
            window.print();
        }
        
        // تحميل أولي
        document.addEventListener('DOMContentLoaded', function() {
            calculateTotals();
            toggleClientFields();
        });
    </script>
</body>
</html>
    