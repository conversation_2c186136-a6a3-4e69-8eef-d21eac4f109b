from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                               QFrame, QTableWidget, QTableWidgetItem, QHeaderView,
                               QPushButton, QDateEdit, QComboBox, QLineEdit, QSizePolicy)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QColor, QIcon, QPixmap

from ..theme import *
from ..icons.icons import *
from ..utils import svg_to_icon_html, svg_to_pixmap

class StatCard(QFrame):
    """Carte statistique pour afficher des informations clés"""
    def __init__(self, title, value, icon_svg, color, currency=True, parent=None):
        super().__init__(parent)
        self.setObjectName("stat_card")
        self.setStyleSheet(f"""
            #stat_card {{
                background-color: {color};
                border-radius: 10px;
                padding: 15px;
                min-height: 120px;
            }}
            QLabel {{
                color: white;
            }}
            #card_title {{
                font-size: 16px;
                font-weight: bold;
            }}
            #card_value {{
                font-size: 24px;
                font-weight: bold;
            }}
            #card_currency {{
                font-size: 14px;
                margin-top: 5px;
            }}
        """)
        
        # Layout principal
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(5)
        
        # Icône et titre
        top_layout = QHBoxLayout()
        top_layout.setContentsMargins(0, 0, 0, 0)
        top_layout.setSpacing(10)
        
        # Icône
        icon_label = QLabel()
        icon_label.setFixedSize(32, 32)
        icon_label.setText(svg_to_icon_html(icon_svg, "white", 32))
        top_layout.addWidget(icon_label)
        
        # Titre
        title_label = QLabel(title)
        title_label.setObjectName("card_title")
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        top_layout.addWidget(title_label)
        top_layout.addStretch()
        
        layout.addLayout(top_layout)
        layout.addStretch()
        
        # Valeur
        value_layout = QHBoxLayout()
        value_layout.setContentsMargins(0, 0, 0, 0)
        value_layout.setSpacing(5)
        
        value_label = QLabel(value)
        value_label.setObjectName("card_value")
        value_layout.addWidget(value_label)
        
        if currency:
            currency_label = QLabel("DH")
            currency_label.setObjectName("card_currency")
            currency_label.setAlignment(Qt.AlignBottom | Qt.AlignLeft)
            value_layout.addWidget(currency_label)
        
        value_layout.addStretch()
        
        layout.addLayout(value_layout)

class DashboardArabicStyle(QWidget):
    """Module de tableau de bord inspiré du style arabe moderne"""
    def __init__(self, db_manager, signals, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.signals = signals
        self.setup_ui()
        
    def setup_ui(self):
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(20)
        
        # Titre de la page
        title_container = QWidget()
        title_layout = QHBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 20)
        
        page_title = QLabel("Gestion des Ventes")
        page_title.setObjectName("page_title")
        page_title.setStyleSheet("""
            #page_title {
                font-size: 24px;
                font-weight: bold;
                color: #333;
            }
        """)
        title_layout.addWidget(page_title)
        title_layout.addStretch()
        
        main_layout.addWidget(title_container)
        
        # Cartes statistiques
        stats_container = QWidget()
        stats_layout = QHBoxLayout(stats_container)
        stats_layout.setContentsMargins(0, 0, 0, 0)
        stats_layout.setSpacing(15)
        
        # Carte 1: Total des ventes
        total_sales_card = StatCard(
            "Total des Ventes", 
            "25,000", 
            CART_ICON, 
            "#26c6da"
        )
        stats_layout.addWidget(total_sales_card)
        
        # Carte 2: Nombre de fournisseurs
        suppliers_card = StatCard(
            "Nombre de Fournisseurs", 
            "45", 
            SUPPLIERS_ICON, 
            "#66bb6a",
            currency=False
        )
        stats_layout.addWidget(suppliers_card)
        
        # Carte 3: Total des retours
        returns_card = StatCard(
            "Total des Retours", 
            "1,500", 
            RETURN_ICON, 
            "#ff7043"
        )
        stats_layout.addWidget(returns_card)
        
        # Carte 4: Ventes nettes
        net_sales_card = StatCard(
            "Ventes Nettes", 
            "23,500", 
            MONEY_ICON, 
            "#42a5f5"
        )
        stats_layout.addWidget(net_sales_card)
        
        main_layout.addWidget(stats_container)
        
        # Onglets pour les différentes vues
        tabs_container = QWidget()
        tabs_layout = QHBoxLayout(tabs_container)
        tabs_layout.setContentsMargins(0, 0, 0, 0)
        tabs_layout.setSpacing(0)
        
        # Onglet 1: Factures de vente
        invoices_tab = QPushButton("Factures de Vente")
        invoices_tab.setObjectName("active_tab")
        invoices_tab.setCheckable(True)
        invoices_tab.setChecked(True)
        
        # Onglet 2: Mouvements des ventes
        movements_tab = QPushButton("Mouvements des Ventes")
        movements_tab.setObjectName("inactive_tab")
        movements_tab.setCheckable(True)
        
        # Onglet 3: Rapports des ventes
        reports_tab = QPushButton("Rapports des Ventes")
        reports_tab.setObjectName("inactive_tab")
        reports_tab.setCheckable(True)
        
        tabs_layout.addWidget(invoices_tab)
        tabs_layout.addWidget(movements_tab)
        tabs_layout.addWidget(reports_tab)
        tabs_layout.addStretch()
        
        main_layout.addWidget(tabs_container)
        
        # Filtres de date
        filters_container = QWidget()
        filters_layout = QHBoxLayout(filters_container)
        filters_layout.setContentsMargins(0, 0, 0, 10)
        filters_layout.setSpacing(10)
        
        # Date de début
        start_date_container = QWidget()
        start_date_container.setObjectName("date_filter")
        start_date_layout = QHBoxLayout(start_date_container)
        start_date_layout.setContentsMargins(10, 5, 10, 5)
        
        start_date_icon = QLabel()
        start_date_icon.setText(svg_to_icon_html(CALENDAR_ICON, "#666", 16))
        start_date_layout.addWidget(start_date_icon)
        
        start_date_label = QLabel("Sélectionner une date")
        start_date_layout.addWidget(start_date_label)
        
        filters_layout.addWidget(start_date_container)
        
        # Date de fin
        end_date_container = QWidget()
        end_date_container.setObjectName("date_filter")
        end_date_layout = QHBoxLayout(end_date_container)
        end_date_layout.setContentsMargins(10, 5, 10, 5)
        
        end_date_icon = QLabel()
        end_date_icon.setText(svg_to_icon_html(CALENDAR_ICON, "#666", 16))
        end_date_layout.addWidget(end_date_icon)
        
        end_date_label = QLabel("Sélectionner une date")
        end_date_layout.addWidget(end_date_label)
        
        filters_layout.addWidget(end_date_container)
        
        # Filtre client
        client_filter = QLineEdit()
        client_filter.setPlaceholderText("Rechercher un client...")
        client_filter.setObjectName("search_input")
        filters_layout.addWidget(client_filter)
        
        filters_layout.addStretch()
        
        main_layout.addWidget(filters_container)
        
        # Tableau des factures
        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(8)
        self.invoices_table.setHorizontalHeaderLabels([
            "N° Facture", "Date", "Client", "Montant Total", "Montant Payé", 
            "Montant Restant", "Statut", "Actions"
        ])
        
        # Configurer le tableau
        self.invoices_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.invoices_table.setAlternatingRowColors(True)
        self.invoices_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.invoices_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.invoices_table.setSelectionMode(QTableWidget.SingleSelection)
        
        # Ajouter des données de test
        self.populate_test_data()
        
        main_layout.addWidget(self.invoices_table)
        
        # Boutons d'action
        actions_container = QWidget()
        actions_layout = QHBoxLayout(actions_container)
        actions_layout.setContentsMargins(0, 10, 0, 0)
        actions_layout.setSpacing(10)
        
        # Bouton: Nouvelle facture
        new_invoice_btn = QPushButton("Nouvelle Facture de Vente")
        new_invoice_btn.setIcon(QIcon(svg_to_pixmap(CART_ICON, "#fff", 16)))
        new_invoice_btn.setObjectName("primary_button")
        actions_layout.addWidget(new_invoice_btn)
        
        # Bouton: Mouvements des ventes
        movements_btn = QPushButton("Mouvements des Ventes")
        movements_btn.setIcon(QIcon(svg_to_pixmap(ORDERS_ICON, "#fff", 16)))
        movements_btn.setObjectName("primary_button")
        actions_layout.addWidget(movements_btn)
        
        # Bouton: Rapports des ventes
        reports_btn = QPushButton("Rapports des Ventes")
        reports_btn.setIcon(QIcon(svg_to_pixmap(REPORT_ICON, "#fff", 16)))
        reports_btn.setObjectName("primary_button")
        actions_layout.addWidget(reports_btn)
        
        actions_layout.addStretch()
        
        main_layout.addWidget(actions_container)
        main_layout.addStretch()
        
        # Appliquer les styles
        self.setStyleSheet("""
            #date_filter {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            #active_tab {
                border-bottom: 2px solid #1E3A8A;
                color: #1E3A8A;
                font-weight: bold;
                background-color: transparent;
                padding: 10px 15px;
            }
            #inactive_tab {
                border-bottom: 1px solid #ddd;
                color: #666;
                background-color: transparent;
                padding: 10px 15px;
            }
            #search_input {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            #primary_button {
                background-color: #1E3A8A;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
            }
            #primary_button:hover {
                background-color: #2563EB;
            }
        """)
        
    def populate_test_data(self):
        """Ajoute des données de test au tableau des factures"""
        test_data = [
            ["INV-001", "2023-05-13", "Société Alpha Travaux", "5,250.50", "5,250.50", "0.00", "Payée"],
            ["INV-002", "2023-05-11", "Fondation Nour", "3,750.75", "2,000.00", "1,750.75", "Partiellement Payée"],
            ["INV-003", "2023-05-09", "Société Al-Amal", "8,500.00", "0.00", "8,500.00", "En attente"],
            ["INV-004", "2023-05-07", "Fondation Al-Fajr", "4,200.25", "4,200.25", "0.00", "Payée"],
            ["INV-005", "2023-05-04", "Société Al-Jadida", "3,300.00", "1,500.00", "1,800.00", "Partiellement Payée"]
        ]
        
        self.invoices_table.setRowCount(len(test_data))
        
        for row, data in enumerate(test_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                item.setTextAlignment(Qt.AlignCenter)
                
                # Colorer les statuts
                if col == 6:  # Colonne de statut
                    if value == "Payée":
                        item.setBackground(QColor("#e6f7e6"))  # Vert clair
                        item.setForeground(QColor("#2e7d32"))  # Vert foncé
                    elif value == "Partiellement Payée":
                        item.setBackground(QColor("#fff8e1"))  # Jaune clair
                        item.setForeground(QColor("#f57f17"))  # Jaune foncé
                    elif value == "En attente":
                        item.setBackground(QColor("#ffebee"))  # Rouge clair
                        item.setForeground(QColor("#c62828"))  # Rouge foncé
                
                self.invoices_table.setItem(row, col, item)
            
            # Ajouter un bouton d'actions
            actions_cell = QWidget()
            actions_layout = QHBoxLayout(actions_cell)
            actions_layout.setContentsMargins(5, 0, 5, 0)
            actions_layout.setAlignment(Qt.AlignCenter)
            
            view_btn = QPushButton()
            view_btn.setIcon(QIcon(svg_to_pixmap(EYE_ICON, "#1E3A8A", 16)))
            view_btn.setToolTip("Voir la facture")
            view_btn.setFixedSize(28, 28)
            view_btn.setObjectName("icon_button")
            
            edit_btn = QPushButton()
            edit_btn.setIcon(QIcon(svg_to_pixmap(EDIT_ICON, "#1E3A8A", 16)))
            edit_btn.setToolTip("Modifier la facture")
            edit_btn.setFixedSize(28, 28)
            edit_btn.setObjectName("icon_button")
            
            delete_btn = QPushButton()
            delete_btn.setIcon(QIcon(svg_to_pixmap(TRASH_ICON, "#1E3A8A", 16)))
            delete_btn.setToolTip("Supprimer la facture")
            delete_btn.setFixedSize(28, 28)
            delete_btn.setObjectName("icon_button")
            
            actions_layout.addWidget(view_btn)
            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(delete_btn)
            
            self.invoices_table.setCellWidget(row, 7, actions_cell)
