from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import mm, cm
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os
import datetime

class FactureGenerator:
    """Classe pour générer des factures PDF basées sur un modèle"""

    def __init__(self, output_path="factures"):
        """Initialise le générateur de factures

        Args:
            output_path (str): Chemin où les factures seront enregistrées
        """
        self.output_path = output_path

        # Créer le dossier de sortie s'il n'existe pas
        if not os.path.exists(output_path):
            os.makedirs(output_path)

        # Styles pour le document
        self.styles = getSampleStyleSheet()

        # Ajouter des styles personnalisés en vérifiant s'ils existent déjà
        self._add_style_if_not_exists('Facture_Title', {
            'parent': self.styles['Heading1'],
            'fontSize': 16,
            'alignment': 1,  # Centre
            'spaceAfter': 12
        })

        self._add_style_if_not_exists('Normal_Center', {
            'parent': self.styles['Normal'],
            'alignment': 1  # Centre
        })

        self._add_style_if_not_exists('Normal_Right', {
            'parent': self.styles['Normal'],
            'alignment': 2  # Droite
        })

        self._add_style_if_not_exists('Small', {
            'parent': self.styles['Normal'],
            'fontSize': 8
        })

        self._add_style_if_not_exists('Bold', {
            'parent': self.styles['Normal'],
            'fontName': 'Helvetica-Bold'
        })

    def generate_facture(self, facture_data):
        """Génère une facture PDF

        Args:
            facture_data (dict): Données de la facture
                - numero (str): Numéro de facture
                - date (str): Date de la facture
                - client (dict): Informations du client
                    - nom (str): Nom du client
                    - adresse (str): Adresse du client
                    - ice (str): ICE du client
                - lignes (list): Liste des lignes de facture
                    - designation (str): Désignation du produit
                    - unite (str): Unité
                    - quantite (int): Quantité
                    - prix_unitaire (float): Prix unitaire HT
                    - taux_tva (float): Taux de TVA (en %)
                - totaux (dict): Totaux de la facture
                    - total_ht (float): Total HT
                    - total_tva (float): Total TVA
                    - total_ttc (float): Total TTC

        Returns:
            str: Chemin vers le fichier PDF généré
        """
        # Nom du fichier de sortie
        filename = f"facture_{facture_data['numero'].replace('/', '_')}.pdf"
        output_file = os.path.join(self.output_path, filename)

        # Créer le document
        doc = SimpleDocTemplate(
            output_file,
            pagesize=A4,
            leftMargin=2*cm,
            rightMargin=2*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )

        # Calculer la largeur disponible pour le contenu
        doc_width = A4[0] - 4*cm  # Largeur de la page moins les marges gauche et droite

        # Contenu du document
        content = []

        # En-tête de la facture
        self._add_header(content, facture_data, doc_width)

        # Informations du client
        self._add_client_info(content, facture_data['client'], doc_width)

        # Tableau des articles
        self._add_articles_table(content, facture_data['lignes'], doc_width)

        # Totaux
        self._add_totals(content, facture_data['totaux'], doc_width)

        # Pied de page
        self._add_footer(content)

        # Générer le PDF
        doc.build(content)

        return output_file

    def _add_style_if_not_exists(self, name, style_dict):
        """Ajoute un style s'il n'existe pas déjà dans la feuille de styles

        Args:
            name (str): Nom du style
            style_dict (dict): Dictionnaire des propriétés du style
        """
        if name not in self.styles:
            parent = style_dict.pop('parent')
            style = ParagraphStyle(name=name, parent=parent, **style_dict)
            self.styles.add(style)

    def _add_header(self, content, facture_data, doc_width):
        """Ajoute l'en-tête de la facture"""
        # Ajouter un style pour le titre de l'entreprise
        self._add_style_if_not_exists('Company_Name', {
            'parent': self.styles['Heading1'],
            'fontSize': 18,
            'alignment': 0,  # Gauche
            'fontName': 'Helvetica-Bold',
            'textColor': colors.darkblue
        })

        # Ajouter un style pour les informations de l'entreprise
        self._add_style_if_not_exists('Company_Info', {
            'parent': self.styles['Normal'],
            'fontSize': 9,
            'alignment': 0  # Gauche
        })

        # Ajouter un style pour le titre de la facture
        self._add_style_if_not_exists('Invoice_Title', {
            'parent': self.styles['Heading1'],
            'fontSize': 16,
            'alignment': 1,  # Centre
            'fontName': 'Helvetica-Bold',
            'textColor': colors.darkblue,
            'spaceAfter': 10
        })

        # Ajouter un style pour les informations de la facture
        self._add_style_if_not_exists('Invoice_Info', {
            'parent': self.styles['Normal'],
            'fontSize': 10,
            'alignment': 2,  # Droite
            'fontName': 'Helvetica-Bold'
        })

        # Informations de l'entreprise (côté gauche)
        company_info = [
            [Paragraph("<font color='darkblue'><b>ISOLOC SERVICE</b></font>", self.styles['Company_Name'])],
            [Paragraph("Adresse: 123 Rue Example, Ville, Pays", self.styles['Company_Info'])],
            [Paragraph("Tél: +*********** 000", self.styles['Company_Info'])],
            [Paragraph("Email: <EMAIL>", self.styles['Company_Info'])],
            [Paragraph("ICE: 001654789000098", self.styles['Company_Info'])],
            [Paragraph("IF: 40253477", self.styles['Company_Info'])],
            [Paragraph("RC: 414653 CASABLANCA", self.styles['Company_Info'])]
        ]

        # Informations de la facture (côté droit)
        invoice_info = [
            [Paragraph("<font color='darkblue'><b>FACTURE</b></font>", self.styles['Invoice_Title'])],
            [Paragraph(f"<b>N° Facture:</b> {facture_data['numero']}", self.styles['Invoice_Info'])],
            [Paragraph(f"<b>Date:</b> {facture_data['date']}", self.styles['Invoice_Info'])],
            [Paragraph(f"<b>Échéance:</b> {facture_data.get('echeance', '')}", self.styles['Invoice_Info'])],
            [Paragraph(f"<b>Bon de commande:</b> {facture_data.get('bon_commande', '')}", self.styles['Invoice_Info'])],
            [Paragraph(f"<b>Mode de paiement:</b> {facture_data.get('mode_paiement', '')}", self.styles['Invoice_Info'])]
        ]

        # Créer une table pour l'en-tête avec deux colonnes
        header_data = [[Table(company_info), Table(invoice_info)]]
        header_table = Table(header_data, colWidths=[doc_width*0.6, doc_width*0.4])
        header_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('TOPPADDING', (0, 0), (-1, -1), 0),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 0),
        ]))

        content.append(header_table)
        content.append(Spacer(1, 15*mm))

        # Ligne de séparation
        content.append(Paragraph("<hr width='100%'/>", self.styles['Normal']))
        content.append(Spacer(1, 10*mm))

    def _add_client_info(self, content, client, doc_width):
        """Ajoute les informations du client"""
        # Ajouter un style pour le titre de la section client
        self._add_style_if_not_exists('Section_Title', {
            'parent': self.styles['Normal'],
            'fontSize': 12,
            'alignment': 0,  # Gauche
            'fontName': 'Helvetica-Bold',
            'textColor': colors.darkblue,
            'spaceBefore': 5,
            'spaceAfter': 5
        })

        # Titre de la section client
        content.append(Paragraph("<font color='darkblue'><b>CLIENT</b></font>", self.styles['Section_Title']))

        # Créer un cadre pour les informations du client
        client_data = [
            [Paragraph("<b>Nom/Raison sociale:</b>", self.styles['Bold']),
             Paragraph(client['nom'], self.styles['Normal'])],
            [Paragraph("<b>Adresse:</b>", self.styles['Bold']),
             Paragraph(client['adresse'], self.styles['Normal'])],
            [Paragraph("<b>ICE:</b>", self.styles['Bold']),
             Paragraph(client['ice'], self.styles['Normal'])],
            [Paragraph("<b>Téléphone:</b>", self.styles['Bold']),
             Paragraph(client.get('telephone', ''), self.styles['Normal'])],
            [Paragraph("<b>Email:</b>", self.styles['Bold']),
             Paragraph(client.get('email', ''), self.styles['Normal'])]
        ]

        client_table = Table(client_data, colWidths=[doc_width*0.2, doc_width*0.8])
        client_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 5),
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightgrey),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BOX', (0, 0), (-1, -1), 1, colors.grey),
        ]))

        content.append(client_table)
        content.append(Spacer(1, 10*mm))

    def _add_articles_table(self, content, lignes, doc_width):
        """Ajoute le tableau des articles"""
        # Titre de la section articles
        content.append(Paragraph("<font color='darkblue'><b>DÉTAIL DES ARTICLES</b></font>", self.styles['Section_Title']))

        # En-têtes du tableau
        headers = [
            Paragraph("<b>N°</b>", self.styles['Normal_Center']),
            Paragraph("<b>Désignation</b>", self.styles['Normal_Center']),
            Paragraph("<b>Unité</b>", self.styles['Normal_Center']),
            Paragraph("<b>Quantité</b>", self.styles['Normal_Center']),
            Paragraph("<b>Prix unitaire HT</b>", self.styles['Normal_Center']),
            Paragraph("<b>Total HT</b>", self.styles['Normal_Center'])
        ]

        # Données du tableau
        data = [headers]

        # Ajouter les lignes
        for i, ligne in enumerate(lignes):
            # Créer un style pour la désignation avec une description optionnelle
            designation_text = ligne['designation']
            if 'description' in ligne and ligne['description']:
                designation_text += f"<br/><font size='8' color='grey'>{ligne['description']}</font>"

            row = [
                Paragraph(str(i+1), self.styles['Normal_Center']),
                Paragraph(designation_text, self.styles['Normal']),
                Paragraph(ligne['unite'], self.styles['Normal_Center']),
                Paragraph(str(ligne['quantite']), self.styles['Normal_Right']),
                Paragraph(f"{ligne['prix_unitaire']:.2f} DH", self.styles['Normal_Right']),
                Paragraph(f"{ligne['quantite'] * ligne['prix_unitaire']:.2f} DH", self.styles['Normal_Right'])
            ]
            data.append(row)

        # Créer le tableau
        table = Table(data, colWidths=[doc_width*0.05, doc_width*0.4, doc_width*0.1,
                                       doc_width*0.1, doc_width*0.15, doc_width*0.2])

        # Style du tableau
        table_style = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (0, -1), 'CENTER'),  # Numéro centré
            ('ALIGN', (3, 0), (-1, -1), 'RIGHT'),  # Chiffres alignés à droite
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BOX', (0, 0), (-1, -1), 1, colors.grey),
            # Lignes alternées
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.whitesmoke, colors.white])
        ])

        table.setStyle(table_style)
        content.append(table)
        content.append(Spacer(1, 10*mm))

    def _add_totals(self, content, totaux, doc_width):
        """Ajoute les totaux de la facture"""
        # Ajouter un style pour les totaux
        self._add_style_if_not_exists('Total_Label', {
            'parent': self.styles['Normal'],
            'fontSize': 10,
            'alignment': 2,  # Droite
            'fontName': 'Helvetica-Bold'
        })

        self._add_style_if_not_exists('Total_Value', {
            'parent': self.styles['Normal'],
            'fontSize': 10,
            'alignment': 2,  # Droite
            'fontName': 'Helvetica-Bold'
        })

        self._add_style_if_not_exists('Grand_Total', {
            'parent': self.styles['Normal'],
            'fontSize': 12,
            'alignment': 2,  # Droite
            'fontName': 'Helvetica-Bold',
            'textColor': colors.darkblue
        })

        # Tableau des totaux
        totals_data = [
            ["", Paragraph("<b>Total HT:</b>", self.styles['Total_Label']),
             Paragraph(f"{totaux['total_ht']:.2f} DH", self.styles['Total_Value'])],
            ["", Paragraph("<b>TVA (20%):</b>", self.styles['Total_Label']),
             Paragraph(f"{totaux['total_tva']:.2f} DH", self.styles['Total_Value'])],
            ["", Paragraph("<b>Total TTC:</b>", self.styles['Grand_Total']),
             Paragraph(f"{totaux['total_ttc']:.2f} DH", self.styles['Grand_Total'])]
        ]

        totals_table = Table(totals_data, colWidths=[doc_width*0.6, doc_width*0.2, doc_width*0.2])
        totals_table.setStyle(TableStyle([
            ('LINEABOVE', (1, 2), (2, 2), 1, colors.black),  # Ligne au-dessus du total TTC
            ('BOTTOMPADDING', (0, 0), (-1, -1), 5),
            ('BACKGROUND', (1, 2), (2, 2), colors.lightgrey),  # Fond gris pour le total TTC
            ('GRID', (1, 2), (2, 2), 0.5, colors.grey),  # Bordure pour le total TTC
        ]))

        content.append(totals_table)

        # Montant en toutes lettres (version simplifiée sans num2words)
        montant_lettres = f"{totaux['total_ttc']:.2f} dirhams"

        # Ajouter un style pour le montant en lettres
        self._add_style_if_not_exists('Montant_Lettres', {
            'parent': self.styles['Normal'],
            'fontSize': 10,
            'alignment': 0,  # Gauche
            'fontName': 'Helvetica-Oblique',
            'textColor': colors.darkblue
        })

        content.append(Spacer(1, 10*mm))
        content.append(Paragraph(f"<b>Arrêté la présente facture à la somme de :</b> {montant_lettres}", self.styles['Montant_Lettres']))
        content.append(Spacer(1, 15*mm))

    def _add_footer(self, content):
        """Ajoute le pied de page de la facture"""
        # Ajouter un style pour les conditions de paiement
        self._add_style_if_not_exists('Payment_Terms', {
            'parent': self.styles['Normal'],
            'fontSize': 9,
            'alignment': 0,  # Gauche
            'fontName': 'Helvetica-Bold',
            'textColor': colors.darkblue
        })

        # Ajouter un style pour le pied de page
        self._add_style_if_not_exists('Footer', {
            'parent': self.styles['Small'],
            'fontSize': 8,
            'alignment': 1,  # Centre
            'textColor': colors.grey
        })

        # Conditions de paiement
        payment_terms = """
        <b>CONDITIONS DE PAIEMENT:</b><br/>
        - Paiement à réception de facture<br/>
        - Tout retard de paiement entraînera des pénalités calculées au taux légal en vigueur<br/>
        - Aucun escompte n'est accordé pour paiement anticipé
        """
        content.append(Paragraph(payment_terms, self.styles['Payment_Terms']))

        # Ligne de séparation
        content.append(Spacer(1, 10*mm))
        content.append(Paragraph("<hr width='100%'/>", self.styles['Normal']))
        content.append(Spacer(1, 5*mm))

        # Pied de page
        footer_text = """
        <para align="center">
        <b>ISOLOC SERVICE</b><br/>
        ICE: 001654789000098 - IF: 40253477 - RC: 414653 CASABLANCA<br/>
        Adresse: 123 Rue Example, Ville, Pays - Tél: +*********** 000 - Email: <EMAIL><br/>
        RIB: 007780002835000000654552 - CNSS: 5302205<br/><br/>
        Merci pour votre confiance!
        </para>
        """

        content.append(Paragraph(footer_text, self.styles['Footer']))


# Exemple d'utilisation
if __name__ == "__main__":
    # Données de test
    facture_data = {
        "numero": "F001-2023",
        "date": "22/05/2023",
        "client": {
            "nom": "Société Example",
            "adresse": "456 Avenue Test, Ville, Pays",
            "ice": "987654321"
        },
        "lignes": [
            {
                "designation": "Produit A",
                "unite": "Pièce",
                "quantite": 5,
                "prix_unitaire": 100.00,
                "taux_tva": 20
            },
            {
                "designation": "Produit B",
                "unite": "Kg",
                "quantite": 10,
                "prix_unitaire": 50.00,
                "taux_tva": 20
            }
        ],
        "totaux": {
            "total_ht": 1000.00,
            "total_tva": 200.00,
            "total_ttc": 1200.00
        }
    }

    # Générer la facture
    generator = FactureGenerator()
    output_file = generator.generate_facture(facture_data)
    print(f"Facture générée: {output_file}")
