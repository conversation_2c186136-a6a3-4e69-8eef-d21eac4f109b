from PySide6.QtWidgets import QTableWidgetItem, QHeaderView, QMessageBox
from PySide6.QtCore import Qt
import sqlite3

# Importer le module de base
from ..components.base_module import BaseModule
from ..icons.icons import SUPPLIERS_ICON

# Importer le dialogue fournisseur
from ..components.fournisseur_dialog import FournisseurDialog

class FournisseursModule(BaseModule):
    """Module de gestion des fournisseurs avec interface simplifiée"""
    
    def __init__(self, db_manager, signals=None):
        super().__init__(
            db_manager=db_manager,
            signals=signals,
            title="Gestion des Fournisseurs",
            description="Ajoutez, modifiez et supprimez des fournisseurs",
            icon=SUPPLIERS_ICON
        )
        
        # Configurer le bouton d'ajout
        self.add_button.setText("Ajouter un fournisseur")
        
        # Configurer le tableau
        self.setup_table()
        
        # Charger les données
        self.load_fournisseurs()
    
    def setup_table(self):
        """Configure le tableau des fournisseurs"""
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "Code", "Nom", "ICE", "IF", "Téléphone", "Adresse", "Actions"
        ])
        
        # Configuration des colonnes
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Code
        self.items_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)  # Nom
        self.items_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)  # ICE
        self.items_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # IF
        self.items_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Téléphone
        self.items_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Stretch)  # Adresse
        self.items_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Actions
        
        # Configurer la recherche
        self.search_input.setPlaceholderText("Rechercher un fournisseur...")
    
    def load_fournisseurs(self):
        """Charge les fournisseurs depuis la base de données"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT * FROM fournisseurs ORDER BY code")
        fournisseurs = cursor.fetchall()
        
        self.items_table.setRowCount(0)
        
        for row_num, fournisseur in enumerate(fournisseurs):
            self.items_table.insertRow(row_num)
            
            # Ajouter les données du fournisseur
            self.items_table.setItem(row_num, 0, QTableWidgetItem(fournisseur['code'] or ""))
            self.items_table.setItem(row_num, 1, QTableWidgetItem(fournisseur['nom']))
            self.items_table.setItem(row_num, 2, QTableWidgetItem(fournisseur['ice'] or ""))
            self.items_table.setItem(row_num, 3, QTableWidgetItem(fournisseur['if_fiscal'] or ""))
            self.items_table.setItem(row_num, 4, QTableWidgetItem(fournisseur['telephone'] or ""))
            self.items_table.setItem(row_num, 5, QTableWidgetItem(fournisseur['adresse'] or ""))
            
            # Créer les boutons d'action
            actions_widget = self.create_action_buttons(row_num, fournisseur['id'], fournisseur['nom'])
            self.items_table.setCellWidget(row_num, 6, actions_widget)
            
            # Stocker l'ID du fournisseur dans la première colonne (invisible)
            self.items_table.item(row_num, 0).setData(Qt.UserRole, fournisseur['id'])
    
    def show_add_dialog(self):
        """Affiche la boîte de dialogue pour ajouter un fournisseur"""
        dialog = FournisseurDialog(self.db_manager, parent=self)
        dialog.fournisseur_saved.connect(self.on_fournisseur_saved)
        dialog.exec()
    
    def show_edit_dialog(self, fournisseur_id):
        """Affiche la boîte de dialogue pour modifier un fournisseur"""
        dialog = FournisseurDialog(self.db_manager, fournisseur_id=fournisseur_id, parent=self)
        dialog.fournisseur_saved.connect(self.on_fournisseur_saved)
        dialog.exec()
    
    def on_fournisseur_saved(self):
        """Appelé lorsqu'un fournisseur est ajouté ou modifié"""
        self.load_fournisseurs()
        
        # Émettre le signal pour informer les autres modules
        if self.signals:
            self.signals.fournisseurs_changed.emit()
    
    def delete_item(self, fournisseur_id, fournisseur_name):
        """Supprime un fournisseur"""
        confirm = QMessageBox.question(
            self, "Confirmation",
            f"Êtes-vous sûr de vouloir supprimer le fournisseur {fournisseur_name} ?\nCette action est irréversible.",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if confirm == QMessageBox.Yes:
            try:
                cursor = self.db_manager.conn.cursor()
                cursor.execute("DELETE FROM fournisseurs WHERE id = ?", (fournisseur_id,))
                self.db_manager.conn.commit()
                
                self.load_fournisseurs()
                
                # Émettre le signal pour informer les autres modules
                if self.signals:
                    self.signals.fournisseurs_changed.emit()
                
                QMessageBox.information(self, "Succès", f"Fournisseur {fournisseur_name} supprimé avec succès.")
            except sqlite3.Error as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression du fournisseur: {str(e)}")
