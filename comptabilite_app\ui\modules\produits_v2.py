from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QHeaderView, QMessageBox, QDialog, QDoubleSpinBox, QSpinBox)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QRegularExpressionValidator
import sqlite3

# Importer les composants de base
from ..components.base_module import BaseModule
from ..components.base_list_view import BaseListView
from ..components.base_form_dialog import BaseFormDialog

# Importer les styles et icônes
from ..theme import COLORS, BORDER_RADIUS, SPACING
from ..icons.icons import PRODUCTS_ICON

class ProduitFormDialog(BaseFormDialog):
    """Boîte de dialogue pour ajouter ou modifier un produit"""

    def __init__(self, db_manager, produit_id=None, parent=None):
        self.produit_id = produit_id
        is_edit_mode = produit_id is not None
        title = "Modifier un produit" if is_edit_mode else "Ajouter un produit"

        super().__init__(db_manager, title, is_edit_mode, parent)

        # Initialiser les champs du formulaire
        self.setup_form()

        # Charger les données si on est en mode édition
        if is_edit_mode:
            self.load_data(produit_id)
        else:
            # Générer un nouveau code produit
            self.code_input.setText(self.db_manager.generer_code_produit())

    def setup_form(self):
        """Configure les champs du formulaire"""
        # Code produit
        self.code_input = QLineEdit()
        self.code_input.setReadOnly(True)
        self.code_input.setStyleSheet("background-color: #F3F4F6; color: #6B7280;")
        self.create_form_field("Code produit", self.code_input, row=0, col=0)

        # Désignation
        self.designation_input = QLineEdit()
        self.create_form_field("Désignation", self.designation_input, required=True, row=1, col=0)

        # Unité
        self.unite_input = QLineEdit()
        self.create_form_field("Unité", self.unite_input, row=2, col=0)

        # Prix d'achat
        self.prix_achat_input = QDoubleSpinBox()
        self.prix_achat_input.setRange(0, 999999.99)
        self.prix_achat_input.setDecimals(2)
        self.prix_achat_input.setSingleStep(1.0)
        self.prix_achat_input.setSuffix(" DH")
        self.create_form_field("Prix d'achat", self.prix_achat_input, row=0, col=1)

        # Prix de vente
        self.prix_vente_input = QDoubleSpinBox()
        self.prix_vente_input.setRange(0, 999999.99)
        self.prix_vente_input.setDecimals(2)
        self.prix_vente_input.setSingleStep(1.0)
        self.prix_vente_input.setSuffix(" DH")
        self.create_form_field("Prix de vente", self.prix_vente_input, row=1, col=1)

        # Stock
        self.stock_input = QSpinBox()
        self.stock_input.setRange(0, 999999)
        self.create_form_field("Stock", self.stock_input, row=2, col=1)

    def validate_form(self):
        """Valide les données du formulaire"""
        designation = self.designation_input.text().strip()
        if not designation:
            QMessageBox.warning(self, "Erreur", "La désignation du produit est obligatoire.")
            return False

        return True

    def save_data(self):
        """Enregistre les données du formulaire"""
        designation = self.designation_input.text().strip()
        code = self.code_input.text()

        cursor = self.db_manager.conn.cursor()

        try:
            if self.is_edit_mode:
                # Modification d'un produit existant
                cursor.execute(
                    """UPDATE produits SET
                       code = ?, designation = ?, unite = ?, prix_achat = ?,
                       prix_vente = ?, stock = ?
                       WHERE id = ?""",
                    (code, designation, self.unite_input.text(),
                     self.prix_achat_input.value(), self.prix_vente_input.value(),
                     self.stock_input.value(), self.produit_id)
                )
                message = f"Produit {designation} modifié avec succès."
            else:
                # Ajout d'un nouveau produit
                cursor.execute(
                    """INSERT INTO produits
                       (code, designation, unite, prix_achat, prix_vente, stock)
                       VALUES (?, ?, ?, ?, ?, ?)""",
                    (code, designation, self.unite_input.text(),
                     self.prix_achat_input.value(), self.prix_vente_input.value(),
                     self.stock_input.value())
                )
                message = f"Produit {designation} ajouté avec succès.\nCode produit: {code}"

            self.db_manager.conn.commit()
            QMessageBox.information(self, "Succès", message)

        except sqlite3.IntegrityError:
            QMessageBox.warning(self, "Erreur", "Ce code produit existe déjà. Veuillez utiliser un autre code.")
            raise

    def load_data(self, produit_id):
        """Charge les données d'un produit existant"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT * FROM produits WHERE id = ?", (produit_id,))
        produit = cursor.fetchone()

        if produit:
            self.code_input.setText(produit['code'] or "")
            self.designation_input.setText(produit['designation'])
            self.unite_input.setText(produit['unite'] or "")
            self.prix_achat_input.setValue(produit['prix_achat'] or 0)
            self.prix_vente_input.setValue(produit['prix_vente'] or 0)
            self.stock_input.setValue(produit['stock'] or 0)

class ProduitsListView(BaseListView):
    """Vue en liste des produits"""

    def __init__(self, db_manager, parent=None):
        super().__init__(
            db_manager=db_manager,
            title="Liste des Produits",
            description="Consultez et gérez vos produits",
            icon=PRODUCTS_ICON,
            add_button_text="Ajouter un produit"
        )

        # Configurer le tableau
        self.setup_table()

        # Charger les données
        self.load_produits()

    def setup_table(self):
        """Configure le tableau des produits"""
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "Code", "Désignation", "Unité", "Prix d'achat", "Prix de vente", "Stock", "Actions"
        ])

        # Configuration des colonnes
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Code
        self.items_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)  # Désignation
        self.items_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Unité
        self.items_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Prix d'achat
        self.items_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Prix de vente
        self.items_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Stock
        self.items_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Actions

    def load_produits(self):
        """Charge les produits depuis la base de données"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT * FROM produits ORDER BY code")
        produits = cursor.fetchall()

        self.items_table.setRowCount(0)

        for row_num, produit in enumerate(produits):
            self.items_table.insertRow(row_num)

            # Ajouter les données du produit
            self.items_table.setItem(row_num, 0, QTableWidgetItem(produit['code'] or ""))
            self.items_table.setItem(row_num, 1, QTableWidgetItem(produit['designation']))
            self.items_table.setItem(row_num, 2, QTableWidgetItem(produit['unite'] or ""))
            self.items_table.setItem(row_num, 3, QTableWidgetItem(f"{produit['prix_achat']:.2f} DH" if produit['prix_achat'] else ""))
            self.items_table.setItem(row_num, 4, QTableWidgetItem(f"{produit['prix_vente']:.2f} DH" if produit['prix_vente'] else ""))
            self.items_table.setItem(row_num, 5, QTableWidgetItem(str(produit['stock'] or 0)))

            # Créer les boutons d'action
            actions_widget = self.create_action_buttons(row_num, produit['id'], produit['designation'])
            self.items_table.setCellWidget(row_num, 6, actions_widget)

            # Stocker l'ID du produit dans la première colonne (invisible)
            self.items_table.item(row_num, 0).setData(Qt.UserRole, produit['id'])

    def on_add_clicked(self):
        """Affiche la boîte de dialogue pour ajouter un produit"""
        dialog = ProduitFormDialog(self.db_manager, parent=self)
        dialog.form_submitted.connect(self.on_produit_saved)
        dialog.exec()

    def on_edit_clicked(self, produit_id):
        """Affiche la boîte de dialogue pour modifier un produit"""
        dialog = ProduitFormDialog(self.db_manager, produit_id=produit_id, parent=self)
        dialog.form_submitted.connect(self.on_produit_saved)
        dialog.exec()

    def on_delete_clicked(self, produit_id, produit_name):
        """Supprime un produit"""
        confirm = QMessageBox.question(
            self, "Confirmation",
            f"Êtes-vous sûr de vouloir supprimer le produit {produit_name} ?\nCette action est irréversible.",
            QMessageBox.Yes | QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                cursor = self.db_manager.conn.cursor()
                cursor.execute("DELETE FROM produits WHERE id = ?", (produit_id,))
                self.db_manager.conn.commit()

                # إعادة ترقيم جميع المنتجات تلقائياً
                self.db_manager.renumber_all_products()

                self.load_produits()
                self.item_changed.emit()

                QMessageBox.information(self, "Succès", f"Produit {produit_name} supprimé avec succès.")
            except sqlite3.Error as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression du produit: {str(e)}")

    def on_produit_saved(self):
        """Appelé lorsqu'un produit est ajouté ou modifié"""
        self.load_produits()
        self.item_changed.emit()

class ProduitsModule(QWidget):
    """Module de gestion des produits avec interface simplifiée"""

    def __init__(self, db_manager, signals=None):
        super().__init__()
        self.db_manager = db_manager
        self.signals = signals

        # Configurer l'interface
        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Créer la vue en liste
        self.produits_list_view = ProduitsListView(self.db_manager, self)
        self.produits_list_view.item_changed.connect(self.on_produit_changed)

        # Ajouter la vue en liste au layout
        layout.addWidget(self.produits_list_view)

    def on_produit_changed(self):
        """Appelé lorsqu'un produit est ajouté, modifié ou supprimé"""
        # Émettre le signal pour informer les autres modules
        if self.signals:
            self.signals.produits_changed.emit()

    def show_add_dialog(self):
        """Affiche la boîte de dialogue pour ajouter un produit"""
        self.produits_list_view.on_add_clicked()

    def show_edit_dialog(self, produit_id):
        """Affiche la boîte de dialogue pour modifier un produit"""
        self.produits_list_view.on_edit_clicked(produit_id)
