#!/usr/bin/env python3
"""
Script de test pour vérifier les données TVA dans la base de données
"""

import sqlite3
import os

def test_tva_data():
    """Test des données TVA"""
    db_path = os.path.join(os.path.dirname(__file__), 'database', 'comptabilite.db')
    
    if not os.path.exists(db_path):
        print(f"❌ Base de données non trouvée: {db_path}")
        return
    
    print(f"🔍 Test des données TVA dans: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 1. Vérifier les tables existantes
        print("\n📋 Tables existantes:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        for table in tables:
            print(f"  - {table['name']}")
        
        # 2. Vérifier les factures de vente
        print("\n📄 Factures de vente:")
        cursor.execute("SELECT COUNT(*) as count FROM factures_vente")
        count = cursor.fetchone()
        print(f"  Nombre total: {count['count']}")
        
        if count['count'] > 0:
            cursor.execute("SELECT * FROM factures_vente LIMIT 3")
            factures = cursor.fetchall()
            for facture in factures:
                print(f"  - ID: {facture['id']}, Numéro: {facture['numero']}, Total HT: {facture['total_ht']}")
        
        # 3. Vérifier les factures d'achat
        print("\n📦 Factures d'achat:")
        cursor.execute("SELECT COUNT(*) as count FROM factures_achat")
        count = cursor.fetchone()
        print(f"  Nombre total: {count['count']}")
        
        if count['count'] > 0:
            cursor.execute("SELECT * FROM factures_achat LIMIT 3")
            factures = cursor.fetchall()
            for facture in factures:
                print(f"  - ID: {facture['id']}, Numéro: {facture['numero']}, Total HT: {facture['total_ht']}")
        
        # 4. Vérifier les produits
        print("\n🛍️ Produits:")
        cursor.execute("SELECT COUNT(*) as count FROM produits")
        count = cursor.fetchone()
        print(f"  Nombre total: {count['count']}")
        
        if count['count'] > 0:
            cursor.execute("SELECT * FROM produits LIMIT 3")
            produits = cursor.fetchall()
            for produit in produits:
                print(f"  - ID: {produit['id']}, Désignation: {produit['designation']}, Prix vente: {produit['prix_vente']}")
        
        # 5. Test de la requête TVA pour les ventes
        print("\n🔍 Test requête TVA ventes:")
        query = """
            SELECT
                fv.date_creation,
                fv.id as ordre,
                fv.numero,
                fv.total_ht,
                20.0 as taux_tva,
                fv.total_tva as montant_tva,
                fv.total_ttc as montant_ttc,
                fv.date_paiement,
                c.nom as client_nom
            FROM factures_vente fv
            LEFT JOIN clients c ON fv.client_id = c.id
            WHERE 1=1
            ORDER BY fv.date_creation DESC
            LIMIT 3
        """
        cursor.execute(query)
        ventes = cursor.fetchall()
        print(f"  Résultats trouvés: {len(ventes)}")
        for vente in ventes:
            print(f"  - Facture {vente['numero']}: {vente['total_ht']} DH HT")
        
        # 6. Test de la requête TVA pour les achats
        print("\n🔍 Test requête TVA achats:")
        query = """
            SELECT
                fa.date_creation,
                fa.id as ordre,
                fa.numero,
                fa.total_ht,
                20.0 as taux_tva,
                (fa.total_ht * 0.20) as montant_tva,
                (fa.total_ht * 1.20) as montant_ttc,
                fa.mode_paiement,
                fa.date_paiement,
                f.nom as fournisseur_nom
            FROM factures_achat fa
            LEFT JOIN fournisseurs f ON fa.fournisseur_id = f.id
            WHERE 1=1
            ORDER BY fa.date_creation DESC
            LIMIT 3
        """
        cursor.execute(query)
        achats = cursor.fetchall()
        print(f"  Résultats trouvés: {len(achats)}")
        for achat in achats:
            print(f"  - Facture {achat['numero']}: {achat['total_ht']} DH HT")
        
        conn.close()
        print("\n✅ Test terminé avec succès!")
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tva_data()
