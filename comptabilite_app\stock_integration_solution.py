#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Solution complète pour l'intégration automatique Stock-Factures
Ce fichier contient le code nécessaire pour:
1. Enregistrer automatiquement les sorties de stock lors de l'ajout de factures
2. Mettre à jour le stock final en temps réel
3. Éviter les doublons lors de la modification des factures
"""

import sqlite3
import datetime
from typing import List, Dict, Any

class StockIntegrationManager:
    """Gestionnaire pour l'intégration automatique entre les factures et le stock"""

    def __init__(self, db_connection):
        self.conn = db_connection
        self.ensure_stock_table_structure()

    def ensure_stock_table_structure(self):
        """S'assure que la table mouvements_stock a la bonne structure"""
        cursor = self.conn.cursor()

        try:
            # Vérifier la structure actuelle
            cursor.execute("PRAGMA table_info(mouvements_stock)")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]

            # Ajouter les colonnes manquantes
            if 'facture_id' not in column_names:
                cursor.execute("ALTER TABLE mouvements_stock ADD COLUMN facture_id INTEGER")
                print("✅ Colonne 'facture_id' ajoutée")

            if 'designation' not in column_names:
                cursor.execute("ALTER TABLE mouvements_stock ADD COLUMN designation TEXT")
                print("✅ Colonne 'designation' ajoutée")

            self.conn.commit()

        except sqlite3.Error as e:
            print(f"❌ Erreur lors de la vérification de la structure: {e}")

    def add_stock_movements_for_invoice(self, facture_id: int, lignes_facture: List[Dict[str, Any]]) -> bool:
        """
        Ajoute les mouvements de stock pour une facture

        Args:
            facture_id: ID de la facture
            lignes_facture: Liste des lignes de la facture avec designation, quantite

        Returns:
            bool: True si succès, False sinon
        """
        cursor = self.conn.cursor()

        try:
            # Supprimer les anciens mouvements pour cette facture (en cas de modification)
            cursor.execute("""
                DELETE FROM mouvements_stock
                WHERE facture_id = ? AND type_mouvement = 'sortie'
            """, (facture_id,))

            print(f"🔄 Traitement de {len(lignes_facture)} lignes pour la facture {facture_id}")

            for ligne in lignes_facture:
                designation = ligne.get('designation', '')
                quantite = ligne.get('quantite', 0)

                if not designation or quantite <= 0:
                    continue

                # Chercher le produit par désignation
                produit_id = self.find_product_by_designation(designation)

                if produit_id:
                    # Ajouter le mouvement de stock
                    cursor.execute("""
                        INSERT INTO mouvements_stock
                        (produit_id, facture_id, designation, type_mouvement, quantite, reference, date_mouvement)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        produit_id,
                        facture_id,
                        designation,
                        'sortie',
                        quantite,
                        f'Facture ID-{facture_id}',
                        datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    ))

                    # IMPORTANT: Mettre à jour le stock dans la table produits
                    # D'abord vérifier quel colonne de stock existe
                    cursor.execute("PRAGMA table_info(produits)")
                    columns = cursor.fetchall()
                    column_names = [col[1] for col in columns]

                    stock_column = 'stock_quantity'
                    if 'stock' in column_names and 'stock_quantity' not in column_names:
                        stock_column = 'stock'

                    cursor.execute(f"""
                        UPDATE produits
                        SET {stock_column} = {stock_column} - ?
                        WHERE id = ? AND {stock_column} >= ?
                    """, (quantite, produit_id, quantite))

                    if cursor.rowcount > 0:
                        print(f"✅ Stock mis à jour: {designation} x {quantite} (ID: {produit_id})")
                    else:
                        print(f"⚠️ Stock insuffisant pour: {designation} x {quantite}")

                    print(f"✅ Sortie ajoutée: {designation} x {quantite}")
                else:
                    print(f"⚠️ Produit non trouvé: {designation}")

            self.conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"❌ Erreur lors de l'ajout des mouvements: {e}")
            self.conn.rollback()
            return False

    def find_product_by_designation(self, designation: str) -> int:
        """
        Trouve un produit par sa désignation

        Args:
            designation: Nom du produit

        Returns:
            int: ID du produit ou None si non trouvé
        """
        cursor = self.conn.cursor()

        # Nettoyer la désignation (enlever les prix et informations supplémentaires)
        clean_designation = self.clean_designation(designation)

        # Recherche exacte avec la désignation nettoyée
        cursor.execute("SELECT id FROM produits WHERE designation = ?", (clean_designation,))
        result = cursor.fetchone()

        if result:
            print(f"🎯 Produit trouvé (exact): {clean_designation} -> ID {result[0]}")
            return result[0]

        # Recherche exacte avec la désignation originale
        cursor.execute("SELECT id FROM produits WHERE designation = ?", (designation,))
        result = cursor.fetchone()

        if result:
            print(f"🎯 Produit trouvé (original): {designation} -> ID {result[0]}")
            return result[0]

        # Recherche approximative avec la désignation nettoyée
        cursor.execute("SELECT id FROM produits WHERE designation LIKE ?", (f"%{clean_designation}%",))
        result = cursor.fetchone()

        if result:
            print(f"🎯 Produit trouvé (approximatif): {clean_designation} -> ID {result[0]}")
            return result[0]

        # Recherche approximative avec la désignation originale
        cursor.execute("SELECT id FROM produits WHERE designation LIKE ?", (f"%{designation}%",))
        result = cursor.fetchone()

        if result:
            print(f"🎯 Produit trouvé (approximatif original): {designation} -> ID {result[0]}")
            return result[0]

        print(f"❌ Produit non trouvé: '{designation}' (nettoyé: '{clean_designation}')")
        return None

    def clean_designation(self, designation: str) -> str:
        """
        Nettoie la désignation en enlevant les prix et informations supplémentaires

        Args:
            designation: Désignation originale

        Returns:
            str: Désignation nettoyée
        """
        import re

        # Enlever les prix (ex: "- 55.00 DH")
        clean = re.sub(r'\s*-\s*\d+\.?\d*\s*DH.*$', '', designation)

        # Enlever les informations entre parenthèses (ex: "(Stock: 150)")
        clean = re.sub(r'\s*\([^)]*\).*$', '', clean)

        # Enlever les espaces en trop
        clean = clean.strip()

        return clean

    def remove_stock_movements_for_invoice(self, facture_id: int) -> bool:
        """
        Supprime les mouvements de stock pour une facture (lors de la suppression)

        Args:
            facture_id: ID de la facture

        Returns:
            bool: True si succès, False sinon
        """
        cursor = self.conn.cursor()

        try:
            # D'abord récupérer les mouvements pour restaurer le stock
            cursor.execute("""
                SELECT produit_id, quantite, designation
                FROM mouvements_stock
                WHERE facture_id = ? AND type_mouvement = 'sortie'
            """, (facture_id,))

            mouvements = cursor.fetchall()

            # Restaurer le stock pour chaque mouvement
            for mouvement in mouvements:
                produit_id, quantite, designation = mouvement

                # Vérifier quel colonne de stock existe
                cursor.execute("PRAGMA table_info(produits)")
                columns = cursor.fetchall()
                column_names = [col[1] for col in columns]

                stock_column = 'stock_quantity'
                if 'stock' in column_names and 'stock_quantity' not in column_names:
                    stock_column = 'stock'

                cursor.execute(f"""
                    UPDATE produits
                    SET {stock_column} = {stock_column} + ?
                    WHERE id = ?
                """, (quantite, produit_id))

                if cursor.rowcount > 0:
                    print(f"✅ Stock restauré: {designation} +{quantite} (ID: {produit_id})")

            # Maintenant supprimer les mouvements
            cursor.execute("""
                DELETE FROM mouvements_stock
                WHERE facture_id = ? AND type_mouvement = 'sortie'
            """, (facture_id,))

            deleted_count = cursor.rowcount
            self.conn.commit()

            print(f"✅ {deleted_count} mouvements supprimés pour la facture {facture_id}")
            return True

        except sqlite3.Error as e:
            print(f"❌ Erreur lors de la suppression: {e}")
            self.conn.rollback()
            return False

    def get_stock_summary(self) -> List[Dict[str, Any]]:
        """
        Récupère un résumé du stock avec les sorties calculées

        Returns:
            List[Dict]: Liste des produits avec stock initial, entrées, sorties, stock final
        """
        cursor = self.conn.cursor()

        query = """
        SELECT
            p.id,
            p.designation,
            p.unite,
            p.stock as stock_actuel,
            p.prix_achat,
            COALESCE(SUM(CASE WHEN m.type_mouvement = 'entree' THEN m.quantite ELSE 0 END), 0) as total_entrees,
            COALESCE(SUM(CASE WHEN m.type_mouvement = 'sortie' THEN m.quantite ELSE 0 END), 0) as total_sorties
        FROM produits p
        LEFT JOIN mouvements_stock m ON p.id = m.produit_id
        GROUP BY p.id, p.designation, p.unite, p.stock, p.prix_achat
        ORDER BY p.designation
        """

        cursor.execute(query)
        results = cursor.fetchall()

        stock_summary = []
        for row in results:
            # Le stock dans la DB est le stock initial (stock de départ)
            stock_initial = row[3]
            entrees = row[5]
            sorties = row[6]

            # Calculer le stock final: stock_initial + entrées - sorties
            stock_final = stock_initial + entrees - sorties
            if stock_final < 0:
                stock_final = 0

            stock_summary.append({
                'id': row[0],
                'designation': row[1],
                'unite': row[2],
                'stock_initial': stock_initial,
                'entrees': entrees,
                'sorties': sorties,
                'stock_final': stock_final,
                'prix_achat': row[4] or 0,
                'valeur_stock': stock_final * (row[4] or 0)
            })

        return stock_summary

# Fonctions utilitaires pour l'intégration dans l'application existante

def integrate_with_invoice_save(db_manager, facture_id: int, lignes_facture: List[Dict[str, Any]]):
    """
    Fonction à appeler lors de la sauvegarde d'une facture

    Usage dans le code de sauvegarde de facture:

    # Après avoir sauvegardé la facture et ses lignes
    from stock_integration_solution import integrate_with_invoice_save

    lignes = [
        {'designation': 'DISJONCTEUR', 'quantite': 5},
        {'designation': 'CABLE', 'quantite': 10},
    ]

    integrate_with_invoice_save(db_manager, facture_id, lignes)
    """
    stock_manager = StockIntegrationManager(db_manager.conn)
    success = stock_manager.add_stock_movements_for_invoice(facture_id, lignes_facture)

    if success:
        print(f"📦 Mouvements de stock ajoutés pour la facture {facture_id}")
    else:
        print(f"❌ Échec de l'ajout des mouvements pour la facture {facture_id}")

    return success

def integrate_with_invoice_delete(db_manager, facture_id: int):
    """
    Fonction à appeler lors de la suppression d'une facture

    Usage:
    from stock_integration_solution import integrate_with_invoice_delete
    integrate_with_invoice_delete(db_manager, facture_id)
    """
    stock_manager = StockIntegrationManager(db_manager.conn)
    return stock_manager.remove_stock_movements_for_invoice(facture_id)

def get_updated_stock_data(db_manager) -> List[Dict[str, Any]]:
    """
    Récupère les données de stock mises à jour

    Usage pour mettre à jour l'affichage du stock:
    from stock_integration_solution import get_updated_stock_data
    stock_data = get_updated_stock_data(db_manager)
    """
    stock_manager = StockIntegrationManager(db_manager.conn)
    return stock_manager.get_stock_summary()

# Exemple d'utilisation complète
if __name__ == "__main__":
    # Test de la solution
    from database.db_manager import DatabaseManager

    db_manager = DatabaseManager()

    # Simuler l'ajout d'une facture
    lignes_test = [
        {'designation': 'DISJONCTEUR', 'quantite': 3},
        {'designation': 'CABLE', 'quantite': 5},
    ]

    # Intégrer avec la facture (remplacer 999 par l'ID réel de la facture)
    integrate_with_invoice_save(db_manager, 999, lignes_test)

    # Afficher le résumé du stock
    stock_data = get_updated_stock_data(db_manager)
    print("\n📊 Résumé du stock:")
    for item in stock_data:
        print(f"- {item['designation']}: Initial={item['stock_initial']}, "
              f"Sorties={item['sorties']}, Final={item['stock_final']}")
