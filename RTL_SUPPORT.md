# دعم RTL (Right-to-Left) في نظام المحاسبة

## نظرة عامة

تم إضافة دعم كامل للغة العربية مع اتجاه النص من اليمين إلى اليسار (RTL) في نظام المحاسبة. هذا يشمل:

## الميزات المضافة

### 1. **تبديل اللغة**
- زر تبديل اللغة في الشريط الجانبي
- تبديل فوري بين العربية والفرنسية
- حفظ تفضيلات اللغة

### 2. **دعم RTL كامل**
- **انعكاس موقع الشريط الجانبي**: ينتقل من اليسار إلى اليمين عند التبديل للعربية
- **ترتيب مختلف للأقسام**: ترتيب خاص بالعربية حسب الأولوية
- **محاذاة النصوص**: جميع النصوص تتمحور حسب اتجاه اللغة
- **ترتيب الأزرار**: انعكاس ترتيب الأزرار في شريط الأدوات

### 3. **قوائم السياق (Context Menus)**

- **أزرار التعديل والحذف**: تم إزالتها من شريط الأدوات
- **قائمة النقر اليمين**: تظهر عند النقر بالزر الأيمن على أي عنصر
- **خيارات متاحة**: تعديل، حذف، إضافة جديد
- **واجهة أنظف**: أزرار أقل في شريط الأدوات
- **الصفحات المحدثة**: العملاء، المنتجات، الموردون

### 4. **ترتيب الأقسام**

#### الترتيب بالفرنسية (LTR):
1. لوحة التحكم
2. **الموردون** (Fournisseurs)
3. **المنتجات** (Produits)
4. **العملاء** (Clients)
5. **إشعارات التسليم** (Bons de Livraison)
6. **الفواتير** (Factures)
7. **أوامر الشراء** (Bons de Commande)
8. المخزون
9. الصندوق
10. ضريبة القيمة المضافة
11. التقارير
12. البحث

#### الترتيب بالعربية (RTL):
1. لوحة التحكم
2. **الموردون** (Fournisseurs)
3. **المنتجات** (Produits)
4. **العملاء** (Clients)
5. **إشعارات التسليم** (Bons de Livraison)
6. **الفواتير** (Factures)
7. **أوامر الشراء** (Bons de Commande)
8. المخزون
9. الصندوق
10. ضريبة القيمة المضافة
11. التقارير
12. البحث

### 5. **انعكاس العناصر**

#### في الواجهة العربية:
- **الشريط الجانبي**: على اليمين بدلاً من اليسار
- **بطاقات الإحصائيات**: ترتيب معكوس (الموردون، العملاء، المنتجات، الفواتير)
- **شريط الأدوات**: البحث على اليمين، الأزرار على اليسار
- **محاذاة النصوص**: جميع النصوص محاذاة لليمين

## الملفات المضافة

### 1. `language_manager.py`
مدير اللغات الذي يحتوي على:
- قاموس الترجمات (عربي/فرنسي)
- وظائف تبديل اللغة
- وظائف تحديد اتجاه النص
- وظائف المحاذاة

### 2. `test_rtl.py`
تطبيق تجريبي لاختبار دعم RTL

## كيفية الاستخدام

### 1. **تشغيل التطبيق**
```bash
python run_app.py
```

### 2. **تبديل اللغة**
- ابحث عن زر 🌐 في أسفل الشريط الجانبي
- اضغط على الزر للتبديل بين العربية والفرنسية
- ستتغير الواجهة فوراً لتعكس اللغة الجديدة

### 3. **اختبار الميزة**
```bash
python test_rtl.py
```

## التحسينات المطبقة

### في `run_app.py`:
1. **إضافة مدير اللغات**
2. **دالة `setup_ui()`**: إعادة ترتيب الواجهة حسب اللغة
3. **دالة `toggle_language()`**: تبديل اللغة
4. **تحديث `create_sidebar()`**: ترتيب مختلف للأقسام
5. **تحديث `show_dashboard()`**: دعم RTL في لوحة التحكم
6. **تحديث `show_clients_page()`**: دعم RTL في صفحة العملاء
7. **تحديث `create_stat_card()`**: محاذاة البطاقات

## الترجمات المتاحة

### النصوص الأساسية:
- عنوان التطبيق
- أسماء الأقسام
- أزرار العمليات (إضافة، تعديل، حذف، حفظ، إلغاء)
- الحقول (الاسم، الهاتف، العنوان، التاريخ، المبلغ)
- الإحصائيات
- الرسائل

## المتطلبات

- Python 3.8+
- CustomTkinter
- SQLite3

## ملاحظات تقنية

### محاذاة النصوص:
- **العربية**: `anchor="e"` (محاذاة لليمين)
- **الفرنسية**: `anchor="w"` (محاذاة لليسار)

### ترتيب العناصر:
- **العربية**: `side="right"` للبحث، `side="left"` للأزرار
- **الفرنسية**: `side="left"` للبحث، `side="right"` للأزرار

### موقع الشريط الجانبي:
- **العربية**: `column=1` (اليمين)
- **الفرنسية**: `column=0` (اليسار)

## التطوير المستقبلي

- [ ] إضافة المزيد من الترجمات
- [ ] دعم لغات إضافية
- [ ] حفظ تفضيلات اللغة في قاعدة البيانات
- [ ] دعم RTL في النوافذ المنبثقة
- [ ] دعم RTL في التقارير المطبوعة

## الاختبار

لاختبار الميزة:

1. **اختبار التبديل**: تأكد من أن زر اللغة يعمل
2. **اختبار الترتيب**: تحقق من ترتيب الأقسام المختلف
3. **اختبار المحاذاة**: تأكد من محاذاة النصوص
4. **اختبار الشريط الجانبي**: تحقق من انتقال الموقع
5. **اختبار البطاقات**: تأكد من انعكاس ترتيب البطاقات

---

تم تطوير هذه الميزة لتوفير تجربة مستخدم محسنة للمستخدمين الناطقين بالعربية مع الحفاظ على الوظائف الكاملة للتطبيق.
