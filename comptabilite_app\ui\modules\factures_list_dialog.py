from PySide6.QtWidgets import (QTableWidgetItem, QHeaderView, QMessageBox, QDialog)
from PySide6.QtCore import Qt, QDate
import sqlite3
import datetime
import sys
import os

# Ajouter le répertoire parent pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# Importer la solution d'intégration stock
try:
    from stock_integration_solution import integrate_with_invoice_delete
    STOCK_INTEGRATION_AVAILABLE = True
    print("✅ Module d'intégration stock importé avec succès dans factures_list_dialog")
except ImportError as e:
    STOCK_INTEGRATION_AVAILABLE = False
    print(f"⚠️ Module d'intégration stock non disponible dans factures_list_dialog: {e}")

from ..components.list_dialog import ListDialog

class FacturesListDialog(ListDialog):
    """Boîte de dialogue pour afficher la liste des factures"""

    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "Liste des factures", parent)
        self.load_items()

    def setup_table(self):
        """Configure le tableau des factures"""
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "N° Facture", "Date", "Client", "Total HT", "TVA", "Total TTC", "Actions"
        ])

        self.items_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # N° Facture
        self.items_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Date
        self.items_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Total HT
        self.items_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # TVA
        self.items_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Total TTC
        self.items_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.Fixed)  # Actions
        self.items_table.setColumnWidth(6, 120)

        self.items_table.setSelectionBehavior(QTableWidgetItem.SelectRows)
        self.items_table.setEditTriggers(QTableWidgetItem.NoEditTriggers)
        self.items_table.verticalHeader().setVisible(False)
        self.items_table.setAlternatingRowColors(True)

        # Connecter le signal de double-clic pour ouvrir la boîte de dialogue d'édition
        self.items_table.itemDoubleClicked.connect(self.on_item_double_clicked)

    def load_items(self):
        """Charge les factures depuis la base de données"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT f.id, f.numero, f.date_creation, c.nom as client_nom,
                       f.total_ht, f.total_tva, f.total_ttc
                FROM factures_vente f
                LEFT JOIN clients c ON f.client_id = c.id
                ORDER BY f.date_creation DESC
            """)
            factures = cursor.fetchall()

            self.items_table.setRowCount(0)

            for row_index, facture in enumerate(factures):
                self.items_table.insertRow(row_index)

                # Formater la date
                date_obj = datetime.datetime.strptime(facture['date_creation'], '%Y-%m-%d')
                date_str = date_obj.strftime('%d/%m/%Y')

                # Ajouter les données de la facture
                self.items_table.setItem(row_index, 0, QTableWidgetItem(facture['numero'] or ""))
                self.items_table.setItem(row_index, 1, QTableWidgetItem(date_str))
                self.items_table.setItem(row_index, 2, QTableWidgetItem(facture['client_nom'] or ""))
                self.items_table.setItem(row_index, 3, QTableWidgetItem(f"{facture['total_ht']:.2f} DH"))
                self.items_table.setItem(row_index, 4, QTableWidgetItem(f"{facture['total_tva']:.2f} DH"))
                self.items_table.setItem(row_index, 5, QTableWidgetItem(f"{facture['total_ttc']:.2f} DH"))

                # Ajouter les boutons d'action
                actions_widget = self.create_action_buttons(row_index, facture['id'])
                self.items_table.setCellWidget(row_index, 6, actions_widget)

                # Stocker l'ID de la facture dans les données de la première colonne
                self.items_table.item(row_index, 0).setData(Qt.UserRole, facture['id'])

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des factures: {str(e)}")

    def add_item(self):
        """Émet le signal pour créer une nouvelle facture"""
        self.item_added.emit()
        self.accept()

    def edit_item(self, facture_id):
        """Émet le signal pour modifier une facture existante"""
        self.item_edited.emit(facture_id)
        self.accept()

    def delete_item(self, facture_id):
        """Supprime une facture"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT numero FROM factures_vente WHERE id = ?", (facture_id,))
            facture = cursor.fetchone()

            if not facture:
                QMessageBox.warning(self, "Erreur", "Facture introuvable.")
                return

            reply = QMessageBox.question(
                self, "Confirmation",
                f"Êtes-vous sûr de vouloir supprimer la facture {facture['numero']} ?",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Intégrer avec le système de stock AVANT la suppression (pour restaurer le stock)
                if STOCK_INTEGRATION_AVAILABLE:
                    print(f"📦 Suppression des mouvements de stock pour facture {facture_id}")
                    success = integrate_with_invoice_delete(self.db_manager, facture_id)
                    if success:
                        print("✅ Mouvements de stock supprimés avec succès")
                    else:
                        print("❌ Échec de la suppression des mouvements de stock")
                else:
                    print("⚠️ Module d'intégration stock non disponible")

                # Supprimer d'abord les lignes de facture
                cursor.execute("DELETE FROM lignes_facture WHERE facture_id = ?", (facture_id,))

                # Puis supprimer la facture
                cursor.execute("DELETE FROM factures_vente WHERE id = ?", (facture_id,))

                self.db_manager.conn.commit()
                self.load_items()
                self.item_deleted.emit(facture_id)
                QMessageBox.information(self, "Succès", "Facture supprimée avec succès.")

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression de la facture: {str(e)}")

    def on_item_double_clicked(self, item):
        """Gère le double-clic sur un élément du tableau"""
        if item is None:
            return

        # Récupérer l'ID de la facture depuis la première colonne
        row = item.row()
        first_column_item = self.items_table.item(row, 0)

        if first_column_item:
            # Récupérer l'ID stocké dans les données utilisateur
            facture_id = first_column_item.data(Qt.UserRole)
            if facture_id:
                # Appeler la méthode d'édition
                self.edit_item(facture_id)
