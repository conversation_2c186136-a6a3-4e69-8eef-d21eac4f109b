#!/usr/bin/env python3
"""
تشخيص مشكلة عدم ظهور المنتجات في القائمة
"""

import sqlite3
import os

def debug_product_display():
    """تشخيص مشكلة عرض المنتجات"""
    print("🔍 تشخيص مشكلة عدم ظهور المنتجات في القائمة")
    print("=" * 60)

    # البحث عن ملف قاعدة البيانات
    db_paths = [
        "database/comptabilite.db",
        "comptabilite_app/data/comptabilite.db",
        "data/comptabilite.db",
        "comptabilite.db"
    ]

    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break

    if not db_path:
        print("❌ لم يتم العثور على ملف قاعدة البيانات")
        return False

    print(f"✅ تم العثور على قاعدة البيانات: {db_path}")

    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # 1. فحص بنية جدول المنتجات
        print("\n📊 1. فحص بنية جدول المنتجات:")
        cursor.execute("PRAGMA table_info(produits)")
        columns = cursor.fetchall()

        print(f"   📋 الأعمدة الموجودة ({len(columns)} عمود):")
        for col in columns:
            print(f"      - {col['name']}: {col['type']}")

        # 2. عد المنتجات
        print("\n📊 2. عدد المنتجات في قاعدة البيانات:")
        cursor.execute("SELECT COUNT(*) as count FROM produits")
        count = cursor.fetchone()['count']
        print(f"   📦 إجمالي المنتجات: {count}")

        # 3. عرض آخر 5 منتجات
        print("\n📊 3. آخر 5 منتجات مضافة:")
        cursor.execute("""
            SELECT id, code, designation, prix_achat, prix_vente, stock, date_creation
            FROM produits
            ORDER BY id DESC
            LIMIT 5
        """)
        products = cursor.fetchall()

        if products:
            for i, product in enumerate(products, 1):
                print(f"   {i}. ID: {product['id']}")
                print(f"      الكود: {product['code']}")
                print(f"      التسمية: {product['designation']}")
                print(f"      سعر الشراء: {product['prix_achat']}")
                print(f"      سعر البيع: {product['prix_vente']}")
                print(f"      المخزون: {product['stock']}")
                date_creation = product['date_creation'] if 'date_creation' in product.keys() else 'غير محدد'
                print(f"      تاريخ الإنشاء: {date_creation}")
                print()
        else:
            print("   ❌ لا توجد منتجات في قاعدة البيانات")

        # 4. اختبار استعلام get_all_products
        print("\n📊 4. اختبار استعلام get_all_products:")

        # فحص وجود عمود famille_id
        column_names = [col['name'] for col in columns]

        if 'famille_id' in column_names:
            print("   ✅ عمود famille_id موجود - استخدام استعلام مع العائلات")
            cursor.execute("""
                SELECT p.*, f.nom as famille_nom
                FROM produits p
                LEFT JOIN familles_produits f ON p.famille_id = f.id
                ORDER BY p.date_creation DESC
            """)
        else:
            print("   ⚠️ عمود famille_id مفقود - استخدام استعلام بسيط")
            cursor.execute("SELECT *, NULL as famille_nom FROM produits ORDER BY date_creation DESC")

        products_with_families = cursor.fetchall()
        print(f"   📦 عدد المنتجات المسترجعة: {len(products_with_families)}")

        if products_with_families:
            print("   📋 أول 3 منتجات:")
            for i, product in enumerate(products_with_families[:3], 1):
                print(f"      {i}. {product['code']} - {product['designation']}")

        # 5. فحص جدول العائلات
        print("\n📊 5. فحص جدول العائلات:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='familles_produits'")
        if cursor.fetchone():
            cursor.execute("SELECT COUNT(*) as count FROM familles_produits")
            families_count = cursor.fetchone()['count']
            print(f"   ✅ جدول العائلات موجود - عدد العائلات: {families_count}")
        else:
            print("   ❌ جدول العائلات مفقود")

        conn.close()

        print("\n🎯 6. التشخيص:")
        if count > 0:
            print("   ✅ المنتجات موجودة في قاعدة البيانات")
            print("   🔍 المشكلة قد تكون في:")
            print("      1. عدم تحديث واجهة المستخدم")
            print("      2. خطأ في استعلام العرض")
            print("      3. مشكلة في دالة create_products_table")
        else:
            print("   ❌ لا توجد منتجات في قاعدة البيانات")
            print("   🔍 تأكد من أن عملية الحفظ تتم بنجاح")

        return True

    except Exception as e:
        print(f"❌ خطأ في التشخيص: {str(e)}")
        return False

if __name__ == "__main__":
    debug_product_display()
