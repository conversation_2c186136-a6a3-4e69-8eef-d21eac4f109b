"""
Module de Bon de Livraison Amélioré - مطابق تماماً للنموذج الفرنسي
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QHeaderView, QMessageBox, QFormLayout,
                              QComboBox, QDateEdit, QTextEdit, QSpinBox,
                              QDialog, QGridLayout, QFrame, QCheckBox,
                              QGroupBox, QSplitter)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QFont, QColor, QPalette
import sqlite3
import datetime

# Importer les composants nécessaires
from ..components.base_module import BaseModule

class BLEnhancedDialog(QDialog):
    """Dialogue pour Bon de Livraison - مطابق للنموذج الفرنسي"""
    bl_saved = Signal()

    def __init__(self, db_manager, bl_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.bl_id = bl_id
        self.setup_database()
        self.setup_ui()
        self.load_clients()
        self.load_produits()
        
        if bl_id:
            self.load_bl_data()
        else:
            self.generate_numero()

    def setup_database(self):
        """إنشاء الجداول المطلوبة"""
        cursor = self.db_manager.conn.cursor()
        
        try:
            # تشغيل migration للحقول الجديدة
            from ...database.migration_bl_enhanced import migrate_bl_enhanced
            migrate_bl_enhanced()
            
        except Exception as e:
            print(f"تحذير: {e}")

    def setup_ui(self):
        """إعداد واجهة المستخدم مطابقة للنموذج"""
        self.setWindowTitle("BON DE LIVRAISON")
        self.setMinimumSize(1200, 800)
        self.setModal(True)
        
        # تطبيق الستايل الفرنسي
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLabel {
                font-size: 11px;
                color: #333;
            }
            QLineEdit, QComboBox {
                border: 1px solid #ccc;
                padding: 4px;
                font-size: 11px;
                background-color: white;
            }
            QLineEdit:disabled {
                background-color: #e9ecef;
                color: #6c757d;
            }
            QTableWidget {
                border: 1px solid #dee2e6;
                gridline-color: #dee2e6;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 4px;
                border-bottom: 1px solid #dee2e6;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # العنوان الرئيسي
        title_label = QLabel("BON DE LIVRAISON")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #000;
            background-color: #e9ecef;
            border: 2px solid #000;
            padding: 8px;
            margin-bottom: 10px;
        """)
        main_layout.addWidget(title_label)

        # إنشاء splitter للتقسيم الأفقي
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # القسم الأيسر - المعلومات الأساسية
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # مجموعة المعلومات الأساسية
        info_group = QGroupBox()
        info_layout = QGridLayout(info_group)
        info_layout.setSpacing(5)

        # N BON DE LIVRAISON
        info_layout.addWidget(QLabel("N BON DE LIVRAISON"), 0, 0)
        self.numero_input = QLineEdit()
        self.numero_input.setReadOnly(True)
        self.numero_input.setText("automatique")
        self.numero_input.setStyleSheet("background-color: #e9ecef;")
        info_layout.addWidget(self.numero_input, 0, 1)

        # Type de marché
        info_layout.addWidget(QLabel("Type de marché"), 1, 0)
        self.type_marche_combo = QComboBox()
        self.type_marche_combo.addItems(["BC", "marché"])
        info_layout.addWidget(self.type_marche_combo, 1, 1)

        # DEVIS N°
        info_layout.addWidget(QLabel("DEVIS N°"), 2, 0)
        self.devis_input = QLineEdit()
        self.devis_input.setPlaceholderText("SELECTIONNE OU TAPER")
        info_layout.addWidget(self.devis_input, 2, 1)

        # N
        info_layout.addWidget(QLabel("N"), 3, 0)
        self.n_input = QLineEdit()
        self.n_input.setText("automatique")
        self.n_input.setReadOnly(True)
        self.n_input.setStyleSheet("background-color: #e9ecef;")
        info_layout.addWidget(self.n_input, 3, 1)

        # CLIENT
        info_layout.addWidget(QLabel("CLIENT"), 4, 0)
        self.client_combo = QComboBox()
        self.client_combo.currentTextChanged.connect(self.on_client_changed)
        info_layout.addWidget(self.client_combo, 4, 1)

        # ICE
        info_layout.addWidget(QLabel("ICE"), 5, 0)
        self.ice_input = QLineEdit()
        self.ice_input.setPlaceholderText("si categorie et privé taper le N si non ligne grisé")
        info_layout.addWidget(self.ice_input, 5, 1)

        # IF
        info_layout.addWidget(QLabel("IF"), 6, 0)
        self.if_input = QLineEdit()
        self.if_input.setPlaceholderText("si categorie et privé taper le N si non ligne grisé")
        info_layout.addWidget(self.if_input, 6, 1)

        # adresse
        info_layout.addWidget(QLabel("adresse"), 7, 0)
        self.adresse_input = QLineEdit()
        self.adresse_input.setPlaceholderText("si categorie et privé taper l'adresse si non ligne grisé")
        info_layout.addWidget(self.adresse_input, 7, 1)

        left_layout.addWidget(info_group)

        # القسم الأيمن - الخيارات
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # مجموعة الخيارات
        options_group = QGroupBox()
        options_layout = QVBoxLayout(options_group)

        # sélectionné
        self.selectionne_checkbox = QCheckBox("sélectionné")
        self.selectionne_checkbox.setChecked(True)
        options_layout.addWidget(self.selectionne_checkbox)

        # SI LiGNE GRISE NON AFFICHE DANS L'impression (3 مرات)
        for i in range(3):
            ligne_grise_checkbox = QCheckBox("SI LiGNE GRISE NON AFFICHE DANS L'impression")
            options_layout.addWidget(ligne_grise_checkbox)

        right_layout.addWidget(options_group)
        right_layout.addStretch()

        # إضافة الأقسام للـ splitter
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([700, 300])  # تحديد النسب

        # قسم التفاصيل
        details_group = QGroupBox("détailles")
        details_layout = QVBoxLayout(details_group)
        
        # إنشاء الجدول
        self.create_details_table()
        details_layout.addWidget(self.details_table)
        
        main_layout.addWidget(details_group)

        # الأزرار السفلية
        self.create_bottom_buttons(main_layout)

    def create_details_table(self):
        """إنشاء جدول التفاصيل مطابق للنموذج"""
        self.details_table = QTableWidget()
        
        # تحديد الأعمدة مطابقة للنموذج
        columns = ["N°", "designation", "U", "qte", "reste au\nBC/MARCHE", "RESTE EN STOCK"]
        self.details_table.setColumnCount(len(columns))
        self.details_table.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        header = self.details_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        # تحديد عرض الأعمدة
        self.details_table.setColumnWidth(0, 50)   # N°
        self.details_table.setColumnWidth(1, 300)  # designation
        self.details_table.setColumnWidth(2, 100)  # U
        self.details_table.setColumnWidth(3, 100)  # qte
        self.details_table.setColumnWidth(4, 150)  # reste au BC/MARCHE
        self.details_table.setColumnWidth(5, 150)  # RESTE EN STOCK
        
        # إضافة صفوف افتراضية
        self.add_default_rows()

    def add_default_rows(self):
        """إضافة الصفوف الافتراضية مطابقة للنموذج"""
        self.details_table.setRowCount(2)
        
        # الصف الأول
        self.details_table.setItem(0, 0, QTableWidgetItem("1"))
        self.details_table.setItem(0, 1, QTableWidgetItem("AUTOMATIQUE"))
        
        # إنشاء combobox للعمود U
        u_combo_1 = QComboBox()
        u_combo_1.addItems(["automatique", "AUTO/manuel"])
        self.details_table.setCellWidget(0, 2, u_combo_1)
        
        # إنشاء combobox للعمود qte
        qte_combo_1 = QComboBox()
        qte_combo_1.addItems(["AUTO/manuel"])
        self.details_table.setCellWidget(0, 3, qte_combo_1)
        
        self.details_table.setItem(0, 4, QTableWidgetItem("qte livré - qte\ndans BC"))
        self.details_table.setItem(0, 5, QTableWidgetItem(""))

        # الصف الثاني
        self.details_table.setItem(1, 0, QTableWidgetItem("2"))
        self.details_table.setItem(1, 1, QTableWidgetItem("AUTOMATIQUE"))
        
        u_combo_2 = QComboBox()
        u_combo_2.addItems(["automatique", "AUTO/manuel"])
        self.details_table.setCellWidget(1, 2, u_combo_2)
        
        qte_combo_2 = QComboBox()
        qte_combo_2.addItems(["AUTO/manuel"])
        self.details_table.setCellWidget(1, 3, qte_combo_2)
        
        self.details_table.setItem(1, 4, QTableWidgetItem("qte livré - qte\ndans BC"))
        self.details_table.setItem(1, 5, QTableWidgetItem(""))

    def create_bottom_buttons(self, layout):
        """إنشاء الأزرار السفلية"""
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        # زر validé
        self.valide_btn = QPushButton("validé")
        self.valide_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 20px;
                font-size: 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.valide_btn.clicked.connect(self.save_bl)
        buttons_layout.addWidget(self.valide_btn)
        
        # زر supprimer
        self.supprimer_btn = QPushButton("supprimer")
        self.supprimer_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 8px 20px;
                font-size: 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        self.supprimer_btn.clicked.connect(self.delete_bl)
        buttons_layout.addWidget(self.supprimer_btn)
        
        # زر imprimer
        self.imprimer_btn = QPushButton("imprimer")
        self.imprimer_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 20px;
                font-size: 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        self.imprimer_btn.clicked.connect(self.print_bl)
        buttons_layout.addWidget(self.imprimer_btn)
        
        layout.addLayout(buttons_layout)

    def load_clients(self):
        """تحميل قائمة العملاء"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT id, nom FROM clients ORDER BY nom")
            clients = cursor.fetchall()
            
            self.client_combo.clear()
            self.client_combo.addItem("automatique", None)
            
            for client_id, nom in clients:
                self.client_combo.addItem(nom, client_id)
                
        except Exception as e:
            print(f"خطأ في تحميل العملاء: {e}")

    def load_produits(self):
        """تحميل قائمة المنتجات"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT id, designation FROM produits ORDER BY designation")
            self.produits = cursor.fetchall()
        except Exception as e:
            print(f"خطأ في تحميل المنتجات: {e}")
            self.produits = []

    def on_client_changed(self):
        """عند تغيير العميل - تحديث حقول ICE و IF والعنوان"""
        client_id = self.client_combo.currentData()
        if client_id:
            try:
                cursor = self.db_manager.conn.cursor()
                cursor.execute("SELECT ice, if_fiscal, adresse FROM clients WHERE id = ?", (client_id,))
                result = cursor.fetchone()
                
                if result:
                    ice, if_fiscal, adresse = result
                    self.ice_input.setText(ice or "")
                    self.if_input.setText(if_fiscal or "")
                    self.adresse_input.setText(adresse or "")
                    
                    # إزالة التعطيل إذا كان العميل خاص
                    self.ice_input.setEnabled(True)
                    self.if_input.setEnabled(True)
                    self.adresse_input.setEnabled(True)
                else:
                    # تفريغ الحقول وتعطيلها
                    self.ice_input.clear()
                    self.if_input.clear()
                    self.adresse_input.clear()
                    self.ice_input.setEnabled(False)
                    self.if_input.setEnabled(False)
                    self.adresse_input.setEnabled(False)
                    
            except Exception as e:
                print(f"خطأ في تحميل بيانات العميل: {e}")

    def generate_numero(self):
        """توليد رقم بون دو ليفريزون تلقائي"""
        try:
            cursor = self.db_manager.conn.cursor()
            date_str = datetime.datetime.now().strftime("%Y%m")
            cursor.execute("SELECT COUNT(*) FROM bons_livraison WHERE numero LIKE ?", (f"BL{date_str}%",))
            count = cursor.fetchone()[0] + 1
            numero = f"BL{date_str}-{count:03d}"
            self.numero_input.setText(numero)
        except Exception as e:
            print(f"خطأ في توليد الرقم: {e}")
            self.numero_input.setText("BL-001")

    def save_bl(self):
        """حفظ بون دو ليفريزون"""
        try:
            # التحقق من صحة البيانات
            if not self.numero_input.text().strip():
                QMessageBox.warning(self, "خطأ", "رقم بون دو ليفريزون مطلوب")
                return
            
            # جمع البيانات
            numero = self.numero_input.text().strip()
            type_marche = self.type_marche_combo.currentText()
            devis_number = self.devis_input.text().strip()
            client_id = self.client_combo.currentData()
            ice_client = self.ice_input.text().strip()
            if_client = self.if_input.text().strip()
            adresse = self.adresse_input.text().strip()
            
            cursor = self.db_manager.conn.cursor()
            
            if self.bl_id:
                # تحديث
                cursor.execute("""
                    UPDATE bons_livraison 
                    SET numero=?, type_marche=?, devis_number=?, client_id=?,
                        ice_client=?, adresse_livraison=?
                    WHERE id=?
                """, (numero, type_marche, devis_number, client_id,
                      ice_client, adresse, self.bl_id))
            else:
                # إنشاء جديد
                cursor.execute("""
                    INSERT INTO bons_livraison 
                    (numero, date_creation, type_marche, devis_number, client_id,
                     ice_client, adresse_livraison, statut)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (numero, datetime.datetime.now().strftime("%Y-%m-%d"),
                      type_marche, devis_number, client_id, ice_client, adresse, "En cours"))
                
                self.bl_id = cursor.lastrowid
            
            # حفظ تفاصيل الجدول
            self.save_details()
            
            self.db_manager.conn.commit()
            QMessageBox.information(self, "نجح", "تم حفظ بون دو ليفريزون بنجاح")
            self.bl_saved.emit()
            
        except Exception as e:
            self.db_manager.conn.rollback()
            QMessageBox.critical(self, "خطأ", f"خطأ في الحفظ: {str(e)}")

    def save_details(self):
        """حفظ تفاصيل الجدول"""
        if not self.bl_id:
            return
            
        cursor = self.db_manager.conn.cursor()
        
        # حذف التفاصيل القديمة
        cursor.execute("DELETE FROM lignes_bl WHERE bl_id = ?", (self.bl_id,))
        
        # حفظ التفاصيل الجديدة
        for row in range(self.details_table.rowCount()):
            designation_item = self.details_table.item(row, 1)
            if designation_item and designation_item.text().strip():
                designation = designation_item.text().strip()
                
                # الحصول على قيم الـ comboboxes
                u_widget = self.details_table.cellWidget(row, 2)
                qte_widget = self.details_table.cellWidget(row, 3)
                
                unite = u_widget.currentText() if u_widget else ""
                auto_manuel = qte_widget.currentText() if qte_widget else ""
                
                cursor.execute("""
                    INSERT INTO lignes_bl 
                    (bl_id, designation, unite, quantite, auto_manuel)
                    VALUES (?, ?, ?, ?, ?)
                """, (self.bl_id, designation, unite, 1, auto_manuel))

    def delete_bl(self):
        """حذف بون دو ليفريزون"""
        if not self.bl_id:
            QMessageBox.warning(self, "تحذير", "لا يوجد بون دو ليفريزون للحذف")
            return
            
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا بون دو ليفريزون؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                cursor = self.db_manager.conn.cursor()
                cursor.execute("DELETE FROM lignes_bl WHERE bl_id = ?", (self.bl_id,))
                cursor.execute("DELETE FROM bons_livraison WHERE id = ?", (self.bl_id,))
                self.db_manager.conn.commit()
                
                QMessageBox.information(self, "نجح", "تم حذف بون دو ليفريزون بنجاح")
                self.bl_saved.emit()
                self.accept()
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في الحذف: {str(e)}")

    def print_bl(self):
        """طباعة بون دو ليفريزون"""
        QMessageBox.information(self, "طباعة", "سيتم تنفيذ وظيفة الطباعة قريباً")

    def load_bl_data(self):
        """تحميل بيانات بون دو ليفريزون موجود"""
        if not self.bl_id:
            return
            
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT * FROM bons_livraison WHERE id = ?", (self.bl_id,))
            bl_data = cursor.fetchone()
            
            if bl_data:
                # تحميل البيانات الأساسية
                # (سيتم تنفيذها حسب هيكل قاعدة البيانات)
                pass
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")


class BLEnhancedModule(BaseModule):
    """وحدة بون دو ليفريزون المحسنة"""

    def __init__(self, db_manager, signals=None):
        super().__init__(
            db_manager=db_manager,
            signals=signals,
            title="Bons de Livraison Améliorés",
            description="إدارة بونات التسليم بالتنسيق الفرنسي",
            icon="📦"
        )
        self.setup_table()
        self.load_bls()

    def setup_table(self):
        """إعداد جدول عرض بونات التسليم"""
        columns = ["ID", "Numéro", "Date", "Client", "Type", "Statut"]
        self.items_table.setColumnCount(len(columns))
        self.items_table.setHorizontalHeaderLabels(columns)
        
        header = self.items_table.horizontalHeader()
        header.setStretchLastSection(True)

    def load_bls(self):
        """تحميل قائمة بونات التسليم"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT b.id, b.numero, b.date_creation, c.nom as client_nom,
                       b.type_marche, b.statut
                FROM bons_livraison b
                LEFT JOIN clients c ON b.client_id = c.id
                ORDER BY b.date_creation DESC
            """)
            bls = cursor.fetchall()
            
            self.items_table.setRowCount(len(bls))
            
            for row, bl in enumerate(bls):
                for col, value in enumerate(bl):
                    item = QTableWidgetItem(str(value) if value else "")
                    self.items_table.setItem(row, col, item)
                    
        except Exception as e:
            print(f"خطأ في تحميل بونات التسليم: {e}")

    def add_item(self):
        """إضافة بون تسليم جديد"""
        dialog = BLEnhancedDialog(self.db_manager, parent=self)
        dialog.bl_saved.connect(self.load_bls)
        dialog.exec()

    def edit_item(self, bl_id):
        """تعديل بون تسليم"""
        dialog = BLEnhancedDialog(self.db_manager, bl_id=bl_id, parent=self)
        dialog.bl_saved.connect(self.load_bls)
        dialog.exec()

    def delete_item(self, bl_id):
        """حذف بون تسليم"""
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا بون دو ليفريزون؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                cursor = self.db_manager.conn.cursor()
                cursor.execute("DELETE FROM lignes_bl WHERE bl_id = ?", (bl_id,))
                cursor.execute("DELETE FROM bons_livraison WHERE id = ?", (bl_id,))
                self.db_manager.conn.commit()
                self.load_bls()
                QMessageBox.information(self, "نجح", "تم الحذف بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في الحذف: {str(e)}")
