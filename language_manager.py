#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير اللغات - Language Manager
إدارة اللغات والترجمات والاتجاه (RTL/LTR)
"""

class LanguageManager:
    """مدير اللغات والترجمات"""

    def __init__(self):
        self.current_language = "fr"  # اللغة الافتراضية: الفرنسية
        self.is_rtl = False  # الاتجاه الافتراضي: من اليسار إلى اليمين

        # قاموس الترجمات - الفرنسية فقط
        self.translations = {
            "fr": {
                # الشريط الجانبي
                "app_title": "Système Comptable",
                "dashboard": "Tableau de Bord",
                "clients": "Clients",
                "suppliers": "Fournisseurs",
                "products": "Produits",
                "invoices": "Factures",
                "purchase_orders": "Bons de Commande",
                "delivery_notes": "Bons de Livraison",
                "devis": "Devis",
                "stock": "Stock",
                "cash": "Caisse",
                "tva": "TVA",
                "marche": "Marché",
                "reports": "Rapports",
                "search": "Recherche",
                "language": "Langue",

                # أزرار
                "add": "Ajouter",
                "edit": "Modifier",
                "delete": "Supprimer",
                "save": "Enregistrer",
                "cancel": "Annuler",
                "close": "Fermer",
                "print": "Imprimer",
                "export": "Exporter",

                # الحقول العامة
                "name": "Nom",
                "phone": "Téléphone",
                "address": "Adresse",
                "ice": "ICE",
                "date": "Date",
                "amount": "Montant",
                "quantity": "Quantité",
                "price": "Prix",
                "total": "Total",
                "currency": "DH",

                # الإحصائيات
                "total_clients": "Total Clients",
                "total_suppliers": "Total Fournisseurs",
                "total_products": "Total Produits",
                "total_invoices": "Total Factures",

                # الرسائل
                "success": "Succès",
                "error": "Erreur",
                "warning": "Avertissement",
                "info": "Information",
                "confirm_delete": "Êtes-vous sûr de vouloir supprimer cet élément?",

                # البحث
                "search_placeholder": "Rechercher...",
                "no_results": "Aucun résultat trouvé",
                "results_found": "résultats trouvés",

                # قسم الصفقات/العروض (Marchés)
                "marche_management": "Gestion des Marchés",
                "marche_general_info": "Informations Générales du Marché",
                "marche_bordereau": "Bordereau de Prix",
                "marche_frais": "Transport & Autres Frais",
                "type_marche": "Type de marché",
                "devis_numero": "DEVIS N°",
                "numero_manuel": "N",
                "nature_prestation": "Nature de prestation",
                "objet": "Objet",
                "delai_execution": "Délai d'execution",
                "client": "Client",
                "montant_ttc": "Montant TTC",
                "caution_provisoire": "Caution provisoire",
                "date_notification": "Date notification d'approbation",
                "caution_definitif": "Caution definitif",
                "ordre_service": "Ordre de service",
                "caution_retenu": "Caution retenu de garantie",
                "date_achevement": "Date prevu d'achevement",
                "designation": "Designation",
                "unite": "U",
                "quantite": "Qte",
                "prix_achat_ht": "Prix ACHAT HT",
                "total_achat_ht": "TOTAL ACHAT HT",
                "prix_u_ht": "Prix U HT",
                "total_vent_ht": "TOTAL VENT HT",
                "marge": "MARGE",
                "transport": "TRANSPORT",
                "courrier": "Courrier",
                "autres": "Autres",
                "reste_beneficiaire": "Reste bénéficiaire",
                "add_article": "+ Ajouter un article",
                "nouveau_marche": "Nouveau Marché",
                "tva_20": "TVA 20%",
                "ttc": "TTC",
                "dh": "DH",
            }
        }

    def get_text(self, key):
        """الحصول على النص المترجم"""
        return self.translations.get(self.current_language, {}).get(key, key)

    def set_language(self, language_code):
        """تغيير اللغة"""
        # الفرنسية فقط
        if language_code == "fr":
            self.current_language = "fr"
            self.is_rtl = False
            return True
        return False

    def get_current_language(self):
        """الحصول على اللغة الحالية"""
        return self.current_language

    def is_right_to_left(self):
        """التحقق من اتجاه النص"""
        return self.is_rtl

    def get_available_languages(self):
        """الحصول على قائمة اللغات المتاحة"""
        return {
            "fr": "Français"
        }

    def get_anchor_for_language(self):
        """الحصول على محاذاة النص حسب اللغة"""
        return "w"  # دائماً من اليسار للفرنسية

    def get_sticky_for_language(self):
        """الحصول على الالتصاق حسب اللغة"""
        return "nw"  # دائماً من اليسار للفرنسية

    def get_column_order(self, total_columns):
        """الحصول على ترتيب الأعمدة حسب اللغة"""
        return list(range(total_columns))  # دائماً ترتيب عادي للفرنسية
