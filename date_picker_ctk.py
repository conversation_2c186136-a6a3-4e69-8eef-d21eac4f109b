#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مكون تقويم مخصص لـ CustomTkinter
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk
from datetime import datetime, timedelta
import calendar

class DatePickerCTK(ctk.CTkFrame):
    """مكون تقويم مخصص لـ CustomTkinter"""

    def __init__(self, parent, initial_date=None, placeholder_text="اختر تاريخ", **kwargs):
        super().__init__(parent, **kwargs)

        self.selected_date = initial_date or datetime.now()
        self.placeholder_text = placeholder_text
        self.callback = None

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        self.grid_columnconfigure(0, weight=1)

        # حقل عرض التاريخ - يدعم الكتابة بلوحة المفاتيح
        self.date_display = ctk.CTkEntry(
            self,
            placeholder_text="DD/MM/YYYY"
        )
        self.date_display.grid(row=0, column=0, sticky="ew", padx=(0, 5))

        # تعيين القيمة الافتراضية
        self.date_display.insert(0, "__/__/____")

        # ربط أحداث الكتابة
        self.date_display.bind('<KeyPress>', self.on_key_press)
        self.date_display.bind('<KeyRelease>', self.on_text_change)
        self.date_display.bind('<BackSpace>', self.on_backspace_delete)
        self.date_display.bind('<Delete>', self.on_backspace_delete)
        self.date_display.bind('<FocusOut>', self.on_focus_out)

        # زر فتح التقويم
        self.calendar_btn = ctk.CTkButton(
            self,
            text="📅",
            width=40,
            command=self.show_calendar
        )
        self.calendar_btn.grid(row=0, column=1)

    def update_display(self):
        """تحديث عرض التاريخ"""
        if self.selected_date:
            date_str = self.selected_date.strftime("%d/%m/%Y")
            self.date_display.delete(0, "end")
            self.date_display.insert(0, date_str)

    def on_key_press(self, event):
        """التحكم في المفاتيح المسموحة"""
        key = event.keysym
        char = event.char

        # السماح بمفاتيح التحكم
        control_keys = ['BackSpace', 'Delete', 'Left', 'Right', 'Home', 'End', 'Tab']
        if key in control_keys:
            return True

        # السماح بالأرقام فقط
        if char.isdigit():
            return True

        # منع باقي المفاتيح
        return "break"

    def on_text_change(self, event):
        """معالجة تغيير النص مع حماية التنسيق"""
        current_text = self.date_display.get()
        cursor_pos = self.date_display.index("insert")

        # السماح فقط بالأرقام والشرطة المائلة والشرطة السفلية
        allowed_chars = '0123456789/_'
        filtered_text = ''.join(char for char in current_text if char in allowed_chars)

        # تطبيق تنسيق DD/MM/YYYY مع الحفاظ على الهيكل
        if len(filtered_text) <= 10:  # DD/MM/YYYY = 10 أحرف
            # إزالة الشرطات المائلة مؤقتاً للمعالجة
            numbers_only = ''.join(filter(str.isdigit, filtered_text))

            # بناء التنسيق الجديد
            formatted = ""
            if len(numbers_only) >= 1:
                formatted = numbers_only[:2].ljust(2, '_')
                if len(numbers_only) >= 3 or '/' in current_text[2:]:
                    formatted += '/' + numbers_only[2:4].ljust(2, '_')
                    if len(numbers_only) >= 5 or '/' in current_text[5:]:
                        formatted += '/' + numbers_only[4:8].ljust(4, '_')

            # تحديث النص إذا تغير
            if formatted != current_text and formatted:
                self.date_display.delete(0, "end")
                self.date_display.insert(0, formatted)
                # الحفاظ على موضع المؤشر
                try:
                    new_pos = min(cursor_pos, len(formatted))
                    # تجنب وضع المؤشر على الشرطة المائلة
                    if new_pos < len(formatted) and formatted[new_pos] == '/':
                        new_pos += 1
                    self.date_display.icursor(new_pos)
                except:
                    pass

    def on_backspace_delete(self, event):
        """معالجة خاصة للحذف والمسح"""
        current_text = self.date_display.get()
        cursor_pos = self.date_display.index("insert")

        if event.keysym == 'BackSpace' and cursor_pos > 0:
            # إذا كان المؤشر على شرطة مائلة، تحريكه للخلف
            if cursor_pos > 0 and current_text[cursor_pos-1] == '/':
                self.date_display.icursor(cursor_pos - 1)
                return "break"
            # استبدال الرقم بـ _
            elif cursor_pos > 0 and current_text[cursor_pos-1].isdigit():
                new_text = current_text[:cursor_pos-1] + '_' + current_text[cursor_pos:]
                self.date_display.delete(0, "end")
                self.date_display.insert(0, new_text)
                self.date_display.icursor(cursor_pos - 1)
                return "break"

        elif event.keysym == 'Delete' and cursor_pos < len(current_text):
            # إذا كان المؤشر على شرطة مائلة، تحريكه للأمام
            if current_text[cursor_pos] == '/':
                self.date_display.icursor(cursor_pos + 1)
                return "break"
            # استبدال الرقم بـ _
            elif current_text[cursor_pos].isdigit():
                new_text = current_text[:cursor_pos] + '_' + current_text[cursor_pos+1:]
                self.date_display.delete(0, "end")
                self.date_display.insert(0, new_text)
                self.date_display.icursor(cursor_pos)
                return "break"

        return "break"

    def on_focus_out(self, event):
        """معالجة فقدان التركيز - تحويل النص إلى تاريخ"""
        text = self.date_display.get().strip()
        if text and text != self.placeholder_text:
            try:
                # محاولة تحويل النص إلى تاريخ
                if '/' in text:
                    parts = text.split('/')
                    if len(parts) == 3:
                        day, month, year = parts
                        # إضافة القرن إذا كانت السنة قصيرة
                        if len(year) == 2:
                            current_year = datetime.now().year
                            century = current_year // 100
                            year = str(century) + year

                        # إنشاء كائن التاريخ
                        date_obj = datetime(int(year), int(month), int(day))
                        self.selected_date = date_obj
                        self.update_display()

                        # استدعاء دالة الاستدعاء إذا وجدت
                        if self.callback:
                            self.callback(date_obj)
            except (ValueError, IndexError):
                # في حالة الخطأ، مسح الحقل
                self.date_display.delete(0, "end")
                if self.selected_date:
                    self.update_display()

    def show_calendar(self):
        """عرض نافذة التقويم"""
        calendar_window = CalendarWindow(self, self.selected_date, self.on_date_selected)

    def on_date_selected(self, date):
        """استدعاء عند اختيار تاريخ"""
        self.selected_date = date
        self.update_display()
        if self.callback:
            self.callback(date)

    def set_callback(self, callback):
        """تعيين دالة الاستدعاء"""
        self.callback = callback

    def get_date(self):
        """الحصول على التاريخ المحدد"""
        return self.selected_date

    def set_date(self, date):
        """تعيين التاريخ"""
        self.selected_date = date
        self.update_display()

    def get_date_string(self, format_str="%Y-%m-%d"):
        """الحصول على التاريخ كنص"""
        if self.selected_date:
            return self.selected_date.strftime(format_str)
        return ""


class CalendarWindow(ctk.CTkToplevel):
    """نافذة التقويم"""

    def __init__(self, parent, initial_date, callback):
        super().__init__(parent)

        self.callback = callback
        self.current_date = initial_date or datetime.now()
        self.selected_date = initial_date

        self.setup_window()
        self.setup_ui()
        self.update_calendar()

    def setup_window(self):
        """إعداد النافذة"""
        self.title("اختيار التاريخ")
        self.geometry("300x350")
        self.resizable(False, False)
        self.transient(self.master)
        self.grab_set()

        # توسيط النافذة
        self.center_window()

    def center_window(self):
        """توسيط النافذة"""
        self.update_idletasks()
        x = (self.winfo_screenwidth() // 2) - (300 // 2)
        y = (self.winfo_screenheight() // 2) - (350 // 2)
        self.geometry(f"300x350+{x}+{y}")

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار التحكم في الشهر/السنة
        control_frame = ctk.CTkFrame(self)
        control_frame.pack(fill="x", padx=10, pady=10)

        # زر الشهر السابق
        self.prev_btn = ctk.CTkButton(
            control_frame,
            text="◀",
            width=30,
            command=self.prev_month
        )
        self.prev_btn.pack(side="left")

        # عرض الشهر والسنة
        self.month_year_label = ctk.CTkLabel(
            control_frame,
            text="",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.month_year_label.pack(side="left", expand=True)

        # زر الشهر التالي
        self.next_btn = ctk.CTkButton(
            control_frame,
            text="▶",
            width=30,
            command=self.next_month
        )
        self.next_btn.pack(side="right")

        # إطار التقويم
        self.calendar_frame = ctk.CTkFrame(self)
        self.calendar_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(self)
        buttons_frame.pack(fill="x", padx=10, pady=(0, 10))

        today_btn = ctk.CTkButton(
            buttons_frame,
            text="اليوم",
            command=self.select_today
        )
        today_btn.pack(side="left", padx=(0, 5))

        clear_btn = ctk.CTkButton(
            buttons_frame,
            text="مسح",
            command=self.clear_date
        )
        clear_btn.pack(side="left", padx=5)

        ok_btn = ctk.CTkButton(
            buttons_frame,
            text="موافق",
            command=self.confirm_selection
        )
        ok_btn.pack(side="right", padx=(5, 0))

        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=self.destroy
        )
        cancel_btn.pack(side="right")

    def update_calendar(self):
        """تحديث التقويم"""
        # مسح التقويم الحالي
        for widget in self.calendar_frame.winfo_children():
            widget.destroy()

        # تحديث عنوان الشهر/السنة
        month_names = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]
        month_name = month_names[self.current_date.month - 1]
        self.month_year_label.configure(text=f"{month_name} {self.current_date.year}")

        # إعداد الشبكة
        for i in range(7):
            self.calendar_frame.grid_columnconfigure(i, weight=1)

        # أسماء الأيام
        day_names = ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"]
        for i, day_name in enumerate(day_names):
            label = ctk.CTkLabel(
                self.calendar_frame,
                text=day_name,
                font=ctk.CTkFont(weight="bold")
            )
            label.grid(row=0, column=i, padx=1, pady=1, sticky="nsew")

        # الحصول على تقويم الشهر
        cal = calendar.monthcalendar(self.current_date.year, self.current_date.month)

        # إضافة أيام الشهر
        for week_num, week in enumerate(cal, 1):
            for day_num, day in enumerate(week):
                if day == 0:
                    continue

                # إنشاء زر اليوم
                day_date = datetime(self.current_date.year, self.current_date.month, day)

                # تحديد لون الزر
                if (self.selected_date and
                    day_date.date() == self.selected_date.date()):
                    # اليوم المحدد
                    fg_color = "#1f538d"
                elif day_date.date() == datetime.now().date():
                    # اليوم الحالي
                    fg_color = "#2fa572"
                else:
                    # يوم عادي
                    fg_color = "#3b3b3b"

                day_btn = ctk.CTkButton(
                    self.calendar_frame,
                    text=str(day),
                    width=35,
                    height=35,
                    fg_color=fg_color,
                    command=lambda d=day_date: self.select_date(d)
                )
                day_btn.grid(row=week_num, column=day_num, padx=1, pady=1, sticky="nsew")

    def prev_month(self):
        """الشهر السابق"""
        if self.current_date.month == 1:
            self.current_date = self.current_date.replace(year=self.current_date.year - 1, month=12)
        else:
            self.current_date = self.current_date.replace(month=self.current_date.month - 1)
        self.update_calendar()

    def next_month(self):
        """الشهر التالي"""
        if self.current_date.month == 12:
            self.current_date = self.current_date.replace(year=self.current_date.year + 1, month=1)
        else:
            self.current_date = self.current_date.replace(month=self.current_date.month + 1)
        self.update_calendar()

    def select_date(self, date):
        """اختيار تاريخ"""
        self.selected_date = date
        self.update_calendar()

    def select_today(self):
        """اختيار اليوم الحالي"""
        today = datetime.now()
        self.current_date = today
        self.selected_date = today
        self.update_calendar()

    def clear_date(self):
        """مسح التاريخ"""
        self.selected_date = None
        self.update_calendar()

    def confirm_selection(self):
        """تأكيد الاختيار"""
        if self.callback and self.selected_date:
            self.callback(self.selected_date)
        self.destroy()


# دالة مساعدة لإنشاء حقل تاريخ محسن
def create_enhanced_date_field(parent, placeholder_text="اختر تاريخ", initial_date=None, **kwargs):
    """إنشاء حقل تاريخ محسن لـ CustomTkinter"""
    return DatePickerCTK(parent, initial_date, placeholder_text, **kwargs)


# مثال للاستخدام
if __name__ == "__main__":
    # تطبيق تجريبي
    app = ctk.CTk()
    app.title("اختبار مكون التاريخ")
    app.geometry("400x200")

    # حقل تاريخ
    date_picker = create_enhanced_date_field(
        app,
        placeholder_text="اختر تاريخ الميلاد"
    )
    date_picker.pack(pady=20, padx=20, fill="x")

    # دالة لعرض التاريخ المحدد
    def on_date_change(date):
        print(f"تم اختيار التاريخ: {date.strftime('%d/%m/%Y')}")

    date_picker.set_callback(on_date_change)

    # زر لعرض التاريخ
    def show_selected_date():
        date = date_picker.get_date()
        if date:
            print(f"التاريخ المحدد: {date.strftime('%d/%m/%Y')}")
            print(f"تنسيق قاعدة البيانات: {date_picker.get_date_string()}")
        else:
            print("لم يتم اختيار تاريخ")

    show_btn = ctk.CTkButton(app, text="عرض التاريخ المحدد", command=show_selected_date)
    show_btn.pack(pady=10)

    app.mainloop()
