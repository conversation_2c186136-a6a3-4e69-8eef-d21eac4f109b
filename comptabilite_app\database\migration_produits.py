import sqlite3
import os

def migrate_produits():
    """
    Ajoute la colonne facture_achat_id à la table produits si elle n'existe pas déjà.
    """
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'comptabilite.db')

    # Vérifier si le répertoire data existe, sinon le créer
    data_dir = os.path.dirname(db_path)
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)

    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    try:
        # Vérifier si la table produits existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='produits'")
        if not cursor.fetchone():
            print("Migration: Création de la table produits...")
            cursor.execute("""
                CREATE TABLE produits (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT UNIQUE,
                    designation TEXT NOT NULL,
                    unite TEXT,
                    prix_achat REAL DEFAULT 0,
                    prix_vente REAL DEFAULT 0,
                    stock INTEGER DEFAULT 0,
                    facture_achat_id INTEGER,
                    FOREIGN KEY (facture_achat_id) REFERENCES factures_achat(id)
                )
            """)
        else:
            # Vérifier si la colonne facture_achat_id existe déjà dans la table produits
            cursor.execute("PRAGMA table_info(produits)")
            columns = cursor.fetchall()
            column_names = [column['name'] for column in columns]

            if 'facture_achat_id' not in column_names:
                print("Migration: Ajout de la colonne facture_achat_id à la table produits...")

                # Ajouter la colonne facture_achat_id
                cursor.execute("ALTER TABLE produits ADD COLUMN facture_achat_id INTEGER REFERENCES factures_achat(id)")

            # Ajouter les colonnes pour le stock si elles n'existent pas
            if 'stock_initial' not in column_names:
                print("Migration: Ajout de la colonne stock_initial à la table produits...")
                cursor.execute("ALTER TABLE produits ADD COLUMN stock_initial INTEGER DEFAULT 0")

            if 'date_creation' not in column_names:
                print("Migration: Ajout de la colonne date_creation à la table produits...")
                cursor.execute("ALTER TABLE produits ADD COLUMN date_creation TEXT DEFAULT CURRENT_DATE")

            if 'date_paiement' not in column_names:
                print("Migration: Ajout de la colonne date_paiement à la table produits...")
                cursor.execute("ALTER TABLE produits ADD COLUMN date_paiement TEXT")

            # Vérifier si la table factures_achat existe, sinon la créer
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='factures_achat'")
            if not cursor.fetchone():
                print("Migration: Création de la table factures_achat...")
                cursor.execute("""
                    CREATE TABLE factures_achat (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        fournisseur_id INTEGER,
                        numero TEXT,
                        date TEXT,
                        mode_paiement TEXT,
                        total_ht REAL,
                        tva REAL,
                        ttc REAL,
                        date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (fournisseur_id) REFERENCES fournisseurs(id)
                    )
                """)

        conn.commit()
        print("Migration des produits terminée avec succès.")

    except sqlite3.Error as e:
        conn.rollback()
        print(f"Erreur lors de la migration des produits: {str(e)}")

    finally:
        conn.close()

if __name__ == "__main__":
    migrate_produits()
