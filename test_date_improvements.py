#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تحسينات خانات التواريخ في النظام
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QFormLayout, QLabel
from PySide6.QtCore import QDate
from comptabilite_app.ui.style import create_styled_date_edit, DATEEDIT_STYLE, DATEEDIT_OPTIONAL_STYLE

class DateTestWindow(QMainWindow):
    """نافذة اختبار خانات التواريخ المحسنة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار خانات التواريخ المحسنة")
        self.setGeometry(100, 100, 600, 400)
        
        # Widget مركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout رئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # عنوان
        title = QLabel("🗓️ اختبار خانات التواريخ المحسنة")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #1E3A8A;
                padding: 15px;
                background-color: #E0E7FF;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        main_layout.addWidget(title)
        
        # نموذج الاختبار
        form_layout = QFormLayout()
        
        # خانة تاريخ عادية
        self.date_normal = create_styled_date_edit()
        form_layout.addRow("📅 تاريخ عادي:", self.date_normal)
        
        # خانة تاريخ اختيارية
        self.date_optional = create_styled_date_edit(
            optional=True, 
            placeholder_text="تاريخ اختياري"
        )
        form_layout.addRow("📅 تاريخ اختياري:", self.date_optional)
        
        # خانة تاريخ مع تاريخ محدد
        self.date_custom = create_styled_date_edit(
            current_date=QDate.currentDate().addDays(30)
        )
        form_layout.addRow("📅 تاريخ مخصص (+30 يوم):", self.date_custom)
        
        # خانة تاريخ تسليم اختيارية
        self.date_delivery = create_styled_date_edit(
            optional=True,
            placeholder_text="تاريخ التسليم (اختياري)"
        )
        form_layout.addRow("🚚 تاريخ التسليم:", self.date_delivery)
        
        # خانة تاريخ دفع اختيارية
        self.date_payment = create_styled_date_edit(
            optional=True,
            placeholder_text="غير مدفوع"
        )
        form_layout.addRow("💰 تاريخ الدفع:", self.date_payment)
        
        main_layout.addLayout(form_layout)
        
        # معلومات إضافية
        info_label = QLabel("""
        ✨ التحسينات المطبقة:
        • تصميم موحد وجميل لجميع خانات التواريخ
        • تقويم منبثق سهل الاستخدام
        • خانات اختيارية مع نص توضيحي
        • ألوان متناسقة مع تأثيرات hover و focus
        • تنسيق التاريخ بالشكل dd/MM/yyyy
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #F0FDF4;
                border: 1px solid #10B981;
                border-radius: 6px;
                padding: 15px;
                margin-top: 20px;
                font-size: 14px;
                line-height: 1.5;
            }
        """)
        main_layout.addWidget(info_label)

def main():
    """تشغيل اختبار خانات التواريخ"""
    app = QApplication(sys.argv)
    
    # تطبيق الستايل العام
    app.setStyleSheet("""
        QMainWindow {
            background-color: #F8FAFC;
        }
        QWidget {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
    """)
    
    window = DateTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
