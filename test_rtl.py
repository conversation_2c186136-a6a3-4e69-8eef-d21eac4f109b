#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار دعم RTL في التطبيق
"""

import customtkinter as ctk
from language_manager import LanguageManager

class TestRTLApp(ctk.CTk):
    def __init__(self):
        super().__init__()

        self.language_manager = LanguageManager()

        self.title("اختبار دعم RTL")
        self.geometry("800x600")

        self.setup_ui()

    def setup_ui(self):
        # حذف العناصر الموجودة
        for widget in self.winfo_children():
            widget.destroy()

        # تحديد موضع الشريط الجانبي حسب اللغة
        if self.language_manager.is_right_to_left():
            sidebar_column = 1
            main_column = 0
            sidebar_sticky = "nse"
        else:
            sidebar_column = 0
            main_column = 1
            sidebar_sticky = "nsw"

        # إعداد الشبكة
        self.grid_columnconfigure(0, weight=1)
        self.grid_columnconfigure(1, weight=3)
        self.grid_rowconfigure(0, weight=1)

        # الشريط الجانبي
        sidebar = ctk.CTkFrame(self, width=200)
        sidebar.grid(row=0, column=sidebar_column, sticky=sidebar_sticky, padx=10, pady=10)

        # عنوان التطبيق
        title = ctk.CTkLabel(
            sidebar,
            text=self.language_manager.get_text("app_title"),
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title.pack(pady=20)

        # أزرار التنقل
        nav_buttons = [
            ("dashboard", "📊"),
            ("clients", "👥"),
            ("suppliers", "🏢"),
            ("products", "📦"),
            ("invoices", "📄")
        ]

        for key, icon in nav_buttons:
            text = f"{icon} {self.language_manager.get_text(key)}"
            btn = ctk.CTkButton(sidebar, text=text, anchor="center")
            btn.pack(pady=5, padx=10, fill="x")

        # زر تغيير اللغة
        current_lang = self.language_manager.get_current_language()
        lang_text = "العربية" if current_lang == "fr" else "Français"

        lang_btn = ctk.CTkButton(
            sidebar,
            text=f"🌐 {lang_text}",
            command=self.toggle_language,
            fg_color="#2B2B2B"
        )
        lang_btn.pack(pady=20, padx=10, fill="x")

        # المحتوى الرئيسي
        main_content = ctk.CTkFrame(self)
        main_content.grid(row=0, column=main_column, sticky="nsew", padx=10, pady=10)

        # محاذاة النص
        anchor = self.language_manager.get_anchor_for_language()

        # عنوان الصفحة
        page_title = ctk.CTkLabel(
            main_content,
            text=self.language_manager.get_text("dashboard"),
            font=ctk.CTkFont(size=24, weight="bold")
        )
        page_title.pack(anchor=anchor, padx=20, pady=20)

        # إحصائيات
        stats_frame = ctk.CTkFrame(main_content)
        stats_frame.pack(fill="x", padx=20, pady=10)
        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        # ترتيب البطاقات حسب اللغة
        if self.language_manager.is_right_to_left():
            stats = [
                ("total_suppliers", "45", "#f59e0b"),
                ("total_clients", "123", "#22c55e"),
                ("total_products", "89", "#3b82f6"),
                ("total_invoices", "67", "#a855f7")
            ]
        else:
            stats = [
                ("total_invoices", "67", "#a855f7"),
                ("total_products", "89", "#3b82f6"),
                ("total_clients", "123", "#22c55e"),
                ("total_suppliers", "45", "#f59e0b")
            ]

        for i, (key, value, color) in enumerate(stats):
            self.create_stat_card(stats_frame, key, value, color, i)

        # نص تجريبي
        test_text = """
هذا نص تجريبي باللغة العربية لاختبار دعم RTL في التطبيق.
يجب أن يظهر النص من اليمين إلى اليسار بشكل صحيح.
        """ if self.language_manager.is_right_to_left() else """
This is a test text in French to test LTR support in the application.
The text should appear from left to right correctly.
        """

        text_label = ctk.CTkLabel(
            main_content,
            text=test_text.strip(),
            font=ctk.CTkFont(size=14),
            justify="right" if self.language_manager.is_right_to_left() else "left"
        )
        text_label.pack(anchor=anchor, padx=20, pady=20)

    def create_stat_card(self, parent, key, value, color, column):
        card = ctk.CTkFrame(parent)
        card.grid(row=0, column=column, padx=5, pady=10, sticky="ew")

        anchor = self.language_manager.get_anchor_for_language()

        title = ctk.CTkLabel(
            card,
            text=self.language_manager.get_text(key),
            font=ctk.CTkFont(size=14)
        )
        title.pack(anchor=anchor, pady=(10, 5))

        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=color
        )
        value_label.pack(anchor=anchor, pady=(0, 10))

    def toggle_language(self):
        current_lang = self.language_manager.get_current_language()
        new_lang = "ar" if current_lang == "fr" else "fr"

        self.language_manager.set_language(new_lang)
        self.title(self.language_manager.get_text("app_title"))
        self.setup_ui()

if __name__ == "__main__":
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("dark-blue")

    app = TestRTLApp()
    app.mainloop()
