from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QComboBox, QDateEdit, QDoubleSpinBox, QSpinBox,
                              QFormLayout, QMessageBox, QHeaderView, QDialog,
                              QFrame, QTabWidget, QRadioButton, QButtonGroup)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QColor
import os
import sqlite3
import datetime

from ..utils.pdf_generator import PDFGenerator

class AjouterLigneDialog(QDialog):
    def __init__(self, db_manager, parent=None, type_doc="commande"):
        super().__init__(parent)
        self.db_manager = db_manager
        self.type_doc = type_doc
        self.setWindowTitle("Ajouter une ligne")
        self.setMinimumWidth(400)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        form_layout = QFormLayout()

        # Sélection du produit
        self.produit_combo = QComboBox()
        self.charger_produits()
        self.produit_combo.currentIndexChanged.connect(self.produit_selectionne)
        form_layout.addRow("Produit:", self.produit_combo)

        # Désignation
        self.designation_input = QLineEdit()
        form_layout.addRow("Désignation:", self.designation_input)

        # Quantité
        self.quantite_input = QSpinBox()
        self.quantite_input.setRange(1, 1000)
        self.quantite_input.setValue(1)
        self.quantite_input.valueChanged.connect(self.calculer_total)
        form_layout.addRow("Quantité:", self.quantite_input)

        # Prix unitaire
        self.prix_input = QDoubleSpinBox()
        self.prix_input.setRange(0, 1000000)
        self.prix_input.setDecimals(2)
        self.prix_input.setSuffix(" DH")
        self.prix_input.valueChanged.connect(self.calculer_total)
        form_layout.addRow("Prix unitaire HT:", self.prix_input)

        # TVA
        self.tva_input = QDoubleSpinBox()
        self.tva_input.setRange(0, 100)
        self.tva_input.setDecimals(1)
        self.tva_input.setValue(20.0)
        self.tva_input.setSuffix(" %")
        form_layout.addRow("TVA:", self.tva_input)

        # Total HT
        self.total_ht_label = QLabel("0.00 DH")
        form_layout.addRow("Total HT:", self.total_ht_label)

        layout.addLayout(form_layout)

        # Boutons
        buttons_layout = QHBoxLayout()

        self.ajouter_btn = QPushButton("Ajouter")
        self.ajouter_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.ajouter_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(self.ajouter_btn)

        self.annuler_btn = QPushButton("Annuler")
        self.annuler_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        self.annuler_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.annuler_btn)

        layout.addLayout(buttons_layout)

    def charger_produits(self):
        cursor = self.db_manager.conn.cursor()

        if self.type_doc == "commande":
            # Pour les commandes, on utilise le prix d'achat
            cursor.execute("SELECT id, reference, designation, prix_achat_ht, taux_tva FROM produits ORDER BY reference")
        else:
            # Pour les bons de livraison, on utilise le prix de vente
            cursor.execute("SELECT id, reference, designation, prix_vente_ht, taux_tva FROM produits ORDER BY reference")

        produits = cursor.fetchall()

        self.produit_combo.clear()
        self.produit_combo.addItem("-- Sélectionner un produit --", None)

        for produit in produits:
            self.produit_combo.addItem(
                f"{produit['reference']} - {produit['designation']}",
                produit['id']
            )

    def produit_selectionne(self):
        produit_id = self.produit_combo.currentData()

        if produit_id:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT * FROM produits WHERE id = ?", (produit_id,))
            produit = cursor.fetchone()

            if produit:
                self.designation_input.setText(produit['designation'])

                if self.type_doc == "commande":
                    # Pour les commandes, on utilise le prix d'achat
                    self.prix_input.setValue(produit['prix_achat_ht'])
                else:
                    # Pour les bons de livraison, on utilise le prix de vente
                    self.prix_input.setValue(produit['prix_vente_ht'])

                self.tva_input.setValue(produit['taux_tva'])
                self.calculer_total()

    def calculer_total(self):
        quantite = self.quantite_input.value()
        prix = self.prix_input.value()
        total = quantite * prix
        self.total_ht_label.setText(f"{total:.2f} DH")

class BonsModule(QWidget):
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.type_doc = "commande"  # Par défaut, on crée un bon de commande
        self.setup_ui()
        self.nouveau_bon()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)

        # Titre du module
        title = QLabel("Gestion des Bons de Commande et de Livraison")
        title.setStyleSheet("font-size: 22px; font-weight: bold; color: #2c3e50; margin-bottom: 15px;")
        layout.addWidget(title)

        # Sélection du type de document
        type_container = QFrame()
        type_container.setFrameShape(QFrame.StyledPanel)
        type_container.setStyleSheet("background-color: #f8f9fa; border-radius: 5px; padding: 10px;")
        type_layout = QHBoxLayout(type_container)

        type_label = QLabel("Type de document:")
        type_label.setStyleSheet("font-weight: bold;")
        type_layout.addWidget(type_label)

        self.type_group = QButtonGroup(self)

        self.commande_radio = QRadioButton("Bon de commande")
        self.commande_radio.setChecked(True)
        self.commande_radio.toggled.connect(self.on_type_changed)
        self.type_group.addButton(self.commande_radio)
        type_layout.addWidget(self.commande_radio)

        self.livraison_radio = QRadioButton("Bon de livraison")
        self.livraison_radio.toggled.connect(self.on_type_changed)
        self.type_group.addButton(self.livraison_radio)
        type_layout.addWidget(self.livraison_radio)

        layout.addWidget(type_container)

        # Formulaire de création de bon
        form_container = QFrame()
        form_container.setFrameShape(QFrame.StyledPanel)
        form_container.setStyleSheet("background-color: #f8f9fa; border-radius: 5px; padding: 15px;")
        form_layout = QFormLayout(form_container)
        form_layout.setSpacing(10)

        # Informations générales du bon
        self.numero_bon = QLineEdit()
        self.numero_bon.setReadOnly(True)
        self.numero_bon.setStyleSheet("background-color: #e9ecef; color: #495057; padding: 8px;")
        form_layout.addRow("Numéro:", self.numero_bon)

        self.date_bon = QDateEdit(QDate.currentDate())
        self.date_bon.setStyleSheet("padding: 8px;")
        form_layout.addRow("Date:", self.date_bon)

        self.date_livraison = QDateEdit(QDate.currentDate().addDays(7))
        self.date_livraison.setStyleSheet("padding: 8px;")
        form_layout.addRow("Date de livraison prévue:", self.date_livraison)

        # Destinataire (fournisseur ou client selon le type)
        self.destinataire_combo = QComboBox()
        self.destinataire_combo.setStyleSheet("padding: 8px;")
        form_layout.addRow("Destinataire:", self.destinataire_combo)

        layout.addWidget(form_container)

        # Tableau des lignes
        table_label = QLabel("Lignes du document")
        table_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin-top: 10px;")
        layout.addWidget(table_label)

        self.lignes_table = QTableWidget(0, 6)
        self.lignes_table.setHorizontalHeaderLabels([
            "Produit", "Désignation", "Quantité", "Prix unitaire HT", "TVA %", "Total HT"
        ])

        # Style du tableau
        self.lignes_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
                gridline-color: #ddd;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 6px;
                border: 1px solid #ddd;
                font-weight: bold;
            }
            QTableWidget::item {
                padding: 6px;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: black;
            }
        """)

        # Configuration des colonnes
        self.lignes_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.lignes_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.lignes_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.lignes_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.lignes_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.lignes_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)

        layout.addWidget(self.lignes_table)

        # Boutons pour gérer les lignes
        lignes_buttons = QHBoxLayout()

        self.ajouter_ligne_btn = QPushButton("Ajouter une ligne")
        self.ajouter_ligne_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.ajouter_ligne_btn.clicked.connect(self.ajouter_ligne)
        lignes_buttons.addWidget(self.ajouter_ligne_btn)

        self.supprimer_ligne_btn = QPushButton("Supprimer la ligne")
        self.supprimer_ligne_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        self.supprimer_ligne_btn.clicked.connect(self.supprimer_ligne)
        lignes_buttons.addWidget(self.supprimer_ligne_btn)

        layout.addLayout(lignes_buttons)

        # Totaux
        totaux_container = QFrame()
        totaux_container.setFrameShape(QFrame.StyledPanel)
        totaux_container.setStyleSheet("background-color: #f8f9fa; border-radius: 5px; padding: 10px;")
        totaux_layout = QFormLayout(totaux_container)

        self.total_ht_label = QLabel("0.00 DH")
        self.total_ht_label.setStyleSheet("font-size: 14px;")
        totaux_layout.addRow("Total HT:", self.total_ht_label)

        self.total_tva_label = QLabel("0.00 DH")
        self.total_tva_label.setStyleSheet("font-size: 14px;")
        totaux_layout.addRow("Total TVA:", self.total_tva_label)

        self.total_ttc_label = QLabel("0.00 DH")
        self.total_ttc_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        totaux_layout.addRow("Total TTC:", self.total_ttc_label)

        layout.addWidget(totaux_container)

        # Boutons d'action
        actions_layout = QHBoxLayout()

        self.enregistrer_btn = QPushButton("Enregistrer")
        self.enregistrer_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0069d9;
            }
        """)
        self.enregistrer_btn.clicked.connect(self.enregistrer_bon)
        actions_layout.addWidget(self.enregistrer_btn)

        self.exporter_pdf_btn = QPushButton("Exporter en PDF")
        self.exporter_pdf_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        self.exporter_pdf_btn.clicked.connect(self.exporter_pdf)
        actions_layout.addWidget(self.exporter_pdf_btn)

        self.nouveau_btn = QPushButton("Nouveau document")
        self.nouveau_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        self.nouveau_btn.clicked.connect(self.nouveau_bon)
        actions_layout.addWidget(self.nouveau_btn)

        layout.addLayout(actions_layout)

        # Charger les destinataires (fournisseurs par défaut)
        self.charger_destinataires()

    def on_type_changed(self):
        """Appelé quand le type de document change"""
        if self.commande_radio.isChecked():
            self.type_doc = "commande"
        else:
            self.type_doc = "livraison"

        # Mettre à jour le numéro du document
        self.generer_numero()

        # Mettre à jour la liste des destinataires
        self.charger_destinataires()

    def charger_destinataires(self):
        """Charge les destinataires en fonction du type de document"""
        self.destinataire_combo.clear()

        cursor = self.db_manager.conn.cursor()

        if self.type_doc == "commande":
            # Pour les bons de commande, on utilise les fournisseurs
            cursor.execute("SELECT id, code, nom FROM fournisseurs ORDER BY nom")
            destinataires = cursor.fetchall()
            self.destinataire_combo.setItemText(0, "Fournisseur:")

            for destinataire in destinataires:
                self.destinataire_combo.addItem(
                    f"{destinataire['code']} - {destinataire['nom']}",
                    destinataire['id']
                )
        else:
            # Pour les bons de livraison, on utilise les clients
            cursor.execute("SELECT id, code, nom FROM clients ORDER BY nom")
            destinataires = cursor.fetchall()
            self.destinataire_combo.setItemText(0, "Client:")

            for destinataire in destinataires:
                self.destinataire_combo.addItem(
                    f"{destinataire['code']} - {destinataire['nom']}",
                    destinataire['id']
                )

    def generer_numero(self):
        """Génère un numéro de document en fonction du type"""
        if self.type_doc == "commande":
            self.numero_bon.setText(self.db_manager.generer_numero_commande())
        else:
            # Générer un numéro de bon de livraison (BL)
            date_str = datetime.datetime.now().strftime("%Y%m")
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM commandes WHERE numero LIKE ?", (f"BL{date_str}%",))
            count = cursor.fetchone()[0] + 1
            self.numero_bon.setText(f"BL{date_str}-{count:03d}")

    def ajouter_ligne(self):
        """Ajoute une ligne au document"""
        dialog = AjouterLigneDialog(self.db_manager, self, self.type_doc)
        if dialog.exec():
            produit_id = dialog.produit_combo.currentData()
            designation = dialog.designation_input.text()
            quantite = dialog.quantite_input.value()
            prix_unitaire = dialog.prix_input.value()
            taux_tva = dialog.tva_input.value()
            total_ht = quantite * prix_unitaire

            row = self.lignes_table.rowCount()
            self.lignes_table.insertRow(row)

            self.lignes_table.setItem(row, 0, QTableWidgetItem(dialog.produit_combo.currentText()))
            self.lignes_table.setItem(row, 1, QTableWidgetItem(designation))
            self.lignes_table.setItem(row, 2, QTableWidgetItem(str(quantite)))
            self.lignes_table.setItem(row, 3, QTableWidgetItem(f"{prix_unitaire:.2f} DH"))
            self.lignes_table.setItem(row, 4, QTableWidgetItem(f"{taux_tva:.1f} %"))
            self.lignes_table.setItem(row, 5, QTableWidgetItem(f"{total_ht:.2f} DH"))

            # Stocker les données pour le calcul
            self.lignes_table.item(row, 0).setData(Qt.UserRole, produit_id)
            self.lignes_table.item(row, 2).setData(Qt.UserRole, quantite)
            self.lignes_table.item(row, 3).setData(Qt.UserRole, prix_unitaire)
            self.lignes_table.item(row, 4).setData(Qt.UserRole, taux_tva)
            self.lignes_table.item(row, 5).setData(Qt.UserRole, total_ht)

            self.calculer_totaux()

    def supprimer_ligne(self):
        """Supprime la ligne sélectionnée"""
        selected_row = self.lignes_table.currentRow()
        if selected_row >= 0:
            self.lignes_table.removeRow(selected_row)
            self.calculer_totaux()

    def calculer_totaux(self):
        """Calcule les totaux du document"""
        total_ht = 0
        total_tva = 0

        for row in range(self.lignes_table.rowCount()):
            quantite = self.lignes_table.item(row, 2).data(Qt.UserRole)
            prix_unitaire = self.lignes_table.item(row, 3).data(Qt.UserRole)
            taux_tva = self.lignes_table.item(row, 4).data(Qt.UserRole)

            ligne_ht = quantite * prix_unitaire
            ligne_tva = ligne_ht * (taux_tva / 100)

            total_ht += ligne_ht
            total_tva += ligne_tva

        total_ttc = total_ht + total_tva

        self.total_ht_label.setText(f"{total_ht:.2f} DH")
        self.total_tva_label.setText(f"{total_tva:.2f} DH")
        self.total_ttc_label.setText(f"{total_ttc:.2f} DH")

    def enregistrer_bon(self):
        """Enregistre le bon dans la base de données"""
        # Vérifier qu'il y a au moins une ligne
        if self.lignes_table.rowCount() == 0:
            QMessageBox.warning(self, "Erreur", "Ajoutez au moins une ligne au document.")
            return

        # Vérifier qu'un destinataire est sélectionné
        if self.destinataire_combo.count() == 0:
            if self.type_doc == "commande":
                QMessageBox.warning(self, "Erreur", "Aucun fournisseur disponible. Veuillez en créer un d'abord.")
            else:
                QMessageBox.warning(self, "Erreur", "Aucun client disponible. Veuillez en créer un d'abord.")
            return

        # Récupérer les données du document
        numero = self.numero_bon.text()
        date_creation = self.date_bon.date().toString("yyyy-MM-dd")
        date_livraison = self.date_livraison.date().toString("yyyy-MM-dd")
        destinataire_id = self.destinataire_combo.currentData()

        # Récupérer les totaux
        total_ht = float(self.total_ht_label.text().replace(" DH", ""))
        total_tva = float(self.total_tva_label.text().replace(" DH", ""))
        total_ttc = float(self.total_ttc_label.text().replace(" DH", ""))

        # Enregistrer le document
        cursor = self.db_manager.conn.cursor()

        if self.type_doc == "commande":
            # Enregistrer comme bon de commande
            cursor.execute(
                """INSERT INTO commandes
                   (numero, date_creation, date_livraison, fournisseur_id,
                    total_ht, total_tva, total_ttc, statut)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                (numero, date_creation, date_livraison, destinataire_id,
                 total_ht, total_tva, total_ttc, "En attente")
            )

            commande_id = cursor.lastrowid

            # Enregistrer les lignes
            for row in range(self.lignes_table.rowCount()):
                produit_id = self.lignes_table.item(row, 0).data(Qt.UserRole)
                designation = self.lignes_table.item(row, 1).text()
                quantite = self.lignes_table.item(row, 2).data(Qt.UserRole)
                prix_unitaire = self.lignes_table.item(row, 3).data(Qt.UserRole)
                taux_tva = self.lignes_table.item(row, 4).data(Qt.UserRole)
                total_ligne = self.lignes_table.item(row, 5).data(Qt.UserRole)

                cursor.execute(
                    """INSERT INTO lignes_commande
                       (commande_id, produit_id, designation, quantite,
                        prix_unitaire_ht, taux_tva, total_ht)
                       VALUES (?, ?, ?, ?, ?, ?, ?)""",
                    (commande_id, produit_id, designation, quantite,
                     prix_unitaire, taux_tva, total_ligne)
                )
        else:
            # Enregistrer comme bon de livraison (dans la même table que les commandes)
            cursor.execute(
                """INSERT INTO commandes
                   (numero, date_creation, date_livraison, client_id,
                    total_ht, total_tva, total_ttc, statut, type)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (numero, date_creation, date_livraison, destinataire_id,
                 total_ht, total_tva, total_ttc, "Livré", "livraison")
            )

            livraison_id = cursor.lastrowid

            # Enregistrer les lignes
            for row in range(self.lignes_table.rowCount()):
                produit_id = self.lignes_table.item(row, 0).data(Qt.UserRole)
                designation = self.lignes_table.item(row, 1).text()
                quantite = self.lignes_table.item(row, 2).data(Qt.UserRole)
                prix_unitaire = self.lignes_table.item(row, 3).data(Qt.UserRole)
                taux_tva = self.lignes_table.item(row, 4).data(Qt.UserRole)
                total_ligne = self.lignes_table.item(row, 5).data(Qt.UserRole)

                cursor.execute(
                    """INSERT INTO lignes_commande
                       (commande_id, produit_id, designation, quantite,
                        prix_unitaire_ht, taux_tva, total_ht)
                       VALUES (?, ?, ?, ?, ?, ?, ?)""",
                    (livraison_id, produit_id, designation, quantite,
                     prix_unitaire, taux_tva, total_ligne)
                )

                # Mettre à jour le stock si c'est un bon de livraison
                cursor.execute(
                    "UPDATE produits SET stock = stock - ? WHERE id = ?",
                    (quantite, produit_id)
                )

        self.db_manager.conn.commit()

        if self.type_doc == "commande":
            QMessageBox.information(self, "Succès", "Bon de commande enregistré avec succès.")
        else:
            QMessageBox.information(self, "Succès", "Bon de livraison enregistré avec succès.")

        self.nouveau_bon()

    def exporter_pdf(self):
        """Exporte le document en PDF"""
        # Vérifier qu'il y a au moins une ligne
        if self.lignes_table.rowCount() == 0:
            QMessageBox.warning(self, "Erreur", "Ajoutez au moins une ligne au document.")
            return

        # Récupérer les données du document
        numero = self.numero_bon.text()
        date = self.date_bon.date().toString("dd/MM/yyyy")
        date_livraison = self.date_livraison.date().toString("dd/MM/yyyy")
        destinataire_nom = self.destinataire_combo.currentText().split(" - ", 1)[1] if " - " in self.destinataire_combo.currentText() else ""

        # Récupérer les lignes
        lignes = []
        for row in range(self.lignes_table.rowCount()):
            ligne = {
                'designation': self.lignes_table.item(row, 1).text(),
                'quantite': self.lignes_table.item(row, 2).data(Qt.UserRole),
                'prix_unitaire': self.lignes_table.item(row, 3).data(Qt.UserRole),
                'taux_tva': self.lignes_table.item(row, 4).data(Qt.UserRole),
                'total_ht': self.lignes_table.item(row, 5).data(Qt.UserRole)
            }
            lignes.append(ligne)

        # Récupérer les totaux
        total_ht = float(self.total_ht_label.text().replace(" DH", ""))
        total_tva = float(self.total_tva_label.text().replace(" DH", ""))
        total_ttc = float(self.total_ttc_label.text().replace(" DH", ""))

        # Créer les données du document
        document_data = {
            'numero': numero,
            'date': date,
            'date_livraison': date_livraison,
            'lignes': lignes,
            'total_ht': total_ht,
            'total_tva': total_tva,
            'total_ttc': total_ttc
        }

        if self.type_doc == "commande":
            document_data['fournisseur_nom'] = destinataire_nom
        else:
            document_data['client_nom'] = destinataire_nom

        # Créer le dossier de sortie s'il n'existe pas
        os.makedirs("documents", exist_ok=True)

        # Définir le nom du fichier
        if self.type_doc == "commande":
            filename = f"documents/bon_commande_{numero.replace('/', '_')}.pdf"
        else:
            filename = f"documents/bon_livraison_{numero.replace('/', '_')}.pdf"

        # Générer le PDF
        pdf_generator = PDFGenerator(logo_path="assets/images/logo.png")
        pdf_generator.generate_bon_commande_pdf(document_data, filename, type_doc=self.type_doc)

        QMessageBox.information(self, "Succès", f"Document exporté avec succès dans le fichier {filename}")

    def nouveau_bon(self):
        """Réinitialise le formulaire pour un nouveau document"""
        # Générer un nouveau numéro
        self.generer_numero()

        # Réinitialiser les dates
        self.date_bon.setDate(QDate.currentDate())
        self.date_livraison.setDate(QDate.currentDate().addDays(7))

        # Réinitialiser le destinataire
        if self.destinataire_combo.count() > 0:
            self.destinataire_combo.setCurrentIndex(0)

        # Vider le tableau des lignes
        self.lignes_table.setRowCount(0)

        # Réinitialiser les totaux
        self.calculer_totaux()
