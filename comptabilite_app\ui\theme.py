"""
Module contenant les styles CSS modernes pour l'application
"""

# Palette de couleurs moderne et professionnelle
COLORS = {
    # Couleur primaire - bleu professionnel
    "primary": "#1E3A8A",       # Bleu professionnel
    "primary_light": "#2563EB", # Ble<PERSON> royal
    "primary_dark": "#1E40AF",  # Bleu marine

    # Couleur secondaire - turquoise/teal
    "secondary": "#6B7280",     # Vert-bleu professionnel
    "secondary_light": "#9CA3AF", # Vert-bleu clair
    "secondary_dark": "#059669", # Vert-bleu foncé

    # Couleur d'accent - violet
    "accent": "#8B5CF6",        # Violet/Indigo
    "accent_light": "#A78BFA",  # Violet clair
    "accent_dark": "#6D28D9",   # Violet foncé

    # Couleurs neutres
    "background": "#F3F4F6",    # Gris très clair
    "surface": "#FFFFFF",       # <PERSON>
    "text_primary": "#111827",  # Presque noir
    "text_secondary": "#4B5563", # Gris foncé
    "divider": "#E5E7EB",       # Gris clair

    # Couleurs d'état
    "success": "#10B981",       # Vert émeraude
    "warning": "#F59E0B",       # Ambre foncé
    "error": "#EF4444",         # Rouge vif
    "info": "#3B82F6",          # Bleu informatif

    # Couleurs supplémentaires
    "card_bg": "#FFFFFF",       # Blanc
    "hover_bg": "#F9FAFB",      # Gris très clair
    "active_bg": "#EBF5FF",     # Bleu très clair
    "disabled": "#9CA3AF",      # Gris moyen

    # Couleurs pour les graphiques
    "chart_1": "#3B82F6",       # Bleu
    "chart_2": "#10B981",       # Vert
    "chart_3": "#F59E0B",       # Ambre
    "chart_4": "#8B5CF6",       # Violet
    "chart_5": "#EC4899",       # Rose
    "chart_6": "#06B6D4",       # Cyan
    "chart_7": "#F97316",       # Orange

    # Couleurs pour les ombres
    "shadow_light": "rgba(0, 0, 0, 0.05)",
    "shadow_medium": "rgba(0, 0, 0, 0.1)",
    "shadow_dark": "rgba(0, 0, 0, 0.2)",
}

# Variables pour les espacements et dimensions
SPACING = {
    "xxs": "4px",
    "xs": "8px",
    "sm": "12px",
    "md": "16px",
    "lg": "24px",
    "xl": "32px",
}

BORDER_RADIUS = {
    "none": "0",
    "xs": "4px",
    "sm": "6px",
    "md": "8px",
    "lg": "12px",
    "xl": "16px",
    "xxl": "24px",
    "pill": "9999px",
    "circle": "50%",
}

# Variables pour les ombres
SHADOWS = {
    "none": "none",
    "sm": "0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)",
    "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
    "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
    "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
}

# Variables pour les transitions
TRANSITIONS = {
    "fast": "0.2s ease-in-out",
    "normal": "0.3s ease-in-out",
    "slow": "0.4s ease-in-out",
}

# Style global de l'application
APP_STYLE = f"""
    QWidget {{
        font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
        color: {COLORS['text_primary']};
        background-color: {COLORS['background']};
        font-size: 13px;
    }}

    QMainWindow {{
        background-color: {COLORS['background']};
    }}

    QLabel {{
        color: {COLORS['text_primary']};
        font-size: 13px;
        padding: {SPACING['xs']};
    }}

    QLabel[title="true"] {{
        font-size: 20px;
        font-weight: bold;
        color: {COLORS['primary']};
        padding: {SPACING['md']};
        letter-spacing: 0.5px;
    }}

    QLabel[subtitle="true"] {{
        font-size: 16px;
        color: {COLORS['text_secondary']};
        padding: {SPACING['sm']};
    }}

    QLabel[heading="true"] {{
        font-size: 18px;
        font-weight: bold;
        color: {COLORS['primary']};
        padding: {SPACING['sm']};
    }}

    QLabel[subheading="true"] {{
        font-size: 15px;
        font-weight: bold;
        color: {COLORS['text_primary']};
        padding: {SPACING['xs']};
    }}

    QLabel[caption="true"] {{
        font-size: 12px;
        color: {COLORS['text_secondary']};
        padding: {SPACING['xs']};
    }}

    QLabel[badge="true"] {{
        background-color: {COLORS['primary_light']};
        color: white;
        border-radius: {BORDER_RADIUS['pill']};
        padding: {SPACING['xs']} {SPACING['sm']};
        font-size: 11px;
        font-weight: bold;
    }}

    QLabel[badge="success"] {{
        background-color: {COLORS['success']};
    }}

    QLabel[badge="warning"] {{
        background-color: {COLORS['warning']};
    }}

    QLabel[badge="error"] {{
        background-color: {COLORS['error']};
    }}

    QLabel[badge="info"] {{
        background-color: {COLORS['info']};
    }}

    QToolTip {{
        background-color: {COLORS['text_primary']};
        color: white;
        border: none;
        padding: {SPACING['sm']};
        border-radius: {BORDER_RADIUS['sm']};
        font-size: 12px;
    }}
"""

# Style pour les boutons
BUTTON_STYLE = f"""
    QPushButton {{
        background-color: {COLORS['primary']};
        color: white;
        border: none;
        border-radius: {BORDER_RADIUS['md']};
        padding: {SPACING['sm']} {SPACING['md']};
        font-weight: bold;
        font-size: 13px;
        min-height: 36px;
        text-align: center;
        margin: {SPACING['xxs']};
    }}

    QPushButton:hover {{
        background-color: {COLORS['primary_light']};
    }}

    QPushButton:pressed {{
        background-color: {COLORS['primary_dark']};
    }}

    QPushButton:focus {{
        outline: none;
        border: 2px solid {COLORS['accent_light']};
    }}

    QPushButton:disabled {{
        background-color: {COLORS['disabled']};
        color: white;
        opacity: 0.7;
    }}

    /* Boutons secondaires */
    QPushButton[secondary="true"] {{
        background-color: {COLORS['secondary']};
    }}

    QPushButton[secondary="true"]:hover {{
        background-color: {COLORS['secondary_light']};
    }}

    QPushButton[secondary="true"]:pressed {{
        background-color: {COLORS['secondary_dark']};
    }}

    QPushButton[secondary="true"]:focus {{
        border: 2px solid {COLORS['secondary_light']};
    }}

    /* Boutons d'accent */
    QPushButton[accent="true"] {{
        background-color: {COLORS['accent']};
    }}

    QPushButton[accent="true"]:hover {{
        background-color: {COLORS['accent_light']};
    }}

    QPushButton[accent="true"]:pressed {{
        background-color: {COLORS['accent_dark']};
    }}

    QPushButton[accent="true"]:focus {{
        border: 2px solid {COLORS['accent_light']};
    }}

    /* Boutons plats */
    QPushButton[flat="true"] {{
        background-color: transparent;
        color: {COLORS['primary']};
        border: 1px solid {COLORS['primary']};
    }}

    QPushButton[flat="true"]:hover {{
        background-color: rgba(30, 64, 175, 0.05);
        color: {COLORS['primary_dark']};
    }}

    QPushButton[flat="true"]:pressed {{
        background-color: rgba(30, 64, 175, 0.1);
    }}

    QPushButton[flat="true"]:focus {{
        border: 2px solid {COLORS['primary_light']};
    }}

    /* Boutons de succès */
    QPushButton[success="true"] {{
        background-color: {COLORS['success']};
    }}

    QPushButton[success="true"]:hover {{
        background-color: #34D399;
    }}

    QPushButton[success="true"]:pressed {{
        background-color: #059669;
    }}

    QPushButton[success="true"]:focus {{
        border: 2px solid #A7F3D0;
    }}

    /* Boutons d'avertissement */
    QPushButton[warning="true"] {{
        background-color: {COLORS['warning']};
    }}

    QPushButton[warning="true"]:hover {{
        background-color: #FBBF24;
    }}

    QPushButton[warning="true"]:pressed {{
        background-color: #D97706;
    }}

    QPushButton[warning="true"]:focus {{
        border: 2px solid #FDE68A;
    }}

    /* Boutons d'erreur */
    QPushButton[error="true"] {{
        background-color: {COLORS['error']};
    }}

    QPushButton[error="true"]:hover {{
        background-color: #F87171;
    }}

    QPushButton[error="true"]:pressed {{
        background-color: #DC2626;
    }}

    QPushButton[error="true"]:focus {{
        border: 2px solid #FECACA;
    }}

    /* Boutons avec icône */
    QPushButton[icon="true"] {{
        padding: {SPACING['sm']};
        min-width: 36px;
        max-width: 36px;
    }}

    /* Boutons texte uniquement */
    QPushButton[text="true"] {{
        background-color: transparent;
        color: {COLORS['primary']};
        border: none;
        padding: {SPACING['xs']} {SPACING['sm']};
    }}

    QPushButton[text="true"]:hover {{
        color: {COLORS['primary_light']};
        text-decoration: underline;
    }}

    QPushButton[text="true"]:pressed {{
        color: {COLORS['primary_dark']};
    }}

    /* Boutons de petite taille */
    QPushButton[size="small"] {{
        padding: {SPACING['xs']} {SPACING['sm']};
        font-size: 12px;
        min-height: 28px;
    }}

    /* Boutons de grande taille */
    QPushButton[size="large"] {{
        padding: {SPACING['md']} {SPACING['lg']};
        font-size: 14px;
        min-height: 44px;
    }}

    /* Boutons ronds */
    QPushButton[round="true"] {{
        border-radius: {BORDER_RADIUS['circle']};
        min-width: 36px;
        max-width: 36px;
        min-height: 36px;
        max-height: 36px;
        padding: 0;
    }}
"""

# Style pour les champs de saisie
INPUT_STYLE = f"""
    QLineEdit, QTextEdit, QPlainTextEdit {{
        border: 1px solid {COLORS['divider']};
        border-radius: {BORDER_RADIUS['md']};
        padding: {SPACING['sm']} {SPACING['md']};
        background-color: {COLORS['surface']};
        color: {COLORS['text_primary']};
        selection-background-color: {COLORS['primary_light']};
        selection-color: white;
        font-size: 13px;
        min-height: 36px;
        margin: {SPACING['xxs']};
    }}

    QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover {{
        border: 1px solid {COLORS['secondary']};
        background-color: {COLORS['hover_bg']};
    }}

    QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
        border: 2px solid {COLORS['primary']};
        background-color: white;
        outline: none;
    }}

    QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {{
        background-color: {COLORS['background']};
        color: {COLORS['disabled']};
        border: 1px solid {COLORS['divider']};
    }}

    QLineEdit[error="true"], QTextEdit[error="true"], QPlainTextEdit[error="true"] {{
        border: 1px solid {COLORS['error']};
    }}

    QLineEdit[error="true"]:focus, QTextEdit[error="true"]:focus, QPlainTextEdit[error="true"]:focus {{
        border: 2px solid {COLORS['error']};
    }}

    QLineEdit[success="true"], QTextEdit[success="true"], QPlainTextEdit[success="true"] {{
        border: 1px solid {COLORS['success']};
    }}

    QLineEdit[success="true"]:focus, QTextEdit[success="true"]:focus, QPlainTextEdit[success="true"]:focus {{
        border: 2px solid {COLORS['success']};
    }}

    QLineEdit[readonly="true"] {{
        background-color: {COLORS['background']};
        border: 1px dashed {COLORS['divider']};
        color: {COLORS['text_secondary']};
    }}

    QLineEdit[search="true"] {{
        border-radius: {BORDER_RADIUS['pill']};
        padding-left: 30px;
        background-image: url(comptabilite_app/ui/icons/search.svg);
        background-repeat: no-repeat;
        background-position: 8px center;
        background-color: {COLORS['hover_bg']};
    }}

    QLineEdit[search="true"]:focus {{
        background-color: white;
    }}

    QLabel[error="true"] {{
        color: {COLORS['error']};
        font-size: 12px;
        padding-top: 2px;
    }}

    QLabel[success="true"] {{
        color: {COLORS['success']};
        font-size: 12px;
        padding-top: 2px;
    }}

    QLabel[info="true"] {{
        color: {COLORS['info']};
        font-size: 12px;
        padding-top: 2px;
    }}

    QLabel[required="true"]::after {{
        content: " *";
        color: {COLORS['error']};
    }}
"""

# Style pour les combobox
COMBOBOX_STYLE = f"""
    QComboBox {{
        border: 1px solid {COLORS['divider']};
        border-radius: {BORDER_RADIUS['md']};
        padding: {SPACING['sm']} {SPACING['md']};
        background-color: {COLORS['surface']};
        color: {COLORS['text_primary']};
        min-height: 36px;
        font-size: 13px;
        margin: {SPACING['xxs']};
    }}

    QComboBox:hover {{
        border: 1px solid {COLORS['secondary']};
        background-color: {COLORS['hover_bg']};
    }}

    QComboBox:focus {{
        border: 2px solid {COLORS['primary']};
        background-color: white;
        outline: none;
    }}

    QComboBox::drop-down {{
        subcontrol-origin: padding;
        subcontrol-position: top right;
        width: 36px;
        border-left-width: 1px;
        border-left-color: {COLORS['divider']};
        border-left-style: solid;
        border-top-right-radius: {BORDER_RADIUS['md']};
        border-bottom-right-radius: {BORDER_RADIUS['md']};
        background-color: {COLORS['primary']};
    }}

    QComboBox::down-arrow {{
        width: 12px;
        height: 12px;
        background: none;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-top: 6px solid white;
        margin-top: 2px;
    }}

    QComboBox QAbstractItemView {{
        border: 1px solid {COLORS['divider']};
        selection-background-color: {COLORS['primary_light']};
        selection-color: white;
        background-color: {COLORS['surface']};
        padding: {SPACING['xs']};
        border-radius: {BORDER_RADIUS['sm']};
    }}

    QComboBox QAbstractItemView::item {{
        min-height: 28px;
        padding: {SPACING['xs']} {SPACING['sm']};
    }}

    QComboBox QAbstractItemView::item:hover {{
        background-color: {COLORS['hover_bg']};
    }}

    QComboBox:disabled {{
        background-color: {COLORS['background']};
        color: {COLORS['disabled']};
        border: 1px solid {COLORS['divider']};
    }}

    QComboBox[error="true"] {{
        border: 1px solid {COLORS['error']};
    }}

    QComboBox[error="true"]:focus {{
        border: 2px solid {COLORS['error']};
    }}

    QComboBox[success="true"] {{
        border: 1px solid {COLORS['success']};
    }}

    QComboBox[success="true"]:focus {{
        border: 2px solid {COLORS['success']};
    }}

    QComboBox[compact="true"] {{
        padding: {SPACING['xs']} {SPACING['sm']};
        min-height: 28px;
    }}

    QComboBox[large="true"] {{
        padding: {SPACING['md']} {SPACING['lg']};
        min-height: 44px;
        font-size: 14px;
    }}
"""

# Style pour les spinbox et dateedit
SPINBOX_STYLE = f"""
    QSpinBox, QDoubleSpinBox, QDateEdit {{
        border: 1px solid {COLORS['divider']};
        border-radius: {BORDER_RADIUS['md']};
        padding: {SPACING['sm']} {SPACING['md']};
        background-color: {COLORS['surface']};
        color: {COLORS['text_primary']};
        min-height: 36px;
        font-size: 13px;
        margin: {SPACING['xxs']};
    }}

    QSpinBox:hover, QDoubleSpinBox:hover, QDateEdit:hover {{
        border: 1px solid {COLORS['secondary']};
        background-color: {COLORS['hover_bg']};
    }}

    QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {{
        border: 2px solid {COLORS['primary']};
        background-color: white;
        outline: none;
    }}

    QSpinBox::up-button, QDoubleSpinBox::up-button, QDateEdit::up-button {{
        subcontrol-origin: border;
        subcontrol-position: top right;
        width: 20px;
        height: 18px;
        border-left-width: 1px;
        border-left-color: {COLORS['divider']};
        border-left-style: solid;
        background-color: {COLORS['primary']};
        border-top-right-radius: {BORDER_RADIUS['md']};
    }}

    QSpinBox::up-arrow, QDoubleSpinBox::up-arrow, QDateEdit::up-arrow {{
        width: 8px;
        height: 8px;
        background: none;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-bottom: 4px solid white;
        margin-bottom: 2px;
    }}

    QSpinBox::down-button, QDoubleSpinBox::down-button, QDateEdit::down-button {{
        subcontrol-origin: border;
        subcontrol-position: bottom right;
        width: 20px;
        height: 18px;
        border-left-width: 1px;
        border-left-color: {COLORS['divider']};
        border-left-style: solid;
        background-color: {COLORS['primary']};
        border-bottom-right-radius: {BORDER_RADIUS['md']};
    }}

    QSpinBox::down-arrow, QDoubleSpinBox::down-arrow, QDateEdit::down-arrow {{
        width: 8px;
        height: 8px;
        background: none;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-top: 4px solid white;
        margin-top: 2px;
    }}

    QSpinBox:disabled, QDoubleSpinBox:disabled, QDateEdit:disabled {{
        background-color: {COLORS['background']};
        color: {COLORS['disabled']};
        border: 1px solid {COLORS['divider']};
    }}

    QSpinBox[error="true"], QDoubleSpinBox[error="true"], QDateEdit[error="true"] {{
        border: 1px solid {COLORS['error']};
    }}

    QSpinBox[error="true"]:focus, QDoubleSpinBox[error="true"]:focus, QDateEdit[error="true"]:focus {{
        border: 2px solid {COLORS['error']};
    }}

    QSpinBox[success="true"], QDoubleSpinBox[success="true"], QDateEdit[success="true"] {{
        border: 1px solid {COLORS['success']};
    }}

    QSpinBox[success="true"]:focus, QDoubleSpinBox[success="true"]:focus, QDateEdit[success="true"]:focus {{
        border: 2px solid {COLORS['success']};
    }}

    QSpinBox[compact="true"], QDoubleSpinBox[compact="true"], QDateEdit[compact="true"] {{
        padding: {SPACING['xs']} {SPACING['sm']};
        min-height: 28px;
    }}

    QSpinBox[large="true"], QDoubleSpinBox[large="true"], QDateEdit[large="true"] {{
        padding: {SPACING['md']} {SPACING['lg']};
        min-height: 44px;
        font-size: 14px;
    }}
"""

# Style pour les tableaux
TABLE_STYLE = f"""
    QTableWidget, QTableView {{
        border: 1px solid {COLORS['divider']};
        border-radius: {BORDER_RADIUS['md']};
        background-color: {COLORS['surface']};
        gridline-color: {COLORS['divider']};
        selection-background-color: {COLORS['active_bg']};
        selection-color: {COLORS['primary']};
        alternate-background-color: {COLORS['background']};
        margin: {SPACING['xxs']};
    }}

    QHeaderView::section {{
        background-color: {COLORS['primary']};
        color: white;
        padding: {SPACING['sm']} {SPACING['md']};
        border: none;
        font-weight: bold;
        min-height: 36px;
        font-size: 13px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }}

    QHeaderView::section:first {{
        border-top-left-radius: {BORDER_RADIUS['sm']};
    }}

    QHeaderView::section:last {{
        border-top-right-radius: {BORDER_RADIUS['sm']};
    }}

    QTableWidget::item, QTableView::item {{
        padding: {SPACING['sm']} {SPACING['md']};
        border-bottom: 1px solid {COLORS['divider']};
        min-height: 32px;
        font-size: 13px;
    }}

    QTableWidget::item:hover, QTableView::item:hover {{
        background-color: {COLORS['hover_bg']};
    }}

    QTableWidget::item:selected, QTableView::item:selected {{
        background-color: {COLORS['active_bg']};
        color: {COLORS['primary']};
        font-weight: bold;
        border-left: 3px solid {COLORS['primary']};
    }}

    QTableWidget::item:selected:focus, QTableView::item:selected:focus {{
        background-color: {COLORS['active_bg']};
        border: 1px solid {COLORS['primary']};
        border-left: 3px solid {COLORS['primary']};
    }}

    QTableWidget::item:selected:!focus, QTableView::item:selected:!focus {{
        background-color: {COLORS['hover_bg']};
        color: {COLORS['text_primary']};
        border-left: 3px solid {COLORS['primary_light']};
    }}

    QTableCornerButton::section {{
        background-color: {COLORS['primary']};
        border: none;
    }}

    /* Scrollbars pour les tableaux */
    QTableWidget QScrollBar:vertical, QTableView QScrollBar:vertical {{
        background: {COLORS['background']};
        width: 12px;
        margin: 0;
    }}

    QTableWidget QScrollBar::handle:vertical, QTableView QScrollBar::handle:vertical {{
        background: {COLORS['divider']};
        min-height: 20px;
        border-radius: 6px;
    }}

    QTableWidget QScrollBar::handle:vertical:hover, QTableView QScrollBar::handle:vertical:hover {{
        background: {COLORS['text_secondary']};
    }}

    QTableWidget QScrollBar::add-line:vertical, QTableView QScrollBar::add-line:vertical,
    QTableWidget QScrollBar::sub-line:vertical, QTableView QScrollBar::sub-line:vertical {{
        height: 0;
        background: none;
    }}

    QTableWidget QScrollBar:horizontal, QTableView QScrollBar:horizontal {{
        background: {COLORS['background']};
        height: 12px;
        margin: 0;
    }}

    QTableWidget QScrollBar::handle:horizontal, QTableView QScrollBar::handle:horizontal {{
        background: {COLORS['divider']};
        min-width: 20px;
        border-radius: 6px;
    }}

    QTableWidget QScrollBar::handle:horizontal:hover, QTableView QScrollBar::handle:horizontal:hover {{
        background: {COLORS['text_secondary']};
    }}

    QTableWidget QScrollBar::add-line:horizontal, QTableView QScrollBar::add-line:horizontal,
    QTableWidget QScrollBar::sub-line:horizontal, QTableView QScrollBar::sub-line:horizontal {{
        width: 0;
        background: none;
    }}
"""

# Style pour les groupbox
GROUPBOX_STYLE = f"""
    QGroupBox {{
        font-size: 15px;
        font-weight: bold;
        color: {COLORS['primary']};
        border: 1px solid {COLORS['divider']};
        border-radius: {BORDER_RADIUS['md']};
        margin-top: 28px;
        padding-top: 24px;
        padding-bottom: 12px;
        padding-left: 12px;
        padding-right: 12px;
        background-color: {COLORS['surface']};
        margin: {SPACING['md']};
    }}

    QGroupBox::title {{
        subcontrol-origin: margin;
        subcontrol-position: top left;
        left: 16px;
        padding: 0 10px;
        background-color: {COLORS['surface']};
    }}

    QGroupBox[flat="true"] {{
        border: none;
        background-color: transparent;
    }}

    QGroupBox[card="true"] {{
        padding: {SPACING['md']};
        margin-top: 30px;
    }}

    QGroupBox[primary="true"] {{
        border: 1px solid {COLORS['primary_light']};
    }}

    QGroupBox[primary="true"]::title {{
        color: {COLORS['primary']};
    }}

    QGroupBox[secondary="true"] {{
        border: 1px solid {COLORS['secondary_light']};
    }}

    QGroupBox[secondary="true"]::title {{
        color: {COLORS['secondary']};
    }}

    QGroupBox[accent="true"] {{
        border: 1px solid {COLORS['accent_light']};
    }}

    QGroupBox[accent="true"]::title {{
        color: {COLORS['accent']};
    }}

    QGroupBox[compact="true"] {{
        margin-top: 20px;
        padding-top: 16px;
        padding-bottom: 8px;
        padding-left: 8px;
        padding-right: 8px;
    }}
"""

# Style pour la barre latérale
SIDEBAR_STYLE = f"""
    #sidebar {{
        background-color: #1E3A8A;
        min-width: 280px;
        max-width: 280px;
        padding: 0;
        border-right: none;
    }}

    #sidebar_scroll_content {{
        background-color: #1E3A8A;
    }}

    #app_title_container {{
        color: white;
        font-size: 24px;
        font-weight: bold;
        padding: 15px 20px;
        background-color: #1E40AF;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        min-height: 70px;
        letter-spacing: 1px;
    }}

    #app_title {{
        color: white;
        font-size: 24px;
        font-weight: bold;
    }}

    #app_logo {{
        margin-right: 12px;
    }}

    #sidebar QPushButton {{
        background-color: transparent;
        border: none;
        border-radius: 8px;
        min-height: 52px;
        margin: 4px 10px;
        text-align: left;
        color: white;
        font-size: 15px;
        font-weight: 500;
        padding: 0 20px;
    }}

    #sidebar QPushButton:hover {{
        background-color: rgba(255, 255, 255, 0.15);
    }}

    #sidebar QPushButton:pressed {{
        background-color: #2563EB;
    }}

    #sidebar QPushButton:checked {{
        background-color: #2563EB;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }}

    #sidebar_icon_label {{
        min-width: 28px;
        max-width: 28px;
        margin-right: 16px;
        qproperty-alignment: AlignCenter;
    }}

    #sidebar_text_label {{
        color: white;
        font-size: 15px;
        font-weight: bold;
        min-width: 160px;
    }}

    #sidebar_active_indicator {{
        min-width: 5px;
        max-width: 5px;
        min-height: 28px;
        max-height: 28px;
        border-radius: 3px;
    }}

    #sidebar QPushButton:checked #sidebar_active_indicator {{
        background-color: white;
    }}

    #sidebar_category {{
        color: #93C5FD;
        font-size: 13px;
        font-weight: bold;
        text-transform: uppercase;
        padding: 20px 20px 8px 20px;
        margin-top: 5px;
        letter-spacing: 1.5px;
    }}

    #sidebar_divider {{
        background-color: rgba(255, 255, 255, 0.1);
        height: 1px;
        margin: 8px 20px;
    }}

    #sidebar_footer {{
        background-color: #1E40AF;
        color: white;
        padding: 15px;
        font-size: 12px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }}

    #sidebar_user_info {{
        background-color: rgba(255, 255, 255, 0.08);
        border-radius: 8px;
        padding: 12px;
        margin: 10px;
    }}

    #sidebar_user_name {{
        color: white;
        font-weight: bold;
        font-size: 15px;
    }}

    #sidebar_user_role {{
        color: #BFDBFE;
        font-size: 13px;
        margin-top: 4px;
    }}
"""

# Style pour les onglets
TAB_STYLE = f"""
    QTabWidget::pane {{
        border: 1px solid {COLORS['divider']};
        border-radius: {BORDER_RADIUS['md']};
        background-color: {COLORS['surface']};
        top: -1px;
        margin: {SPACING['xxs']};
    }}

    QTabBar::tab {{
        background-color: {COLORS['background']};
        color: {COLORS['text_secondary']};
        padding: {SPACING['md']} {SPACING['lg']};
        margin-right: 4px;
        border: 1px solid {COLORS['divider']};
        border-bottom: none;
        border-top-left-radius: {BORDER_RADIUS['md']};
        border-top-right-radius: {BORDER_RADIUS['md']};
        min-width: 120px;
        font-size: 13px;
        font-weight: 500;
    }}

    QTabBar::tab:selected {{
        background-color: {COLORS['surface']};
        border-bottom: 3px solid {COLORS['primary']};
        color: {COLORS['primary']};
        font-weight: bold;
    }}

    QTabBar::tab:hover {{
        background-color: {COLORS['hover_bg']};
        color: {COLORS['primary']};
    }}

    QTabBar::tab:disabled {{
        color: {COLORS['disabled']};
        background-color: {COLORS['background']};
    }}

    QTabWidget[modern="true"]::pane {{
        border-top: 2px solid {COLORS['primary']};
        border-radius: 0;
        border-left: none;
        border-right: none;
        border-bottom: none;
    }}

    QTabWidget[modern="true"] QTabBar::tab {{
        background-color: transparent;
        border: none;
        border-radius: 0;
        padding: {SPACING['md']} {SPACING['lg']};
        margin-right: 4px;
        min-width: 120px;
        font-size: 14px;
    }}

    QTabWidget[modern="true"] QTabBar::tab:selected {{
        border-bottom: 3px solid {COLORS['primary']};
        color: {COLORS['primary']};
        font-weight: bold;
    }}

    QTabWidget[modern="true"] QTabBar::tab:hover {{
        background-color: rgba(59, 130, 246, 0.05);
        color: {COLORS['primary']};
    }}
"""

# Style pour les cartes
CARD_STYLE = f"""
    #card {{
        background-color: {COLORS['surface']};
        border-radius: {BORDER_RADIUS['md']};
        border: 1px solid {COLORS['divider']};
        padding: {SPACING['md']};
        margin: {SPACING['sm']};
    }}

    #card_title {{
        color: {COLORS['primary']};
        font-size: 16px;
        font-weight: bold;
        padding-bottom: {SPACING['sm']};
        border-bottom: 1px solid {COLORS['divider']};
        margin-bottom: {SPACING['md']};
    }}

    #card_content {{
        padding: {SPACING['sm']} 0;
    }}

    #card_actions {{
        padding-top: {SPACING['sm']};
        border-top: 1px solid {COLORS['divider']};
        margin-top: {SPACING['md']};
    }}

    #card[elevated="true"] {{
        border: none;
        background-color: {COLORS['surface']};
        border: 2px solid {COLORS['primary_light']};
    }}

    #card[primary="true"] {{
        border-left: 4px solid {COLORS['primary']};
    }}

    #card[secondary="true"] {{
        border-left: 4px solid {COLORS['secondary']};
    }}

    #card[accent="true"] {{
        border-left: 4px solid {COLORS['accent']};
    }}

    #card[success="true"] {{
        border-left: 4px solid {COLORS['success']};
    }}

    #card[warning="true"] {{
        border-left: 4px solid {COLORS['warning']};
    }}

    #card[error="true"] {{
        border-left: 4px solid {COLORS['error']};
    }}

    #card[info="true"] {{
        border-left: 4px solid {COLORS['info']};
    }}

    #card[compact="true"] {{
        padding: {SPACING['sm']};
    }}

    #card[flat="true"] {{
        border: none;
        background-color: transparent;
        box-shadow: none;
    }}

    #card:hover {{
        background-color: {COLORS['hover_bg']};
        border-color: {COLORS['primary_light']};
    }}
"""

# Style pour les éléments de navigation
NAVIGATION_STYLE = f"""
    #breadcrumb {{
        background-color: transparent;
        padding: {SPACING['sm']} {SPACING['md']};
        font-size: 13px;
    }}

    #breadcrumb_item {{
        color: {COLORS['text_secondary']};
    }}

    #breadcrumb_item:hover {{
        color: {COLORS['primary']};
        text-decoration: underline;
    }}

    #breadcrumb_separator {{
        color: {COLORS['text_secondary']};
        margin: 0 {SPACING['xs']};
    }}

    #breadcrumb_current {{
        color: {COLORS['primary']};
        font-weight: bold;
    }}

    #pagination {{
        margin: {SPACING['md']} 0;
    }}

    #pagination_button {{
        background-color: transparent;
        color: {COLORS['text_primary']};
        border: 1px solid {COLORS['divider']};
        border-radius: {BORDER_RADIUS['sm']};
        min-width: 36px;
        min-height: 36px;
        font-size: 13px;
        margin: 0 2px;
    }}

    #pagination_button:hover {{
        background-color: {COLORS['hover_bg']};
        border-color: {COLORS['primary_light']};
    }}

    #pagination_button:pressed {{
        background-color: {COLORS['active_bg']};
    }}

    #pagination_button[active="true"] {{
        background-color: {COLORS['primary']};
        color: white;
        border-color: {COLORS['primary']};
    }}

    #pagination_ellipsis {{
        color: {COLORS['text_secondary']};
        min-width: 36px;
        text-align: center;
    }}
"""

# Style pour les éléments de dashboard
DASHBOARD_STYLE = f"""
    #dashboard_card {{
        background-color: {COLORS['surface']};
        border-radius: {BORDER_RADIUS['md']};
        border: 1px solid {COLORS['divider']};
        padding: {SPACING['md']};
    }}

    #dashboard_card:hover {{
        background-color: {COLORS['hover_bg']};
        border-color: {COLORS['primary_light']};
    }}

    #dashboard_card_title {{
        font-size: 14px;
        font-weight: bold;
        color: {COLORS['text_secondary']};
        margin-bottom: {SPACING['xs']};
    }}

    #dashboard_card_value {{
        font-size: 28px;
        font-weight: bold;
        color: {COLORS['text_primary']};
    }}

    #dashboard_card_subtitle {{
        font-size: 12px;
        color: {COLORS['text_secondary']};
        margin-top: {SPACING['xs']};
    }}

    #dashboard_card_icon {{
        font-size: 24px;
        color: {COLORS['primary']};
        margin-right: {SPACING['md']};
    }}

    #dashboard_welcome {{
        background-color: {COLORS['primary']};
        border-radius: {BORDER_RADIUS['md']};
        padding: {SPACING['lg']};
        margin-bottom: {SPACING['md']};
    }}

    #activity_item {{
        border: 1px solid {COLORS['divider']};
    }}

    #activity_item:hover {{
        background-color: {COLORS['hover_bg']};
        border-color: {COLORS['primary_light']};
    }}

    #dashboard_card[primary="true"] {{
        border-left: 4px solid {COLORS['primary']};
    }}

    #dashboard_card[secondary="true"] {{
        border-left: 4px solid {COLORS['secondary']};
    }}

    #dashboard_card[success="true"] {{
        border-left: 4px solid {COLORS['success']};
    }}

    #dashboard_card[warning="true"] {{
        border-left: 4px solid {COLORS['warning']};
    }}

    #dashboard_card[error="true"] {{
        border-left: 4px solid {COLORS['error']};
    }}

    #dashboard_card[info="true"] {{
        border-left: 4px solid {COLORS['info']};
    }}

    #dashboard_chart_container {{
        background-color: {COLORS['surface']};
        border-radius: {BORDER_RADIUS['md']};
        border: 1px solid {COLORS['divider']};
        padding: {SPACING['md']};
        min-height: 300px;
    }}

    #dashboard_chart_title {{
        font-size: 16px;
        font-weight: bold;
        color: {COLORS['text_primary']};
        margin-bottom: {SPACING['md']};
    }}
"""

# Style pour les alertes et notifications
ALERT_STYLE = f"""
    #alert {{
        border-radius: {BORDER_RADIUS['md']};
        padding: {SPACING['md']};
        margin: {SPACING['md']} 0;
        font-size: 13px;
    }}

    #alert_success {{
        background-color: #ECFDF5;
        border-left: 4px solid {COLORS['success']};
        color: #065F46;
    }}

    #alert_info {{
        background-color: #EFF6FF;
        border-left: 4px solid {COLORS['info']};
        color: #1E40AF;
    }}

    #alert_warning {{
        background-color: #FFFBEB;
        border-left: 4px solid {COLORS['warning']};
        color: #92400E;
    }}

    #alert_error {{
        background-color: #FEF2F2;
        border-left: 4px solid {COLORS['error']};
        color: #991B1B;
    }}

    #alert_icon {{
        padding-right: {SPACING['sm']};
    }}

    #alert_title {{
        font-weight: bold;
        margin-bottom: 4px;
        font-size: 14px;
    }}

    #alert_message {{
        margin-top: 2px;
    }}

    #alert_close {{
        background: transparent;
        border: none;
        color: inherit;
        font-size: 16px;
        font-weight: bold;
    }}

    #alert_close:hover {{
        color: {COLORS['text_primary']};
    }}

    #alert[compact="true"] {{
        padding: {SPACING['sm']};
        margin: {SPACING['sm']} 0;
    }}

    #alert[outlined="true"] {{
        background-color: transparent;
    }}

    #alert[outlined="true"]#alert_success {{
        border: 1px solid {COLORS['success']};
    }}

    #alert[outlined="true"]#alert_info {{
        border: 1px solid {COLORS['info']};
    }}

    #alert[outlined="true"]#alert_warning {{
        border: 1px solid {COLORS['warning']};
    }}

    #alert[outlined="true"]#alert_error {{
        border: 1px solid {COLORS['error']};
    }}

    #toast {{
        border-radius: {BORDER_RADIUS['md']};
        padding: {SPACING['md']};
        color: white;
        font-size: 13px;
        min-width: 250px;
        max-width: 400px;
    }}

    #toast_success {{
        background-color: {COLORS['success']};
    }}

    #toast_info {{
        background-color: {COLORS['info']};
    }}

    #toast_warning {{
        background-color: {COLORS['warning']};
    }}

    #toast_error {{
        background-color: {COLORS['error']};
    }}
"""

# Style pour les boutons personnalisés
CUSTOM_BUTTON_STYLE = f"""
    /* Boutons primaires avec ID */
    QPushButton#primary_button {{
        background-color: {COLORS['primary_light']};
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: {BORDER_RADIUS['md']};
        font-weight: bold;
        font-size: 14px;
    }}

    QPushButton#primary_button:hover {{
        background-color: {COLORS['primary']};
    }}

    QPushButton#primary_button:pressed {{
        background-color: {COLORS['primary_dark']};
    }}

    /* Boutons avec icône par ID */
    QPushButton#icon_button {{
        background-color: {COLORS['primary']};
        color: white;
        border-radius: {BORDER_RADIUS['sm']};
        padding: {SPACING['xs']};
        min-width: 30px;
        min-height: 30px;
    }}

    QPushButton#icon_button:hover {{
        background-color: {COLORS['primary_light']};
    }}

    QPushButton#icon_button:pressed {{
        background-color: {COLORS['primary_dark']};
    }}

    QPushButton#icon_button_danger {{
        background-color: {COLORS['error']};
        color: white;
        border-radius: {BORDER_RADIUS['sm']};
        padding: {SPACING['xs']};
        min-width: 30px;
        min-height: 30px;
    }}

    QPushButton#icon_button_danger:hover {{
        background-color: #F87171;
    }}
"""

# Appliquer tous les styles
THEME = (
    APP_STYLE +
    BUTTON_STYLE +
    INPUT_STYLE +
    COMBOBOX_STYLE +
    SPINBOX_STYLE +
    TABLE_STYLE +
    GROUPBOX_STYLE +
    SIDEBAR_STYLE +
    TAB_STYLE +
    CARD_STYLE +
    NAVIGATION_STYLE +
    DASHBOARD_STYLE +
    ALERT_STYLE +
    CUSTOM_BUTTON_STYLE
)
