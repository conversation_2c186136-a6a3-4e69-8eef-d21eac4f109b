from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QFormLayout, QMessageBox, QGridLayout,
                              QDoubleSpinBox, QSpinBox, QComboBox)
from PySide6.QtCore import Qt, Signal
import sqlite3

try:
    from .autocomplete_widget import apply_autocomplete_to_dialog
except ImportError:
    def apply_autocomplete_to_dialog(dialog, db_manager):
        pass

class ProduitDialog(QDialog):
    """Boîte de dialogue modale pour ajouter ou modifier un produit"""

    # Signal émis lorsqu'un produit est ajouté ou modifié
    produit_saved = Signal()

    def __init__(self, db_manager, produit_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.produit_id = produit_id
        self.is_edit_mode = produit_id is not None

        self.setWindowTitle("Ajouter un produit" if not self.is_edit_mode else "Modifier un produit")
        self.setMinimumWidth(600)
        self.setModal(True)

        self.setup_ui()

        if self.is_edit_mode:
            self.load_produit_data()
        else:
            self.code_input.setText(self.db_manager.generer_code_produit())

    def setup_ui(self):
        """Configure l'interface utilisateur de la boîte de dialogue"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title = QLabel("Informations du produit")
        title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #1A56DB;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)

        # Formulaire
        form_grid = QGridLayout()
        form_grid.setSpacing(15)

        # Code produit
        code_label = QLabel("Code produit:")
        self.code_input = QLineEdit()
        self.code_input.setReadOnly(True)
        self.code_input.setStyleSheet("background-color: #F3F4F6; color: #6B7280;")
        form_grid.addWidget(code_label, 0, 0)
        form_grid.addWidget(self.code_input, 0, 1)

        # Désignation
        designation_label = QLabel("Désignation*:")
        self.designation_input = QLineEdit()
        form_grid.addWidget(designation_label, 1, 0)
        form_grid.addWidget(self.designation_input, 1, 1, 1, 3)  # Span sur 3 colonnes

        # Unité
        unite_label = QLabel("Unité:")
        self.unite_input = QLineEdit()
        form_grid.addWidget(unite_label, 2, 0)
        form_grid.addWidget(self.unite_input, 2, 1)

        # Prix d'achat
        prix_achat_label = QLabel("Prix d'achat (HT):")
        self.prix_achat_input = QDoubleSpinBox()
        self.prix_achat_input.setRange(0, 1000000)
        self.prix_achat_input.setDecimals(2)
        self.prix_achat_input.setSuffix(" DH")
        self.prix_achat_input.setButtonSymbols(QDoubleSpinBox.NoButtons)
        form_grid.addWidget(prix_achat_label, 3, 0)
        form_grid.addWidget(self.prix_achat_input, 3, 1)

        # Prix de vente
        prix_vente_label = QLabel("Prix de vente (HT):")
        self.prix_vente_input = QDoubleSpinBox()
        self.prix_vente_input.setRange(0, 1000000)
        self.prix_vente_input.setDecimals(2)
        self.prix_vente_input.setSuffix(" DH")
        self.prix_vente_input.setButtonSymbols(QDoubleSpinBox.NoButtons)
        form_grid.addWidget(prix_vente_label, 3, 2)
        form_grid.addWidget(self.prix_vente_input, 3, 3)

        # Stock
        stock_label = QLabel("Quantité en stock:")
        self.stock_input = QSpinBox()
        self.stock_input.setRange(0, 1000000)
        self.stock_input.setButtonSymbols(QSpinBox.NoButtons)
        form_grid.addWidget(stock_label, 4, 0)
        form_grid.addWidget(self.stock_input, 4, 1)

        # Section d'achat (seulement pour les nouveaux produits)
        if not self.is_edit_mode:
            # Séparateur
            separator = QLabel("─" * 50)
            separator.setStyleSheet("color: #D1D5DB; margin: 10px 0;")
            form_grid.addWidget(separator, 5, 0, 1, 4)

            # Titre section achat
            achat_title = QLabel("Informations d'achat (optionnel):")
            achat_title.setStyleSheet("font-weight: bold; color: #374151; margin-top: 10px;")
            form_grid.addWidget(achat_title, 6, 0, 1, 4)

            # Fournisseur
            fournisseur_label = QLabel("Fournisseur:")
            self.fournisseur_input = QLineEdit()
            form_grid.addWidget(fournisseur_label, 7, 0)
            form_grid.addWidget(self.fournisseur_input, 7, 1)

            # Numéro de facture
            facture_label = QLabel("N° Facture:")
            self.facture_input = QLineEdit()
            form_grid.addWidget(facture_label, 7, 2)
            form_grid.addWidget(self.facture_input, 7, 3)

            # Mode de paiement
            paiement_label = QLabel("Mode de paiement:")
            self.paiement_combo = QComboBox()
            self.paiement_combo.addItems([
                "Aucun",
                "Espèces",
                "Chèque",
                "Virement bancaire",
                "Carte bancaire",
                "Crédit"
            ])
            form_grid.addWidget(paiement_label, 8, 0)
            form_grid.addWidget(self.paiement_combo, 8, 1)

        layout.addLayout(form_grid)

        # Boutons d'action
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # Bouton Annuler
        self.cancel_btn = QPushButton("Annuler")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #F3F4F6;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        # Bouton Enregistrer
        self.save_btn = QPushButton("Enregistrer")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #1A56DB;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1E40AF;
            }
        """)
        self.save_btn.clicked.connect(self.save_produit)
        buttons_layout.addWidget(self.save_btn)

        layout.addLayout(buttons_layout)

        # تطبيق الاقتراحات التلقائية
        apply_autocomplete_to_dialog(self, self.db_manager)

    def load_produit_data(self):
        """Charge les données du produit à modifier"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT * FROM produits WHERE id = ?", (self.produit_id,))
        produit = cursor.fetchone()

        if produit:
            self.code_input.setText(produit['code'] or "")
            self.designation_input.setText(produit['designation'])
            self.unite_input.setText(produit['unite'] or "")
            self.prix_achat_input.setValue(produit['prix_achat'] or 0)
            self.prix_vente_input.setValue(produit['prix_vente'] or 0)
            self.stock_input.setValue(produit['stock'] or 0)

    def save_produit(self):
        """Enregistre le produit (ajout ou modification)"""
        designation = self.designation_input.text().strip()
        if not designation:
            QMessageBox.warning(self, "Erreur", "La désignation du produit est obligatoire.")
            return

        code = self.code_input.text()
        if not code:
            code = self.db_manager.generer_code_produit()

        cursor = self.db_manager.conn.cursor()
        try:
            if self.is_edit_mode:
                # Modification d'un produit existant
                cursor.execute(
                    """UPDATE produits SET
                       code = ?, designation = ?, unite = ?, prix_achat = ?,
                       prix_vente = ?, stock = ?
                       WHERE id = ?""",
                    (code, designation, self.unite_input.text(),
                     self.prix_achat_input.value(), self.prix_vente_input.value(),
                     self.stock_input.value(), self.produit_id)
                )
                message = f"Produit {designation} modifié avec succès."
            else:
                # Ajout d'un nouveau produit
                cursor.execute(
                    """INSERT INTO produits
                       (code, designation, unite, prix_achat, prix_vente, stock)
                       VALUES (?, ?, ?, ?, ?, ?)""",
                    (code, designation, self.unite_input.text(),
                     self.prix_achat_input.value(), self.prix_vente_input.value(),
                     self.stock_input.value())
                )
                message = f"Produit {designation} ajouté avec succès.\nCode produit: {code}"

            self.db_manager.conn.commit()

            # Intégrer avec la caisse si c'est un nouveau produit avec paiement en espèces
            if not self.is_edit_mode and hasattr(self, 'paiement_combo'):
                mode_paiement = self.paiement_combo.currentText()
                if mode_paiement == "Espèces" and self.stock_input.value() > 0:
                    self.integrate_with_caisse_new_product(code, designation)

            # Émettre le signal pour informer que le produit a été enregistré
            self.produit_saved.emit()

            QMessageBox.information(self, "Succès", message)
            self.accept()

        except sqlite3.IntegrityError:
            QMessageBox.warning(self, "Erreur", "Ce code produit existe déjà. Veuillez utiliser un autre code.")

    def integrate_with_caisse_new_product(self, code, designation):
        """Intègre automatiquement avec la caisse pour l'achat d'un nouveau produit en espèces"""
        try:
            from datetime import datetime

            # Calculer le montant total (prix d'achat × quantité)
            montant_total = self.prix_achat_input.value() * self.stock_input.value()

            if montant_total > 0:
                # Créer une référence pour la transaction
                reference = f"PROD_{code}"
                if hasattr(self, 'facture_input') and self.facture_input.text().strip():
                    reference = self.facture_input.text().strip()

                # Ajouter une sortie dans la caisse
                cursor = self.db_manager.conn.cursor()
                cursor.execute("""
                    INSERT INTO sorties_caisse (date, nature, objet, reference, montant)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    datetime.now().strftime("%Y-%m-%d"),
                    "شراء",
                    f"شراء منتج جديد بالنقد - {designation}",
                    reference,
                    montant_total
                ))

                self.db_manager.conn.commit()
                print(f"💸 Sortie de caisse automatique ajoutée: {montant_total:.2f} DH pour le produit {designation}")

        except Exception as e:
            print(f"❌ Erreur lors de l'intégration avec la caisse: {str(e)}")
