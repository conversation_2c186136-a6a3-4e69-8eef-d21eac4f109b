from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QComboBox, QMessageBox, QHeaderView)
from PySide6.QtCore import Qt
import sqlite3

# Importer le module de base
from ..components.base_module import BaseModule
from ..icons.icons import SEARCH_ICON
from ..theme import COLORS, BORDER_RADIUS, SPACING

class RechercheModule(BaseModule):
    """Module de recherche avec interface simplifiée"""

    def __init__(self, db_manager, signals=None):
        super().__init__(
            db_manager=db_manager,
            signals=signals,
            title="Recherche Globale",
            description="Recherchez des clients, fournisseurs, produits et factures",
            icon=SEARCH_ICON
        )

        # Masquer le bouton d'ajout
        self.add_button.setVisible(False)

        # Configurer la recherche
        self.setup_search()

    def setup_search(self):
        """Configure l'interface de recherche"""
        # Modifier le placeholder de la barre de recherche
        self.search_input.setPlaceholderText("Rechercher dans toute la base de données...")

        # Ajouter un sélecteur de catégorie
        category_container = QWidget()
        category_layout = QHBoxLayout(category_container)
        category_layout.setContentsMargins(0, 0, 0, 0)

        category_label = QLabel("Catégorie:")
        self.category_combo = QComboBox()
        self.category_combo.addItem("Tous", "all")
        self.category_combo.addItem("Clients", "clients")
        self.category_combo.addItem("Fournisseurs", "fournisseurs")
        self.category_combo.addItem("Produits", "produits")
        self.category_combo.addItem("Factures d'achat", "factures_achat")
        self.category_combo.addItem("Factures de vente", "factures_vente")
        self.category_combo.currentIndexChanged.connect(self.perform_search)

        category_layout.addWidget(category_label)
        category_layout.addWidget(self.category_combo)
        category_layout.addStretch()

        # Ajouter le sélecteur de catégorie après la barre de recherche
        search_container_layout = self.search_input.parentWidget().layout()
        search_container_layout.addWidget(category_container)

        # Configurer le tableau des résultats
        self.setup_results_table()

        # Connecter la recherche
        self.search_input.textChanged.connect(self.perform_search)

    def setup_results_table(self):
        """Configure le tableau des résultats de recherche"""
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels([
            "Code", "Nom/Désignation", "Catégorie", "Détails", "Actions"
        ])

        # Configuration des colonnes
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Code
        self.items_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)  # Nom/Désignation
        self.items_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Catégorie
        self.items_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)  # Détails
        self.items_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Actions

    def perform_search(self):
        """Effectue la recherche en fonction du texte et de la catégorie sélectionnée"""
        search_text = self.search_input.text().strip()
        category = self.category_combo.currentData()

        # Vider le tableau
        self.items_table.setRowCount(0)

        if not search_text:
            return

        try:
            cursor = self.db_manager.conn.cursor()

            # Recherche dans les clients
            if category in ["all", "clients"]:
                self.search_clients(cursor, search_text)

            # Recherche dans les fournisseurs
            if category in ["all", "fournisseurs"]:
                self.search_fournisseurs(cursor, search_text)

            # Recherche dans les produits
            if category in ["all", "produits"]:
                self.search_produits(cursor, search_text)

            # Recherche dans les factures d'achat
            if category in ["all", "factures_achat"]:
                self.search_factures_achat(cursor, search_text)

            # Recherche dans les factures de vente
            if category in ["all", "factures_vente"]:
                self.search_factures_vente(cursor, search_text)

        except sqlite3.Error as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la recherche: {str(e)}")

    def search_clients(self, cursor, search_text):
        """Recherche dans les clients"""
        cursor.execute("""
            SELECT id, code, nom, ice, if_fiscal, telephone, adresse
            FROM clients
            WHERE code LIKE ? OR nom LIKE ? OR ice LIKE ? OR if_fiscal LIKE ? OR telephone LIKE ? OR adresse LIKE ?
        """, (f"%{search_text}%", f"%{search_text}%", f"%{search_text}%", f"%{search_text}%", f"%{search_text}%", f"%{search_text}%"))

        clients = cursor.fetchall()

        for client in clients:
            row = self.items_table.rowCount()
            self.items_table.insertRow(row)

            self.items_table.setItem(row, 0, QTableWidgetItem(client['code'] or ""))
            self.items_table.setItem(row, 1, QTableWidgetItem(client['nom']))
            self.items_table.setItem(row, 2, QTableWidgetItem("Client"))

            details = f"ICE: {client['ice'] or '-'}, IF: {client['if_fiscal'] or '-'}, Tél: {client['telephone'] or '-'}"
            self.items_table.setItem(row, 3, QTableWidgetItem(details))

            # Créer les boutons d'action
            actions_widget = self.create_action_buttons(row, client['id'], client['nom'], "client")
            self.items_table.setCellWidget(row, 4, actions_widget)

            # Stocker l'ID et le type dans la première colonne
            self.items_table.item(row, 0).setData(Qt.UserRole, {"id": client['id'], "type": "client"})

    def search_fournisseurs(self, cursor, search_text):
        """Recherche dans les fournisseurs"""
        cursor.execute("""
            SELECT id, code, nom, ice, if_fiscal, telephone, adresse
            FROM fournisseurs
            WHERE code LIKE ? OR nom LIKE ? OR ice LIKE ? OR if_fiscal LIKE ? OR telephone LIKE ? OR adresse LIKE ?
        """, (f"%{search_text}%", f"%{search_text}%", f"%{search_text}%", f"%{search_text}%", f"%{search_text}%", f"%{search_text}%"))

        fournisseurs = cursor.fetchall()

        for fournisseur in fournisseurs:
            row = self.items_table.rowCount()
            self.items_table.insertRow(row)

            self.items_table.setItem(row, 0, QTableWidgetItem(fournisseur['code'] or ""))
            self.items_table.setItem(row, 1, QTableWidgetItem(fournisseur['nom']))
            self.items_table.setItem(row, 2, QTableWidgetItem("Fournisseur"))

            details = f"ICE: {fournisseur['ice'] or '-'}, IF: {fournisseur['if_fiscal'] or '-'}, Tél: {fournisseur['telephone'] or '-'}"
            self.items_table.setItem(row, 3, QTableWidgetItem(details))

            # Créer les boutons d'action
            actions_widget = self.create_action_buttons(row, fournisseur['id'], fournisseur['nom'], "fournisseur")
            self.items_table.setCellWidget(row, 4, actions_widget)

            # Stocker l'ID et le type dans la première colonne
            self.items_table.item(row, 0).setData(Qt.UserRole, {"id": fournisseur['id'], "type": "fournisseur"})

    def search_produits(self, cursor, search_text):
        """Recherche dans les produits"""
        cursor.execute("""
            SELECT id, code, designation, unite, prix_achat, prix_vente, stock
            FROM produits
            WHERE code LIKE ? OR designation LIKE ? OR unite LIKE ?
        """, (f"%{search_text}%", f"%{search_text}%", f"%{search_text}%"))

        produits = cursor.fetchall()

        for produit in produits:
            row = self.items_table.rowCount()
            self.items_table.insertRow(row)

            self.items_table.setItem(row, 0, QTableWidgetItem(produit['code'] or ""))
            self.items_table.setItem(row, 1, QTableWidgetItem(produit['designation']))
            self.items_table.setItem(row, 2, QTableWidgetItem("Produit"))

            details = f"Unité: {produit['unite'] or '-'}, Prix achat: {produit['prix_achat'] or 0} DH, Prix vente: {produit['prix_vente'] or 0} DH, Stock: {produit['stock'] or 0}"
            self.items_table.setItem(row, 3, QTableWidgetItem(details))

            # Créer les boutons d'action
            actions_widget = self.create_action_buttons(row, produit['id'], produit['designation'], "produit")
            self.items_table.setCellWidget(row, 4, actions_widget)

            # Stocker l'ID et le type dans la première colonne
            self.items_table.item(row, 0).setData(Qt.UserRole, {"id": produit['id'], "type": "produit"})

    def search_factures_achat(self, cursor, search_text):
        """Recherche dans les factures d'achat"""
        cursor.execute("""
            SELECT fa.id, fa.numero_facture, fa.date_facture, f.nom as fournisseur_nom, fa.montant_total
            FROM factures_achat fa
            JOIN fournisseurs f ON fa.fournisseur_id = f.id
            WHERE fa.numero_facture LIKE ? OR f.nom LIKE ?
        """, (f"%{search_text}%", f"%{search_text}%"))

        factures = cursor.fetchall()

        for facture in factures:
            row = self.items_table.rowCount()
            self.items_table.insertRow(row)

            self.items_table.setItem(row, 0, QTableWidgetItem(facture['numero_facture'] or ""))
            self.items_table.setItem(row, 1, QTableWidgetItem(f"Facture d'achat - {facture['fournisseur_nom']}"))
            self.items_table.setItem(row, 2, QTableWidgetItem("Facture d'achat"))

            details = f"Date: {facture['date_facture'] or '-'}, Montant: {facture['montant_total'] or 0} DH"
            self.items_table.setItem(row, 3, QTableWidgetItem(details))

            # Créer les boutons d'action
            actions_widget = self.create_action_buttons(row, facture['id'], facture['numero_facture'], "facture_achat")
            self.items_table.setCellWidget(row, 4, actions_widget)

            # Stocker l'ID et le type dans la première colonne
            self.items_table.item(row, 0).setData(Qt.UserRole, {"id": facture['id'], "type": "facture_achat"})

    def search_factures_vente(self, cursor, search_text):
        """Recherche dans les factures de vente"""
        cursor.execute("""
            SELECT fv.id, fv.numero_facture, fv.date_facture, c.nom as client_nom, fv.montant_total
            FROM factures_vente fv
            JOIN clients c ON fv.client_id = c.id
            WHERE fv.numero_facture LIKE ? OR c.nom LIKE ?
        """, (f"%{search_text}%", f"%{search_text}%"))

        factures = cursor.fetchall()

        for facture in factures:
            row = self.items_table.rowCount()
            self.items_table.insertRow(row)

            self.items_table.setItem(row, 0, QTableWidgetItem(facture['numero_facture'] or ""))
            self.items_table.setItem(row, 1, QTableWidgetItem(f"Facture de vente - {facture['client_nom']}"))
            self.items_table.setItem(row, 2, QTableWidgetItem("Facture de vente"))

            details = f"Date: {facture['date_facture'] or '-'}, Montant: {facture['montant_total'] or 0} DH"
            self.items_table.setItem(row, 3, QTableWidgetItem(details))

            # Créer les boutons d'action
            actions_widget = self.create_action_buttons(row, facture['id'], facture['numero_facture'], "facture_vente")
            self.items_table.setCellWidget(row, 4, actions_widget)

            # Stocker l'ID et le type dans la première colonne
            self.items_table.item(row, 0).setData(Qt.UserRole, {"id": facture['id'], "type": "facture_vente"})

    def show_add_dialog(self):
        """Cette méthode est vide car le module de recherche n'a pas de fonction d'ajout"""
        pass

    def show_edit_dialog(self, item_id):
        """Affiche la boîte de dialogue pour modifier un élément"""
        # Récupérer le type d'élément
        selected_row = self.get_row_by_id(item_id)
        if selected_row >= 0:
            item_data = self.items_table.item(selected_row, 0).data(Qt.UserRole)
            item_type = item_data["type"]

            # Rediriger vers le module approprié
            if item_type == "client":
                self.redirect_to_module(1, item_id)
            elif item_type == "fournisseur":
                self.redirect_to_module(2, item_id)
            elif item_type == "produit":
                self.redirect_to_module(7, item_id)
            elif item_type == "facture_achat":
                self.redirect_to_module(3, item_id)
            elif item_type == "facture_vente":
                self.redirect_to_module(4, item_id)

    def delete_item(self, item_id, item_name):
        """Supprime un élément"""
        # Récupérer le type d'élément
        selected_row = self.get_row_by_id(item_id)
        if selected_row >= 0:
            item_data = self.items_table.item(selected_row, 0).data(Qt.UserRole)
            item_type = item_data["type"]

            confirm = QMessageBox.question(
                self, "Confirmation",
                f"Êtes-vous sûr de vouloir supprimer {item_name} ?\nCette action est irréversible.",
                QMessageBox.Yes | QMessageBox.No
            )

            if confirm == QMessageBox.Yes:
                try:
                    cursor = self.db_manager.conn.cursor()

                    if item_type == "client":
                        cursor.execute("DELETE FROM clients WHERE id = ?", (item_id,))
                        self.db_manager.conn.commit()
                        # إعادة ترقيم جميع العملاء تلقائياً
                        self.db_manager.renumber_all_clients()
                        if self.signals:
                            self.signals.clients_changed.emit()
                    elif item_type == "fournisseur":
                        cursor.execute("DELETE FROM fournisseurs WHERE id = ?", (item_id,))
                        self.db_manager.conn.commit()
                        # إعادة ترقيم جميع الموردين تلقائياً
                        self.db_manager.renumber_all_fournisseurs()
                        if self.signals:
                            self.signals.fournisseurs_changed.emit()
                    elif item_type == "produit":
                        cursor.execute("DELETE FROM produits WHERE id = ?", (item_id,))
                        self.db_manager.conn.commit()
                        # إعادة ترقيم جميع المنتجات تلقائياً
                        self.db_manager.renumber_all_products()
                        if self.signals:
                            self.signals.produits_changed.emit()
                    elif item_type == "facture_achat":
                        cursor.execute("DELETE FROM factures_achat WHERE id = ?", (item_id,))
                    elif item_type == "facture_vente":
                        cursor.execute("DELETE FROM factures_vente WHERE id = ?", (item_id,))

                    self.db_manager.conn.commit()
                    self.perform_search()

                    QMessageBox.information(self, "Succès", f"{item_name} supprimé avec succès.")
                except sqlite3.Error as e:
                    QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression: {str(e)}")

    def get_row_by_id(self, item_id):
        """Récupère l'index de la ligne correspondant à l'ID"""
        for row in range(self.items_table.rowCount()):
            item_data = self.items_table.item(row, 0).data(Qt.UserRole)
            if item_data and item_data["id"] == item_id:
                return row
        return -1

    def redirect_to_module(self, module_index, item_id):
        """Redirige vers un autre module"""
        # Trouver la fenêtre principale
        main_window = self.window()

        # Changer de module
        main_window.content_stack.setCurrentIndex(module_index)

        # Sélectionner l'élément dans le module
        current_module = main_window.content_stack.currentWidget()

        # Appeler la méthode appropriée du module
        if hasattr(current_module, "show_edit_dialog"):
            current_module.show_edit_dialog(item_id)
