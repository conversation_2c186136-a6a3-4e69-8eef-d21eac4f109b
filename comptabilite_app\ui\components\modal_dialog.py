from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QFrame, QWidget)
from PySide6.QtCore import Qt, Signal

from ..theme import COLORS, BORDER_RADIUS, SPACING
from ..icons.icons import *

class ModalDialog(QDialog):
    """
    Boîte de dialogue modale de base avec un style moderne et cohérent.
    Cette classe sert de base pour toutes les boîtes de dialogue de l'application.
    """
    
    def __init__(self, title, icon_svg, parent=None):
        """
        Initialise la boîte de dialogue modale.
        
        Args:
            title (str): Titre de la boîte de dialogue
            icon_svg (str): SVG de l'icône à afficher
            parent (QWidget, optional): Widget parent
        """
        super().__init__(parent)
        self.title_text = title
        self.icon_svg = icon_svg
        
        self.setModal(True)
        self.setup_ui_base()
    
    def setup_ui_base(self):
        """Configure l'interface utilisateur de base de la boîte de dialogue"""
        # Configurer la boîte de dialogue
        self.setMinimumWidth(750)
        self.setMinimumHeight(500)
        self.setWindowTitle(self.title_text)
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {COLORS['background']};
                border-radius: {BORDER_RADIUS['md']};
            }}
        """)
        
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setSpacing(int(SPACING['lg'].replace('px', '')))
        self.main_layout.setContentsMargins(30, 30, 30, 30)
        
        # En-tête avec icône et titre
        header_container = QWidget()
        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        # Icône
        icon_label = QLabel()
        icon_label.setText(svg_to_icon_html(self.icon_svg, COLORS['primary'], 32))
        icon_label.setFixedSize(32, 32)
        header_layout.addWidget(icon_label)
        
        # Titre
        title = QLabel(self.title_text)
        title.setStyleSheet(f"""
            font-size: 22px;
            font-weight: bold;
            color: {COLORS['primary']};
            margin-bottom: {SPACING['sm']};
        """)
        header_layout.addWidget(title)
        header_layout.addStretch()
        
        self.main_layout.addWidget(header_container)
        
        # Ligne de séparation
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet(f"background-color: {COLORS['divider']}; min-height: 1px;")
        self.main_layout.addWidget(separator)
        
        # Conteneur de contenu (à remplir par les classes dérivées)
        self.content_container = QFrame()
        self.content_container.setObjectName("form_container")
        self.content_container.setStyleSheet(f"""
            #form_container {{
                background-color: {COLORS['surface']};
                border-radius: {BORDER_RADIUS['md']};
                border: 1px solid {COLORS['divider']};
                padding: {SPACING['md']};
            }}
        """)
        self.content_layout = QVBoxLayout(self.content_container)
        self.content_layout.setContentsMargins(20, 20, 20, 20)
        self.content_layout.setSpacing(20)
        
        # Le contenu spécifique sera ajouté par les classes dérivées
        
        self.main_layout.addWidget(self.content_container)
        
        # Boutons d'action
        self.buttons_container = QFrame()
        self.buttons_container.setStyleSheet(f"""
            background-color: {COLORS['hover_bg']};
            border-radius: {BORDER_RADIUS['md']};
            border-top: 1px solid {COLORS['divider']};
        """)
        self.buttons_layout = QHBoxLayout(self.buttons_container)
        self.buttons_layout.setContentsMargins(20, 15, 20, 15)
        self.buttons_layout.setSpacing(15)
        
        self.buttons_layout.addStretch()
        
        # Bouton Annuler
        self.cancel_btn = QPushButton("Annuler")
        self.cancel_btn.setFixedHeight(40)
        self.cancel_btn.setMinimumWidth(120)
        self.cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLORS['hover_bg']};
                color: {COLORS['text_primary']};
                border: 1px solid {COLORS['divider']};
                border-radius: {BORDER_RADIUS['md']};
                padding: 8px 16px;
                font-weight: bold;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {COLORS['divider']};
            }}
        """)
        self.cancel_btn.clicked.connect(self.reject)
        self.buttons_layout.addWidget(self.cancel_btn)
        
        # Bouton Enregistrer (à connecter par les classes dérivées)
        self.save_btn = QPushButton("Enregistrer")
        self.save_btn.setFixedHeight(40)
        self.save_btn.setMinimumWidth(120)
        self.save_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLORS['primary']};
                color: white;
                border: none;
                border-radius: {BORDER_RADIUS['md']};
                padding: 8px 16px;
                font-weight: bold;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {COLORS['primary_light']};
            }}
            QPushButton:pressed {{
                background-color: {COLORS['primary_dark']};
            }}
        """)
        self.buttons_layout.addWidget(self.save_btn)
        
        self.main_layout.addWidget(self.buttons_container)
    
    def set_save_button_text(self, text):
        """
        Définit le texte du bouton d'enregistrement.
        
        Args:
            text (str): Nouveau texte pour le bouton
        """
        self.save_btn.setText(text)
    
    def set_cancel_button_text(self, text):
        """
        Définit le texte du bouton d'annulation.
        
        Args:
            text (str): Nouveau texte pour le bouton
        """
        self.cancel_btn.setText(text)
