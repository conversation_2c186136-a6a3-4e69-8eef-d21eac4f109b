"""
Migration pour ajouter les champs bon_livraison_numero et mode_paiement à la table factures_vente
"""
import sqlite3
import os

def migrate_factures_fields():
    """Ajoute les colonnes bon_livraison_numero et mode_paiement à la table factures_vente"""
    
    # Chemin vers la base de données
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'comptabilite.db')
    
    if not os.path.exists(db_path):
        print(f"Base de données non trouvée: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("Migration: Ajout des champs bon_livraison_numero et mode_paiement...")
        
        # Vérifier si la table factures_vente existe
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='factures_vente'")
        if not cursor.fetchone():
            print("Table factures_vente n'existe pas, création...")
            cursor.execute("""
                CREATE TABLE factures_vente (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    numero TEXT,
                    date_creation TEXT,
                    date_echeance TEXT,
                    client_id INTEGER,
                    bon_commande TEXT,
                    marche TEXT,
                    objet TEXT,
                    bon_livraison_numero TEXT,
                    mode_paiement TEXT DEFAULT 'Virement bancaire',
                    total_ht REAL DEFAULT 0,
                    total_tva REAL DEFAULT 0,
                    total_ttc REAL DEFAULT 0,
                    statut TEXT DEFAULT 'En attente',
                    date_paiement TEXT,
                    FOREIGN KEY (client_id) REFERENCES clients (id)
                )
            """)
            print("Table factures_vente créée avec les nouveaux champs")
        else:
            # Vérifier les colonnes existantes
            cursor.execute("PRAGMA table_info(factures_vente)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]
            
            # Ajouter bon_livraison_numero si elle n'existe pas
            if 'bon_livraison_numero' not in column_names:
                print("Ajout de la colonne bon_livraison_numero...")
                cursor.execute("ALTER TABLE factures_vente ADD COLUMN bon_livraison_numero TEXT")
                print("✅ Colonne bon_livraison_numero ajoutée")
            else:
                print("✅ Colonne bon_livraison_numero existe déjà")
            
            # Ajouter mode_paiement si elle n'existe pas
            if 'mode_paiement' not in column_names:
                print("Ajout de la colonne mode_paiement...")
                cursor.execute("ALTER TABLE factures_vente ADD COLUMN mode_paiement TEXT DEFAULT 'Virement bancaire'")
                print("✅ Colonne mode_paiement ajoutée")
            else:
                print("✅ Colonne mode_paiement existe déjà")
            
            # Ajouter date_paiement si elle n'existe pas (au cas où)
            if 'date_paiement' not in column_names:
                print("Ajout de la colonne date_paiement...")
                cursor.execute("ALTER TABLE factures_vente ADD COLUMN date_paiement TEXT")
                print("✅ Colonne date_paiement ajoutée")
            else:
                print("✅ Colonne date_paiement existe déjà")
        
        conn.commit()
        print("✅ Migration des champs de factures terminée avec succès")
        
    except sqlite3.Error as e:
        print(f"❌ Erreur lors de la migration: {str(e)}")
        conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    migrate_factures_fields()
