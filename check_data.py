import sqlite3
import os

print('Current directory:', os.getcwd())
print('Files in directory:', os.listdir('.'))

db_path = 'comptabilite.db'
print(f'Looking for database: {db_path}')
print(f'Database exists: {os.path.exists(db_path)}')

if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    print('=== بيانات المنتجات ===')
    cursor.execute('SELECT * FROM produits')
    products = cursor.fetchall()

    if products:
        print(f'عدد المنتجات: {len(products)}')
        for product in products:
            print(f'ID: {product[0]}, Code: {product[1]}, Designation: {product[2]}, Prix Achat: {product[3]}, Prix Vente: {product[4]}, Stock: {product[5]}')
    else:
        print('لا توجد منتجات في قاعدة البيانات')

    print()
    print('=== بيانات العائلات ===')
    cursor.execute('SELECT * FROM familles_produits')
    families = cursor.fetchall()

    if families:
        print(f'عدد العائلات: {len(families)}')
        for family in families:
            print(f'ID: {family[0]}, Nom: {family[1]}')
    else:
        print('لا توجد عائلات في قاعدة البيانات')

    conn.close()
else:
    print('قاعدة البيانات غير موجودة')
