#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة عرض الجدول
"""

import sqlite3
from pathlib import Path

# إعداد المسار لقاعدة البيانات
DB_PATH = Path(__file__).parent / "database"
DATABASE_FILE = DB_PATH / "accounting.db"

def debug_table_display():
    """تشخيص مشكلة عرض الجدول"""
    try:
        conn = sqlite3.connect(str(DATABASE_FILE))
        cursor = conn.cursor()
        
        print("🔍 تشخيص مشكلة عرض الجدول")
        print("=" * 60)
        
        # 1. الأعمدة المطلوبة في الجدول
        expected_columns = [
            "Code", "Désignation", "Prix Achat (DH)", "Prix Vente (DH)", 
            "QTE ACHAT", "STOCK INITIAL", "العائلة/Famille", "Fournisseur"
        ]
        
        print("📋 الأعمدة المطلوبة في الجدول:")
        for i, col in enumerate(expected_columns, 1):
            print(f"  {i}. {col}")
        
        # 2. الاستعلام الحالي
        cursor.execute("""
            SELECT
                p.id,
                p.code,
                p.designation,
                p.prix_achat,
                p.prix_vente,
                COALESCE(p.qte_achat, 0) as qte_achat,
                p.stock,
                p.fournisseur,
                p.date_creation,
                p.famille_id,
                f.nom as famille_nom
            FROM produits p
            LEFT JOIN familles_produits f ON p.famille_id = f.id
            ORDER BY p.date_creation DESC
        """)
        
        products = cursor.fetchall()
        
        print(f"\n📊 البيانات من قاعدة البيانات:")
        print("الترتيب: id, code, designation, prix_achat, prix_vente, qte_achat, stock, fournisseur, date_creation, famille_id, famille_nom")
        
        for i, product in enumerate(products, 1):
            print(f"\n--- المنتج {i} ---")
            print(f"  ID: {product[0]}")
            print(f"  Code: {product[1]}")
            print(f"  Designation: {product[2]}")
            print(f"  Prix Achat: {product[3]}")
            print(f"  Prix Vente: {product[4]}")
            print(f"  QTE Achat: {product[5]}")
            print(f"  Stock: {product[6]}")
            print(f"  Fournisseur: {product[7]}")
            print(f"  Date Creation: {product[8]}")
            print(f"  Famille ID: {product[9]}")
            print(f"  Famille Nom: {product[10]}")
            
            # 3. كيف يجب أن تظهر في الجدول
            display_values = [
                product[1],  # Code
                product[2],  # Designation
                f"{product[3]:.2f} DH" if product[3] else "0.00 DH",  # Prix Achat
                f"{product[4]:.2f} DH" if product[4] else "0.00 DH",  # Prix Vente
                str(product[5]) if product[5] else "0",  # QTE ACHAT
                str(product[6]) if product[6] else "0",  # STOCK INITIAL
                product[10] if product[10] else "Aucune",  # Famille
                product[7] if product[7] else "Aucun"  # Fournisseur
            ]
            
            print(f"\n  📋 كيف يجب أن تظهر في الجدول:")
            for j, (col_name, value) in enumerate(zip(expected_columns, display_values), 1):
                print(f"    {j}. {col_name}: {value}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_table_display()