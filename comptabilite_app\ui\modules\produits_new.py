from PySide6.QtWidgets import QTableWidgetItem, QHeaderView, QMessageBox, QWidget, QHBoxLayout, QPushButton
from PySide6.QtCore import Qt
import sqlite3

# Importer le module de base
from ..components.base_module import BaseModule
from ..icons.icons import PRODUCTS_ICON

# Importer le dialogue produit
from ..components.produit_dialog import ProduitDialog

class ProduitsModule(BaseModule):
    """Module de gestion des produits avec interface simplifiée"""

    def __init__(self, db_manager, signals=None):
        super().__init__(
            db_manager=db_manager,
            signals=signals,
            title="Gestion des Produits",
            description="Ajoutez, modifiez et supprimez des produits",
            icon=PRODUCTS_ICON
        )

        # Configurer le bouton d'ajout
        self.add_button.setText("Ajouter un produit")

        # Configurer le tableau
        self.setup_table()

        # Charger les données
        self.load_produits()

    def setup_table(self):
        """Configure le tableau des produits"""
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "Code", "Désignation", "Unité", "Prix d'achat", "Prix de vente", "Stock", "Actions"
        ])

        # Configuration des colonnes
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Code
        self.items_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)  # Désignation
        self.items_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Unité
        self.items_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Prix d'achat
        self.items_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Prix de vente
        self.items_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Stock
        self.items_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Actions

        # Configurer la recherche
        self.search_input.setPlaceholderText("Rechercher un produit...")

    def load_produits(self):
        """Charge les produits depuis la base de données"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT * FROM produits ORDER BY code")
        produits = cursor.fetchall()

        self.items_table.setRowCount(0)

        for row_num, produit in enumerate(produits):
            self.items_table.insertRow(row_num)

            # Ajouter les données du produit
            self.items_table.setItem(row_num, 0, QTableWidgetItem(produit['code'] or ""))
            self.items_table.setItem(row_num, 1, QTableWidgetItem(produit['designation']))
            self.items_table.setItem(row_num, 2, QTableWidgetItem(produit['unite'] or ""))
            self.items_table.setItem(row_num, 3, QTableWidgetItem(f"{produit['prix_achat']:.2f} DH" if produit['prix_achat'] else ""))
            self.items_table.setItem(row_num, 4, QTableWidgetItem(f"{produit['prix_vente']:.2f} DH" if produit['prix_vente'] else ""))
            self.items_table.setItem(row_num, 5, QTableWidgetItem(str(produit['stock'] or 0)))

            # Créer les boutons d'action
            actions_widget = self.create_action_buttons(row_num, produit['id'], produit['designation'])
            self.items_table.setCellWidget(row_num, 6, actions_widget)

            # Stocker l'ID du produit dans la première colonne (invisible)
            self.items_table.item(row_num, 0).setData(Qt.UserRole, produit['id'])

    def create_action_buttons(self, row_id, item_id, item_name=None):
        """Crée les boutons d'action pour une ligne du tableau avec bouton Ajout entrées"""
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(2, 2, 2, 2)
        actions_layout.setSpacing(6)

        # Bouton d'édition
        edit_btn = QPushButton("✏️")
        edit_btn.setProperty("secondary", "true")
        edit_btn.setToolTip("Modifier")
        edit_btn.setFixedSize(32, 32)
        edit_btn.clicked.connect(lambda: self.show_edit_dialog(item_id))

        # Bouton Ajout entrées
        stock_btn = QPushButton("📦")
        stock_btn.setProperty("primary", "true")
        stock_btn.setToolTip("Ajout entrées")
        stock_btn.setFixedSize(32, 32)
        stock_btn.clicked.connect(lambda: self.show_ajout_entrees_dialog(item_id, item_name))

        # Bouton de suppression
        delete_btn = QPushButton("🗑️")
        delete_btn.setProperty("error", "true")
        delete_btn.setToolTip("Supprimer")
        delete_btn.setFixedSize(32, 32)
        delete_btn.clicked.connect(lambda: self.delete_item(item_id, item_name))

        actions_layout.addWidget(edit_btn)
        actions_layout.addWidget(stock_btn)
        actions_layout.addWidget(delete_btn)

        return actions_widget

    def show_add_dialog(self):
        """Affiche la boîte de dialogue pour ajouter un produit"""
        dialog = ProduitDialog(self.db_manager, parent=self)
        dialog.produit_saved.connect(self.on_produit_saved)
        dialog.exec()

    def show_edit_dialog(self, produit_id):
        """Affiche la boîte de dialogue pour modifier un produit"""
        dialog = ProduitDialog(self.db_manager, produit_id=produit_id, parent=self)
        dialog.produit_saved.connect(self.on_produit_saved)
        dialog.exec()

    def on_produit_saved(self):
        """Appelé lorsqu'un produit est ajouté ou modifié"""
        self.load_produits()

        # Émettre le signal pour informer les autres modules
        if self.signals:
            self.signals.produits_changed.emit()

    def delete_item(self, produit_id, produit_name):
        """Supprime un produit"""
        confirm = QMessageBox.question(
            self, "Confirmation",
            f"Êtes-vous sûr de vouloir supprimer le produit {produit_name} ?\nCette action est irréversible.",
            QMessageBox.Yes | QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                cursor = self.db_manager.conn.cursor()
                cursor.execute("DELETE FROM produits WHERE id = ?", (produit_id,))
                self.db_manager.conn.commit()

                self.load_produits()

                # Émettre le signal pour informer les autres modules
                if self.signals:
                    self.signals.produits_changed.emit()

                QMessageBox.information(self, "Succès", f"Produit {produit_name} supprimé avec succès.")
            except sqlite3.Error as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression du produit: {str(e)}")

    def show_ajout_entrees_dialog(self, produit_id, produit_designation):
        """Affiche la boîte de dialogue pour ajouter des entrées de stock"""
        from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QSpinBox, QPushButton, QMessageBox, QLineEdit, QComboBox
        
        # Récupérer toutes les informations du produit
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("SELECT * FROM produits WHERE id = ?", (produit_id,))
            produit = cursor.fetchone()
            if not produit:
                QMessageBox.warning(self, "Erreur", "Produit non trouvé!")
                return
            stock_actuel = produit['stock'] if produit['stock'] else 0
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la récupération du produit: {e}")
            return

        dialog = QDialog(self)
        dialog.setWindowTitle(f"Ajout entrées - {produit_designation}")
        dialog.setFixedSize(500, 600)
        layout = QVBoxLayout(dialog)
        layout.setSpacing(10)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title_label = QLabel(f"Ajout entrées - {produit_designation}")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # Informations modifiables du produit
        info_layout = QVBoxLayout()

        # Prix d'achat (HT) - modifiable
        prix_achat_layout = QHBoxLayout()
        prix_achat_label = QLabel("Prix d'achat (HT):")
        prix_achat_spin = QSpinBox()
        prix_achat_spin.setMaximum(999999)
        prix_achat_spin.setValue(int(produit['prix_achat']) if produit['prix_achat'] else 0)
        prix_achat_spin.setSuffix(" DH")
        prix_achat_layout.addWidget(prix_achat_label)
        prix_achat_layout.addWidget(prix_achat_spin)
        info_layout.addLayout(prix_achat_layout)

        # Prix de vente (HT) - modifiable
        prix_vente_layout = QHBoxLayout()
        prix_vente_label = QLabel("Prix de vente (HT):")
        prix_vente_spin = QSpinBox()
        prix_vente_spin.setMaximum(999999)
        prix_vente_spin.setValue(int(produit['prix_vente']) if produit['prix_vente'] else 0)
        prix_vente_spin.setSuffix(" DH")
        prix_vente_layout.addWidget(prix_vente_label)
        prix_vente_layout.addWidget(prix_vente_spin)
        info_layout.addLayout(prix_vente_layout)

        # Quantité en stock actuel (lecture seule)
        stock_layout = QHBoxLayout()
        stock_label = QLabel(f"Quantité en stock: {stock_actuel}")
        stock_layout.addWidget(stock_label)
        info_layout.addLayout(stock_layout)

        # Quantité à ajouter
        quantite_label = QLabel("Quantité à ajouter:")
        quantite_spin = QSpinBox()
        quantite_spin.setMinimum(1)
        quantite_spin.setMaximum(999999)
        quantite_spin.setValue(1)
        info_layout.addWidget(quantite_label)
        info_layout.addWidget(quantite_spin)

        # Nouveau stock (calculé automatiquement)
        nouveau_stock_label = QLabel(f"Nouveau stock: {stock_actuel + 1}")
        nouveau_stock_label.setStyleSheet("color: blue; font-weight: bold;")
        info_layout.addWidget(nouveau_stock_label)

        layout.addLayout(info_layout)

        # Section d'informations d'achat
        achat_group = QLabel("Informations d'achat:")
        achat_group.setStyleSheet("font-weight: bold; margin-top: 20px; margin-bottom: 10px;")
        layout.addWidget(achat_group)

        # Fournisseur
        fournisseur_layout = QHBoxLayout()
        fournisseur_label = QLabel("Fournisseur:")
        fournisseur_combo = QComboBox()
        fournisseur_combo.setEditable(True)
        
        # Charger les fournisseurs depuis la base de données
        try:
            cursor.execute("SELECT nom FROM fournisseurs ORDER BY nom")
            fournisseurs = cursor.fetchall()
            for fournisseur in fournisseurs:
                fournisseur_combo.addItem(fournisseur['nom'])
        except:
            # Si la table n'existe pas, ajouter quelques options par défaut
            fournisseur_combo.addItems(["Fournisseur A", "Fournisseur B", "Fournisseur C"])
        
        fournisseur_layout.addWidget(fournisseur_label)
        fournisseur_layout.addWidget(fournisseur_combo)
        layout.addLayout(fournisseur_layout)

        # Numéro de facture d'achat
        facture_layout = QHBoxLayout()
        facture_label = QLabel("Numéro de facture d'achat:")
        facture_input = QLineEdit()
        facture_input.setPlaceholderText("Ex: 545965")
        facture_layout.addWidget(facture_label)
        facture_layout.addWidget(facture_input)
        layout.addLayout(facture_layout)

        # Date
        date_layout = QHBoxLayout()
        date_label = QLabel("Date:")
        date_input = QLineEdit()
        date_input.setText("25/05/2025")
        date_layout.addWidget(date_label)
        date_layout.addWidget(date_input)
        layout.addLayout(date_layout)

        # Date de paiement
        date_paiement_layout = QHBoxLayout()
        date_paiement_label = QLabel("Date de paiement:")
        date_paiement_input = QLineEdit()
        date_paiement_input.setText("01/01/2025")
        date_paiement_layout.addWidget(date_paiement_label)
        date_paiement_layout.addWidget(date_paiement_input)
        layout.addLayout(date_paiement_layout)

        # Mode de paiement
        mode_paiement_layout = QHBoxLayout()
        mode_paiement_label = QLabel("Mode de paiement:")
        mode_paiement_combo = QComboBox()
        mode_paiement_combo.setEditable(True)
        
        # Ajouter les modes de paiement courants
        mode_paiement_combo.addItems([
            "Espèces",
            "Chèque", 
            "Virement bancaire",
            "Carte bancaire",
            "Crédit"
        ])
        
        mode_paiement_layout.addWidget(mode_paiement_label)
        mode_paiement_layout.addWidget(mode_paiement_combo)
        layout.addLayout(mode_paiement_layout)

        # Boutons
        buttons_layout = QHBoxLayout()
        cancel_button = QPushButton("Annuler")
        confirm_button = QPushButton("Confirmer")
        
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #F3F4F6;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
        """)
        
        confirm_button.setStyleSheet("""
            QPushButton {
                background-color: #1A56DB;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1E40AF;
            }
        """)
        
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

        # Fonction pour mettre à jour le nouveau stock
        def update_nouveau_stock():
            nouveau_stock = stock_actuel + quantite_spin.value()
            nouveau_stock_label.setText(f"Nouveau stock: {nouveau_stock}")

        quantite_spin.valueChanged.connect(update_nouveau_stock)

        # Connexions des boutons
        def confirmer_ajout():
            quantite_ajoutee = quantite_spin.value()
            nouveau_prix_achat = prix_achat_spin.value()
            nouveau_prix_vente = prix_vente_spin.value()

            # Informations d'achat
            fournisseur = fournisseur_combo.currentText().strip()
            numero_facture = facture_input.text().strip()
            date_achat = date_input.text().strip()
            date_paiement = date_paiement_input.text().strip()
            mode_paiement = mode_paiement_combo.currentText().strip()

            try:
                cursor = self.db_manager.conn.cursor()

                # Vérifier si les colonnes existent et les ajouter si nécessaire
                cursor.execute("PRAGMA table_info(produits)")
                columns = [col[1] for col in cursor.fetchall()]
                
                # Ajouter les colonnes manquantes
                if 'fournisseur' not in columns:
                    cursor.execute("ALTER TABLE produits ADD COLUMN fournisseur TEXT")
                if 'numero_facture' not in columns:
                    cursor.execute("ALTER TABLE produits ADD COLUMN numero_facture TEXT")

                # Mettre à jour le produit avec toutes les nouvelles informations
                cursor.execute("""
                    UPDATE produits
                    SET stock = stock + ?,
                        prix_achat = ?,
                        prix_vente = ?,
                        date_paiement = ?,
                        fournisseur = ?,
                        numero_facture = ?
                    WHERE id = ?
                """, (quantite_ajoutee, nouveau_prix_achat, nouveau_prix_vente, date_paiement, fournisseur, numero_facture, produit_id))

                self.db_manager.conn.commit()

                # Intégrer avec la caisse si paiement en espèces
                if mode_paiement == "Espèces":
                    prix_total = nouveau_prix_achat * quantite_ajoutee
                    self.integrate_with_caisse_purchase(numero_facture, prix_total)

                # Afficher un message de succès
                QMessageBox.information(
                    dialog,
                    "Succès",
                    f"Entrée ajoutée avec succès!\n\n"
                    f"Produit: {produit_designation}\n"
                    f"Quantité ajoutée: {quantite_ajoutee}\n"
                    f"Nouveau stock: {stock_actuel + quantite_ajoutee}\n"
                    f"Fournisseur: {fournisseur}\n"
                    f"Facture: {numero_facture}\n"
                    f"Date de paiement: {date_paiement}"
                )

                # Recharger la liste des produits
                self.load_produits()

                # Émettre le signal pour informer les autres modules
                if self.signals:
                    self.signals.produits_changed.emit()

                dialog.accept()

            except Exception as e:
                QMessageBox.critical(
                    dialog,
                    "Erreur",
                    f"Erreur lors de l'ajout de l'entrée:\n{str(e)}"
                )

        confirm_button.clicked.connect(confirmer_ajout)
        cancel_button.clicked.connect(dialog.reject)

        # Afficher la boîte de dialogue
        dialog.exec()

    def integrate_with_caisse_purchase(self, numero_facture, montant):
        """Intègre automatiquement avec la caisse pour les achats de produits en espèces"""
        try:
            from datetime import datetime
            # Ajouter une sortie dans la caisse
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                INSERT INTO caisse_sorties (date, nature, objet, reference, montant)
                VALUES (?, ?, ?, ?, ?)
            """, (
                datetime.now().strftime("%Y-%m-%d"),
                "شراء",
                f"شراء منتجات بالنقد - فاتورة {numero_facture}",
                numero_facture,
                montant
            ))
            self.db_manager.conn.commit()
            print(f"💸 Sortie de caisse automatique ajoutée: {montant} DH pour l'achat de produits {numero_facture}")
        except Exception as e:
            print(f"❌ Erreur lors de l'intégration avec la caisse: {str(e)}")
