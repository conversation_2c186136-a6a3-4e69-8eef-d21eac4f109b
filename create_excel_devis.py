#!/usr/bin/env python3
"""
إنشاء نموذج عرض أسعار في Excel باستخدام CSV
ثم تحويله إلى Excel إذا كانت المكتبات متوفرة
"""

import csv
import os

def create_excel_devis():
    """إنشاء نموذج عرض أسعار في Excel"""
    
    # بيانات النموذج
    data = [
        # العنوان والمعلومات الأساسية
        ["", "", "", "", "", "", "", "", "ملاحظات خارج الطباعة"],
        ["", "عرض أسعار / DEVIS", "", "", "", "", "", "", ""],
        ["", "", "", "", "", "", "", "", ""],
        ["", "", "", "", "", "", "", "", ""],
        ["", "رقم العرض:", "DEV-2024-001", "", "", "التاريخ:", "2024-01-01", "", ""],
        ["", "", "", "", "", "", "", "", ""],
        
        # قسم معلومات العميل
        ["", "معلومات العميل", "", "", "", "", "", "", ""],
        ["", "نوع العميل", "Privé", "", "", "", "", "", "اختيار Privé/Public"],
        ["", "طبيعة الخدمة", "Travaux", "", "", "", "", "", "لا يظهر في الطباعة إذا كان Public"],
        ["", "العميل", "Client A", "", "", "", "", "", "اختيار العميل"],
        ["", "العنوان", "123 Rue Example", "", "", "", "", "", "يظهر تلقائياً إذا كان Privé - خارج الطباعة"],
        ["", "ICE", "ICE001", "", "", "", "", "", "يظهر تلقائياً إذا كان Privé - خارج الطباعة"],
        ["", "الموضوع", "Fourniture matériel informatique", "", "", "", "", "", ""],
        ["", "", "", "", "", "", "", "", ""],
        
        # جدول المنتجات
        ["", "تفاصيل المنتجات والخدمات", "", "", "", "", "", "", ""],
        ["", "N°", "المنتج/الخدمة", "الوحدة", "الكمية", "سعر الشراء HT", "نسبة الربح", "سعر البيع HT", "المجموع HT"],
        
        # صفوف المنتجات
        ["", "1", "اختيار المنتج", "تلقائي", "1", "100", "1.2", "=F17*G17", "=E17*H17"],
        ["", "2", "اختيار المنتج", "تلقائي", "1", "50", "1.1", "=F18*G18", "=E18*H18"],
        ["", "3", "اختيار المنتج", "تلقائي", "1", "75", "1.2", "=F19*G19", "=E19*H19"],
        ["", "4", "اختيار المنتج", "تلقائي", "1", "0", "1.1", "=F20*G20", "=E20*H20"],
        ["", "5", "اختيار المنتج", "تلقائي", "1", "0", "1.2", "=F21*G21", "=E21*H21"],
        ["", "6", "اختيار المنتج", "تلقائي", "1", "0", "1.1", "=F22*G22", "=E22*H22"],
        ["", "7", "اختيار المنتج", "تلقائي", "1", "0", "1.2", "=F23*G23", "=E23*H23"],
        ["", "8", "اختيار المنتج", "تلقائي", "1", "0", "1.1", "=F24*G24", "=E24*H24"],
        ["", "9", "اختيار المنتج", "تلقائي", "1", "0", "1.2", "=F25*G25", "=E25*H25"],
        ["", "10", "اختيار المنتج", "تلقائي", "1", "0", "1.1", "=F26*G26", "=E26*H26"],
        ["", "", "", "", "", "", "", "", ""],
        
        # المجاميع
        ["", "", "", "", "", "", "", "المجموع HT:", "=SUM(I17:I26)"],
        ["", "", "", "", "", "", "", "TVA 20%:", "=I28*0.2"],
        ["", "", "", "", "", "", "", "المجموع TTC:", "=I28+I29"],
        ["", "", "", "", "", "", "", "", ""],
        
        # المبلغ بالأحرف
        ["", "المبلغ بالأحرف:", "مائتان وخمسة وعشرون درهماً", "", "", "", "", "", ""],
        ["", "", "", "", "", "", "", "", ""],
        ["", "Arrêté le présent devis à la somme de :", "225.00 DH TTC", "", "", "", "", "", ""],
        ["", "", "", "", "", "", "", "", ""],
        
        # أزرار التحكم
        ["", "Valider", "", "Supprimer", "", "Imprimer", "", "", "لا يظهر في الطباعة"],
    ]
    
    # إنشاء ملف CSV
    csv_filename = "devis_template.csv"
    with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerows(data)
    
    print(f"✅ تم إنشاء ملف CSV: {csv_filename}")
    
    # محاولة تحويل إلى Excel إذا كانت المكتبات متوفرة
    try:
        import pandas as pd
        
        # قراءة CSV وتحويل إلى Excel
        df = pd.read_csv(csv_filename, header=None)
        excel_filename = "devis_professionnel.xlsx"
        
        # إنشاء Excel مع تنسيق
        with pd.ExcelWriter(excel_filename, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='Devis', index=False, header=False)
            
            # الحصول على workbook و worksheet
            workbook = writer.book
            worksheet = writer.sheets['Devis']
            
            # تنسيقات
            header_format = workbook.add_format({
                'bold': True,
                'font_color': 'white',
                'bg_color': '#4A90E2',
                'border': 1,
                'align': 'center'
            })
            
            title_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'font_color': '#1E3A8A',
                'bg_color': '#E8F4FD',
                'border': 2,
                'align': 'center'
            })
            
            cell_format = workbook.add_format({
                'border': 1,
                'align': 'center'
            })
            
            note_format = workbook.add_format({
                'font_size': 10,
                'font_color': '#856404',
                'bg_color': '#FFF3CD',
                'border': 1
            })
            
            # تطبيق التنسيقات
            # العنوان الرئيسي
            worksheet.merge_range('B2:H4', 'عرض أسعار / DEVIS', title_format)
            
            # عناوين الجدول
            for col in range(1, 9):  # B إلى I
                worksheet.write(15, col, df.iloc[15, col], header_format)
            
            # تعديل عرض الأعمدة
            worksheet.set_column('A:A', 3)
            worksheet.set_column('B:B', 15)
            worksheet.set_column('C:C', 20)
            worksheet.set_column('D:D', 12)
            worksheet.set_column('E:E', 10)
            worksheet.set_column('F:F', 15)
            worksheet.set_column('G:G', 12)
            worksheet.set_column('H:H', 15)
            worksheet.set_column('I:I', 15)
            worksheet.set_column('J:J', 25)
            
            # إخفاء خطوط الشبكة
            worksheet.hide_gridlines(2)
        
        print(f"✅ تم إنشاء ملف Excel: {excel_filename}")
        return excel_filename
        
    except ImportError:
        print("⚠️ pandas غير متوفرة - تم إنشاء ملف CSV فقط")
        print("💡 يمكنك فتح ملف CSV في Excel وحفظه كملف .xlsx")
        return csv_filename
    
    except Exception as e:
        print(f"⚠️ خطأ في إنشاء Excel: {e}")
        return csv_filename

def create_devis_instructions():
    """إنشاء ملف تعليمات للنموذج"""
    
    instructions = """
# 📋 تعليمات استخدام نموذج عرض الأسعار

## 🎯 الميزات الرئيسية:

### 1. **نوع العميل**
- اختر "Privé" أو "Public" من القائمة المنسدلة
- عند اختيار "Privé": تظهر حقول العنوان وICE (خارج منطقة الطباعة)
- عند اختيار "Public": تختفي هذه الحقول تماماً

### 2. **طبيعة الخدمة**
- اختر "Travaux" أو "Fourniture"
- لا يظهر في الطباعة إذا كان العميل "Public"

### 3. **العميل**
- اختر من القائمة المنسدلة أو أدخل يدوياً
- في حالة "Privé": يتم ملء العنوان وICE تلقائياً

### 4. **جدول المنتجات**
- 10 صفوف قابلة للاستخدام
- رقم تسلسلي تلقائي
- اختيار المنتج من قائمة منسدلة
- وحدة القياس تلقائية
- الكمية قابلة للتعديل
- سعر الشراء تلقائي من قاعدة البيانات
- نسبة الربح قابلة للتعديل (مثال: 1.2 = 20% ربح)
- سعر البيع = سعر الشراء × نسبة الربح
- المجموع = الكمية × سعر البيع

### 5. **الحسابات**
- المجموع HT: مجموع كل الصفوف
- TVA 20%: محسوبة تلقائياً
- المجموع TTC: المجموع النهائي
- المبلغ بالأحرف: تحويل تلقائي للأرقام

### 6. **الأزرار**
- **Valider**: التحقق من صحة البيانات
- **Supprimer**: حذف جميع البيانات
- **Imprimer**: طباعة (يخفي الحقول غير المطلوبة)

## 🎨 التصميم:

### الألوان المستخدمة:
- **الأزرق الفاتح** (#E8F4FD): خلفية العنوان
- **الأزرق المتوسط** (#4A90E2): عناوين الجداول
- **الرمادي الداكن** (#2C3E50): النصوص
- **الأخضر الفاتح** (#D5EDDA): المجموع النهائي
- **الأصفر الفاتح** (#FFF3CD): الملاحظات

### الخطوط:
- **Calibri**: الخط الأساسي
- **أحجام متدرجة**: 16px للعنوان، 12px للعناوين، 11px للنص العادي

## 📱 الاستجابة:
- تصميم متجاوب يعمل على جميع الأحجام
- طباعة محسنة تخفي العناصر غير المطلوبة
- ألوان متناسقة ومريحة للعين

## 🔧 التخصيص:
يمكن تخصيص النموذج بسهولة:
- إضافة منتجات جديدة
- تعديل نسب الضرائب
- تغيير الألوان والخطوط
- إضافة حقول جديدة

## 💾 الحفظ والطباعة:
- احفظ كملف HTML للاستخدام التفاعلي
- اطبع مباشرة من المتصفح
- احفظ كـ PDF من خيارات الطباعة

---
**🎉 النموذج جاهز للاستخدام الاحترافي!**
    """
    
    with open("devis_instructions.md", 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ تم إنشاء ملف التعليمات: devis_instructions.md")

if __name__ == "__main__":
    # إنشاء النموذج
    filename = create_excel_devis()
    
    # إنشاء التعليمات
    create_devis_instructions()
    
    print(f"\n🎉 تم إنجاز المهمة بنجاح!")
    print(f"📁 الملفات المنشأة:")
    print(f"   - {filename}")
    print(f"   - devis_professionnel.html")
    print(f"   - devis_instructions.md")
    print(f"\n💡 يمكنك فتح الملف HTML في المتصفح للاستخدام التفاعلي")
    print(f"💡 أو فتح ملف CSV/Excel في برنامج Excel للتعديل")
