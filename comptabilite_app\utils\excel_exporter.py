"""
نظام تصدير البيانات إلى Excel
Excel Export System
"""

import pandas as pd
from datetime import datetime
import os
from PySide6.QtWidgets import QMessageBox, QFileDialog
from PySide6.QtCore import QStandardPaths
import sqlite3

class ExcelExporter:
    """فئة تصدير البيانات إلى Excel"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        
    def export_factures_achat(self, parent_widget=None):
        """تصدير فواتير الشراء إلى Excel"""
        try:
            # الحصول على البيانات
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT 
                    date_facture as "Date de paiement",
                    numero_facture as "N° facture achat",
                    code_produit as "Code",
                    designation as "Désignation",
                    montant_ht as "Montant HT",
                    montant_tva as "Montant TVA",
                    montant_ttc as "TTC",
                    prix_vente as "Px de vente HT",
                    valeur_stock as "Valeur stock",
                    nom_fournisseur as "Fournisseur",
                    if_number as "IF",
                    ice as "ICE"
                FROM factures_achat 
                ORDER BY date_facture DESC
            """)
            
            data = cursor.fetchall()
            columns = [description[0] for description in cursor.description]
            
            if not data:
                QMessageBox.information(parent_widget, "Information", "Aucune donnée à exporter.")
                return False
            
            # إنشاء DataFrame
            df = pd.DataFrame(data, columns=columns)
            
            # تنسيق التواريخ
            if 'Date de paiement' in df.columns:
                df['Date de paiement'] = pd.to_datetime(df['Date de paiement']).dt.strftime('%d/%m/%Y')
            
            # تنسيق الأرقام
            numeric_columns = ['Montant HT', 'Montant TVA', 'TTC', 'Px de vente HT', 'Valeur stock']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = df[col].apply(lambda x: f"{x:.2f}" if pd.notnull(x) else "0.00")
            
            # حفظ الملف
            filename = f"factures_achat_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            return self._save_excel_file(df, filename, "Factures d'Achat", parent_widget)
            
        except Exception as e:
            QMessageBox.critical(parent_widget, "Erreur", f"Erreur lors de l'exportation: {str(e)}")
            return False

    def export_factures_vente(self, parent_widget=None):
        """تصدير فواتير البيع إلى Excel"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT 
                    fv.date_facture as "Date",
                    fv.numero_facture as "N° Facture",
                    fv.client_nom as "Client",
                    fv.objet as "Objet",
                    fv.montant_ht as "Montant HT",
                    fv.montant_tva as "TVA",
                    fv.montant_ttc as "TTC",
                    fv.mode_paiement as "Mode Paiement",
                    fv.marche as "Marché",
                    fv.date_paiement as "Date Paiement"
                FROM factures_vente fv
                ORDER BY fv.date_facture DESC
            """)
            
            data = cursor.fetchall()
            columns = [description[0] for description in cursor.description]
            
            if not data:
                QMessageBox.information(parent_widget, "Information", "Aucune facture de vente à exporter.")
                return False
            
            df = pd.DataFrame(data, columns=columns)
            
            # تنسيق التواريخ
            date_columns = ['Date', 'Date Paiement']
            for col in date_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col], errors='coerce').dt.strftime('%d/%m/%Y')
            
            # تنسيق الأرقام
            numeric_columns = ['Montant HT', 'TVA', 'TTC']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = df[col].apply(lambda x: f"{x:.2f}" if pd.notnull(x) else "0.00")
            
            filename = f"factures_vente_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            return self._save_excel_file(df, filename, "Factures de Vente", parent_widget)
            
        except Exception as e:
            QMessageBox.critical(parent_widget, "Erreur", f"Erreur lors de l'exportation: {str(e)}")
            return False

    def export_bons_commande(self, parent_widget=None):
        """تصدير بونات الطلب إلى Excel"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT 
                    bc.date_bon as "Date",
                    bc.numero_bon as "N° Bon",
                    bc.client_nom as "Client",
                    bc.objet as "Objet",
                    bc.montant_ht as "Montant HT",
                    bc.montant_tva as "TVA",
                    bc.montant_ttc as "TTC",
                    bc.marche as "Marché",
                    bc.statut as "Statut"
                FROM bons_commande bc
                ORDER BY bc.date_bon DESC
            """)
            
            data = cursor.fetchall()
            columns = [description[0] for description in cursor.description]
            
            if not data:
                QMessageBox.information(parent_widget, "Information", "Aucun bon de commande à exporter.")
                return False
            
            df = pd.DataFrame(data, columns=columns)
            
            # تنسيق التواريخ
            if 'Date' in df.columns:
                df['Date'] = pd.to_datetime(df['Date'], errors='coerce').dt.strftime('%d/%m/%Y')
            
            # تنسيق الأرقام
            numeric_columns = ['Montant HT', 'TVA', 'TTC']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = df[col].apply(lambda x: f"{x:.2f}" if pd.notnull(x) else "0.00")
            
            filename = f"bons_commande_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            return self._save_excel_file(df, filename, "Bons de Commande", parent_widget)
            
        except Exception as e:
            QMessageBox.critical(parent_widget, "Erreur", f"Erreur lors de l'exportation: {str(e)}")
            return False

    def export_marches(self, parent_widget=None):
        """تصدير المشاريع إلى Excel"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT 
                    m.date_creation as "Date Création",
                    m.reference_marche as "Référence",
                    m.objet as "Objet",
                    m.montant as "Montant",
                    m.delai_execution as "Délai",
                    m.statut as "Statut",
                    m.caution_provisoire as "Caution Provisoire",
                    m.montant_caution_definitif as "Caution Définitive",
                    m.date_approbation as "Date Approbation",
                    m.date_notification as "Date Notification"
                FROM marches m
                ORDER BY m.date_creation DESC
            """)
            
            data = cursor.fetchall()
            columns = [description[0] for description in cursor.description]
            
            if not data:
                QMessageBox.information(parent_widget, "Information", "Aucun marché à exporter.")
                return False
            
            df = pd.DataFrame(data, columns=columns)
            
            # تنسيق التواريخ
            date_columns = ['Date Création', 'Date Approbation', 'Date Notification']
            for col in date_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col], errors='coerce').dt.strftime('%d/%m/%Y')
            
            # تنسيق الأرقام
            numeric_columns = ['Montant', 'Caution Provisoire', 'Caution Définitive']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = df[col].apply(lambda x: f"{x:.2f}" if pd.notnull(x) else "0.00")
            
            filename = f"marches_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            return self._save_excel_file(df, filename, "Marchés", parent_widget)
            
        except Exception as e:
            QMessageBox.critical(parent_widget, "Erreur", f"Erreur lors de l'exportation: {str(e)}")
            return False

    def export_stock(self, parent_widget=None):
        """تصدير المخزون إلى Excel"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT 
                    p.code as "Code",
                    p.designation as "Désignation",
                    p.unite as "Unité",
                    p.stock as "Stock",
                    p.prix_achat as "Prix Achat",
                    p.prix_vente as "Prix Vente",
                    (p.stock * p.prix_achat) as "Valeur Stock",
                    p.date_paiement as "Date Paiement"
                FROM produits p
                WHERE p.stock > 0
                ORDER BY p.designation
            """)
            
            data = cursor.fetchall()
            columns = [description[0] for description in cursor.description]
            
            if not data:
                QMessageBox.information(parent_widget, "Information", "Aucun produit en stock à exporter.")
                return False
            
            df = pd.DataFrame(data, columns=columns)
            
            # تنسيق التواريخ
            if 'Date Paiement' in df.columns:
                df['Date Paiement'] = pd.to_datetime(df['Date Paiement'], errors='coerce').dt.strftime('%d/%m/%Y')
            
            # تنسيق الأرقام
            numeric_columns = ['Stock', 'Prix Achat', 'Prix Vente', 'Valeur Stock']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = df[col].apply(lambda x: f"{x:.2f}" if pd.notnull(x) else "0.00")
            
            filename = f"stock_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            return self._save_excel_file(df, filename, "État du Stock", parent_widget)
            
        except Exception as e:
            QMessageBox.critical(parent_widget, "Erreur", f"Erreur lors de l'exportation: {str(e)}")
            return False

    def export_clients(self, parent_widget=None):
        """تصدير العملاء إلى Excel"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT 
                    c.code as "Code",
                    c.nom as "Nom",
                    c.adresse as "Adresse",
                    c.telephone as "Téléphone",
                    c.email as "Email",
                    c.ice as "ICE"
                FROM clients c
                ORDER BY c.nom
            """)
            
            data = cursor.fetchall()
            columns = [description[0] for description in cursor.description]
            
            if not data:
                QMessageBox.information(parent_widget, "Information", "Aucun client à exporter.")
                return False
            
            df = pd.DataFrame(data, columns=columns)
            
            filename = f"clients_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            return self._save_excel_file(df, filename, "Liste des Clients", parent_widget)
            
        except Exception as e:
            QMessageBox.critical(parent_widget, "Erreur", f"Erreur lors de l'exportation: {str(e)}")
            return False

    def export_fournisseurs(self, parent_widget=None):
        """تصدير الموردين إلى Excel"""
        try:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("""
                SELECT 
                    f.code as "Code",
                    f.nom as "Nom",
                    f.adresse as "Adresse",
                    f.telephone as "Téléphone",
                    f.email as "Email",
                    f.ice as "ICE",
                    f.if_number as "IF"
                FROM fournisseurs f
                ORDER BY f.nom
            """)
            
            data = cursor.fetchall()
            columns = [description[0] for description in cursor.description]
            
            if not data:
                QMessageBox.information(parent_widget, "Information", "Aucun fournisseur à exporter.")
                return False
            
            df = pd.DataFrame(data, columns=columns)
            
            filename = f"fournisseurs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            return self._save_excel_file(df, filename, "Liste des Fournisseurs", parent_widget)
            
        except Exception as e:
            QMessageBox.critical(parent_widget, "Erreur", f"Erreur lors de l'exportation: {str(e)}")
            return False

    def _save_excel_file(self, df, default_filename, sheet_name, parent_widget):
        """حفظ ملف Excel"""
        try:
            # اختيار مجلد الحفظ
            documents_path = QStandardPaths.writableLocation(QStandardPaths.DocumentsLocation)
            file_path, _ = QFileDialog.getSaveFileName(
                parent_widget,
                f"Enregistrer {sheet_name}",
                os.path.join(documents_path, default_filename),
                "Fichiers Excel (*.xlsx);;Tous les fichiers (*)"
            )
            
            if not file_path:
                return False
            
            # إنشاء ملف Excel مع تنسيق
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # تنسيق الورقة
                worksheet = writer.sheets[sheet_name]
                
                # تعديل عرض الأعمدة
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
                
                # تنسيق الرأس
                for cell in worksheet[1]:
                    cell.font = cell.font.copy(bold=True)
                    cell.fill = cell.fill.copy(fgColor="366092")
            
            QMessageBox.information(
                parent_widget, 
                "Succès", 
                f"Données exportées avec succès vers:\n{file_path}"
            )
            return True
            
        except Exception as e:
            QMessageBox.critical(parent_widget, "Erreur", f"Erreur lors de la sauvegarde: {str(e)}")
            return False
