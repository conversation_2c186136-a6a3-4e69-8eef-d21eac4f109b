import os
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side
from openpyxl.drawing.image import Image
from openpyxl.utils import get_column_letter
import datetime
from copy import copy

class ExcelInvoiceGenerator:
    """
    Générateur de factures basé sur un modèle Excel.
    Cette classe permet de générer des factures en utilisant un modèle Excel existant.
    """

    def __init__(self, template_path=None):
        """
        Initialise le générateur de factures avec un modèle Excel.
        
        Args:
            template_path (str): Chemin vers le fichier modèle Excel. Si None, utilise le chemin par défaut.
        """
        if template_path is None:
            # Chemin par défaut vers le modèle de facture
            self.template_path = "F:\\REDRED1\\model facture.xlsx"
        else:
            self.template_path = template_path
            
        # Vérifier que le modèle existe
        if not os.path.exists(self.template_path):
            raise FileNotFoundError(f"Le modèle de facture n'a pas été trouvé: {self.template_path}")
            
        # Dossier de sortie pour les factures générées
        self.output_dir = "factures"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def generate_invoice(self, invoice_data):
        """
        Génère une facture à partir des données fournies en utilisant le modèle Excel.
        
        Args:
            invoice_data (dict): Données de la facture avec les clés suivantes:
                - numero (str): Numéro de facture
                - date (str): Date de la facture
                - client (dict): Informations du client (nom, adresse, ice, etc.)
                - lignes (list): Liste des lignes de facture (designation, unite, quantite, prix_unitaire, etc.)
                - totaux (dict): Totaux de la facture (total_ht, total_tva, total_ttc)
                
        Returns:
            str: Chemin vers le fichier Excel généré
        """
        # Charger le modèle Excel
        wb = openpyxl.load_workbook(self.template_path)
        ws = wb.active  # Utiliser la feuille active
        
        # Remplir les informations de la facture
        self._fill_invoice_header(ws, invoice_data)
        self._fill_client_info(ws, invoice_data['client'])
        self._fill_invoice_lines(ws, invoice_data['lignes'])
        self._fill_totals(ws, invoice_data['totaux'])
        
        # Définir le nom du fichier de sortie
        safe_numero = invoice_data['numero'].replace('/', '_').replace(' ', '_').replace('(', '').replace(')', '')
        output_file = os.path.join(self.output_dir, f"facture_{safe_numero}.xlsx")
        
        # Enregistrer le fichier
        wb.save(output_file)
        
        return output_file
        
    def generate_pdf(self, invoice_data):
        """
        Génère une facture au format PDF à partir des données fournies.
        
        Args:
            invoice_data (dict): Données de la facture (voir generate_invoice)
                
        Returns:
            str: Chemin vers le fichier PDF généré
        """
        # D'abord générer le fichier Excel
        excel_file = self.generate_invoice(invoice_data)
        
        # Définir le nom du fichier PDF
        pdf_file = excel_file.replace('.xlsx', '.pdf')
        
        # Convertir Excel en PDF
        try:
            # Utiliser une bibliothèque de conversion Excel vers PDF
            # Note: Cette partie nécessite une implémentation spécifique selon la bibliothèque choisie
            # Exemple avec win32com (Windows uniquement):
            self._convert_excel_to_pdf(excel_file, pdf_file)
            return pdf_file
        except Exception as e:
            print(f"Erreur lors de la conversion en PDF: {str(e)}")
            # Retourner le fichier Excel si la conversion échoue
            return excel_file
    
    def _convert_excel_to_pdf(self, excel_file, pdf_file):
        """
        Convertit un fichier Excel en PDF.
        
        Args:
            excel_file (str): Chemin vers le fichier Excel
            pdf_file (str): Chemin vers le fichier PDF à générer
        """
        try:
            # Méthode utilisant win32com (Windows uniquement)
            import win32com.client
            excel = win32com.client.Dispatch("Excel.Application")
            excel.Visible = False
            
            wb = excel.Workbooks.Open(os.path.abspath(excel_file))
            ws = wb.Worksheets[0]
            
            try:
                wb.SaveAs(os.path.abspath(pdf_file), FileFormat=57)  # 57 est le code pour PDF
            except Exception as e:
                print(f"Erreur lors de l'enregistrement en PDF: {str(e)}")
            finally:
                wb.Close()
                excel.Quit()
        except ImportError:
            print("La bibliothèque win32com n'est pas disponible. Conversion en PDF impossible.")
            raise
    
    def _fill_invoice_header(self, worksheet, invoice_data):
        """Remplit l'en-tête de la facture avec les informations de base"""
        # Ces cellules doivent être adaptées en fonction de la structure du modèle Excel
        # Numéro de facture
        worksheet['C5'] = invoice_data['numero']
        
        # Date de facture
        worksheet['C6'] = invoice_data['date']
        
        # Autres informations d'en-tête selon le modèle
        if 'objet' in invoice_data:
            worksheet['C7'] = invoice_data['objet']
        
        if 'bon_commande' in invoice_data:
            worksheet['C8'] = invoice_data['bon_commande']
            
        if 'marche' in invoice_data:
            worksheet['C9'] = invoice_data['marche']
    
    def _fill_client_info(self, worksheet, client_data):
        """Remplit les informations du client"""
        # Ces cellules doivent être adaptées en fonction de la structure du modèle Excel
        worksheet['G5'] = client_data.get('nom', '')
        worksheet['G6'] = client_data.get('adresse', '')
        
        if 'ice' in client_data:
            worksheet['G7'] = f"ICE: {client_data['ice']}"
    
    def _fill_invoice_lines(self, worksheet, lines_data):
        """Remplit les lignes de la facture avec les articles"""
        # Ligne de départ pour les articles (à adapter selon le modèle)
        start_row = 12
        
        for i, line in enumerate(lines_data):
            row = start_row + i
            
            # Numéro de ligne
            worksheet[f'A{row}'] = i + 1
            
            # Désignation
            worksheet[f'B{row}'] = line['designation']
            
            # Unité
            worksheet[f'C{row}'] = line.get('unite', '')
            
            # Quantité
            worksheet[f'D{row}'] = line['quantite']
            
            # Prix unitaire HT
            worksheet[f'E{row}'] = line['prix_unitaire']
            
            # Total HT
            worksheet[f'F{row}'] = line['quantite'] * line['prix_unitaire']
            
            # Copier le style des cellules du modèle si nécessaire
            self._copy_cell_style(worksheet, f'A{start_row}', f'A{row}')
            self._copy_cell_style(worksheet, f'B{start_row}', f'B{row}')
            self._copy_cell_style(worksheet, f'C{start_row}', f'C{row}')
            self._copy_cell_style(worksheet, f'D{start_row}', f'D{row}')
            self._copy_cell_style(worksheet, f'E{start_row}', f'E{row}')
            self._copy_cell_style(worksheet, f'F{start_row}', f'F{row}')
    
    def _fill_totals(self, worksheet, totals_data):
        """Remplit les totaux de la facture"""
        # Ces cellules doivent être adaptées en fonction de la structure du modèle Excel
        # Ligne pour les totaux (à adapter selon le modèle)
        total_row = 25
        
        # Total HT
        worksheet[f'F{total_row}'] = totals_data['total_ht']
        
        # TVA
        worksheet[f'F{total_row+1}'] = totals_data['total_tva']
        
        # Total TTC
        worksheet[f'F{total_row+2}'] = totals_data['total_ttc']
    
    def _copy_cell_style(self, worksheet, source_cell, target_cell):
        """Copie le style d'une cellule source vers une cellule cible"""
        if isinstance(source_cell, str):
            source_cell = worksheet[source_cell]
        if isinstance(target_cell, str):
            target_cell = worksheet[target_cell]
            
        # Copier la police
        if source_cell.font:
            target_cell.font = copy(source_cell.font)
            
        # Copier l'alignement
        if source_cell.alignment:
            target_cell.alignment = copy(source_cell.alignment)
            
        # Copier les bordures
        if source_cell.border:
            target_cell.border = copy(source_cell.border)
            
        # Copier le format de nombre
        if source_cell.number_format:
            target_cell.number_format = source_cell.number_format
