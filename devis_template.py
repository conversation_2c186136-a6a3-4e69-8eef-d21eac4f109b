#!/usr/bin/env python3
"""
إنشاء نموذج عرض أسعار (devis) احترافي في Excel
مع تصميم منسجم وألوان هادئة ووظائف ديناميكية
"""

import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.datavalidation import DataValidation
import os

class DevisTemplate:
    def __init__(self):
        self.wb = openpyxl.Workbook()
        self.ws = self.wb.active
        self.ws.title = "Devis"
        
        # ألوان منسجمة وهادئة
        self.colors = {
            'header': 'E8F4FD',      # أزرق فاتح جداً
            'subheader': 'F0F8FF',   # أزرق شاحب
            'accent': '4A90E2',      # أزرق متوسط
            'text_dark': '2C3E50',   # رمادي داكن
            'border': 'BDC3C7',     # رمادي فاتح
            'success': 'D5EDDA',     # أخضر فاتح
            'warning': 'FFF3CD',     # أصفر فاتح
            'white': 'FFFFFF'
        }
        
        # خطوط متناسقة
        self.fonts = {
            'title': Font(name='Calibri', size=16, bold=True, color='1E3A8A'),
            'header': Font(name='Calibri', size=12, bold=True, color='2C3E50'),
            'normal': Font(name='Calibri', size=11, color='2C3E50'),
            'small': Font(name='Calibri', size=10, color='5A6C7D'),
            'italic': Font(name='Calibri', size=10, italic=True, color='7F8C8D')
        }
        
        # حدود
        self.borders = {
            'thin': Border(
                left=Side(style='thin', color='BDC3C7'),
                right=Side(style='thin', color='BDC3C7'),
                top=Side(style='thin', color='BDC3C7'),
                bottom=Side(style='thin', color='BDC3C7')
            ),
            'thick': Border(
                left=Side(style='medium', color='4A90E2'),
                right=Side(style='medium', color='4A90E2'),
                top=Side(style='medium', color='4A90E2'),
                bottom=Side(style='medium', color='4A90E2')
            )
        }

    def create_devis_template(self):
        """إنشاء نموذج عرض الأسعار الكامل"""
        
        # 1. إعداد عرض الأعمدة
        self.setup_column_widths()
        
        # 2. إنشاء العنوان الرئيسي
        self.create_header()
        
        # 3. قسم معلومات العميل
        self.create_client_section()
        
        # 4. جدول المنتجات
        self.create_products_table()
        
        # 5. الحسابات النهائية
        self.create_totals_section()
        
        # 6. الأزرار والتحكم
        self.create_control_buttons()
        
        # 7. إضافة التحقق من البيانات
        self.add_data_validation()
        
        # 8. إخفاء الخطوط الشبكية
        self.ws.sheet_view.showGridLines = False
        
        return self.wb

    def setup_column_widths(self):
        """إعداد عرض الأعمدة"""
        column_widths = {
            'A': 3,   # مساحة فارغة
            'B': 15,  # التسميات
            'C': 20,  # القيم الأساسية
            'D': 15,  # قيم إضافية
            'E': 12,  # أرقام
            'F': 15,  # حسابات
            'G': 15,  # حسابات
            'H': 18,  # المجاميع
            'I': 3,   # مساحة فارغة
            'J': 25   # ملاحظات خارج الطباعة
        }
        
        for col, width in column_widths.items():
            self.ws.column_dimensions[col].width = width

    def create_header(self):
        """إنشاء العنوان الرئيسي"""
        # دمج الخلايا للعنوان
        self.ws.merge_cells('B2:H4')
        header_cell = self.ws['B2']
        header_cell.value = "عرض أسعار / DEVIS"
        header_cell.font = self.fonts['title']
        header_cell.alignment = Alignment(horizontal='center', vertical='center')
        header_cell.fill = PatternFill(start_color=self.colors['header'], 
                                     end_color=self.colors['header'], 
                                     fill_type='solid')
        header_cell.border = self.borders['thick']
        
        # رقم العرض والتاريخ
        self.ws['B6'] = "رقم العرض:"
        self.ws['C6'] = "DEV-2024-001"
        self.ws['F6'] = "التاريخ:"
        self.ws['G6'] = "=TODAY()"
        
        # تنسيق الصف
        for cell in ['B6', 'C6', 'F6', 'G6']:
            self.ws[cell].font = self.fonts['normal']
            self.ws[cell].border = self.borders['thin']

    def create_client_section(self):
        """إنشاء قسم معلومات العميل"""
        start_row = 8
        
        # عنوان القسم
        self.ws.merge_cells(f'B{start_row}:H{start_row}')
        section_title = self.ws[f'B{start_row}']
        section_title.value = "معلومات العميل"
        section_title.font = self.fonts['header']
        section_title.fill = PatternFill(start_color=self.colors['subheader'], 
                                       end_color=self.colors['subheader'], 
                                       fill_type='solid')
        section_title.border = self.borders['thin']
        section_title.alignment = Alignment(horizontal='center')
        
        # الحقول
        fields = [
            ("نوع العميل", "type_client", "J9"),
            ("طبيعة الخدمة", "nature_prestation", "J10"), 
            ("العميل", "client", "J11"),
            ("العنوان", "adresse", "J12"),
            ("ICE", "ice", "J13"),
            ("الموضوع", "objet", "")
        ]
        
        row = start_row + 1
        for label, field_name, note_cell in fields:
            self.ws[f'B{row}'] = label
            self.ws[f'B{row}'].font = self.fonts['normal']
            
            # خلية الإدخال
            input_cell = self.ws[f'C{row}']
            if field_name in ['type_client', 'nature_prestation', 'client']:
                input_cell.value = f"=DROPDOWN_{field_name.upper()}"
            else:
                input_cell.value = ""
            
            input_cell.border = self.borders['thin']
            input_cell.fill = PatternFill(start_color=self.colors['white'], 
                                        end_color=self.colors['white'], 
                                        fill_type='solid')
            
            # ملاحظة خارج الطباعة
            if note_cell:
                note = self.ws[note_cell]
                if field_name == 'type_client':
                    note.value = "اختيار Privé/Public"
                elif field_name == 'nature_prestation':
                    note.value = "لا يظهر في الطباعة إذا كان Public"
                elif field_name == 'client':
                    note.value = "اختيار العميل"
                elif field_name == 'adresse':
                    note.value = "يظهر تلقائياً إذا كان Privé - خارج الطباعة"
                elif field_name == 'ice':
                    note.value = "يظهر تلقائياً إذا كان Privé - خارج الطباعة"
                
                note.font = self.fonts['small']
                note.fill = PatternFill(start_color=self.colors['warning'], 
                                      end_color=self.colors['warning'], 
                                      fill_type='solid')
            
            row += 1

    def create_products_table(self):
        """إنشاء جدول المنتجات"""
        start_row = 16
        
        # عنوان الجدول
        self.ws.merge_cells(f'B{start_row}:H{start_row}')
        table_title = self.ws[f'B{start_row}']
        table_title.value = "تفاصيل المنتجات والخدمات"
        table_title.font = self.fonts['header']
        table_title.fill = PatternFill(start_color=self.colors['subheader'], 
                                     end_color=self.colors['subheader'], 
                                     fill_type='solid')
        table_title.border = self.borders['thin']
        table_title.alignment = Alignment(horizontal='center')
        
        # رؤوس الأعمدة
        headers = ["N°", "المنتج/الخدمة", "الوحدة", "الكمية", "سعر الشراء HT", "نسبة الربح", "سعر البيع HT", "المجموع HT"]
        header_row = start_row + 1
        
        for i, header in enumerate(headers):
            col = chr(ord('B') + i)
            cell = self.ws[f'{col}{header_row}']
            cell.value = header
            cell.font = self.fonts['header']
            cell.fill = PatternFill(start_color=self.colors['accent'], 
                                  end_color=self.colors['accent'], 
                                  fill_type='solid')
            cell.border = self.borders['thin']
            cell.alignment = Alignment(horizontal='center')
        
        # صفوف البيانات (10 صفوف)
        for row_num in range(header_row + 1, header_row + 11):
            for col_num in range(8):
                col = chr(ord('B') + col_num)
                cell = self.ws[f'{col}{row_num}']
                
                if col_num == 0:  # رقم تسلسلي
                    cell.value = f"=ROW()-{header_row}"
                elif col_num == 1:  # المنتج
                    cell.value = "اختيار المنتج"
                elif col_num == 2:  # الوحدة
                    cell.value = "تلقائي"
                elif col_num == 3:  # الكمية
                    cell.value = 1
                elif col_num == 4:  # سعر الشراء
                    cell.value = "تلقائي"
                elif col_num == 5:  # نسبة الربح
                    cell.value = 1.2
                elif col_num == 6:  # سعر البيع
                    cell.value = f"={chr(ord('B') + 4)}{row_num}*{chr(ord('B') + 5)}{row_num}"
                elif col_num == 7:  # المجموع
                    cell.value = f"={chr(ord('B') + 3)}{row_num}*{chr(ord('B') + 6)}{row_num}"
                
                cell.border = self.borders['thin']
                cell.alignment = Alignment(horizontal='center')
                
                # تلوين متناوب للصفوف
                if row_num % 2 == 0:
                    cell.fill = PatternFill(start_color='F8F9FA', 
                                          end_color='F8F9FA', 
                                          fill_type='solid')

    def create_totals_section(self):
        """إنشاء قسم الحسابات النهائية"""
        start_row = 28
        
        # المجموع الفرعي
        self.ws[f'G{start_row}'] = "المجموع HT:"
        self.ws[f'H{start_row}'] = f"=SUM(I18:I27)"
        
        # الضريبة
        self.ws[f'G{start_row + 1}'] = "TVA 20%:"
        self.ws[f'H{start_row + 1}'] = f"=H{start_row}*0.2"
        
        # المجموع النهائي
        self.ws[f'G{start_row + 2}'] = "المجموع TTC:"
        self.ws[f'H{start_row + 2}'] = f"=H{start_row}+H{start_row + 1}"
        
        # تنسيق خلايا المجاميع
        for row in range(start_row, start_row + 3):
            self.ws[f'G{row}'].font = self.fonts['header']
            self.ws[f'H{row}'].font = self.fonts['header']
            self.ws[f'G{row}'].border = self.borders['thin']
            self.ws[f'H{row}'].border = self.borders['thin']
            
            if row == start_row + 2:  # المجموع النهائي
                self.ws[f'G{row}'].fill = PatternFill(start_color=self.colors['success'], 
                                                    end_color=self.colors['success'], 
                                                    fill_type='solid')
                self.ws[f'H{row}'].fill = PatternFill(start_color=self.colors['success'], 
                                                    end_color=self.colors['success'], 
                                                    fill_type='solid')
        
        # المبلغ بالأحرف
        self.ws[f'B{start_row + 4}'] = "المبلغ بالأحرف:"
        self.ws.merge_cells(f'C{start_row + 4}:H{start_row + 4}')
        amount_words = self.ws[f'C{start_row + 4}']
        amount_words.value = f"=NUMBERTEXT(H{start_row + 2}) & \" درهم\""
        amount_words.font = self.fonts['italic']
        amount_words.border = self.borders['thin']
        
        # النص الختامي
        self.ws[f'B{start_row + 6}'] = "Arrêté le présent devis à la somme de :"
        self.ws.merge_cells(f'C{start_row + 6}:H{start_row + 6}')
        final_text = self.ws[f'C{start_row + 6}']
        final_text.value = f"=H{start_row + 2} & \" DH TTC\""
        final_text.font = self.fonts['italic']

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        button_row = 38
        
        buttons = [
            ("Valider", "B", self.colors['success']),
            ("Supprimer", "D", self.colors['warning']),
            ("Imprimer", "F", self.colors['accent'])
        ]
        
        for button_text, col, color in buttons:
            cell = self.ws[f'{col}{button_row}']
            cell.value = button_text
            cell.font = Font(name='Calibri', size=11, bold=True, color='FFFFFF')
            cell.fill = PatternFill(start_color=color, end_color=color, fill_type='solid')
            cell.border = self.borders['thin']
            cell.alignment = Alignment(horizontal='center', vertical='center')

    def add_data_validation(self):
        """إضافة التحقق من البيانات والقوائم المنسدلة"""
        
        # قائمة نوع العميل
        client_type_validation = DataValidation(type="list", formula1='"Privé,Public"')
        self.ws.add_data_validation(client_type_validation)
        client_type_validation.add('C9')
        
        # قائمة طبيعة الخدمة
        service_type_validation = DataValidation(type="list", formula1='"Travaux,Fourniture"')
        self.ws.add_data_validation(service_type_validation)
        service_type_validation.add('C10')
        
        # قائمة العملاء (مثال)
        clients_validation = DataValidation(type="list", formula1='"Client A,Client B,Client C,إدخال يدوي"')
        self.ws.add_data_validation(clients_validation)
        clients_validation.add('C11')

    def save_template(self, filename="devis_template.xlsx"):
        """حفظ النموذج"""
        try:
            self.wb.save(filename)
            print(f"✅ تم إنشاء نموذج عرض الأسعار: {filename}")
            return True
        except Exception as e:
            print(f"❌ خطأ في حفظ الملف: {e}")
            return False

def create_devis_template():
    """إنشاء نموذج عرض الأسعار"""
    template = DevisTemplate()
    workbook = template.create_devis_template()
    template.save_template("devis_professionnel.xlsx")
    return workbook

if __name__ == "__main__":
    create_devis_template()
    print("🎉 تم إنشاء نموذج عرض الأسعار الاحترافي بنجاح!")
