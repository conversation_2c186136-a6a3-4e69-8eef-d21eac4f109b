from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import mm, cm
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
import os
import datetime

class PDFGenerator:
    def __init__(self, logo_path=None):
        self.logo_path = logo_path

        # Styles pour le PDF
        self.styles = getSampleStyleSheet()

        # Style centré
        self.styles.add(ParagraphStyle(
            name='Center',
            parent=self.styles['Normal'],
            alignment=TA_CENTER,
        ))

        # Style gras
        self.styles.add(ParagraphStyle(
            name='Bold',
            parent=self.styles['Normal'],
            fontName='Helvetica-Bold',
        ))

        # Style titre
        self.styles.add(ParagraphStyle(
            name='Title',
            parent=self.styles['Heading1'],
            alignment=TA_CENTER,
            fontSize=18,
            spaceAfter=6,
        ))

        # Style sous-titre
        self.styles.add(ParagraphStyle(
            name='Subtitle',
            parent=self.styles['Heading2'],
            fontSize=14,
            alignment=TA_CENTER,
        ))

        # Style aligné à droite
        self.styles.add(ParagraphStyle(
            name='Right',
            parent=self.styles['Normal'],
            alignment=TA_RIGHT,
        ))

        # Style pour les informations de l'entreprise
        self.styles.add(ParagraphStyle(
            name='Company',
            parent=self.styles['Normal'],
            fontSize=9,
            alignment=TA_LEFT,
            textColor=colors.darkblue,
        ))

        # Style pour les mentions légales
        self.styles.add(ParagraphStyle(
            name='Legal',
            parent=self.styles['Normal'],
            fontSize=8,
            alignment=TA_CENTER,
            textColor=colors.gray,
        ))

    def set_logo(self, logo_path):
        """Définit le chemin du logo de l'entreprise"""
        self.logo_path = logo_path

    def generate_facture_pdf(self, facture_data, output_path, company_info=None):
        """
        Génère un PDF de facture à partir des données fournies

        Args:
            facture_data (dict): Données de la facture
            output_path (str): Chemin de sortie du fichier PDF
            company_info (dict): Informations sur l'entreprise
        """
        # Créer le dossier de sortie s'il n'existe pas
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Informations par défaut de l'entreprise si non fournies
        if company_info is None:
            company_info = {
                'name': 'Votre Entreprise',
                'address': '123 Rue Principale, 12345 Ville',
                'phone': '+212 5XX-XXXXXX',
                'email': '<EMAIL>',
                'website': 'www.entreprise.com',
                'ice': 'ICE: 000000000000000',
                'if': 'IF: 00000000',
            }

        # Créer le document
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=15*mm,
            leftMargin=15*mm,
            topMargin=15*mm,
            bottomMargin=20*mm
        )

        # Contenu du document
        content = []

        # En-tête avec logo et informations de l'entreprise
        header_data = [['', '']]

        # Logo de l'entreprise
        logo_cell = []
        if self.logo_path and os.path.exists(self.logo_path):
            logo = Image(self.logo_path)
            logo.drawHeight = 2.5*cm
            logo.drawWidth = 5*cm
            logo_cell.append(logo)
        else:
            logo_cell.append(Paragraph(company_info['name'], self.styles['Bold']))

        # Informations de l'entreprise
        company_cell = []
        company_cell.append(Paragraph(company_info['name'], self.styles['Bold']))
        company_cell.append(Paragraph(company_info['address'], self.styles['Company']))
        company_cell.append(Paragraph(f"Tél: {company_info['phone']}", self.styles['Company']))
        company_cell.append(Paragraph(f"Email: {company_info['email']}", self.styles['Company']))
        company_cell.append(Paragraph(company_info['ice'], self.styles['Company']))
        company_cell.append(Paragraph(company_info['if'], self.styles['Company']))

        header_data[0][0] = logo_cell
        header_data[0][1] = company_cell

        header_table = Table(header_data, colWidths=[doc.width/2.0, doc.width/2.0])
        header_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('ALIGN', (0, 0), (0, 0), 'LEFT'),
            ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
        ]))

        content.append(header_table)
        content.append(Spacer(1, 10*mm))

        # Titre
        content.append(Paragraph("FACTURE", self.styles['Title']))

        # Informations de la facture et du client en deux colonnes
        info_data = [['', '']]

        # Informations de la facture
        facture_cell = []
        facture_cell.append(Paragraph(f"<b>N° Facture:</b> {facture_data['numero']}", self.styles['Normal']))
        facture_cell.append(Paragraph(f"<b>Date:</b> {facture_data['date']}", self.styles['Normal']))
        facture_cell.append(Paragraph(f"<b>Date d'échéance:</b> {facture_data['date_echeance']}", self.styles['Normal']))

        # Informations du client
        client_cell = []
        client_cell.append(Paragraph("<b>Client:</b>", self.styles['Bold']))
        client_cell.append(Paragraph(facture_data['client_nom'], self.styles['Normal']))

        if facture_data.get('client_adresse'):
            client_cell.append(Paragraph(facture_data['client_adresse'], self.styles['Normal']))

        if facture_data.get('client_ice'):
            client_cell.append(Paragraph(f"ICE: {facture_data['client_ice']}", self.styles['Normal']))

        if facture_data.get('client_if'):
            client_cell.append(Paragraph(f"IF: {facture_data['client_if']}", self.styles['Normal']))

        info_data[0][0] = facture_cell
        info_data[0][1] = client_cell

        info_table = Table(info_data, colWidths=[doc.width/2.0, doc.width/2.0])
        info_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('ALIGN', (0, 0), (0, 0), 'LEFT'),
            ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
        ]))

        content.append(info_table)
        content.append(Spacer(1, 10*mm))

        # Tableau des lignes de facture
        table_data = [
            ["Désignation", "Quantité", "Prix unitaire HT", "TVA", "Total HT"]
        ]

        for ligne in facture_data['lignes']:
            table_data.append([
                ligne['designation'],
                str(ligne['quantite']),
                f"{ligne['prix_unitaire']:.2f} DH",
                f"{ligne['taux_tva']:.1f} %",
                f"{ligne['total_ht']:.2f} DH"
            ])

        # Style du tableau
        table_style = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('ALIGN', (0, 1), (0, -1), 'LEFT'),
            ('ALIGN', (1, 1), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.lightgrey),
            ('BOX', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.whitesmoke]),
        ])

        table = Table(table_data, colWidths=[doc.width*0.40, doc.width*0.10, doc.width*0.20, doc.width*0.10, doc.width*0.20])
        table.setStyle(table_style)
        content.append(table)
        content.append(Spacer(1, 10*mm))

        # Totaux alignés à droite
        totals_data = [
            ['', f"<b>Total HT:</b> {facture_data['total_ht']:.2f} DH"],
            ['', f"<b>Total TVA:</b> {facture_data['total_tva']:.2f} DH"],
            ['', f"<b>Total TTC:</b> {facture_data['total_ttc']:.2f} DH"],
        ]

        totals_table = Table(totals_data, colWidths=[doc.width*0.7, doc.width*0.3])
        totals_table.setStyle(TableStyle([
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
            ('LINEABOVE', (1, 2), (1, 2), 1, colors.black),
            ('FONTNAME', (1, 2), (1, 2), 'Helvetica-Bold'),
        ]))

        content.append(totals_table)
        content.append(Spacer(1, 15*mm))

        # Mentions légales et conditions de paiement
        content.append(Paragraph("Conditions de paiement : Paiement à réception de facture.", self.styles['Normal']))
        content.append(Spacer(1, 5*mm))
        content.append(Paragraph("En cas de retard de paiement, une pénalité de 3% sera appliquée.", self.styles['Normal']))
        content.append(Spacer(1, 20*mm))

        # Pied de page
        footer_text = (
            f"Document généré le {datetime.datetime.now().strftime('%d/%m/%Y à %H:%M')} - "
            f"{company_info['name']} - {company_info['ice']} - {company_info['if']}"
        )
        content.append(Paragraph(footer_text, self.styles['Legal']))

        # Générer le PDF
        doc.build(content)

        return output_path

    def generate_bon_commande_pdf(self, commande_data, output_path, company_info=None, type_doc="commande"):
        """
        Génère un PDF de bon de commande ou de livraison à partir des données fournies

        Args:
            commande_data (dict): Données de la commande
            output_path (str): Chemin de sortie du fichier PDF
            company_info (dict): Informations sur l'entreprise
            type_doc (str): Type de document ("commande" ou "livraison")
        """
        # Créer le dossier de sortie s'il n'existe pas
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Informations par défaut de l'entreprise si non fournies
        if company_info is None:
            company_info = {
                'name': 'Votre Entreprise',
                'address': '123 Rue Principale, 12345 Ville',
                'phone': '+212 5XX-XXXXXX',
                'email': '<EMAIL>',
                'website': 'www.entreprise.com',
                'ice': 'ICE: 000000000000000',
                'if': 'IF: 00000000',
            }

        # Créer le document
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=15*mm,
            leftMargin=15*mm,
            topMargin=15*mm,
            bottomMargin=20*mm
        )

        # Contenu du document
        content = []

        # En-tête avec logo et informations de l'entreprise
        header_data = [['', '']]

        # Logo de l'entreprise
        logo_cell = []
        if self.logo_path and os.path.exists(self.logo_path):
            logo = Image(self.logo_path)
            logo.drawHeight = 2.5*cm
            logo.drawWidth = 5*cm
            logo_cell.append(logo)
        else:
            logo_cell.append(Paragraph(company_info['name'], self.styles['Bold']))

        # Informations de l'entreprise
        company_cell = []
        company_cell.append(Paragraph(company_info['name'], self.styles['Bold']))
        company_cell.append(Paragraph(company_info['address'], self.styles['Company']))
        company_cell.append(Paragraph(f"Tél: {company_info['phone']}", self.styles['Company']))
        company_cell.append(Paragraph(f"Email: {company_info['email']}", self.styles['Company']))
        company_cell.append(Paragraph(company_info['ice'], self.styles['Company']))
        company_cell.append(Paragraph(company_info['if'], self.styles['Company']))

        header_data[0][0] = logo_cell
        header_data[0][1] = company_cell

        header_table = Table(header_data, colWidths=[doc.width/2.0, doc.width/2.0])
        header_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('ALIGN', (0, 0), (0, 0), 'LEFT'),
            ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
        ]))

        content.append(header_table)
        content.append(Spacer(1, 10*mm))

        # Titre
        if type_doc == "commande":
            title = "BON DE COMMANDE"
            destinataire_label = "Fournisseur:"
            destinataire_nom = commande_data.get('fournisseur_nom', '')
        else:
            title = "BON DE LIVRAISON"
            destinataire_label = "Client:"
            destinataire_nom = commande_data.get('client_nom', '')

        content.append(Paragraph(title, self.styles['Title']))

        # Informations de la commande et du destinataire en deux colonnes
        info_data = [['', '']]

        # Informations de la commande
        commande_cell = []
        commande_cell.append(Paragraph(f"<b>N° {title.lower()}:</b> {commande_data['numero']}", self.styles['Normal']))
        commande_cell.append(Paragraph(f"<b>Date:</b> {commande_data['date']}", self.styles['Normal']))

        if type_doc == "commande":
            commande_cell.append(Paragraph(f"<b>Date de livraison prévue:</b> {commande_data.get('date_livraison', '')}", self.styles['Normal']))

        # Informations du destinataire
        destinataire_cell = []
        destinataire_cell.append(Paragraph(f"<b>{destinataire_label}</b>", self.styles['Bold']))
        destinataire_cell.append(Paragraph(destinataire_nom, self.styles['Normal']))

        if commande_data.get('destinataire_adresse'):
            destinataire_cell.append(Paragraph(commande_data['destinataire_adresse'], self.styles['Normal']))

        if commande_data.get('destinataire_ice'):
            destinataire_cell.append(Paragraph(f"ICE: {commande_data['destinataire_ice']}", self.styles['Normal']))

        if commande_data.get('destinataire_if'):
            destinataire_cell.append(Paragraph(f"IF: {commande_data['destinataire_if']}", self.styles['Normal']))

        info_data[0][0] = commande_cell
        info_data[0][1] = destinataire_cell

        info_table = Table(info_data, colWidths=[doc.width/2.0, doc.width/2.0])
        info_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('ALIGN', (0, 0), (0, 0), 'LEFT'),
            ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
        ]))

        content.append(info_table)
        content.append(Spacer(1, 10*mm))

        # Tableau des lignes de commande
        table_data = [
            ["Désignation", "Quantité", "Prix unitaire HT", "TVA", "Total HT"]
        ]

        for ligne in commande_data['lignes']:
            table_data.append([
                ligne['designation'],
                str(ligne['quantite']),
                f"{ligne['prix_unitaire']:.2f} DH",
                f"{ligne['taux_tva']:.1f} %",
                f"{ligne['total_ht']:.2f} DH"
            ])

        # Style du tableau
        table_style = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('ALIGN', (0, 1), (0, -1), 'LEFT'),
            ('ALIGN', (1, 1), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.lightgrey),
            ('BOX', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.whitesmoke]),
        ])

        table = Table(table_data, colWidths=[doc.width*0.40, doc.width*0.10, doc.width*0.20, doc.width*0.10, doc.width*0.20])
        table.setStyle(table_style)
        content.append(table)
        content.append(Spacer(1, 10*mm))

        # Totaux alignés à droite
        totals_data = [
            ['', f"<b>Total HT:</b> {commande_data['total_ht']:.2f} DH"],
            ['', f"<b>Total TVA:</b> {commande_data['total_tva']:.2f} DH"],
            ['', f"<b>Total TTC:</b> {commande_data['total_ttc']:.2f} DH"],
        ]

        totals_table = Table(totals_data, colWidths=[doc.width*0.7, doc.width*0.3])
        totals_table.setStyle(TableStyle([
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
            ('LINEABOVE', (1, 2), (1, 2), 1, colors.black),
            ('FONTNAME', (1, 2), (1, 2), 'Helvetica-Bold'),
        ]))

        content.append(totals_table)
        content.append(Spacer(1, 15*mm))

        # Zone de signature
        if type_doc == "livraison":
            signature_data = [
                ['Signature et cachet de l\'entreprise', 'Signature du client'],
                ['', ''],
                ['', ''],
                ['', ''],
            ]

            signature_table = Table(signature_data, colWidths=[doc.width/2.0, doc.width/2.0])
            signature_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('LINEBELOW', (0, 2), (0, 2), 1, colors.black),
                ('LINEBELOW', (1, 2), (1, 2), 1, colors.black),
                ('TOPPADDING', (0, 1), (-1, -1), 30),
            ]))

            content.append(signature_table)

        # Pied de page
        footer_text = (
            f"Document généré le {datetime.datetime.now().strftime('%d/%m/%Y à %H:%M')} - "
            f"{company_info['name']} - {company_info['ice']} - {company_info['if']}"
        )
        content.append(Paragraph(footer_text, self.styles['Legal']))

        # Générer le PDF
        doc.build(content)

        return output_path
