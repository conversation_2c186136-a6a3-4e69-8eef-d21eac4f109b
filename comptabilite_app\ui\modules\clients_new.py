from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QHeaderView, QFrame, QMessageBox, QSpacerItem, QSizePolicy)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon, QFont
import sqlite3

# Importer les styles modernes et les icônes
from ..theme import COLORS, BORDER_RADIUS, SPACING, SHADOWS
from ..icons.icons import *

# Importer le dialogue client
from ..components.client_dialog import ClientDialog

class ClientsModule(QWidget):
    def __init__(self, db_manager, signals=None):
        super().__init__()
        self.db_manager = db_manager
        self.signals = signals  # Signaux pour la communication entre modules
        self.setup_ui()
        self.load_clients()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(int(SPACING['lg'].replace('px', '')))
        layout.setContentsMargins(int(SPACING['md'].replace('px', '')),
                                 int(SPACING['md'].replace('px', '')),
                                 int(SPACING['md'].replace('px', '')),
                                 int(SPACING['md'].replace('px', '')))

        # Conteneur principal (carte)
        main_container = QFrame()
        main_container.setObjectName("card")
        main_container.setStyleSheet(f"""
            #card {{
                background-color: {COLORS['surface']};
                border-radius: {BORDER_RADIUS['md']};
                border: 1px solid {COLORS['divider']};
                padding: {SPACING['md']};
            }}
        """)
        main_layout = QVBoxLayout(main_container)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # En-tête avec titre et bouton d'ajout (plus proéminent)
        header_container = QWidget()
        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(0, 0, 0, 0)

        # Icône et titre
        title_container = QWidget()
        title_layout = QHBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(10)

        # Icône
        icon_label = QLabel()
        icon_label.setText(svg_to_icon_html(CLIENTS_ICON, COLORS['primary'], 32))
        icon_label.setFixedSize(32, 32)
        title_layout.addWidget(icon_label)

        # Titre
        title_label = QLabel("Liste des Clients")
        title_label.setObjectName("page_title")
        title_label.setStyleSheet(f"""
            font-size: 22px;
            font-weight: bold;
            color: {COLORS['primary']};
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        header_layout.addWidget(title_container)
        header_layout.addStretch()

        # Bouton pour ajouter un client (plus grand et plus visible)
        self.add_client_btn = QPushButton("+ Ajouter un client")
        self.add_client_btn.setObjectName("primary_button")
        self.add_client_btn.setFixedHeight(44)
        self.add_client_btn.setMinimumWidth(180)
        self.add_client_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLORS['primary']};
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: {BORDER_RADIUS['md']};
                font-weight: bold;
                font-size: 15px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }}
            QPushButton:hover {{
                background-color: {COLORS['primary_light']};
            }}
            QPushButton:pressed {{
                background-color: {COLORS['primary_dark']};
            }}
        """)
        self.add_client_btn.clicked.connect(self.show_add_client_dialog)
        header_layout.addWidget(self.add_client_btn)

        main_layout.addWidget(header_container)

        # Barre de recherche
        search_container = QFrame()
        search_container.setObjectName("search_container")
        search_container.setStyleSheet(f"""
            #search_container {{
                background-color: {COLORS['surface']};
                border-radius: {BORDER_RADIUS['md']};
                border: 1px solid {COLORS['divider']};
                padding: 8px;
            }}
        """)
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(10, 10, 10, 10)

        search_icon = QLabel()
        search_icon.setText(svg_to_icon_html(SEARCH_ICON, COLORS['text_secondary'], 20))
        search_icon.setFixedSize(20, 20)
        search_layout.addWidget(search_icon)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Rechercher un client...")
        self.search_input.setStyleSheet("""
            border: none;
            font-size: 14px;
            padding: 5px;
        """)
        self.search_input.textChanged.connect(self.filter_clients)
        search_layout.addWidget(self.search_input)

        main_layout.addWidget(search_container)

        # Tableau des clients
        table_container = QFrame()
        table_container.setObjectName("table_container")
        table_container.setStyleSheet(f"""
            #table_container {{
                background-color: {COLORS['surface']};
                border-radius: {BORDER_RADIUS['md']};
                border: 1px solid {COLORS['divider']};
            }}
        """)
        table_layout = QVBoxLayout(table_container)
        table_layout.setContentsMargins(0, 0, 0, 0)

        # Créer le tableau
        self.clients_table = QTableWidget(0, 6)  # Supprimer la colonne Actions
        self.clients_table.setHorizontalHeaderLabels([
            "Code", "Nom", "ICE", "IF", "Téléphone", "Adresse"
        ])

        # Appliquer les styles du tableau
        self.clients_table.setStyleSheet(f"""
            QTableWidget {{
                border: none;
                gridline-color: {COLORS['divider']};
            }}
            QHeaderView::section {{
                background-color: {COLORS['hover_bg']};
                color: {COLORS['text_primary']};
                padding: 12px;
                border: none;
                border-bottom: 1px solid {COLORS['divider']};
                font-weight: bold;
            }}
            QTableWidget::item {{
                padding: 12px;
                border-bottom: 1px solid {COLORS['divider']};
            }}
        """)
        self.clients_table.setAlternatingRowColors(True)
        table_layout.addWidget(self.clients_table)

        # Configuration des colonnes
        self.clients_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Code
        self.clients_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)  # Nom
        self.clients_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)  # ICE
        self.clients_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # IF
        self.clients_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Téléphone
        self.clients_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Stretch)  # Adresse
        self.clients_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Actions

        self.clients_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.clients_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.clients_table.verticalHeader().setVisible(False)  # Cacher les numéros de ligne

        # Connecter le signal de double-clic pour ouvrir la boîte de dialogue d'édition
        self.clients_table.itemDoubleClicked.connect(self.on_item_double_clicked)

        # Activer le menu contextuel (clic droit)
        self.clients_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.clients_table.customContextMenuRequested.connect(self.show_context_menu)

        # Ajouter le tableau au conteneur
        main_layout.addWidget(table_container)

        # Ajouter le conteneur principal au layout
        layout.addWidget(main_container)

    def show_context_menu(self, position):
        """Affiche le menu contextuel au clic droit"""
        from PySide6.QtWidgets import QMenu
        from PySide6.QtGui import QAction

        # Vérifier qu'une ligne est sélectionnée
        item = self.clients_table.itemAt(position)
        if not item:
            return

        row = item.row()

        # Récupérer les données de la ligne
        code_item = self.clients_table.item(row, 0)
        nom_item = self.clients_table.item(row, 1)

        if not code_item or not nom_item:
            return

        client_code = code_item.text()
        client_nom = nom_item.text()
        client_id = code_item.data(Qt.UserRole)

        if not client_id:
            return

        # Créer le menu contextuel
        menu = QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                padding: 4px;
            }
            QMenu::item {
                padding: 8px 16px;
                border-radius: 4px;
                margin: 2px;
            }
            QMenu::item:selected {
                background-color: #F3F4F6;
            }
            QMenu::separator {
                height: 1px;
                background-color: #E5E7EB;
                margin: 4px 8px;
            }
        """)

        # Action Modifier
        edit_action = QAction("✏️ Modifier", self)
        edit_action.triggered.connect(lambda: self.edit_client(client_id))
        menu.addAction(edit_action)

        menu.addSeparator()

        # Action Supprimer
        delete_action = QAction("🗑️ Supprimer", self)
        delete_action.triggered.connect(lambda: self.delete_client(client_id, client_nom))
        menu.addAction(delete_action)

        # Afficher le menu à la position du curseur
        menu.exec(self.clients_table.mapToGlobal(position))

    def load_clients(self):
        """Charge les clients depuis la base de données"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT * FROM clients ORDER BY code")
        clients = cursor.fetchall()

        self.clients_table.setRowCount(0)

        for row_num, client in enumerate(clients):
            self.clients_table.insertRow(row_num)

            # Ajouter les données du client
            self.clients_table.setItem(row_num, 0, QTableWidgetItem(client['code'] or ""))
            self.clients_table.setItem(row_num, 1, QTableWidgetItem(client['nom']))
            self.clients_table.setItem(row_num, 2, QTableWidgetItem(client['ice'] or ""))
            self.clients_table.setItem(row_num, 3, QTableWidgetItem(client['if_fiscal'] or ""))
            self.clients_table.setItem(row_num, 4, QTableWidgetItem(client['telephone'] or ""))
            self.clients_table.setItem(row_num, 5, QTableWidgetItem(client['adresse'] or ""))

            # Créer les boutons d'action
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(2, 2, 2, 2)
            actions_layout.setSpacing(8)

            # Bouton d'édition
            edit_btn = QPushButton()
            edit_btn.setToolTip("Modifier")
            edit_btn.setFixedSize(36, 36)
            edit_btn.setText(svg_to_icon_html(EDIT_ICON, COLORS['secondary'], 18))
            edit_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {COLORS['surface']};
                    border: 1px solid {COLORS['secondary']};
                    border-radius: {BORDER_RADIUS['sm']};
                }}
                QPushButton:hover {{
                    background-color: {COLORS['secondary_light']};
                    color: white;
                }}
            """)
            edit_btn.clicked.connect(lambda _, id=client['id']: self.show_edit_client_dialog(id))

            # Bouton de suppression
            delete_btn = QPushButton()
            delete_btn.setToolTip("Supprimer")
            delete_btn.setFixedSize(36, 36)
            delete_btn.setText(svg_to_icon_html(DELETE_ICON, COLORS['error'], 18))
            delete_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {COLORS['surface']};
                    border: 1px solid {COLORS['error']};
                    border-radius: {BORDER_RADIUS['sm']};
                }}
                QPushButton:hover {{
                    background-color: {COLORS['error']};
                    color: white;
                }}
            """)
            delete_btn.clicked.connect(lambda _, id=client['id'], nom=client['nom']: self.delete_client(id, nom))

            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(delete_btn)
            actions_layout.addStretch()

            self.clients_table.setCellWidget(row_num, 6, actions_widget)

            # Stocker l'ID du client dans la première colonne (invisible)
            self.clients_table.item(row_num, 0).setData(Qt.UserRole, client['id'])

    def show_add_client_dialog(self):
        """Affiche la boîte de dialogue pour ajouter un client"""
        dialog = ClientDialog(self.db_manager, parent=self)
        dialog.client_saved.connect(self.on_client_saved)
        dialog.exec()

    def show_edit_client_dialog(self, client_id):
        """Affiche la boîte de dialogue pour modifier un client"""
        dialog = ClientDialog(self.db_manager, client_id=client_id, parent=self)
        dialog.client_saved.connect(self.on_client_saved)
        dialog.exec()

    def on_client_saved(self):
        """Appelé lorsqu'un client est ajouté ou modifié"""
        self.load_clients()

        # Émettre le signal pour informer les autres modules
        if self.signals:
            self.signals.clients_changed.emit()

    def delete_client(self, client_id, nom):
        """Supprime un client"""
        confirm = QMessageBox.question(
            self, "Confirmation",
            f"Êtes-vous sûr de vouloir supprimer le client {nom} ?\nCette action est irréversible.",
            QMessageBox.Yes | QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            cursor = self.db_manager.conn.cursor()
            cursor.execute("DELETE FROM clients WHERE id = ?", (client_id,))
            self.db_manager.conn.commit()

            self.load_clients()

            # Émettre le signal pour informer les autres modules
            if self.signals:
                self.signals.clients_changed.emit()

            QMessageBox.information(self, "Succès", f"Client {nom} supprimé avec succès.")

    def filter_clients(self):
        """Filtre les clients en fonction du texte de recherche"""
        search_text = self.search_input.text().lower()

        for row in range(self.clients_table.rowCount()):
            match_found = False

            for col in range(6):  # Exclure la colonne des actions
                item = self.clients_table.item(row, col)
                if item and search_text in item.text().lower():
                    match_found = True
                    break

            self.clients_table.setRowHidden(row, not match_found)

    def on_item_double_clicked(self, item):
        """Gère le double-clic sur un élément du tableau"""
        if item is None:
            return

        # Récupérer l'ID du client depuis la première colonne
        row = item.row()
        first_column_item = self.clients_table.item(row, 0)

        if first_column_item:
            # Récupérer l'ID stocké dans les données utilisateur
            client_id = first_column_item.data(Qt.UserRole)
            if client_id:
                # Ouvrir la boîte de dialogue d'édition
                self.show_edit_client_dialog(client_id)
