#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح بيانات الموردين في جدول المنتجات
"""

import sqlite3
from pathlib import Path

# إعداد المسار لقاعدة البيانات
DB_PATH = Path(__file__).parent / "database"
DATABASE_FILE = DB_PATH / "accounting.db"

def fix_supplier_data():
    """إصلاح بيانات الموردين"""
    try:
        conn = sqlite3.connect(str(DATABASE_FILE))
        cursor = conn.cursor()
        
        print("🔧 إصلاح بيانات الموردين")
        print("=" * 50)
        
        # 1. عرض المشكلة الحالية
        print("\n📋 البيانات الحالية:")
        cursor.execute("SELECT id, code, designation, fournisseur FROM produits")
        products = cursor.fetchall()
        
        for product in products:
            print(f"  ID: {product[0]} | Code: {product[1]} | Fournisseur: '{product[3]}'")
        
        # 2. إصلاح البيانات - تعيين مورد افتراضي
        print(f"\n🔧 إصلاح البيانات...")
        
        # الحصول على أول مورد من جدول الموردين
        cursor.execute("SELECT id, nom FROM fournisseurs LIMIT 1")
        default_supplier = cursor.fetchone()
        
        if default_supplier:
            supplier_name = default_supplier[1]
            print(f"📦 استخدام المورد الافتراضي: {supplier_name}")
            
            # تحديث جميع المنتجات التي تحتوي على تاريخ في حقل المورد
            cursor.execute("""
                UPDATE produits 
                SET fournisseur = ? 
                WHERE fournisseur LIKE '%-%-%:%:%'
            """, (supplier_name,))
            
            print(f"✅ تم تحديث {cursor.rowcount} منتج")
            
        else:
            # إذا لم يوجد موردين، استخدم قيمة افتراضية
            print("📦 لا يوجد موردين، استخدام قيمة افتراضية")
            cursor.execute("""
                UPDATE produits 
                SET fournisseur = 'مورد افتراضي' 
                WHERE fournisseur LIKE '%-%-%:%:%'
            """)
            print(f"✅ تم تحديث {cursor.rowcount} منتج")
        
        conn.commit()
        
        # 3. عرض النتيجة
        print(f"\n📋 البيانات بعد الإصلاح:")
        cursor.execute("SELECT id, code, designation, fournisseur FROM produits")
        products = cursor.fetchall()
        
        for product in products:
            print(f"  ID: {product[0]} | Code: {product[1]} | Fournisseur: '{product[3]}'")
        
        conn.close()
        print(f"\n🎉 تم إصلاح البيانات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_supplier_data()