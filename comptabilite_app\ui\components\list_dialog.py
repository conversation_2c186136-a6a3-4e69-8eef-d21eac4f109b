from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                              QHeaderView, QWidget, QSizePolicy, QFrame)
from PySide6.QtCore import Qt, QSize, Signal
from PySide6.QtGui import QIcon
import sqlite3

class ListDialog(QDialog):
    """
    Boîte de dialogue générique pour afficher une liste d'éléments avec des fonctionnalités de recherche et d'actions.
    Cette classe est conçue pour être héritée et personnalisée pour différents types d'éléments.
    """
    
    item_selected = Signal(int)  # Signal émis lorsqu'un élément est sélectionné (avec l'ID de l'élément)
    item_added = Signal()        # Signal émis lorsqu'un élément est ajouté
    item_edited = Signal(int)    # Signal émis lorsqu'un élément est modifié
    item_deleted = Signal(int)   # Signal émis lorsqu'un élément est supprimé
    
    def __init__(self, db_manager, title="Liste des éléments", parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setWindowTitle(title)
        self.setMinimumSize(900, 600)
        
        self.setup_ui(title)
    
    def setup_ui(self, title):
        """Configure l'interface utilisateur de la boîte de dialogue"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # En-tête avec titre et bouton d'ajout
        header_container = QWidget()
        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        # Titre
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #1A56DB;
        """)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Bouton d'ajout
        self.add_button = QPushButton("Ajouter")
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #1A56DB;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1E40AF;
            }
        """)
        self.add_button.clicked.connect(self.add_item)
        header_layout.addWidget(self.add_button)
        
        layout.addWidget(header_container)
        
        # Barre de recherche
        search_container = QWidget()
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(0, 0, 0, 0)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Rechercher...")
        self.search_input.setStyleSheet("""
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #D1D5DB;
            font-size: 14px;
        """)
        self.search_input.textChanged.connect(self.filter_items)
        
        search_layout.addWidget(self.search_input)
        
        layout.addWidget(search_container)
        
        # Tableau des éléments
        self.items_table = QTableWidget()
        self.setup_table()
        
        self.items_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
                background-color: white;
            }
            QHeaderView::section {
                background-color: #F3F4F6;
                padding: 8px;
                border: none;
                font-weight: bold;
                color: #1F2937;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #F3F4F6;
            }
            QTableWidget::item:selected {
                background-color: #EFF6FF;
                color: #1E40AF;
            }
        """)
        
        layout.addWidget(self.items_table)
        
        # Boutons de fermeture
        buttons_container = QWidget()
        buttons_layout = QHBoxLayout(buttons_container)
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        
        buttons_layout.addStretch()
        
        close_button = QPushButton("Fermer")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        close_button.clicked.connect(self.accept)
        buttons_layout.addWidget(close_button)
        
        layout.addWidget(buttons_container)
    
    def setup_table(self):
        """
        Configure le tableau des éléments.
        Cette méthode doit être surchargée par les classes dérivées.
        """
        pass
    
    def load_items(self):
        """
        Charge les éléments depuis la base de données.
        Cette méthode doit être surchargée par les classes dérivées.
        """
        pass
    
    def filter_items(self):
        """
        Filtre les éléments en fonction du texte de recherche.
        Cette méthode peut être surchargée par les classes dérivées pour une recherche personnalisée.
        """
        search_text = self.search_input.text().lower()
        
        for row in range(self.items_table.rowCount()):
            show_row = False
            
            for col in range(self.items_table.columnCount() - 1):  # Exclure la colonne des actions
                item = self.items_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            
            self.items_table.setRowHidden(row, not show_row)
    
    def add_item(self):
        """
        Ajoute un nouvel élément.
        Cette méthode doit être surchargée par les classes dérivées.
        """
        pass
    
    def edit_item(self, item_id):
        """
        Modifie un élément existant.
        Cette méthode doit être surchargée par les classes dérivées.
        """
        pass
    
    def delete_item(self, item_id):
        """
        Supprime un élément existant.
        Cette méthode doit être surchargée par les classes dérivées.
        """
        pass
    
    def select_item(self, item_id):
        """
        Sélectionne un élément et émet le signal item_selected.
        """
        self.item_selected.emit(item_id)
        self.accept()
    
    def create_action_buttons(self, row_index, item_id):
        """
        Crée les boutons d'action pour une ligne du tableau.
        """
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(5)
        
        # Bouton de sélection
        select_button = QPushButton()
        select_button.setIcon(QIcon(":/icons/select.png"))
        select_button.setToolTip("Sélectionner")
        select_button.setObjectName("icon_button")
        select_button.setFixedSize(QSize(30, 30))
        select_button.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
        """)
        select_button.clicked.connect(lambda: self.select_item(item_id))
        
        # Bouton d'édition
        edit_button = QPushButton()
        edit_button.setIcon(QIcon(":/icons/edit.png"))
        edit_button.setToolTip("Modifier")
        edit_button.setObjectName("icon_button")
        edit_button.setFixedSize(QSize(30, 30))
        edit_button.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
        """)
        edit_button.clicked.connect(lambda: self.edit_item(item_id))
        
        # Bouton de suppression
        delete_button = QPushButton()
        delete_button.setIcon(QIcon(":/icons/delete.png"))
        delete_button.setToolTip("Supprimer")
        delete_button.setObjectName("icon_button_danger")
        delete_button.setFixedSize(QSize(30, 30))
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #EF4444;
                color: white;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #DC2626;
            }
        """)
        delete_button.clicked.connect(lambda: self.delete_item(item_id))
        
        actions_layout.addWidget(select_button)
        actions_layout.addWidget(edit_button)
        actions_layout.addWidget(delete_button)
        actions_layout.addStretch()
        
        return actions_widget
