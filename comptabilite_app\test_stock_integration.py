#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test d'intégration pour le système de gestion automatique du stock lors des factures
"""

import sqlite3
import datetime
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_manager import DatabaseManager

def test_stock_integration():
    """Test l'intégration entre les factures et le stock"""

    print("🧪 Test d'intégration Stock-Factures")
    print("=" * 50)

    # Initialiser la base de données
    db_manager = DatabaseManager()
    cursor = db_manager.conn.cursor()

    try:
        # 1. Vérifier la structure de la table mouvements_stock
        print("\n📋 1. Vérification de la structure de mouvements_stock:")
        cursor.execute("PRAGMA table_info(mouvements_stock)")
        columns = cursor.fetchall()

        print("   Colonnes disponibles:")
        for col in columns:
            print(f"   - {col[1]} ({col[2]})")

        # 2. Vérifier les produits existants
        print("\n📦 2. Produits disponibles:")
        cursor.execute("SELECT id, designation, stock FROM produits")
        produits = cursor.fetchall()

        for produit in produits:
            print(f"   - ID: {produit[0]}, Nom: {produit[1]}, Stock: {produit[2]}")

        # 3. Vérifier les mouvements de stock existants
        print("\n📊 3. Mouvements de stock existants:")
        cursor.execute("""
            SELECT id, produit_id, type_mouvement, quantite, reference, facture_id, designation
            FROM mouvements_stock
            ORDER BY date_mouvement DESC
            LIMIT 10
        """)
        mouvements = cursor.fetchall()

        if mouvements:
            for mouvement in mouvements:
                print(f"   - ID: {mouvement[0]}, Produit: {mouvement[1]}, Type: {mouvement[2]}, "
                      f"Qté: {mouvement[3]}, Ref: {mouvement[4]}, Facture: {mouvement[5]}, "
                      f"Désignation: {mouvement[6]}")
        else:
            print("   Aucun mouvement de stock trouvé")

        # 4. Simuler l'ajout d'une facture avec mouvements de stock
        print("\n🧪 4. Simulation d'ajout de facture:")

        # Créer une facture test
        cursor.execute("""
            INSERT INTO factures_vente (numero, date_creation, client_id, total_ht, total_ttc)
            VALUES (?, ?, ?, ?, ?)
        """, ("TEST-001", datetime.datetime.now().strftime('%Y-%m-%d'), 1, 100.0, 120.0))

        facture_id = cursor.lastrowid
        print(f"   ✅ Facture créée avec ID: {facture_id}")

        # Ajouter des lignes de facture
        if produits:
            produit_test = produits[0]  # Prendre le premier produit

            cursor.execute("""
                INSERT INTO lignes_facture (facture_id, designation, unite, quantite, prix_unitaire_ht, taux_tva, total_ht)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (facture_id, produit_test[1], "pièce", 2, 25.0, 20.0, 50.0))

            print(f"   ✅ Ligne de facture ajoutée: {produit_test[1]} x 2")

            # Ajouter le mouvement de stock correspondant
            cursor.execute("""
                INSERT INTO mouvements_stock (produit_id, facture_id, designation, type_mouvement, quantite, reference, date_mouvement)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (produit_test[0], facture_id, produit_test[1], 'sortie', 2, f'Facture TEST-001', datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')))

            print(f"   ✅ Mouvement de stock ajouté: sortie de {produit_test[1]} x 2")

        # 5. Vérifier le résultat
        print("\n📊 5. Vérification du résultat:")
        cursor.execute("""
            SELECT
                p.designation,
                p.stock as stock_actuel,
                COALESCE(SUM(CASE WHEN m.type_mouvement = 'sortie' THEN m.quantite ELSE 0 END), 0) as total_sorties
            FROM produits p
            LEFT JOIN mouvements_stock m ON p.id = m.produit_id
            GROUP BY p.id, p.designation, p.stock
        """)

        resultats = cursor.fetchall()
        for resultat in resultats:
            print(f"   - {resultat[0]}: Stock={resultat[1]}, Sorties={resultat[2]}")

        # Valider les changements
        db_manager.conn.commit()
        print("\n✅ Test terminé avec succès!")

    except Exception as e:
        print(f"\n❌ Erreur pendant le test: {str(e)}")
        db_manager.conn.rollback()

    finally:
        # Nettoyer les données de test
        try:
            cursor.execute("DELETE FROM mouvements_stock WHERE reference LIKE 'Facture TEST-%'")
            cursor.execute("DELETE FROM lignes_facture WHERE facture_id IN (SELECT id FROM factures_vente WHERE numero LIKE 'TEST-%')")
            cursor.execute("DELETE FROM factures_vente WHERE numero LIKE 'TEST-%'")
            db_manager.conn.commit()
            print("🧹 Données de test nettoyées")
        except:
            pass

if __name__ == "__main__":
    test_stock_integration()
