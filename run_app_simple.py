#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Système de Comptabilité - Tkinter Simple
Application de comptabilité temporaire sans CustomTkinter
"""

import sys
import os
import traceback
import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from pathlib import Path

# إعداد المسار النسبي لقاعدة البيانات
DB_PATH = Path(__file__).parent / "database"
DB_PATH.mkdir(exist_ok=True)
DATABASE_FILE = DB_PATH / "accounting.db"

class SimpleAccountingApp(tk.Tk):
    def __init__(self):
        super().__init__()

        self.title("Système de Comptabilité")
        self.geometry("1200x800")

        self.db_path = str(DATABASE_FILE)
        self.setup_database()

        # إعداد الواجهة
        self.setup_ui()

    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء جدول العملاء
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS clients (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT,
                    address TEXT,
                    ice TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # إنشاء جدول الموردين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS suppliers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT,
                    address TEXT,
                    ice TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # إنشاء جدول المنتجات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    price REAL NOT NULL,
                    quantity INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            conn.commit()
            conn.close()
            print("✅ Base de données prête")

        except Exception as e:
            print(f"❌ Erreur lors de la configuration de la base de données: {e}")

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء القائمة الرئيسية
        menubar = tk.Menu(self)
        self.config(menu=menubar)

        # Menu Fichier
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Fichier", menu=file_menu)
        file_menu.add_command(label="Quitter", command=self.quit)

        # Menu Données
        data_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Données", menu=data_menu)
        data_menu.add_command(label="Clients", command=self.show_clients)
        data_menu.add_command(label="Fournisseurs", command=self.show_suppliers)
        data_menu.add_command(label="Produits", command=self.show_products)

        # الإطار الرئيسي
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Titre de bienvenue
        welcome_label = ttk.Label(main_frame, text="Bienvenue dans le Système de Comptabilité", font=("Arial", 16, "bold"))
        welcome_label.pack(pady=20)

        # إطار الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=20)

        # Boutons de navigation
        ttk.Button(buttons_frame, text="Gestion Clients", command=self.show_clients).pack(side=tk.LEFT, padx=10)
        ttk.Button(buttons_frame, text="Gestion Fournisseurs", command=self.show_suppliers).pack(side=tk.LEFT, padx=10)
        ttk.Button(buttons_frame, text="Gestion Produits", command=self.show_products).pack(side=tk.LEFT, padx=10)

        # إطار المحتوى
        self.content_frame = ttk.Frame(main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True, pady=20)

        # عرض الإحصائيات الأولية
        self.show_dashboard()

    def show_dashboard(self):
        """عرض لوحة التحكم"""
        # مسح المحتوى السابق
        for widget in self.content_frame.winfo_children():
            widget.destroy()

        # Titre du tableau de bord
        title_label = ttk.Label(self.content_frame, text="Tableau de Bord", font=("Arial", 14, "bold"))
        title_label.pack(pady=10)

        # إطار الإحصائيات
        stats_frame = ttk.Frame(self.content_frame)
        stats_frame.pack(fill=tk.X, pady=10)

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # عدد العملاء
            cursor.execute("SELECT COUNT(*) FROM clients")
            clients_count = cursor.fetchone()[0]

            # عدد الموردين
            cursor.execute("SELECT COUNT(*) FROM suppliers")
            suppliers_count = cursor.fetchone()[0]

            # عدد المنتجات
            cursor.execute("SELECT COUNT(*) FROM products")
            products_count = cursor.fetchone()[0]

            conn.close()

            # Affichage des statistiques
            ttk.Label(stats_frame, text=f"Nombre de clients: {clients_count}").pack(side=tk.LEFT, padx=20)
            ttk.Label(stats_frame, text=f"Nombre de fournisseurs: {suppliers_count}").pack(side=tk.LEFT, padx=20)
            ttk.Label(stats_frame, text=f"Nombre de produits: {products_count}").pack(side=tk.LEFT, padx=20)

        except Exception as e:
            ttk.Label(stats_frame, text=f"Erreur lors du chargement des statistiques: {e}").pack()

    def show_clients(self):
        """عرض صفحة العملاء"""
        # مسح المحتوى السابق
        for widget in self.content_frame.winfo_children():
            widget.destroy()

        # Titre de la page
        title_label = ttk.Label(self.content_frame, text="Gestion des Clients", font=("Arial", 14, "bold"))
        title_label.pack(pady=10)

        # إطار الأزرار
        buttons_frame = ttk.Frame(self.content_frame)
        buttons_frame.pack(fill=tk.X, pady=5)

        ttk.Button(buttons_frame, text="Ajouter un nouveau client", command=self.add_client).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Actualiser la liste", command=self.show_clients).pack(side=tk.LEFT, padx=5)

        # جدول العملاء
        columns = ("ID", "Nom", "Téléphone", "Adresse", "ICE")
        tree = ttk.Treeview(self.content_frame, columns=columns, show="headings", height=15)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)

        # تحميل بيانات العملاء
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT id, name, phone, address, ice FROM clients")
            clients = cursor.fetchall()
            conn.close()

            for client in clients:
                tree.insert("", tk.END, values=client)

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des clients: {e}")

        tree.pack(fill=tk.BOTH, expand=True, pady=10)

    def add_client(self):
        """إضافة عميل جديد"""
        # Fenêtre d'ajout de client
        add_window = tk.Toplevel(self)
        add_window.title("Ajouter un nouveau client")
        add_window.geometry("400x300")

        # Champs de saisie
        ttk.Label(add_window, text="Nom du client:").pack(pady=5)
        name_entry = ttk.Entry(add_window, width=40)
        name_entry.pack(pady=5)

        ttk.Label(add_window, text="Numéro de téléphone:").pack(pady=5)
        phone_entry = ttk.Entry(add_window, width=40)
        phone_entry.pack(pady=5)

        ttk.Label(add_window, text="Adresse:").pack(pady=5)
        address_entry = ttk.Entry(add_window, width=40)
        address_entry.pack(pady=5)

        ttk.Label(add_window, text="Numéro ICE:").pack(pady=5)
        ice_entry = ttk.Entry(add_window, width=40)
        ice_entry.pack(pady=5)

        def save_client():
            name = name_entry.get().strip()
            if not name:
                messagebox.showerror("Erreur", "Veuillez saisir le nom du client")
                return

            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO clients (name, phone, address, ice)
                    VALUES (?, ?, ?, ?)
                """, (name, phone_entry.get(), address_entry.get(), ice_entry.get()))
                conn.commit()
                conn.close()

                messagebox.showinfo("Succès", "Client ajouté avec succès")
                add_window.destroy()
                self.show_clients()  # Actualiser la liste des clients

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de l'ajout du client: {e}")

        # Boutons Sauvegarder et Annuler
        buttons_frame = ttk.Frame(add_window)
        buttons_frame.pack(pady=20)

        ttk.Button(buttons_frame, text="Sauvegarder", command=save_client).pack(side=tk.LEFT, padx=10)
        ttk.Button(buttons_frame, text="Annuler", command=add_window.destroy).pack(side=tk.LEFT, padx=10)

    def show_suppliers(self):
        """عرض صفحة الموردين"""
        messagebox.showinfo("Bientôt", "Page des fournisseurs en cours de développement")

    def show_products(self):
        """عرض صفحة المنتجات"""
        messagebox.showinfo("Bientôt", "Page des produits en cours de développement")

def main():
    """Fonction principale"""
    print("🚀 Démarrage du système de comptabilité simple...")

    try:
        app = SimpleAccountingApp()
        app.mainloop()
        print("✅ Application fermée avec succès")

    except Exception as e:
        print(f"❌ Erreur dans l'application: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
