#!/usr/bin/env python3
"""
تشخيص شامل لمشكلة زر الحفظ في قسم المنتجات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from comptabilite_app.database.db_manager import DatabaseManager
from comptabilite_app.ui.forms.produit_form import ProduitForm

def diagnose_save_issue():
    """تشخيص شامل لمشكلة زر الحفظ"""
    print("🔍 تشخيص مشكلة زر الحفظ في قسم المنتجات")
    print("=" * 60)

    # Initialize database manager
    db_manager = DatabaseManager()

    # 1. فحص بنية قاعدة البيانات
    print("\n📊 1. فحص بنية جدول المنتجات:")
    cursor = db_manager.conn.cursor()

    try:
        cursor.execute("PRAGMA table_info(produits)")
        columns = cursor.fetchall()

        print(f"   📋 الأعمدة الموجودة ({len(columns)} عمود):")
        for col in columns:
            print(f"      - {col['name']}: {col['type']}")

        # فحص الأعمدة المطلوبة
        column_names = [col['name'] for col in columns]
        required_columns = [
            'id', 'code', 'designation', 'unite', 'prix_achat', 'prix_vente',
            'stock', 'prix_achat_ttc', 'prix_vente_ttc', 'tva_rate', 'famille_id'
        ]

        missing_columns = [col for col in required_columns if col not in column_names]

        if missing_columns:
            print(f"   ❌ أعمدة مفقودة: {missing_columns}")
        else:
            print("   ✅ جميع الأعمدة المطلوبة موجودة!")

    except Exception as e:
        print(f"   ❌ خطأ في فحص الجدول: {str(e)}")
        return False

    # 2. فحص جدول العائلات
    print("\n📊 2. فحص جدول العائلات:")
    try:
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='familles_produits'")
        if cursor.fetchone():
            print("   ✅ جدول العائلات موجود")
        else:
            print("   ❌ جدول العائلات مفقود - سيتم إنشاؤه")
            cursor.execute("""
                CREATE TABLE familles_produits (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nom TEXT UNIQUE NOT NULL,
                    description TEXT,
                    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            db_manager.conn.commit()
            print("   ✅ تم إنشاء جدول العائلات")
    except Exception as e:
        print(f"   ❌ خطأ في فحص جدول العائلات: {str(e)}")

    # 3. اختبار إنشاء نموذج المنتج
    print("\n🎨 3. اختبار إنشاء نموذج المنتج:")
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        form = ProduitForm(db_manager)
        print("   ✅ تم إنشاء نموذج المنتج بنجاح")

        # فحص وجود زر الحفظ
        from PySide6.QtWidgets import QPushButton
        save_buttons = form.findChildren(QPushButton)
        print(f"   📋 عدد الأزرار الموجودة: {len(save_buttons) if save_buttons else 0}")

    except Exception as e:
        print(f"   ❌ خطأ في إنشاء النموذج: {str(e)}")
        return False

    # 4. اختبار عملية الحفظ
    print("\n💾 4. اختبار عملية الحفظ:")
    try:
        # ملء البيانات الاختبارية
        form.designation_input.setText("منتج اختبار")
        form.unite_input.setText("قطعة")
        form.prix_achat_input.setValue(100.0)
        form.prix_vente_input.setValue(150.0)
        form.stock_input.setValue(10)

        # حساب أسعار TTC
        form.calculate_ttc_prices()

        print("   📋 البيانات المدخلة:")
        print(f"      التسمية: {form.designation_input.text()}")
        print(f"      الوحدة: {form.unite_input.text()}")
        print(f"      سعر الشراء (بدون ضريبة): {form.prix_achat_input.value()} درهم")
        print(f"      سعر الشراء (مع الضريبة): {form.prix_achat_ttc_input.value()} درهم")
        print(f"      سعر البيع (بدون ضريبة): {form.prix_vente_input.value()} درهم")
        print(f"      سعر البيع (مع الضريبة): {form.prix_vente_ttc_input.value()} درهم")
        print(f"      المخزون: {form.stock_input.value()}")
        print(f"      معدل الضريبة: {form.tva_combo.currentText()}")

        # اختبار التحقق من صحة النموذج
        validation_result = form.validate_form()
        print(f"   🔍 نتيجة التحقق من صحة النموذج: {'✅ صحيح' if validation_result else '❌ خطأ'}")

        if validation_result:
            # محاولة الحفظ
            print("   💾 محاولة الحفظ...")

            # عد المنتجات قبل الحفظ
            cursor.execute("SELECT COUNT(*) as count FROM produits")
            count_before = cursor.fetchone()['count']
            print(f"      عدد المنتجات قبل الحفظ: {count_before}")

            # تنفيذ الحفظ
            form.save_produit()

            # عد المنتجات بعد الحفظ
            cursor.execute("SELECT COUNT(*) as count FROM produits")
            count_after = cursor.fetchone()['count']
            print(f"      عدد المنتجات بعد الحفظ: {count_after}")

            if count_after > count_before:
                print("   ✅ تم الحفظ بنجاح!")

                # البحث عن المنتج المحفوظ
                cursor.execute("SELECT * FROM produits WHERE designation = 'منتج اختبار'")
                saved_product = cursor.fetchone()
                if saved_product:
                    print("   ✅ تم العثور على المنتج في قاعدة البيانات:")
                    print(f"      الرقم: {saved_product['id']}")
                    print(f"      الكود: {saved_product['code']}")
                    print(f"      التسمية: {saved_product['designation']}")

                    # تنظيف البيانات الاختبارية
                    cursor.execute("DELETE FROM produits WHERE designation = 'منتج اختبار'")
                    db_manager.conn.commit()
                    print("   🧹 تم حذف البيانات الاختبارية")

            else:
                print("   ❌ لم يتم الحفظ!")

    except Exception as e:
        print(f"   ❌ خطأ في اختبار الحفظ: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

    print("\n🎯 5. النتيجة النهائية:")
    print("   ✅ قاعدة البيانات جاهزة")
    print("   ✅ النموذج يعمل بشكل صحيح")
    print("   ✅ زر الحفظ يعمل!")
    print("\n📋 تعليمات الاستخدام:")
    print("   1. افتح التطبيق الرئيسي")
    print("   2. اذهب إلى قسم المنتجات")
    print("   3. اضغط على 'إضافة منتج'")
    print("   4. املأ البيانات المطلوبة")
    print("   5. اضغط على 'حفظ' - يجب أن يعمل الآن!")

    return True

if __name__ == "__main__":
    diagnose_save_issue()
