from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                              QLabel, QLineEdit, QFormLayout, QMessageBox, QGridLayout)
from PySide6.QtCore import Qt, QRegularExpression, Signal
from PySide6.QtGui import QRegularExpressionValidator
import sqlite3

try:
    from .autocomplete_widget import apply_autocomplete_to_dialog
except ImportError:
    def apply_autocomplete_to_dialog(dialog, db_manager):
        pass

class FournisseurDialog(QDialog):
    """Boîte de dialogue modale pour ajouter ou modifier un fournisseur"""

    # Signal émis lorsqu'un fournisseur est ajouté ou modifié
    fournisseur_saved = Signal()

    def __init__(self, db_manager, fournisseur_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.fournisseur_id = fournisseur_id
        self.is_edit_mode = fournisseur_id is not None

        self.setWindowTitle("Ajouter un fournisseur" if not self.is_edit_mode else "Modifier un fournisseur")
        self.setMinimumWidth(700)
        self.setModal(True)

        self.setup_ui()

        if self.is_edit_mode:
            self.load_fournisseur_data()
        else:
            self.code_input.setText(self.db_manager.generer_code_fournisseur())

    def setup_ui(self):
        """Configure l'interface utilisateur de la boîte de dialogue"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title = QLabel("Informations du fournisseur")
        title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #1A56DB;
            margin-bottom: 10px;
        """)
        layout.addWidget(title)

        # Formulaire
        form_grid = QGridLayout()
        form_grid.setSpacing(15)

        # Première colonne
        code_label = QLabel("Code fournisseur:")
        self.code_input = QLineEdit()
        self.code_input.setReadOnly(True)
        self.code_input.setStyleSheet("background-color: #F3F4F6; color: #6B7280;")
        form_grid.addWidget(code_label, 0, 0)
        form_grid.addWidget(self.code_input, 0, 1)

        nom_label = QLabel("Nom / Raison sociale*:")
        self.nom_input = QLineEdit()
        self.nom_input.setPlaceholderText("Obligatoire")
        form_grid.addWidget(nom_label, 1, 0)
        form_grid.addWidget(self.nom_input, 1, 1)

        ice_label = QLabel("ICE:")
        self.ice_input = QLineEdit()
        self.ice_input.setPlaceholderText("Identifiant Commun de l'Entreprise")
        form_grid.addWidget(ice_label, 2, 0)
        form_grid.addWidget(self.ice_input, 2, 1)

        if_label = QLabel("IF:")
        self.if_input = QLineEdit()
        self.if_input.setPlaceholderText("Identifiant Fiscal")
        form_grid.addWidget(if_label, 3, 0)
        form_grid.addWidget(self.if_input, 3, 1)

        # Deuxième colonne
        adresse_label = QLabel("Adresse:")
        self.adresse_input = QLineEdit()
        form_grid.addWidget(adresse_label, 0, 2)
        form_grid.addWidget(self.adresse_input, 0, 3)

        telephone_label = QLabel("Téléphone:")
        self.telephone_input = QLineEdit()
        # Ajouter un validateur pour le téléphone
        telephone_regex = QRegularExpression("^[0-9+\\-\\s]{8,15}$")
        self.telephone_input.setValidator(QRegularExpressionValidator(telephone_regex))
        form_grid.addWidget(telephone_label, 1, 2)
        form_grid.addWidget(self.telephone_input, 1, 3)

        email_label = QLabel("E-mail:")
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("<EMAIL>")
        form_grid.addWidget(email_label, 2, 2)
        form_grid.addWidget(self.email_input, 2, 3)

        contact_label = QLabel("Personne à contacter:")
        self.contact_input = QLineEdit()
        form_grid.addWidget(contact_label, 3, 2)
        form_grid.addWidget(self.contact_input, 3, 3)

        layout.addLayout(form_grid)

        # Boutons d'action
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # Bouton Annuler
        self.cancel_btn = QPushButton("Annuler")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #F3F4F6;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        # Bouton Enregistrer
        self.save_btn = QPushButton("Enregistrer")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #1A56DB;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1E40AF;
            }
        """)
        self.save_btn.clicked.connect(self.save_fournisseur)
        buttons_layout.addWidget(self.save_btn)

        layout.addLayout(buttons_layout)

        # تطبيق الاقتراحات التلقائية
        apply_autocomplete_to_dialog(self, self.db_manager)

    def load_fournisseur_data(self):
        """Charge les données du fournisseur à modifier"""
        cursor = self.db_manager.conn.cursor()
        cursor.execute("SELECT * FROM fournisseurs WHERE id = ?", (self.fournisseur_id,))
        fournisseur = cursor.fetchone()

        if fournisseur:
            self.code_input.setText(fournisseur['code'] or "")
            self.nom_input.setText(fournisseur['nom'])
            self.ice_input.setText(fournisseur['ice'] or "")
            self.if_input.setText(fournisseur['if_fiscal'] or "")
            self.adresse_input.setText(fournisseur['adresse'] or "")
            self.telephone_input.setText(fournisseur['telephone'] or "")
            self.email_input.setText(fournisseur['email'] or "")
            self.contact_input.setText(fournisseur['contact'] or "")

    def save_fournisseur(self):
        """Enregistre le fournisseur (ajout ou modification)"""
        nom = self.nom_input.text().strip()
        if not nom:
            QMessageBox.warning(self, "Erreur", "Le nom du fournisseur est obligatoire.")
            return

        code = self.code_input.text()
        if not code:
            code = self.db_manager.generer_code_fournisseur()

        cursor = self.db_manager.conn.cursor()
        try:
            if self.is_edit_mode:
                # Modification d'un fournisseur existant
                cursor.execute(
                    """UPDATE fournisseurs SET
                       code = ?, nom = ?, ice = ?, if_fiscal = ?, adresse = ?,
                       telephone = ?, email = ?, contact = ?
                       WHERE id = ?""",
                    (code, nom, self.ice_input.text(), self.if_input.text(),
                     self.adresse_input.text(), self.telephone_input.text(),
                     self.email_input.text(), self.contact_input.text(), self.fournisseur_id)
                )
                message = f"Fournisseur {nom} modifié avec succès."
            else:
                # Ajout d'un nouveau fournisseur
                cursor.execute(
                    """INSERT INTO fournisseurs
                       (code, nom, ice, if_fiscal, adresse, telephone, email, contact)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                    (code, nom, self.ice_input.text(), self.if_input.text(),
                     self.adresse_input.text(), self.telephone_input.text(),
                     self.email_input.text(), self.contact_input.text())
                )
                message = f"Fournisseur {nom} ajouté avec succès.\nCode fournisseur: {code}"

            self.db_manager.conn.commit()

            # Émettre le signal pour informer que le fournisseur a été enregistré
            self.fournisseur_saved.emit()

            QMessageBox.information(self, "Succès", message)
            self.accept()

        except sqlite3.IntegrityError:
            QMessageBox.warning(self, "Erreur", "Ce code fournisseur existe déjà. Veuillez utiliser un autre code.")
